<template>
  <div>
    <CetDialog
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
      :width="actionTitle_in === 1 ? '960px' : '640px'"
    >
      <el-main class="eem-cont-c1 fullheight content">
        <CetForm
          :data.sync="CetForm_1.data"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
          class="form"
        >
          <el-row :gutter="$J3" class="row-box">
            <el-col :span="12" v-if="actionTitle_in === 2">
              <el-form-item
                :label="$T('选择仪表')"
                class="fullwidth"
                prop="selectInstrument"
                :rules="{
                  required: true,
                  message: $T('请选择仪表'),
                  trigger: ['blur', 'change']
                }"
              >
                <ElSelect
                  v-model="CetForm_1.data.selectInstrument"
                  v-bind="ElSelect_select"
                  v-on="ElSelect_select.event"
                  @change="handlerInstrument"
                  :disabled="editData_in && editData_in.id ? true : false"
                  clearable
                  filterable
                  :placeholder="$T('请输入仪表名称或仪表编号')"
                >
                  <ElOption
                    v-for="item in instrumentList"
                    :key="item.id"
                    :label="item.text"
                    :value="item.id"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col
              :span="
                item.name == 'certiication' ? 24 : actionTitle_in === 2 ? 12 : 8
              "
              v-for="(item, index) in templateArr"
              :key="index"
            >
              <el-form-item
                :label="`${item.alias}`"
                :prop="item.name"
                :rules="judgeRules(item)"
                class="fullwidth"
              >
                <!-- 测量对象使用弹框 -->
                <div
                  class="text img measuring"
                  :title="CetForm_1.data[item.name]"
                  v-if="item.name == 'deviceid'"
                  style="width: 100%"
                >
                  <!-- 先注释名称 -->
                  <div class="text-ellipsis fl">
                    {{ CetForm_1.data?.devicename || "--" }}
                  </div>
                  <CetButton
                    class="fr"
                    v-bind="CetButton_add"
                    v-on="CetButton_add.event"
                  ></CetButton>
                </div>

                <!-- 检定记录仪表编号用输入框搜索模式 -->
                <el-autocomplete
                  style="width: 100%"
                  v-else-if="
                    actionTitle_in === 2 &&
                    (item.name == 'code' || item.name == 'name')
                  "
                  v-model="CetForm_1.data[item.name]"
                  :fetch-suggestions="querySearch"
                  :placeholder="$T('请选择仪表')"
                  :trigger-on-focus="false"
                  :disabled="true"
                  @select="handleSelect"
                  @change="autocompleteChange"
                >
                  <template slot-scope="{ item }">
                    <div>{{ `${item.code}(${item.name || ""})` }}</div>
                  </template>
                </el-autocomplete>
                <!-- 检定记录回填方式 -->
                <div
                  class="text img"
                  v-else-if="
                    actionTitle_in === 2 &&
                    backfillArr.indexOf(item.name) !== -1
                  "
                  :title="CetForm_1.data[item.name]"
                >
                  <ElInput
                    v-bind="ElInput_string"
                    v-on="ElInput_string.event"
                    v-model.trim="CetForm_1.data[item.name]"
                    disabled
                    placeholder=""
                  ></ElInput>
                </div>
                <ElInput
                  v-else-if="item.datatype == 'string'"
                  v-model.trim="CetForm_1.data[item.name]"
                  v-bind="ElInput_string"
                  v-on="ElInput_string.event"
                  :disabled="
                    item.datatype == 'string' &&
                    (item.name === 'monitorname' || item.name === 'location')
                  "
                ></ElInput>
                <!-- 单独处理检定/校准周期 增加周期选项 -->
                <div
                  v-else-if="
                    item.datatype == 'int4' &&
                    item.name === 'plannedverificationcycle'
                  "
                >
                  <ElInputNumber
                    v-model="CetForm_1.data[item.name]"
                    :min="
                      item.name === 'plannedverificationcycle' ? 1 : -99999999
                    "
                    v-bind="ElInputNumber_num1"
                    v-on="ElInputNumber_num1.event"
                  ></ElInputNumber>
                  <el-select
                    v-model="CetForm_1.data.verificationcycle"
                    style="width: 64px"
                    class="cycleSelect"
                    clearable
                  >
                    <ElOption
                      v-for="item in cycleList"
                      :key="item.id"
                      :label="item.text"
                      :value="item.id"
                    ></ElOption>
                  </el-select>
                </div>
                <ElInputNumber
                  v-else-if="item.datatype == 'int4'"
                  v-model="CetForm_1.data[item.name]"
                  :min="
                    item.name === 'plannedverificationcycle' ? 1 : -99999999
                  "
                  v-bind="ElInputNumber_num1"
                  v-on="ElInputNumber_num1.event"
                ></ElInputNumber>
                <ElInputNumber
                  v-else-if="item.datatype == 'int8'"
                  v-model="CetForm_1.data[item.name]"
                  :min="
                    item.name === 'plannedverificationcycle' ? 1 : -99999999
                  "
                  v-bind="ElInputNumber_num1"
                  v-on="ElInputNumber_num1.event"
                ></ElInputNumber>
                <ElInputNumber
                  v-else-if="item.datatype == 'float'"
                  v-model="CetForm_1.data[item.name]"
                  :min="
                    item.name === 'plannedverificationcycle' ? 1 : -99999999.99
                  "
                  v-bind="ElInputNumber_num2"
                  v-on="ElInputNumber_num2.event"
                ></ElInputNumber>
                <div v-else-if="item.datatype == 'date'">
                  <el-date-picker
                    :style="{
                      width:
                        item.name == 'nextoverhauldate' && actionTitle_in === 2
                          ? '93% !important'
                          : '100% !important'
                    }"
                    v-model="CetForm_1.data[item.name]"
                    :placeholder="$T('选择日期')"
                    value-format="timestamp"
                  ></el-date-picker>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="按照最近一次检定日期+检定周期，自动生成下一次检定日期"
                    placement="bottom-end"
                  >
                    <i
                      class="el-icon-question ml-J0"
                      v-if="
                        item.name == 'nextoverhauldate' && actionTitle_in === 2
                      "
                    ></i>
                  </el-tooltip>
                </div>
                <ElSelect
                  v-else-if="item.datatype == 'boolean'"
                  v-model="CetForm_1.data[item.name]"
                  v-bind="ElSelect_boolean"
                  v-on="ElSelect_boolean.event"
                >
                  <ElOption
                    v-for="item in ElOption_boolean.options_in"
                    :key="item[ElOption_boolean.key]"
                    :label="item[ElOption_boolean.label]"
                    :value="item[ElOption_boolean.value]"
                    :disabled="item[ElOption_boolean.disabled]"
                  ></ElOption>
                </ElSelect>
                <ElSelect
                  v-else-if="
                    (item.datatype == 'enum' ||
                      (item.enumerationvalue &&
                        item.enumerationvalue.length)) &&
                    item.name !== 'metertype'
                  "
                  v-model="CetForm_1.data[item.name]"
                  v-bind="ElSelect_select"
                  v-on="ElSelect_select.event"
                >
                  <ElOption
                    v-for="(item, index) in item.enumerationvalue"
                    :key="index"
                    :label="item.text"
                    :value="item.id"
                  ></ElOption>
                </ElSelect>
                <el-cascader
                  style="width: 100%"
                  v-if="item.datatype == 'enum' && item.name === 'metertype'"
                  clearable
                  v-model="CetForm_1.data[item.name]"
                  :props="metertypeProps"
                  :options="metertypeOptions"
                ></el-cascader>
                <div
                  class="text img flexImg"
                  :title="CetForm_1.data[item.name]"
                  v-else-if="item.datatype == 'img'"
                  :style="{
                    width: item.name == 'certiication' ? '100%' : null
                  }"
                >
                  <CetButton
                    class="fr"
                    v-bind="CetButton_upload"
                    @click="CetButton_upload_click_out(item.name)"
                    v-on="CetButton_upload.event"
                  ></CetButton>
                  <div
                    class="text-ellipsis imgName mt-J1 pl-J0 pr-J0 brC"
                    v-if="CetForm_1.data[item.name]"
                  >
                    <div class="content">
                      <div>
                        <i class="el-icon-document"></i>

                        <span class="fcZS">
                          {{ CetForm_1.data[item.name] || "--" }}
                        </span>
                      </div>
                      <div class="cursor-pointer" @click="handelDelFile(item)">
                        <i class="el-icon-close"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </CetForm>
      </el-main>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="ml-J1"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
    <el-upload
      v-show="false"
      ref="upload"
      accept=".png,.jpeg,.bmp,.gif,.jpg"
      :infoText_in="$T('支持 JPG,JPEG,BMP,GIF,PNG 文件')"
      action=""
      :auto-upload="true"
      :multiple="false"
      :limit="1"
      :http-request="httpRequest"
    >
      <el-button
        slot="trigger"
        v-show="false"
        size="small"
        type="primary"
        ref="uploadBtn"
      >
        {{ $T("选取文件") }}
      </el-button>
    </el-upload>
    <measureObj
      :visibleTrigger_in="measureObj.visibleTrigger_in"
      :closeTrigger_in="measureObj.closeTrigger_in"
      :inputData_in="measureObj.inputData_in"
      @CetButton_confirm_out="measureObj_CetButton_confirm_out"
    />
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import common from "eem-base/utils/common.js";
import measureObj from "./measureObj.vue";
export default {
  name: "templateAdmin",
  components: {
    measureObj
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    template_in: {
      type: Array,
      default() {
        return [];
      }
    },
    editData_in: {
      type: Object,
      default() {
        return {};
      }
    },
    actionTitle_in: {
      //1 仪表台账  2检定记录
      type: Number
    },
    metertypeOptions: {
      type: Array,
      default: () => []
    }
  },
  data(vm) {
    // 自定义验证仪表编号方法
    var checkEquipmentnumber = (rule, value, callback) => {
      if (!value) {
        return callback(new Error($T("仪表编号不能为空")));
      }
      if (
        this.actionTitle_in === 2 &&
        !this.devices.find(item => item.code === value)
      ) {
        return callback(new Error($T("仪表编号不存在")));
      }
      return callback();
    };
    return {
      backfillArr: ["name", "model", "location"], // 检定记录用回填方式字段
      devices: [], //所有仪表
      requireArr: ["name", "code"], //必填字段
      fieldsName: "", // 暂存上传成功后要存储的对应字段名称
      templateArr: [],
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelPosition: "top",
        rules: {
          code: [
            {
              required: true,
              validator: checkEquipmentnumber,
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        },
        inline: true
      },
      // string组件
      ElInput_string: {
        value: "",
        clearable: true,
        placeholder: $T("请输入"),
        style: {
          width: "100%"
        },
        event: {}
      },
      ElSelect_select: {
        value: "",
        clearable: true,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElSelect_boolean: {
        value: "",
        clearable: true,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_boolean: {
        options_in: [
          {
            value: true,
            label: $T("是")
          },
          {
            value: false,
            label: $T("否")
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      ElInputNumber_num1: {
        ...common.check_numberInt,
        value: "",
        controls: false,
        clearable: true,
        placeholder: $T("请输入"),
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInputNumber_num2: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        clearable: true,
        placeholder: $T("请输入"),
        style: {
          width: "100%"
        },
        event: {}
      },
      CetButton_upload: {
        visible_in: true,
        disable_in: false,
        icon: "el-icon-upload2",
        title: $T("点击上传"),
        type: "primary",
        plain: true,
        event: {}
      },
      CetButton_add: {
        visible_in: true,
        title: $T("选择"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      measureObj: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      // 1弹窗组件
      CetDialog_1: {
        title: $T("新增"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,

        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      cycleList: [
        {
          id: 12,
          text: $T("天")
        },
        {
          id: 14,
          text: $T("月")
        },
        {
          id: 17,
          text: $T("年")
        }
      ],
      metertypeProps: {
        expandTrigger: "hover",
        children: "child",
        checkStrictly: true,
        label: "text",
        value: "id"
      },
      selectInstrument: undefined,
      instrumentList: []
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    userId() {
      return this.$store.state.userInfo.id;
    }
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      vm.createCondition();
      if (vm.editData_in) {
        vm.CetForm_1.data = vm._.cloneDeep(vm.editData_in);
        this.CetForm_1.data.location = this.CetForm_1.data.location || "--";
        if (this.actionTitle_in === 2) {
          this.CetForm_1.data.model = this.CetForm_1.data.model || "--";
        } else if (this.actionTitle_in === 1) {
          this.CetForm_1.data.monitorname =
            this.CetForm_1.data.monitorname || "--";
        }
        const { metertype, plannedverificationcycle } = vm.CetForm_1.data;
        if (metertype) {
          const result = vm.getParentIds(vm.metertypeOptions, metertype);
          vm.$set(vm.CetForm_1.data, "metertype", result);
        }
        if (!plannedverificationcycle) {
          vm.$set(
            vm.CetForm_1.data,
            "plannedverificationcycle",
            plannedverificationcycle ?? undefined
          );
        }
        vm.CetDialog_1.title =
          vm.actionTitle_in === 1 ? $T("编辑设备") : $T("编辑检定记录");
      } else {
        vm.CetForm_1.data = { verificationcycle: 12 };
        vm.CetDialog_1.title =
          vm.actionTitle_in === 1 ? $T("新增设备") : $T("新增检定记录");
      }
      vm.$set(
        vm.CetForm_1.data,
        "verificationcycle",
        vm.CetForm_1.data.verificationcycle ?? 12
      );
      vm.CetForm_1.resetTrigger_in = new Date().getTime();
      vm.$refs.upload.clearFiles();
      if (vm.actionTitle_in === 2) {
        this.selectInstrument = undefined;
        vm.getDeviceTableDate();
      }
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    "CetForm_1.data.lastoverhauldate": "calculateNextTime",
    "CetForm_1.data.plannedverificationcycle": "calculateNextTime",
    "CetForm_1.data.verificationcycle": "calculateNextTime"
  },
  methods: {
    // 生成表单验证规则
    judgeRules(item) {
      const list =
        this.actionTitle_in === 2
          ? ["nextoverhauldate", "lastoverhauldate"]
          : this.requireArr;
      var arr = [];
      if (list.indexOf(item.name) !== -1) {
        arr.push({
          required: true,
          message: `${item.alias}${$T("不能为空")}`,
          trigger: ["blur", "change"]
        });
      }
      return arr;
    },
    // 构造查询表单 过滤出启用项
    createCondition() {
      this.templateArr = this.template_in.filter(item => {
        if (this.actionTitle_in === 1) {
          if (item.onlydisplay === true) {
            return false;
          }
          // 设备台账测量对象使用弹框交互
          if (item.name === "monitorname") {
            // 测量对象改名
            item.alias = $T("测量对象");
          }
          if (["monitorlabel", "monitorid"].indexOf(item.name) === -1) {
            return !item.allowdeactivation || item.active;
          } else {
            return false;
          }
        } else {
          // 检定记录仪表
          return !item.allowdeactivation || item.active;
        }
      });
    },
    // 获取所有仪表
    async getDeviceTableDate() {
      const queryData = {
        rootCondition: {
          filter: {
            expressions: [
              { prop: "projectId", operator: "EQ", limit: this.projectId }
            ]
          }
        }
      };
      const res = await customApi.getDeviceTableDate(queryData);
      if (res.code !== 0) return;
      const list = this._.get(res, "data", []);
      if (!list.length) return;
      this.instrumentList = list.map(i => {
        return {
          ...i,
          text: i.code + "(" + i.name + ")"
        };
      });
      if (this.editData_in?.code) {
        const obj = this._.find(this.instrumentList, {
          code: this.editData_in.code
        });
        this.$set(this.CetForm_1.data, "selectInstrument", obj.id ?? undefined);
      }
    },
    // 输入框过滤
    querySearch(queryString, cb) {
      var devices = this.devices;
      var results = queryString
        ? devices.filter(item => {
            if (item.code === null) {
              console.error($T("ERROR:为啥会有code为空的表"), item);
            }
            return (
              !this._.isNil(item.code) && item.code.indexOf(queryString) !== -1
            );
          })
        : devices;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return restaurant => {
        return (
          restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
          0
        );
      };
    },
    handleSelect(item) {
      this.backfillArr.forEach(code => {
        this.$set(this.CetForm_1.data, code, item ? item[code] : "");
      });
      this.$set(this.CetForm_1.data, "equipmentid", item ? item.id : "");
      this.$set(this.CetForm_1.data, "code", item ? item.code : "");
      this.$set(
        this.CetForm_1.data,
        "verificationagency",
        item ? item.verificationagency : ""
      );
    },
    autocompleteChange() {},
    addHandel(val) {
      var addFn, addData;
      val.projectid = this.projectId;
      // 对象表计关联
      if ("monitorid" in val && val.monitorid && !_.isNumber(val.monitorid)) {
        val.monitorid = Number(val.monitorid.split("_")[1]);
      }
      if (val.metertype) {
        val.metertype = val.metertype?.at(-1) ?? null;
      }
      // 不入库字段
      const deleteKeys = [
        "location",
        "monitorid",
        "monitorlabel",
        "monitorname",
        "devicename"
      ];
      deleteKeys.forEach(key => {
        if (key in val) delete val[key];
      });
      if (!val.plannedverificationcycle || !val.verificationcycle) {
        delete val.verificationcycle;
        delete val.plannedverificationcycle;
      }
      if (this.actionTitle_in === 1) {
        addFn = "editDashboard";
        val.modelLabel = "meterextendinfo";
        addData = [val];
      } else if (this.actionTitle_in === 2) {
        delete val.selectInstrument;
        addFn = "editRecord";
        val.modelLabel = "instrumentinspectrecord";
        addData = val;
        // 检定记录新增的时候加传userid
        if (!this.editData_in) {
          addData.userid = this.userId;
        }
      }
      customApi[addFn](addData).then(response => {
        if (response.code === 0) {
          this.$message({
            type: "success",
            message: $T("保存成功")
          });
          this.CetDialog_1.closeTrigger_in = new Date().getTime();
          this.$emit("upTableData_out");
        }
      });
    },
    httpRequest(val) {
      var type = val.file.type.toUpperCase().split("/")[1];
      if (["JPG", "JPEG", "BMP", "GIF", "PNG"].indexOf(type) === -1) {
        this.$message.error($T("仅支持 JPG,JPEG,BMP,GIF,PNG 文件"));
        this.$refs.upload.clearFiles();
        return;
      }
      const formData = new FormData();
      formData.append("file", val.file);
      this.CetButton_confirm.disable_in = true;
      customApi.uploadFile(formData).then(
        response => {
          if (response.code === 0) {
            this.$message({
              type: "success",
              message: $T("上传成功！")
            });
            this.CetForm_1.data[this.fieldsName] = this._.get(response, "data");
          }
          this.$refs.upload.clearFiles();
          this.CetButton_confirm.disable_in = false;
        },
        () => {
          this.$refs.upload.clearFiles();
          this.CetButton_confirm.disable_in = false;
        }
      );
    },
    CetButton_add_statusTrigger_out(val) {
      if (this.CetForm_1.data.monitorid) {
        this.measureObj.inputData_in = {
          monitorid: this.CetForm_1.data.monitorid,
          monitorlabel: this.CetForm_1.data.monitorlabel,
          name: this.CetForm_1.data.monitorname
        };
      } else {
        this.measureObj.inputData_in = {};
      }
      this.measureObj.visibleTrigger_in = new Date().getTime();
    },
    measureObj_CetButton_confirm_out(val) {
      if (!val) {
        this.$set(this.CetForm_1.data, "devicename", null);
        this.$set(this.CetForm_1.data, "monitorname", null);
        this.$set(this.CetForm_1.data, "monitorid", null);
        this.$set(this.CetForm_1.data, "monitorlabel", null);
        this.$set(this.CetForm_1.data, "deviceid", null);
        this.$set(this.CetForm_1.data, "location", null);
        return;
      }
      this.$set(this.CetForm_1.data, "devicename", val.text);
      this.queryDeviceConnectInfo(val);
    },
    async queryDeviceConnectInfo(params) {
      const res = await customApi.queryDeviceConnectInfo(params.nodeId);
      if (res.code !== 0) return;
      const { deviceid, monitorid, location, monitorlabel, monitorname } =
        res.data;
      if (deviceid && monitorid) {
        this.$set(this.CetForm_1.data, "monitorname", monitorname || "--");
        this.$set(this.CetForm_1.data, "monitorid", monitorid);
        this.$set(this.CetForm_1.data, "monitorlabel", monitorlabel);
        this.$set(this.CetForm_1.data, "deviceid", Number(deviceid));
        this.$set(this.CetForm_1.data, "location", location || "--");
      } else {
        this.$message.warning($T("关联表计暂无信息"));
      }
    },
    CetButton_upload_click_out(name) {
      this.fieldsName = name;
      this.$refs.uploadBtn.$el.click();
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = new Date().getTime();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = val;
    },
    CetForm_1_saveData_out(val) {
      // 检定周期数值与单位仅有一个为空时提示
      if (!val.plannedverificationcycle && val.verificationcycle) {
        this.$message.warning($T("请输入检定周期数值"));
      } else if (!val.verificationcycle && val.plannedverificationcycle) {
        this.$message.warning($T("请选择检定周期单位"));
      } else {
        this.addHandel(val);
      }
    },
    handelDelFile(item) {
      this.$delete(this.CetForm_1.data, item.name);
    },
    // 自动填充下一次时间
    calculateNextTime() {
      const { lastoverhauldate, plannedverificationcycle, verificationcycle } =
        this._.cloneDeep(this.CetForm_1.data);
      if (!lastoverhauldate || !plannedverificationcycle || !verificationcycle)
        return null;
      const date = new Date(lastoverhauldate);
      switch (verificationcycle) {
        case 12: // 天
          this.$set(
            this.CetForm_1.data,
            "nextoverhauldate",
            lastoverhauldate + plannedverificationcycle * 86400000
          );
          break;
        case 14: // 月
          date.setMonth(date.getMonth() + plannedverificationcycle);
          this.$set(this.CetForm_1.data, "nextoverhauldate", date.getTime());
          break;
        case 17: // 年
          date.setFullYear(date.getFullYear() + plannedverificationcycle);
          this.$set(this.CetForm_1.data, "nextoverhauldate", date.getTime());
          break;
        default:
          break;
      }
    },
    getParentIds(data, targetId) {
      if (!data.length || !targetId) return null;
      function findPath(node, targetId, path = []) {
        for (const item of node) {
          if (item.id === targetId) {
            return [...path, item.id];
          }
          if (item.child && item.child.length > 0) {
            const result = findPath(item.child, targetId, [...path, item.id]);
            if (result) return result;
          }
        }
        return null;
      }

      return findPath(data, targetId);
    },
    handlerInstrument(e) {
      const obj = this._.find(this.instrumentList, { id: e });
      const fields = ["code", "name", "model", "location"];
      fields.forEach(key => {
        this.$set(this.CetForm_1.data, key, obj ? obj[key] || "--" : "--");
      });
      this.$set(
        this.CetForm_1.data,
        "verificationcycle",
        obj ? obj.verificationcycle : ""
      );
      this.$set(this.CetForm_1.data, "equipmentid", obj ? obj.id : "");
      this.$set(
        this.CetForm_1.data,
        "plannedverificationcycle",
        obj?.plannedverificationcycle ?? undefined
      );
    }
  },
  activated: function () {}
};
</script>
<style lang="scss" scoped>
.content {
  max-height: 632px;
  overflow-y: auto;
}
.form {
  overflow-y: hidden;
  overflow-x: hidden;
  .row-box {
    display: flex;
    flex-wrap: wrap;
  }
  .measuring {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .cycleSelect {
    position: absolute;
    right: 1px;
    :deep(.el-input__inner) {
      border: 0;
      height: 30px;
      line-height: 30px;
      @include background_color(BG);
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
  .flexImg {
    display: flex;
    flex-direction: column;
    .imgName {
      .content {
        display: flex;
        justify-content: space-between;
      }
      height: 26px;
      line-height: 26px;
      box-sizing: border-box;
      background: #0e306c;
    }
  }
  .commandWidth {
    display: block;
    width: 120px;
  }
}
</style>
