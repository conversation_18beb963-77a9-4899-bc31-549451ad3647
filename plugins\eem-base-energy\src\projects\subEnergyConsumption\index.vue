<template>
  <div class="fullheight">
    <CetAside class="cet-aside">
      <template #aside>
        <div class="h-full flex flex-col">
          <customElSelect
            v-model="ElSelect_1.value"
            v-bind="ElSelect_1"
            v-on="ElSelect_1.event"
            :prefix_in="$T('能源类型')"
            class="mb-J3"
          >
            <ElOption
              v-for="item in ElOption_1.options_in"
              :key="item[ElOption_1.key]"
              :label="item[ElOption_1.label]"
              :value="item[ElOption_1.value]"
            ></ElOption>
          </customElSelect>
          <customElSelect
            :prefix_in="$T('所属项目')"
            v-model="ElSelect_project.value"
            v-bind="ElSelect_project"
            v-on="ElSelect_project.event"
            class="mb-J3"
            v-show="ElOption_project.options_in?.length > 1"
          >
            <ElOption
              v-for="item in ElOption_project.options_in"
              :key="item[ElOption_project.key]"
              :label="item[ElOption_project.label]"
              :value="item[ElOption_project.value]"
              :disabled="item[ElOption_project.disabled]"
            ></ElOption>
          </customElSelect>
          <customElSelect
            class="mb-J3"
            v-model="ElSelect_2.value"
            v-bind="ElSelect_2"
            v-on="ElSelect_2.event"
            :prefix_in="$T('分项维度')"
          >
            <ElOption
              v-for="item in ElOption_2.options_in"
              :key="item[ElOption_2.key]"
              :label="item[ElOption_2.label]"
              :value="item[ElOption_2.value]"
            ></ElOption>
          </customElSelect>
          <CetTree
            :selectNode.sync="CetTree_1.selectNode"
            :checkedNodes.sync="CetTree_1.checkedNodes"
            v-bind="CetTree_1"
            v-on="CetTree_1.event"
          >
            <div
              class="custom-tree-node el-tree-node__label"
              slot-scope="{ node }"
              :level="node.level"
            >
              <span
                :style="{
                  color: filNodeColor(node)
                }"
              >
                {{ node.label }}
              </span>
            </div>
          </CetTree>
        </div>
      </template>
      <template #container>
        <div class="h-full flex flex-col">
          <div class="header">
            <div class="title">{{ nodeName || "--" }}</div>
            <CetDateSelect
              v-bind="CetDateSelect_time"
              v-on="CetDateSelect_time.event"
            ></CetDateSelect>
          </div>
          <div class="relative middle box">
            <div class="flex-row flex title-line">
              <div class="title">{{ $T("各分项用能趋势") }}</div>
              <chartTypeSwitch
                v-model="chartType"
                @change="handlerChartTypeChange"
              />
            </div>
            <CetChart v-bind="trendChart" :key="trendKey"></CetChart>
          </div>
          <div class="bottom">
            <div class="box left">
              <div class="title">{{ $T("分项用能占比") }}</div>
              <CetChart v-if="!showEmpty" v-bind="proportionChart"></CetChart>
              <el-empty
                class="flex-auto pro-box"
                v-else-if="isLight"
                :image-size="216"
                image="static/assets/empty_min_light.png"
              ></el-empty>
              <el-empty
                v-else
                class="flex-auto pro-box"
                :image-size="216"
                image="static/assets/empty_min.png"
              ></el-empty>
            </div>
            <div class="box ml-J3 right">
              <div class="title">{{ $T("用能同环比分析") }}</div>
              <div class="slider">
                <div
                  class="button-arrow mrJ1"
                  :class="{ disable: left }"
                  @click="onLeftClick"
                >
                  <i class="el-icon-arrow-left"></i>
                </div>
                <el-carousel
                  height="100%"
                  indicator-position="none"
                  arrow="never"
                  :initial-index="initIndex"
                  :interval="15000"
                  ref="sideCarousel"
                >
                  <el-carousel-item
                    v-for="(list, index) in blockList"
                    :key="index"
                    class="slider-block"
                  >
                    <div v-for="(item, i) in list" :key="i" class="card">
                      <AnalysisCard
                        :item="item"
                        :index="getNum(index, i)"
                        :cycle="dateObj.type"
                      />
                    </div>
                  </el-carousel-item>
                </el-carousel>
                <div
                  class="button-arrow ml-J1"
                  :class="{ disable: left }"
                  @click="onRightClick"
                >
                  <i class="el-icon-arrow-right"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </CetAside>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import common from "eem-base/utils/common";
import * as echarts from "echarts";
import AnalysisCard from "./components/AnalysisCard.vue";
import omegaTheme from "@omega/theme";
import {
  lightColorList,
  darkColorList,
  lightAreaColorList,
  darkAreaColorList
} from "./components/chartData.js";
import chartTypeSwitch from "eem-base/components/chartTypeSwitch.vue";

export default {
  name: "subEnergyConsumption",
  components: { AnalysisCard, chartTypeSwitch },
  data(vm) {
    return {
      chartType: 2,
      showEmpty: true,
      initIndex: 0,
      ElSelect_1: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name"
      },
      ElSelect_2: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name"
      },
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: true,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      nodeName: null,
      selectNode: null,
      dateTypeOpt: {
        5: 17,
        3: 14,
        1: 12
      },
      dateObj: {
        type: 14,
        time: this.getToday()
      },
      CetDateSelect_time: {
        value: { dateType: "3", value: new Date().getTime() },
        typeList: ["day", "month", "year"],
        layout: "button",
        align: "right",
        nextDisabledNum: -1,
        yearDateOptions: {
          disabledDate(time) {
            return (
              time.getTime() > vm.$moment(Date.now()).add(1, "y").valueOf()
            );
          },
          shortcuts: [
            {
              text: this.$T("今年"),
              onClick(picker) {
                picker.$emit("pick", new Date());
              }
            }
          ]
        },
        monthDateOption: {
          disabledDate(time) {
            return (
              time.getTime() > vm.$moment(Date.now()).add(1, "M").valueOf()
            );
          },
          shortcuts: [
            {
              text: this.$T("当月"),
              onClick(picker) {
                picker.$emit("pick", new Date());
              }
            }
          ]
        },
        dayDateOption: {
          firstDayOfWeek: 1,
          disabledDate(time) {
            return (
              time.getTime() > vm.$moment(Date.now()).add(1, "d").valueOf()
            );
          },
          shortcuts: [
            {
              text: this.$T("今天"),
              onClick(picker) {
                picker.$emit("pick", new Date());
              }
            }
          ]
        },
        event: {
          date_out: this.CetDateSelect_time_date_out
        }
      },
      trendKey: 0,
      trendUnit: "--",
      trendChart: {
        inputData_in: null,
        options: {
          toolbox: {
            show: true,
            top: 10,
            feature: {
              saveAsImage: {
                name: $T("各分项用能趋势")
              } // 导出图片
            }
          },
          legend: {
            show: true,
            data: []
          },
          tooltip: {
            trigger: "axis",
            formatter: params => {
              let str = "";
              str += this.$moment(this.dateObj.time[0]).format(
                this.getFormat(this.dateObj.type)
              );
              let symbol = this.dateObj.type === 12 ? " " : "-";
              str += symbol + params[0]?.axisValueLabel + "<br/>";
              params.forEach(item => {
                str += `${item.marker} ${item.seriesName}: ${
                  item.data.tag ? "--" : item.value
                } ${this.trendUnit}<br>`;
              });
              return str;
            }
          },
          grid: {
            top: 56,
            bottom: 4,
            left: 0,
            right: 0,
            containLabel: true
          },
          color: omegaTheme.theme === "light" ? lightColorList : darkColorList,
          xAxis: {
            type: "category",
            axisTick: {
              show: false
            },
            data: []
          },
          yAxis: {
            name: "",
            type: "value",
            nameTextStyle: {
              align: "left"
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed"
              }
            },
            axisLabel: {
              formatter: function (value, index) {
                return value.toFixed(2);
              }
            }
          },
          series: []
        }
      },
      proportionChart: {
        options: {
          tooltip: {
            trigger: "item",
            position: "right",
            formatter: params => {
              return `${params.marker} ${params.name}</br>
              能耗：${params.value ?? "--"}${this.proUnit}</br>
              占比：${params.data.per}%`;
            }
          },
          legend: {
            type: "scroll",
            top: "center",
            right: 0,
            orient: "vertical",
            icon: "circle",
            data: [],
            textStyle: {
              rich: {
                name: {
                  width: 112,
                  fontSize: 14,
                  align: "left"
                },
                per: {
                  width: 45,
                  fontSize: 14,
                  align: "right"
                }
              }
            },
            formatter: name => {
              let obj = this.proportionChart.options.series[0].data.find(
                i => i.name === name
              );
              let str = `{name|${echarts.format.truncateText(
                name,
                90,
                "",
                "…"
              )}} {per|${echarts.format.truncateText(
                `${obj.per}`,
                45,
                "",
                "…"
              )}%}`;
              return str;
            },
            tooltip: {
              show: true,
              formatter: ({ name }) => {
                let obj = this.proportionChart.options.series[0].data.find(
                  i => i.name === name
                );
                return `${name}</br>
                能耗：${obj.value ?? "--"}${this.proUnit}</br>
                占比：${obj.per}%`;
              }
            }
          },
          series: [
            {
              type: "pie",
              radius: ["35%", "50%"],
              center: ["25%", "50%"],
              avoidLabelOverlap: false,
              silent: false,
              label: {
                show: false
              },
              data: []
            }
          ]
        }
      },
      proUnit: "--",
      propertysList: [],
      blockList: [],
      ElSelect_project: {
        value: 0,
        style: {},
        event: {
          change: this.ElSelect_project_change_out
        }
      },
      ElOption_project: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  computed: {
    left() {
      return [0, 1].includes(this.blockList?.length);
    },
    isLight() {
      return omegaTheme.theme === "light";
    },
    rootNode() {
      const id = this.ElSelect_project.value;
      const list = this.ElOption_project.options_in;
      return list.find(item => item.id === id);
    }
  },
  methods: {
    handlerChartTypeChange(val) {
      this.trendChart.options.series.forEach(item => {
        item.type = val === 1 ? "bar" : "line";
      });
    },
    //通过节点childSelectState属性，调整字体颜色
    filNodeColor(node) {
      const state = this._.get(node, "data.childSelectState", null);
      if (state !== 1) {
        return "#989898";
      }
    },
    getNum(num1, num2) {
      if (this.initIndex === 0) {
        return num1 * 4 + num2;
      }
      //处理只有两个幻灯片时滑动方向不一致的问题
      if ([0, 2].includes(num1)) {
        return 4 + num2;
      } else if ([1, 3].includes(num1)) {
        return num2;
      }
    },
    getToday() {
      let start = this.$moment().startOf("month").valueOf();
      let end = this.$moment().endOf("month").valueOf() + 1;
      return [start, end];
    },
    getFormat(type) {
      let obj = {
        17: "YYYY",
        14: "YYYY-MM",
        12: "YYYY-MM-DD"
      };
      return obj[type];
    },
    // 获取项目能源类型
    async getProjectEnergy() {
      const res = await customApi.getProjectEnergy();
      if (res.code === 0) {
        let list = res.data || [];
        this.ElOption_1.options_in = list;
        let obj = list.find(item => item.energytype === 2);
        this.ElSelect_1.value = obj ? obj.energytype : list[0]?.energytype;
      }
    },
    async ElSelect_project_change_out() {
      await this.getSubList();
      await this.getPropertys();
      this.getTree();
    },
    //获取分项维度
    async getSubList() {
      const queryData = {
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };
      const res = await customApi.dimensionAll(queryData);
      if (res.code !== 0) {
        return;
      }
      let list = res.data || [];
      const cus = list.reduce((cus, item) => {
        if (item.modelLabel === "dimension") {
          cus.push({
            id: item.id,
            name: item.name
          });
        }
        return cus;
      }, []);
      this.ElOption_2.options_in = cus;
      this.ElSelect_2.value = cus[0]?.id;
    },
    //获取节点树
    async getTree() {
      if (!this.ElSelect_1.value || !this.ElSelect_2.value) return;
      let params = {
        dimId: this.ElSelect_2.value,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        },
        energyType: this.ElSelect_1.value,
        plugin: "eem-base",
        business: "energy-analysis"
      };
      const res = await customApi.querySubConsumptionTree(params);
      if (res.code === 0 && res.data?.length > 0) {
        this.setTreeLeaf(res.data);
        this.CetTree_1.inputData_in = res.data;
        let obj = this._.find(this.dataTransform(res.data), [
          "childSelectState",
          1
        ]);
        this.CetTree_1.selectNode = obj;
      } else {
        this.CetTree_1.inputData_in = [];
        this.CetTree_1.selectNode = {};
        this.CetTree_1_currentNode_out({});
      }
    },
    // 获取第一个有权限的节点
    dataTransform(array) {
      const cloneData = this._.cloneDeep(array);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
        }
        return arr;
      };
      return expanded(cloneData);
    },
    // 设置节点禁用
    setTreeLeaf(nodesAll) {
      if (nodesAll && nodesAll.length > 0) {
        nodesAll.forEach(item => {
          if (item.childSelectState == 2) {
            this.$set(item, "disabled", true);
          } else if (item.childSelectState == 1) {
            this.$set(item, "disabled", false);
          }
          this.setTreeLeaf(this._.get(item, "children", []));
        });
      }
    },
    //获取标签数据列表
    async getPropertys() {
      if (!this.ElSelect_2.value) return;
      let params = {
        dimensionId: this.ElSelect_2.value
      };
      const res = await customApi.getPropertysList(params);
      if (res.code === 0) {
        let list = res.data || [];
        let tags = [];
        list.forEach(item => {
          tags.push({
            name: item.name,
            tag: item.id
          });
        });
        this.propertysList = tags;
        this.getTrendChartData();
        this.getProChartData();
        this.getRadioData();
      }
    },
    async getTrendChartData() {
      this.chartType = 2;
      if (_.isEmpty(this.selectNode)) {
        this.trendChart.options.xAxis.data = [];
        this.trendChart.options.yAxis.name =
          $T("用能量") + "(" + this.trendUnit + ")";
        this.trendChart.options.legend.data = [];
        this.trendChart.options.series = [];
        this.trendKey++;
        return;
      }
      let params = {
        id: this.selectNode?.id ?? null,
        modelLabel: this.selectNode?.modelLabel ?? null,
        aggregationCycle: this.dateObj.type,
        time: this.dateObj.time[0],
        tags: this.propertysList,
        energyType: this.ElSelect_1.value,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };
      const res = await customApi.queryAccumItemized(params);
      if (res.code === 0) {
        let list = res.data || [];
        let xAxisList = [];
        let series = [];
        let legend = [];
        list.forEach((item, index) => {
          let datas = item.datas;
          let yData = [];
          if (index === 0) {
            datas.forEach(d => {
              let time = this.getAxis(d.time, this.dateObj.type);
              xAxisList.push(time);
              let value = d.value || 0;
              yData.push({ value: value.toFixed2(2), tag: d.value == null });
            });
          } else {
            datas.forEach(d => {
              let value = d.value || 0;
              yData.push({ value: value.toFixed2(2), tag: d.value == null });
            });
          }
          legend.push(item.name);
          let obj = {
            name: item.name,
            data: yData,
            type: "line",
            symbolSize: 1,
            areaStyle: {
              color:
                omegaTheme.theme === "light"
                  ? lightAreaColorList[index]
                  : darkAreaColorList[index]
            },
            stack: "总量"
          };
          series.push(obj);
          this.trendUnit = item.unitName || "--";
        });
        this.trendChart.options.xAxis.data = xAxisList;
        this.trendChart.options.yAxis.name =
          $T("用能量") + "(" + this.trendUnit + ")";
        this.trendChart.options.legend.data = legend;
        this.trendChart.options.series = series;
        this.trendKey++;
      }
    },
    async getProChartData() {
      if (_.isEmpty(this.selectNode)) {
        this.showEmpty = true;
        this.proportionChart.options.series[0].data = [];
        this.proportionChart.options.legend.data = [];
        return;
      }
      let params = {
        id: this.selectNode?.id ?? null,
        modelLabel: this.selectNode?.modelLabel ?? null,
        tags: this.propertysList,
        energyType: this.ElSelect_1.value,
        normal: true,
        startTime: this.dateObj.time[0],
        endTime: this.dateObj.time[1],
        aggregationCycle: this.dateObj.type,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };
      const res = await customApi.queryItemConsumption(params);
      if (res.code === 0) {
        let list = res.data || [];
        let cus = [];
        let total = 0;
        let flag = 0;
        let legend = [];
        this.proUnit = "--";
        list.forEach(item => {
          total += item.value != null ? item.value : 0;
          flag += !item.value ? 1 : 0;
          this.proUnit = item.unitName || "--";
        });
        list.forEach(item => {
          let per =
            total && item.value != null
              ? ((item.value / total) * 100).toFixed(2)
              : "--";
          cus.push({
            name: item.name,
            value: item.value
              ? common.formatNumberWithPrecision(item.value, 2)
              : item.value,
            per: per
          });
          legend.push(item.name);
        });
        this.showEmpty = !list?.length || flag === list.length;
        let tem = [
          ...cus,
          {
            name: "a",
            value: 0,
            itemStyle: {
              color: this.isLight ? "#F8FAFB" : "#1F2B54",
              borderWidth: 0
            }
          }
        ];
        this.proportionChart.options.legend.data = legend;
        this.proportionChart.options.series[0].silent = flag === list.length;
        this.proportionChart.options.series[0].data =
          flag === list.length ? tem : cus;
      }
    },
    //查询同环比数据
    async getRadioData() {
      if (_.isEmpty(this.selectNode)) {
        this.initIndex = 0;
        this.blockList = [];
        return;
      }
      let tags = this.propertysList.map(item => item.tag);
      let params = {
        aggregationCycle: this.dateObj.type,
        endTime: this.dateObj.time[1],
        energyType: this.ElSelect_1.value,
        node: {
          id: this.selectNode?.id ?? null,
          modelLabel: this.selectNode?.modelLabel ?? null
        },
        projectId: this.$store.state.projectId,
        startTime: this.dateObj.time[0],
        tagIds: tags,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };
      const res = await customApi.querySubConsumption(params);
      if (res.code === 0) {
        let list = res.data || [];
        //处理只有两个幻灯片时滑动方向不一致的问题
        let cusList = this.splitArrChunks(list, 4);
        if (cusList?.length === 2) {
          this.initIndex = 1;
          this.blockList = [cusList[1], ...cusList, cusList[0]];
        } else {
          this.initIndex = 0;
          this.blockList = cusList;
        }
      }
    },
    ElSelect_1_change_out(val) {
      this.getTree();
    },
    ElSelect_2_change_out(val) {
      this.getTree();
      this.getPropertys();
    },
    CetTree_1_currentNode_out(val) {
      if (val.childSelectState == 2) {
        return this.$message.warning($T("没有该节点权限"));
      }
      this.selectNode = val;
      this.nodeName = val.name;
      this.getTrendChartData();
      this.getProChartData();
      this.getRadioData();
    },
    CetDateSelect_time_date_out(time, type) {
      this.dateObj = {
        type: this.dateTypeOpt[type],
        time: [time[0], time[1] + 1]
      };
      this.getTrendChartData();
      this.getProChartData();
      this.getRadioData();
    },
    getAxis(val, type) {
      let fromatStr = "";
      if (type === 12) {
        fromatStr = "HH:mm";
      } else if (type === 14) {
        fromatStr = "DD";
      } else if (type === 17) {
        fromatStr = "MM";
      }
      return this.$moment(val).format(fromatStr);
    },
    splitArrChunks(arr, chunk) {
      let result = [];
      let temp = null;
      for (let i = 0; i < arr?.length; i += chunk) {
        temp = arr.slice(i, i + chunk);
        result.push(temp);
      }
      return result;
    },
    onLeftClick() {
      if (this.left) return;
      this.$refs.sideCarousel.prev();
    },
    onRightClick() {
      if (this.left) return;
      this.$refs.sideCarousel.next();
    },
    async getRootNode() {
      this.ElOption_project.options_in = [];
      const res = await customApi.rootNode();
      const data = res.data || [];
      this.ElOption_project.options_in = data;
      this.ElSelect_project.value = data?.[0]?.id;
    },
    async init() {
      await this.getProjectEnergy();
      await this.getRootNode();
      await this.getSubList();
      await this.getPropertys();
      await this.getTree();
    }
  },
  mounted() {
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 24px;
  @include background_color(BG1);
  .title {
    font-weight: bold;
    font-size: 18px;
  }
  :deep(.el-button-group) {
    display: flex !important;
  }
}
.middle {
  height: calc(50% - 32px);
}
.bottom {
  height: calc(50% - 64px);
  margin-top: 16px;
  display: flex;
  .left {
    width: 32%;
  }
  .right {
    width: calc(68% - 16px);
  }
}
.box {
  border-radius: 4px;
  padding: 24px;
  box-sizing: border-box;
  @include background_color(BG1);
  .title {
    font-weight: bold;
    font-size: 16px;
  }
  .echarts {
    height: calc(100% - 35px);
  }
}
.pro-box {
  height: calc(100% - 21px);
}
.slider {
  display: flex;
  height: calc(100% - 37px);
  margin-top: 16px;
  .button-arrow {
    width: 16px;
    height: 100%;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 12px;
    @include background_color(BG12);
  }
  .disable {
    cursor: not-allowed;
    i {
      @include font_color(T6);
    }
  }
  .el-carousel {
    width: calc(100% - 48px);
  }
  .slider-block {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    row-gap: 8px;
    column-gap: 8px;
  }
}
.title-line {
  justify-content: space-between;
  align-items: center;
}
.cet-aside :deep(.cet-content-aside-container) {
  padding: 0;
}
</style>
