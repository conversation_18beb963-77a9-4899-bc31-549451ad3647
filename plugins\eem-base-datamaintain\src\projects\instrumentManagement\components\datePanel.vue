<template>
  <div class="date-panel">
    <dateRangePanel ref="datePanel" @pick="pick" />
  </div>
</template>
<script>
import dateRangePanel from "./date-picker/src/panel/date-range.vue";

export default {
  components: { dateRangePanel },
  props: {
    value: {
      type: Array
    }
  },
  data() {
    return {};
  },
  watch: {
    value: {
      handler: function (val) {
        this.$nextTick(() => {
          if (this.$refs.datePanel) {
            this.$refs.datePanel.value = val;
          }
        });
      },
      immediate: true
    }
  },
  methods: {
    pick(date, visible) {
      this.$emit("update:value", [
        new Date(date[0]).valueOf(),
        new Date(date[1]).valueOf()
      ]);
    }
  },
  mounted() {
    this.$refs.datePanel.visible = true;
  }
};
</script>
<style lang="scss" scoped>
.date-panel {
  .el-picker-panel {
    border: none;
    margin: 0;
  }
  :deep(.el-date-range-picker__content.is-left) {
    border-right: none;
  }
  :deep(.el-date-range-picker__content) {
    padding: 16px 16px 0 16px;
  }
}
</style>
