<template>
  <div>
    <CetDialog
      class="CetDialog"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
      :before-close="beforeClose"
    >
      <div>
        <div class="title mb-J3">{{ $T("基础信息") }}</div>
        <CetForm
          :data.sync="CetForm_1.data"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
        >
          <el-row :gutter="16">
            <el-col :span="8" v-show="ElOption_3.options_in?.length > 1">
              <el-form-item :label="$T('所属项目')" prop="rootnodeid">
                <ElSelect
                  v-model="CetForm_1.data.rootnodeid"
                  v-bind="ElSelect_3"
                  v-on="ElSelect_3.event"
                  :disabled="isEdit"
                >
                  <ElOption
                    v-for="item in ElOption_3.options_in"
                    :key="item[ElOption_3.key]"
                    :label="item[ElOption_3.label]"
                    :value="item[ElOption_3.value]"
                    :disabled="item[ElOption_3.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('方案名称')" prop="name">
                <ElInput
                  v-model.trim="CetForm_1.data.name"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                  show-word-limit
                ></ElInput>
              </el-form-item>
            </el-col>
          </el-row>
        </CetForm>
      </div>
      <div class="flex-row flex mt-J1">
        <div class="w-[250px]">
          <div class="title">{{ $T("被分摊信息") }}</div>
          <el-row :gutter="16">
            <el-col :span="24">
              <div class="label">
                {{ $T("被分摊能源类型") }}
                <span style="color: red">*</span>
              </div>
              <div class="value mt-J1">
                <ElSelect
                  class="mr-J1 w-full"
                  v-model="queryBody.objectenergytype"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                >
                  <ElOption
                    v-for="item in ElOption_1.options_in"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </ElSelect>
              </div>
            </el-col>
          </el-row>
          <div class="mt-J1">
            <div class="label">
              {{ $T("关联被分摊对象") }}
              <span style="color: red">*</span>
            </div>
            <div class="value mt-J1 value_border">
              <div class="label tree_title">
                {{ $T("被分摊对象关联") }}
              </div>
              <CetTree
                class="value_tree"
                style="height: calc(100% - 36px)"
                :selectNode.sync="CetTree_1.selectNode"
                :checkedNodes.sync="CetTree_1.checkedNodes"
                :searchText_in.sync="CetTree_1.searchText_in"
                v-bind="CetTree_1"
                v-on="CetTree_1.event"
                ref="cetTree"
              ></CetTree>
            </div>
          </div>
        </div>
        <div class="flex-auto ml-J3">
          <div class="title">{{ $T("分摊信息") }}</div>
          <el-row :gutter="16">
            <el-col :span="5">
              <div class="label">
                {{ $T("分摊方式") }}
                <span style="color: red">*</span>
              </div>
              <div class="value mt-J1">
                <el-radio-group
                  v-model="queryBody.energysharemethod"
                  class="fullwidth method_radio"
                  @input="changeRadio"
                >
                  <template v-for="item in options_method">
                    <el-radio-button
                      :key="item.value"
                      :label="item.value"
                      style="width: 50%"
                    >
                      {{ item.label }}
                    </el-radio-button>
                  </template>
                </el-radio-group>
              </div>
            </el-col>
            <el-col :span="7">
              <div class="label">
                {{ $T("生效时间") }}
                <span style="color: red">*</span>
              </div>
              <div class="value mt-J1">
                <CustomElDatePicker
                  v-show="!queryBody.isForever"
                  v-bind="CetDatePicker_time.config"
                  v-model="queryBody.effectiveTime"
                  style="width: 240px"
                />
                <CustomElDatePicker
                  v-show="queryBody.isForever"
                  v-bind="CetDatePicker_time2.config"
                  v-model="queryBody.starttime"
                  style="width: 240px"
                />
                <el-checkbox
                  class="ml-J1"
                  v-model="queryBody.isForever"
                  @change="foreverChange"
                >
                  {{ $T("永远") }}
                </el-checkbox>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="label">
                {{ $T("分摊时间间隔") }}
                <span style="color: red">*</span>
              </div>
              <div class="value mt-J1">
                <el-radio-group
                  v-model="queryBody.aggregationcycle"
                  class="fullwidth method_radio"
                >
                  <template v-for="item in options_interval">
                    <el-radio-button
                      :key="item.value"
                      :label="item.value"
                      style="width: 33%"
                    >
                      {{ item.label }}
                    </el-radio-button>
                  </template>
                </el-radio-group>
              </div>
            </el-col>
            <el-col :span="6" v-if="queryBody.energysharemethod == 2">
              <div class="label">
                {{ $T("分摊能源类型") }}
                <span style="color: red">*</span>
              </div>
              <div class="value mt-J1">
                <ElSelect
                  class="mr-J1"
                  v-model="queryBody.energytype"
                  v-bind="ElSelect_2"
                  v-on="ElSelect_2.event"
                >
                  <ElOption
                    v-for="item in ElOption_1.options_in"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </ElSelect>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <div class="label">
                {{ $T("关联分摊对象") }}
                <span style="color: red">*</span>
              </div>
              <div class="value mt-J1 transfer_flex" v-if="testTransfer">
                <TreeTransfer
                  ref="treeTransfer"
                  width="550px"
                  height="300px"
                  :openAll="false"
                  :data-source.sync="objectData"
                  node-key="tree_id"
                  :titles="['对象列表', '已选对象列表']"
                  :defaultProps="{ label: 'name', children: 'children' }"
                  :father-choose="true"
                  :listSortFifo="false"
                  :default-checked-keys="defaultChecked"
                  :default-expanded-keys="defaultChecked"
                  :filterable="true"
                  @change="handleChange"
                  @left-check-change="leftCheckChange"
                ></TreeTransfer>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="label">
                {{ $T("分摊节点列表") }}
                <span style="color: red">*</span>
              </div>
              <div class="value mt-J1">
                <el-table
                  :data="rightSelectedNode"
                  border
                  height="300"
                  class="table_style"
                >
                  <el-table-column
                    :label="$T('序号')"
                    type="index"
                    width="70"
                  ></el-table-column>
                  <template v-if="queryBody.energysharemethod == 1">
                    <el-table-column
                      :show-overflow-tooltip="true"
                      :label="$T('分摊对象')"
                      prop="name"
                      header-align="left"
                      align="left"
                    ></el-table-column>
                    <el-table-column
                      :show-overflow-tooltip="true"
                      :label="$T('分摊对象')"
                      prop="rate"
                      header-align="left"
                      align="left"
                    >
                      <template slot="header">
                        <div>
                          {{ $T("分摊比例") }}
                          <el-tooltip
                            width="200"
                            :content="titleMsg"
                            effect="light"
                          >
                            <i class="el-icon-question"></i>
                          </el-tooltip>
                        </div>
                      </template>
                      <template slot-scope="scope">
                        <el-input
                          v-model="scope.row.rate"
                          @keyup.native="handleNum(scope.row, 'rate', 4)"
                          @blur="handleNum(scope.row, 'rate', 4)"
                          :placeholder="$T('输入系数')"
                        ></el-input>
                      </template>
                    </el-table-column>
                  </template>
                  <template v-if="queryBody.energysharemethod == 2">
                    <el-table-column
                      :show-overflow-tooltip="true"
                      :label="$T('分摊对象')"
                      prop="name"
                      header-align="left"
                      align="left"
                    ></el-table-column>
                  </template>
                </el-table>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="ml-J1"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom";
import TreeTransfer from "eem-base/components/TreeTransfer";
import common from "eem-base/utils/common";

export default {
  name: "addPlan",
  components: {
    TreeTransfer
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    isEdit() {
      return !!this.inputData_in?.id;
    },
    rootNode() {
      const rootnodeid = this.CetForm_1.data.rootnodeid;
      const list = this.ElOption_3.options_in;
      return list.find(item => item.id === rootnodeid);
    }
  },
  data(vm) {
    return {
      // 设置组件唯一识别字段弹窗组件
      CetDialog_1: {
        title: "新增方案",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        width: "1440px",
        showClose: true,
        top: "10vh",
        "append-to-body": true
      },
      // confirm组件
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "确定",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      // cancel组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "关闭",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        maxlength: 50,
        placeholder: $T("请输入方案名称"),
        style: {
          width: "100%"
        },
        event: {}
      },
      // 被分摊能源类型
      ElSelect_1: {
        value: "",
        style: {},
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        label: "text",
        value: "id",
        disabled: "disabled"
      },
      // 1树组件
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      options_method: [
        {
          value: 1,
          label: "固定比例"
        },
        {
          value: 2,
          label: "动态分摊"
        }
      ],
      // 选择时段
      CetDatePicker_time: {
        val: null,
        config: {
          valueFormat: "timestamp",
          type: "daterange",
          clearable: false,
          unlinkPanels: true,
          rangeSeparator: $T("至"),
          // 时间范围限制
          pickerOptions: {
            // 禁止选择器选中同一天
            onPick: ({ maxDate, minDate }) => {
              if (minDate && !maxDate) {
                vm.disabledTime = minDate;
              }
              if (maxDate) {
                vm.disabledTime = null;
              }
            },
            disabledDate(time) {
              let disabledTime = vm.disabledTime;
              if (disabledTime) {
                return time.getTime() === disabledTime.getTime();
              }
            }
          }
        }
      },
      // 开始时间
      CetDatePicker_time2: {
        val: null,
        config: {
          valueFormat: "timestamp",
          type: "date",
          clearable: false
        }
      },
      // 永远时间
      foreverTime: 9999000000000,
      // 动态分摊间隔
      options_interval: [
        {
          value: 7,
          label: "小时"
        },
        {
          value: 12,
          label: "日"
        },
        {
          value: 14,
          label: "月"
        }
      ],
      // 分摊能源类型
      ElSelect_2: {
        value: "",
        style: {
          width: "250px"
        },
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      objectData: [],
      // 分摊节点列表
      tableData: [],
      tableDataColumns: [
        {
          title: $T("分摊对象"),
          key: "name",
          width: 100
        },
        {
          title: $T("分摊比例"),
          key: "rate",
          width: 100
        }
      ],
      // 关联被分摊对象当前选中节点
      currentNode: {},
      // 关联对象中已被选择的节点
      rightSelectedNode: [],
      testTransfer: false,
      // 穿梭框默认选择节点
      defaultChecked: [],
      titleMsg: "比例相加必须等于1，单个比例必须大于0",
      //  定义是否进行重新重算弹窗
      whetherToRecal: false,
      queryBody: {
        name: "",
        energysharemethod: 1,
        effectiveTime: [
          this.$moment().startOf("day").valueOf(),
          this.$moment().add(1, "day").startOf("day").valueOf()
        ],
        starttime: this.$moment().startOf("day").valueOf(),
        aggregationcycle: 7,
        energysharetoobject_model: [],
        energytype: null,
        isForever: true
      },
      // 按张壮要求对方案名称进行输入校验
      // 1表单组件
      CetForm_1: {
        dataMode: "static", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入方案名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          rootnodeid: [
            {
              required: true,
              message: $T("请选择所属项目"),
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          // saveData_out: this.CetForm_1_saveData_out
        }
      },
      // 按张壮要求进行被分摊对象和关联对象如果一致的话进行提示
      leftObjectKey: [],
      ElSelect_3: {
        value: 0,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_3_change_out
        }
      },
      ElOption_3: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  watch: {
    openTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = this._.cloneDeep(val);
      this.CetForm_1.data = {};
      this.CetForm_1.resetTrigger_in = Date.now();
      this.leftObjectKey = [];
      this.init();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
      this.testTransfer = false;
    }
  },
  methods: {
    async init() {
      await this.getRootNode();
      await this.getApportionedEnergy();
      await this.getTreeData1();
      if (
        (this.inputData_in.id === 0 || this.inputData_in.id) &&
        this.inputData_in.energytype &&
        this.inputData_in.energysharemethod === 2
      ) {
        this.queryBody.energytype = this.inputData_in.energytype;
        await this.getTreeData2();
      } else {
        await this.getObjectTree();
      }
      this.testTransfer = true;
      this.rightSelectedNode = [];
      this.defaultChecked = [];
      this.whetherToRecal = false;
      if (this.inputData_in.id === 0 || this.inputData_in.id) {
        this.CetDialog_1.title =
          this.inputData_in.id === 0 ? $T("新增分摊方案") : $T("修改分摊方案");
        if (
          this.inputData_in.id &&
          this.inputData_in.starttime <= this.$moment().valueOf()
        ) {
          this.whetherToRecal = true;
        }
        this.queryBody = this._.cloneDeep(this.inputData_in);
        // 要使用set进行双向绑定处理，否则无法响应
        this.$set(this.queryBody, "effectiveTime", [
          this.inputData_in.starttime,
          this.inputData_in.endtime
        ]);
        this.$set(this.CetForm_1.data, "name", this.inputData_in.name);
        this.CetTree_1.selectNode = {
          id: this.inputData_in.objectid,
          modelLabel: this.inputData_in.objectlabel,
          tree_id:
            this.inputData_in.objectlabel + "_" + this.inputData_in.objectid
        };
        let data = [];
        this.inputData_in.energysharetoobject_model.forEach(item => {
          data.push({
            id: item.objectid,
            modelLabel: item.objectlabel,
            name: item.objectname,
            rate: item.rate
          });
          let tree_id = item.objectlabel + "_" + item.objectid;
          this.defaultChecked.push(tree_id);
        });
        this.rightSelectedNode = data;
      } else {
        this.CetDialog_1.title = $T("新增分摊方案");
        // 新增方案进行默认值处理
        this.queryBody.name = "";
        this.queryBody.energysharemethod = 1;
        this.queryBody.effectiveTime = [
          this.$moment().startOf("day").valueOf(),
          this.$moment().add(1, "day").startOf("day").valueOf()
        ];
        this.queryBody.starttime = this.$moment().startOf("day").valueOf();
        this.queryBody.aggregationcycle = 7;
        this.queryBody.energysharetoobject_model = [];
        // this.currentNode = {};
        this.rightSelectedNode = [];
        this.queryBody.isForever = true;
      }
    },
    async getRootNode() {
      this.ElOption_3.options_in = [];
      const res = await customApi.rootNode();
      const data = res.data || [];
      this.ElOption_3.options_in = data;
      const rootnodeid = this.isEdit
        ? this.inputData_in.rootnodeid
        : data?.[0]?.id;
      this.$set(this.CetForm_1.data, "rootnodeid", rootnodeid);
    },
    CetButton_confirm_statusTrigger_out() {
      let param = this.updateQueryBody();
      if (!param) {
        return;
      }
      customApi.checkSchemeConfig(param).then(res => {
        if (res.code === 0) {
          let result = this._.get(res, "data", {});
          if (result.checkType === 1) {
            this.ischeckRecal(param);
          } else if (result.checkType === 2) {
            let str = "";
            str += `<div>当前分摊方案与${
              result.configName
            }在生效时间上存在交叉</div>
            <div>当前方案生效时间：${this.getTimeText(param.starttime)} ~ ${
              this.queryBody.isForever
                ? "永远"
                : this.getTimeText(param.endtime)
            }</div>
            <div>冲突方案生效时间：${this.getTimeText(
              result.startTime
            )} ~ ${this.getTimeText(result.endTime)}</div></br>
            <div>是否可以修改冲突方案的生效时间</div>
            <div>修改之后两个方案生效时间：</div>
            <div>当前方案生效时间：${this.getTimeText(param.starttime)} ~ ${
              this.queryBody.isForever
                ? "永远"
                : this.getTimeText(param.endtime)
            }</div>
            <div>冲突方案生效时间：${this.getTimeText(
              result.startTimeEdit
            )} ~ ${this.getTimeText(result.endTimeEdit)}</div></br>`;
            this.$confirm(str, "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
              dangerouslyUseHTMLString: true
            })
              .then(() => {
                this.ischeckRecal(param);
              })
              .catch(() => {});
          } else if (result.checkType === 3) {
            let str = "";
            if (result.desc) {
              str = result.desc;
            } else {
              str += `<div>当前分摊方案与${
                result.configName
              }在生效时间上存在交叉</div>
            <div>当前方案生效时间：${this.getTimeText(param.starttime)} ~ ${
                this.queryBody.isForever
                  ? "永远"
                  : this.getTimeText(param.endtime)
              }</div>
            <div>冲突方案生效时间：${this.getTimeText(
              result.startTime
            )} ~ ${this.getTimeText(result.endTime)}</div></br>
            <div>请修改当前方案的生效时间</div>`;
            }

            this.$confirm(str, "提示", {
              confirmButtonText: "确定",
              showCancelButton: false,
              type: "warning",
              dangerouslyUseHTMLString: true
            })
              .then(() => {})
              .catch(() => {});
          }
        }
      });
    },
    ischeckRecal(param) {
      if (param.id) {
        if (this.whetherToRecal) {
          this.$confirm(
            "该方案正在生效中,生效时间已修改,请点击重算",
            "重算提示",
            {
              type: "warning",
              distinguishCancelAndClose: true,
              confirmButtonText: "确定",
              showClose: false,
              showCancelButton: false,
              closeOnClickModal: false,
              closeOnPressEscape: false
            }
          )
            .then(() => {
              this.confirmSheme(param);
            })
            .catch(() => {});
        } else {
          this.confirmSheme(param);
        }
      } else {
        this.confirmSheme(param);
      }
    },
    getTimeText(time) {
      if (time === 9999000000000) {
        return "永远";
      } else {
        return this.$moment(time).format("YYYY-MM-DD");
      }
    },
    // 更新方案条件
    updateQueryBody() {
      let param = {
        id: 0,
        aggregationcycle: this.queryBody.aggregationcycle,
        createtime: this.$moment().valueOf(),
        endtime: this.queryBody.isForever
          ? this.foreverTime
          : this.$moment(this.queryBody.effectiveTime[1])
              .startOf("day")
              .valueOf(),
        starttime: this.queryBody.isForever
          ? this.queryBody.starttime
          : this.$moment(this.queryBody.effectiveTime[0])
              .startOf("day")
              .valueOf(),
        energysharemethod: this.queryBody.energysharemethod,
        energysharetoobject_model: [],
        energytype: this.queryBody.energytype,
        name: this.CetForm_1.data.name,
        objectenergytype: this.queryBody.objectenergytype,
        objectid: this.currentNode.id,
        objectlabel: this.currentNode.modelLabel,
        objectname: this.currentNode.name,
        rootnodeid: this.rootNode.id,
        rootnodelabel: this.rootNode.modelLabel
      };
      if (!param.objectid) {
        this.$message.error($T("被分摊对象不能为空"));
        return false;
      }
      if (!(this.rightSelectedNode && this.rightSelectedNode.length > 0)) {
        this.$message.error($T("分摊对象不能为空"));
        return false;
      }
      if (!param.name) {
        this.$message.error($T("方案名称不能为空"));
        return false;
      }
      if (!param.objectenergytype) {
        this.$message.error($T("能源类型不能为空"));
        return false;
      }
      if (this.inputData_in && this.inputData_in.id) {
        param.id = this.inputData_in.id;
      }
      if (param.energysharemethod == 1) {
        let num = 0;
        for (let i = 0; i < this.rightSelectedNode.length; i++) {
          if (
            this.rightSelectedNode[i].id == param.objectid &&
            this.rightSelectedNode[i].modelLabel == param.objectlabel
          ) {
            this.$message.error($T("分摊对象与被分摊对象不能是同一个节点"));
            return false;
          }
          if (Number(this.rightSelectedNode[i].rate) == 0) {
            this.$message.error($T("单个比例必须大于0"));
            return false;
          }
          param.energysharetoobject_model.push({
            objectlabel: this.rightSelectedNode[i].modelLabel,
            objectid: this.rightSelectedNode[i].id,
            objectname: this.rightSelectedNode[i].name,
            rate: Number(this.rightSelectedNode[i].rate)
          });
          num += Number(this.rightSelectedNode[i].rate) * 1000;
        }
        if (num / 1000 !== 1) {
          this.$message.error($T("分摊比例总和必须为1"));
          return false;
        }
        param.energytype = this.queryBody.objectenergytype;
      } else if (param.energysharemethod == 2) {
        for (let i = 0; i < this.rightSelectedNode.length; i++) {
          if (
            this.rightSelectedNode[i].id == param.objectid &&
            this.rightSelectedNode[i].modelLabel == param.objectlabel
          ) {
            this.$message.error($T("分摊对象与被分摊对象不能是同一个节点"));
            return false;
          }
          param.energysharetoobject_model.push({
            objectlabel: this.rightSelectedNode[i].modelLabel,
            objectid: this.rightSelectedNode[i].id,
            objectname: this.rightSelectedNode[i].name
          });
        }
        param.energytype = this.queryBody.energytype;
      }
      return param;
    },
    // 方案保存
    confirmSheme(param) {
      customApi.saveEnergyShareConfigScheme(param).then(res => {
        if (res.code === 0) {
          this.$message({
            message: $T("保存成功"),
            type: "success"
          });
          this.$emit("finishTrigger_out");
          this.CetDialog_1.closeTrigger_in = new Date().getTime();
          this.testTransfer = false;
        }
      });
    },
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
      this.testTransfer = false;
    },
    filterMethod() {},
    // 输入数字控制
    handleNum(row, key, num) {
      var value;
      if (typeof row[key] === "object") {
        if (row[key]) {
          value = row[key].val;
        } else {
          return;
        }
      } else {
        value = row[key];
      }
      var reg = new RegExp("^(\\-)*(\\d+)\\.(\\d{0," + num + "}).*$");
      var reg2 = new RegExp("^(\\d{0,1})(\\d+)\\.(\\d{0," + num + "}).*$");
      var reg3 = new RegExp("^(\\d{0,1})(\\d+)$");
      var val = String(value);
      if (val) {
        val = val.replace(/[^\d.]/g, ""); //清除数字和'.'以外的字符
        val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", "."); //只保留顺位第一的'.'
        val = val.replace(reg, "$1$2.$3"); //只能输入两位位小数
      }
      if (val.length >= 2 && val.indexOf(".") != 1 && val[0] == 0) {
        //在非小数的时候清除前导0
        val = val.replace(/0/, "");
      }
      if (val.length >= 2 && val.indexOf(".") == 0) {
        // 在先输入小数点时补0
        val = Number(val);
      }
      if (typeof row[key] === "object") {
        if (String(val).indexOf(".") != -1 && Number(val) > 1) {
          val = val.replace(reg2, "$1.$3");
        } else if (String(val).indexOf(".") == -1 && String(val).length > 1) {
          val = val.replace(reg3, "$1");
        }
        row[key].val = val;
      } else {
        if (String(val).indexOf(".") != -1 && Number(val) > 1) {
          val = val.replace(reg2, "$1.$3");
        } else if (String(val).indexOf(".") == -1 && String(val).length > 1) {
          val = val.replace(reg3, "$1");
        }
        row[key] = val;
      }
    },
    ElSelect_1_change_out() {
      this.currentNode = {};
      this.getTreeData1();
    },
    ElSelect_2_change_out() {
      this.$refs.treeTransfer.clearReset();
      this.rightSelectedNode = [];
      this.getTreeData2();
    },
    // 被关联对象节点树获取
    async getTreeData1() {
      const queryData = {
        nodeTreeGroupId: 2,
        energyType: this.queryBody.objectenergytype,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };
      const res = await customApi.getNodeTree(queryData);
      if (res.code !== 0) {
        return;
      }
      let data = this._.get(res, "data", []);
      this.CetTree_1.inputData_in = data;
      this.CetTree_1.selectNode = data.length > 0 ? data[0] : {};
    },
    // 关联对象节点树根据分摊能源类型获取
    async getTreeData2() {
      const queryData = {
        nodeTreeGroupId: 2,
        energyType: this.queryBody.energytype,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };
      const res = await customApi.getNodeTree(queryData);
      if (res.code !== 0) {
        return;
      }
      let data = this._.get(res, "data", []);
      this.objectData = this._.cloneDeep(data);
    },
    CetTree_1_currentNode_out(val) {
      if (this.currentNode.tree_id === val.tree_id) {
        return;
      }
      console.log(val, this.leftObjectKey, "test");
      this.currentNode = val;
      if (this.leftObjectKey.find(item => item === val.tree_id)) {
        this.$message.error("分摊对象与被分摊对象不能是同一个节点");
        return;
      }
    },
    // 获取能源类型
    async getApportionedEnergy() {
      await customApi.getProjectEnergyNoStandardized().then(res => {
        if (res.code === 0) {
          let data = this._.get(res, "data", []);
          let selectData = [];
          if (data.length > 0) {
            data.forEach(item => {
              selectData.push({
                id: item.energytype,
                text: item.name
              });
            });
          }
          this.ElOption_1.options_in = this._.cloneDeep(selectData);
          this.queryBody.energytype = selectData.length
            ? selectData[0].id
            : null;
          let objectenergytype = selectData.length ? selectData[0].id : null;
          this.queryBody.objectenergytype =
            this.inputData_in.id === 0 || this.inputData_in.id
              ? this.inputData_in.objectenergytype
              : objectenergytype;
        }
      });
    },
    handleChange(value) {
      let data = this._.cloneDeep(value);
      let result = [];
      data.forEach(item => {
        let testData =
          this.$refs.treeTransfer.$refs["from-tree"].getNode(item).data;
        result.push({
          id: testData.id,
          modelLabel: testData.modelLabel,
          name: testData.name,
          rate: 0
        });
      });
      // 除去第一次以外，
      // 把选中的节点和之前选中的节点进行比较，
      // 如果有相同的，就更新rate，
      if (this.rightSelectedNode.length) {
        result.forEach(item => {
          this.rightSelectedNode.forEach(node => {
            if (item.id === node.id && item.modelLabel === node.modelLabel) {
              item.rate = node.rate;
            }
          });
        });
      }
      this.rightSelectedNode = result;
    },
    // 弹窗关闭之前的调用方法
    beforeClose(done) {
      this.testTransfer = false;
      done();
    },
    // 获取公共分摊关联对象节点树展示
    async getObjectTree() {
      const queryData = {
        nodeTreeGroupId: 2,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };
      const res = await customApi.getNodeTree(queryData);
      if (res.code !== 0) {
        return;
      }
      const data = res.data || [];
      this.objectData = data;
    },
    // 分摊方式进行修改
    changeRadio(val) {
      this.$refs.treeTransfer.clearReset();
      this.rightSelectedNode = [];
      if (val == 2) {
        this.getTreeData2();
      } else if (val == 1) {
        this.getObjectTree();
      }
    },
    // 勾选永远的交互逻辑
    foreverChange(val) {
      if (val) {
        let date = this.queryBody.effectiveTime;
        this.queryBody.starttime = date[0];
      } else {
        let date = this.queryBody.starttime;
        let end = this.$moment(date).add(1, "day").startOf("day").valueOf();
        this.queryBody.effectiveTime = [date, end];
      }
    },
    // 穿梭框左侧点击触发
    leftCheckChange(treeCheckKeys, keys) {
      this.leftObjectKey = treeCheckKeys;
      if (
        treeCheckKeys.find(item => item === this.CetTree_1.selectNode.tree_id)
      ) {
        this.$message.error("分摊对象与被分摊对象不能是同一个节点");
        return;
      }
    },
    /**
     * 所属项目切换
     */
    ElSelect_3_change_out() {
      // 清除已勾选的节点
      this.getTreeData1();
      this.changeRadio(this.queryBody.energysharemethod);
    }
  }
};
</script>

<style lang="scss" scoped>
.title {
  font-weight: bold;
  font-size: 16px;
}
.label {
  font-weight: 400;
  font-size: 14px;
  margin-top: 16px;
}
.value_border {
  height: 300px;
  border: 1px solid;
  @include border_color(B1);
}
.tree_title {
  border-bottom: 1px solid;
  @include border_color(B1);
  @include background_color(BG);
  padding: 8px;
  margin-top: 0px;
}
.method_radio {
  :deep(.el-radio-button__inner) {
    width: 100%;
  }
}
.value_tree {
  :deep(.el-tree) {
    width: 100%;
  }
}
.table_style {
  :deep(.el-table__body-wrapper) {
    height: 258px !important;
  }
}
</style>
