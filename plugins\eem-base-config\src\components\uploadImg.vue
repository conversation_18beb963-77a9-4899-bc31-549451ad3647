<template>
  <div class="uploadImg">
    <el-upload
      v-if="!static_in"
      class="elupload"
      action=""
      :before-upload="handleBeforeUpload"
      :multiple="false"
      :show-file-list="false"
      :http-request="uploadFile"
    >
      <div class="logImgBox">
        <div class="noImg" v-show="!imgSrc">
          <div class="addWrap">
            <i class="el-icon-plus"></i>
            <div class="el-add">{{ $T("添加图片") }}</div>
          </div>
        </div>
        <img class="logImg" v-show="imgSrc" :src="imgSrc" alt="" />
        <div class="logImgHandle" v-show="imgSrc">
          <span class="handle">{{ $T("替换") }}</span>
          <span class="delete" @click.stop="deleteImg">{{ $T("删除") }}</span>
        </div>
      </div>
    </el-upload>
    <template v-else>
      <img v-if="imgSrc" class="staticImg" :src="imgSrc" alt="" />
      <span v-else class="staticNoImg">{{ $T("暂无图片") }}</span>
    </template>
  </div>
</template>
<script>
import customApi from "@/api/custom";
export default {
  props: {
    imgUrl: {
      type: String
    },
    static_in: {
      type: Boolean
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    }
  },
  data() {
    return {
      resUrl: "",
      imgSrc: ""
    };
  },
  watch: {
    imgUrl: {
      handler: function (val) {
        if (val) {
          this.getImgUrl(val);
        } else {
          this.resUrl = "";
          this.imgSrc = "";
        }
      },
      immediate: true
    }
  },
  methods: {
    handleBeforeUpload(file) {
      if (
        file.name.indexOf(".jpg") != -1 ||
        file.name.indexOf(".png") != -1 ||
        file.name.indexOf(".JPG") != -1 ||
        file.name.indexOf(".PNG") != -1
      ) {
        var uploadPicSize = this.systemCfg.uploadPicSize;
        const isLimit100M = file.size / 1024 / 1024 < uploadPicSize;
        if (!isLimit100M) {
          this.$message.error($T("上传文件超过规定的最大上传大小"));
        }
        return isLimit100M;
      } else {
        this.$message({
          type: "error",
          message: $T("只能上传jpg/png格式文件")
        });
        return false;
      }
    },
    deleteImg() {
      this.resUrl = "";
      this.imgSrc = "";
      this.urlOut();
    },
    urlOut() {
      this.$emit("update:imgUrl", this.resUrl);
      this.$emit("url_out", this._.cloneDeep(this.resUrl));
    },
    async getImgUrl(path) {
      if (!path) {
        this.resUrl = "";
        this.imgSrc = "";
        return;
      }
      this.resUrl = path;
      const response = await customApi.commonDownloadFile({ path });
      this.imgSrc = window.URL.createObjectURL(response.data);
    },
    deleteImgSave(url) {
      if (!url) {
        return;
      }
      customApi.commonDeleteFile({ path: url });
    },
    async uploadFile({ file }) {
      const data = new FormData();
      data.append("file", file);
      const response = await customApi.commonUploadFile(data);
      if (response.code !== 0) {
        return;
      }
      // 删除上一次的图片
      this.deleteImgSave(this._.cloneDeep(this.resUrl));
      this.resUrl = response.data;
      this.urlOut();
    }
  }
};
</script>
<style lang="scss" scoped>
.uploadImg {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: var(--Ra);
  background-color: var(--BG);
}
.elupload {
  width: 100%;
  height: 100%;
  position: relative;
  :deep(.el-upload) {
    height: 100%;
    width: 100%;
  }
  .logImgBox,
  .noImg {
    height: 100%;
    width: 100%;
  }
  .noImg {
    line-height: 0;
    position: relative;
    .addWrap {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
      margin: auto;
      height: 40px;
      .el-icon-plus {
        margin-top: 0;
        height: 15px;
        width: 15px;
        display: inline-block;
      }
      .el-add {
        font-size: var(--Ab);
        line-height: var(--Ab);
        margin-top: var(--J1);
        color: var(--ZS);
      }
    }
  }
  .logImg {
    height: 100%;
    width: 100%;
    display: block;
  }
  .logImgBox:hover .logImgHandle {
    display: block;
  }
  .logImgHandle {
    display: none;
    position: absolute;
    left: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    background-color: rgba(var(--BG1), 0.8);
  }
  .handle {
    cursor: pointer;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(calc(-100% - 4px)) translateY(-50%);
    font-size: 12px;
    color: var(--ZS);
  }
  .delete {
    cursor: pointer;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(4px) translateY(-50%);
    font-size: 12px;
    color: var(--Sta3);
  }
}
.staticImg {
  width: 100%;
  height: 100%;
  border-radius: var(--Ra2);
  overflow: hidden;
}
.staticNoImg {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  font-size: var(--Ab);
  line-height: var(--Ab);
  height: var(--Ab);
  text-align: center;
}
</style>
