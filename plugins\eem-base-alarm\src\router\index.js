/** @type {import("vue-router").RouteConfig[]} */

import main from "./main.vue";

export const appRoutes = [
  {
    path: "/",
    component: main,
    children: [
      // {
      //   path: "/emailserverset",
      //   component: () => import("@/projects/emailserverset/index.vue")
      // }
      // {
      //   path: "/messagePush",
      //   component: () => import("@/projects/messagePush/index.vue")
      // }
    ]
  }
];

export default {
  // mode: "history",
  routes: appRoutes
};
