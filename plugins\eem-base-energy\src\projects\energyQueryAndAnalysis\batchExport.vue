<template>
  <CetDialog
    class="CetDialog"
    v-bind="CetDialog_1"
    v-on="CetDialog_1.event"
    width="620px"
  >
    <div class="eem-cont-c1">
      <el-checkbox v-model="checked" @change="checkedChange" class="mb-J1">
        {{ $T("默认选中子节点") }}
      </el-checkbox>
      <div class="treeBox">
        <CetGiantTree
          class="giantTree"
          ref="giantTree1"
          v-show="!checked"
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
        ></CetGiantTree>
        <CetGiantTree
          class="giantTree"
          ref="giantTree2"
          v-show="checked"
          v-bind="CetGiantTree_2"
          v-on="CetGiantTree_2.event"
        ></CetGiantTree>
      </div>
      <div class="flex-row mtJ3">
        <div class="label mrJ1">{{ $T("时间排序") }}</div>
        <div class="flex-auto value">
          <el-radio v-model="returnVertical" :label="false">
            {{ $T("横排列") }}
          </el-radio>
          <el-radio v-model="returnVertical" :label="true">
            {{ $T("纵排列") }}
          </el-radio>
        </div>
      </div>
      <div class="flex-row mtJ3">
        <div class="label mrJ1">{{ $T("文件名称") }}</div>
        <div class="flex-auto value">
          <ElInput
            v-model="ElInput_name.value"
            v-bind="ElInput_name"
            v-on="ElInput_name.event"
          ></ElInput>
        </div>
      </div>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        class="ml-J1"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>

<script>
export default {
  name: "batchExport",
  props: {
    openTrigger_in: Number,
    closeTrigger_in: Number,
    treeData_in: Array,
    modelName: String,
    defaultCheckedNodes: Array,
    confirmFn: Function,
    treeLabel: {
      type: String,
      default: "name"
    }
  },
  computed: {},
  data() {
    return {
      returnVertical: false,
      checked: false,
      checkNodes: [],
      CetDialog_1: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        title: $T("批量导出"),
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: this.treeLabel
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out //勾选节点输出
        }
      },
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: this.treeLabel
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_2_checkedNodes_out //勾选节点输出
        }
      },
      ElInput_name: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {
          width: "100%"
        },
        event: {}
      }
    };
  },
  watch: {
    openTrigger_in() {
      this.init();
      this.CetDialog_1.openTrigger_in = Date.now();
    },
    closeTrigger_in() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    }
  },
  methods: {
    async init() {
      this.returnVertical = false;
      this.checked = false;
      this.ElInput_name.value = this.modelName;
      await this.$nextTick();
      this.getTreeData();
    },
    async getTreeData() {
      const treeData = this._.cloneDeep(this.treeData_in);
      const checkedNodes = this._.cloneDeep(this.defaultCheckedNodes);
      this.CetGiantTree_1.checkedNodes = this._.cloneDeep(
        this.defaultCheckedNodes
      );
      this.CetGiantTree_2.checkedNodes = [];
      this.checkNodes = checkedNodes;
      this.CetGiantTree_1.inputData_in = treeData;
      this.CetGiantTree_2.inputData_in = treeData;
      await this.$nextTick();
      this.expandNode(checkedNodes, "tree_id", this.$refs.giantTree1.ztreeObj);
      this.expandNode(checkedNodes, "tree_id", this.$refs.giantTree2.ztreeObj);
    },
    expandNode(nodes, key, ztreeObj) {
      nodes.forEach(item => {
        let node = ztreeObj.getNodeByParam(key, item[key]);
        let parentNodes = [],
          parentNode = node && node.getParentNode();
        while (parentNode) {
          parentNodes.push(parentNode);
          parentNode = parentNode.getParentNode();
        }
        parentNodes.forEach(i => {
          ztreeObj.expandNode(i, true);
        });
      });
      $(this.$refs.giantTree1.$el).find("#giantTree").scrollTop(0);
      $(this.$refs.giantTree2.$el).find("#giantTree").scrollTop(0);
    },
    async checkedChange() {
      $(this.$refs.giantTree1.$el).find(".ztree").scrollTop(0);
      $(this.$refs.giantTree2.$el).find(".ztree").scrollTop(0);

      await this.$nextTick();

      let checkNodes = this._.cloneDeep(this.checkNodes);
      const tree = this.checked ? this.CetGiantTree_2 : this.CetGiantTree_1;
      tree.checkedNodes = checkNodes;
      if (!checkNodes.length) {
        tree.unCheckTrigger_in = Date.now();
      }
    },
    CetGiantTree_1_checkedNodes_out(val) {
      if (this.checked) return;
      this.checkNodes = this._.cloneDeep(val);
    },
    CetGiantTree_2_checkedNodes_out(val) {
      if (!this.checked) return;
      this.checkNodes = this._.cloneDeep(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      if (!this.checkNodes?.length) {
        this.$message.warning($T("请选择节点"));
        return;
      }

      if (!this.ElInput_name.value) {
        this.$message.warning($T("请输入文件名称"));
        return;
      }

      const timeRanges = this.checkNodes.reduce((ranges, node) => {
        if (!node?.effTimeList?.[0]?.startTime) {
          return ranges;
        }

        ranges.push({
          objectId: node.id,
          objectLabel: node.modelLabel,
          timeRanges: node.effTimeList
        });
        return ranges;
      }, []);

      const params = {
        returnVertical: this.returnVertical,
        fileName: this.ElInput_name.value,
        nodes: this.checkNodes.map(item => {
          const obj = {
            id: item.id,
            modelLabel: item.modelLabel,
            tree_id: item.tree_id
          };
          obj[this.treeLabel] = item[this.treeLabel];
          return obj;
        })
      };
      if (timeRanges.length) {
        params.timeRanges = timeRanges;
      }
      this.confirmFn && this.confirmFn(params);
      this.CetDialog_1.closeTrigger_in = Date.now();
    }
  }
};
</script>

<style lang="scss" scoped>
.treeBox {
  height: 300px;
  :deep(#giantTree) {
    height: calc(100% - 32px) !important;
  }
}
.label,
.value {
  line-height: 32px;
}
</style>
