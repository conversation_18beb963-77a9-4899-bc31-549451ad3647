<template>
  <div class="flex flex-col fullfilled" :class="{ light: isLight }">
    <div class="font-bold title text-H3 mb-J3">{{ $T("综合成本概览") }}</div>
    <div v-if="!showEmpty">
      <div class="flex flex-row totalData pl-J3 pr-J3">
        <div class="iconBox">
          <div class="icon"></div>
        </div>
        <div class="text ml-J1 text-bottom">{{ $T("总用能成本") }}</div>

        <el-tooltip :content="valueFormat(total)">
          <div class="flex-auto value ml-J1 text-ellipsis">
            {{ valueFormat(total) }}
          </div>
        </el-tooltip>
        <div class="unit ml-J1 text-bottom">{{ unit }}</div>
      </div>
      <div class="flex flex-row progressBox">
        <div
          :class="{
            tb: true,
            'flex-auto': true,
            fullwidth: true,
            lower: checkLower(yoyValue, total),
            up: checkUp(yoyValue, total)
          }"
        >
          <el-progress
            :width="170"
            :stroke-width="20"
            :class="{
              progress: true,
              noHb: aggregationCycle === 17
            }"
            type="circle"
            :percentage="calculateRadioValue(yoyValue, total)"
            :define-back-color="isLight ? '#F8FAFB' : '#1F2B54'"
            :show-text="false"
            :color="progressColor(yoyValue, total)"
          ></el-progress>
          <div class="content">
            <div class="label">{{ $T("同比") }}</div>
            <div class="value">{{ valueFormat(yoyValue) }}</div>
            <div class="radioBox">
              <span class="icon"></span>
              <span class="radioVal ml-J1 mr-J0">
                {{ calculateRadio(yoyValue, total) }}
              </span>
              <span>%</span>
            </div>
          </div>
        </div>
        <div
          v-if="aggregationCycle !== 17"
          class="flex-auto hb fullwidth ml-J3"
          :class="{
            lower: checkLower(chainValue, total),
            up: checkUp(chainValue, total)
          }"
        >
          <el-progress
            :width="170"
            :stroke-width="20"
            class="progress"
            type="circle"
            :percentage="calculateRadioValue(chainValue, total)"
            :define-back-color="isLight ? '#F8FAFB' : '#1F2B54'"
            :show-text="false"
            :color="progressColor(chainValue, total)"
          ></el-progress>

          <div class="content">
            <div class="label">{{ $T("环比") }}</div>
            <div class="value">{{ valueFormat(chainValue) }}</div>
            <div class="radioBox">
              <span class="icon"></span>
              <span class="radioVal ml-J1 mr-J0">
                {{ calculateRadio(chainValue, total) }}
              </span>
              <span>%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-empty
      class="flex-auto"
      v-else-if="isLight"
      :image-size="216"
      image="static/assets/empty_min_light.png"
    ></el-empty>
    <el-empty
      v-else
      class="flex-auto"
      :image-size="216"
      image="static/assets/empty_min.png"
    ></el-empty>
  </div>
</template>

<script>
import omegaTheme from "@omega/theme";
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
export default {
  props: {
    params: Object
  },
  computed: {
    isLight() {
      return omegaTheme.theme === "light";
    },
    aggregationCycle() {
      return this.params?.aggregationCycle;
    }
  },
  data() {
    return {
      showEmpty: true,
      total: null,
      yoyValue: null,
      chainValue: null,
      unit: "--"
    };
  },
  watch: {
    params: {
      handler() {
        this.getData();
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async getData() {
      this.showEmpty = true;
      if (!this.params) return;
      const res = await customApi.realtimeCostRadio(this.params);
      this.showEmpty = !res?.data;
      this.total = res?.data?.value;
      this.yoyValue = res?.data?.yoyValue;
      this.chainValue = res?.data?.chainValue;
      this.unit = res?.data?.unitName ?? "--";
    },
    valueFormat(val) {
      if (val == null) return "--";
      return common.formatNum(val.toFixed(2));
    },
    checkLower(value, total) {
      const totalValue = total || 0;
      if (value == null) return;
      return totalValue < value;
    },
    checkUp(value, total) {
      const totalValue = total || 0;
      if (value == null) return;
      return totalValue > value;
    },
    calculateRadio(value, total) {
      if (!value) return "--";
      const totalValue = total || 0;
      return ((Math.abs(totalValue - value) / value) * 100).toFixed(2);
    },
    calculateRadioValue(value, total) {
      const totalValue = total || 0;
      if (value == null) return 0;
      return (Math.abs(totalValue - value) / value) * 100;
    },
    progressColor(value, total) {
      if (this.isLight) {
        return value > total ? "#70E09E" : "#F95E5A";
      }
      return value > total ? "#05F974" : "#FF5D5D";
    }
  }
};
</script>

<style lang="scss" scoped>
.totalData {
  height: 72px;
  box-sizing: border-box;
  background-image: url("../assets/overviewBG.png");
  background-size: 100% 100%;
  border-radius: 12px 12px 12px 12px;
  .iconBox {
    width: 24px;
    position: relative;
    .icon {
      height: 24px;
      width: 24px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      background-image: url("../assets/overviewIcon.png");
      background-size: 100% 100%;
    }
  }
  .text {
    font-size: 16px;
    margin-bottom: 24px;
  }
  .value {
    text-align: right;
    font-family: Barlow, Barlow;
    font-weight: bold;
    font-size: 28px;
    margin-top: 19px;
  }
  .unit {
    font-size: 16px;
    margin-bottom: 24px;
  }
}
.progressBox {
  margin-top: 26px;
  .progress {
    width: 170px;
    height: 170px;
    &.noHb {
      margin-left: 100px;
    }
  }
  .tb,
  .hb {
    position: relative;
    .content {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      .value {
        font-family: Barlow, Barlow;
        font-weight: bold;
        font-size: 20px;
      }
      .radioBox {
        .icon {
          width: 14px;
          height: 14px;
          display: none;
          transform: translate(0, 3px);
        }
        .radioVal {
          font-size: 16px;
          font-weight: bold;
          font-family: Barlow, Barlow;
        }
      }
    }
    &.lower .content .radioBox {
      .icon {
        display: inline-block;
        background-image: url("../assets/down.png");
        background-size: 100% 100%;
      }
      .radioVal {
        background: linear-gradient(270deg, #78ffb6 0%, #e5fff1 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        display: inline-block;
      }
    }
    &.up .content .radioBox {
      .icon {
        display: inline-block;
        background-image: url("../assets/up.png");
        background-size: 100% 100%;
      }
      .radioVal {
        background: linear-gradient(270deg, #ff8b8b 0%, #ffe8e8 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        display: inline-block;
      }
    }
  }
}

.light {
  .totalData {
    background-image: url("../assets/overviewBG_light.png");
    .iconBox .icon {
      background-image: url("../assets/overviewIcon_light.png");
    }
  }
  .tb,
  .hb {
    &.lower .content .radioBox .radioVal {
      background: linear-gradient(270deg, #29b061 0%, #58db8f 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      display: inline-block;
    }
    &.up .content .radioBox .radioVal {
      background: linear-gradient(270deg, #f95e5a 0%, #ffa8a6 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      display: inline-block;
    }
  }
}
.text-bottom {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
</style>
