<template>
  <div class="flex flex-col fullfilled">
    <div class="font-bold title text-H3">{{ $T("综合成本趋势") }}</div>
    <CetChart class="flex-auto" v-bind="CetChart_1"></CetChart>
  </div>
</template>

<script>
import { themeMap } from "cet-chart";
import omegaTheme from "@omega/theme";
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
export default {
  props: {
    params: Object
  },
  data() {
    const currentTheme = omegaTheme.theme;
    const themeMapConfig = themeMap.get(currentTheme);
    let colorList = themeMapConfig.color;
    return {
      colorList,
      unit: "--",
      CetChart_1: {
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "axis"
          },
          legend: {},
          xAxis: [
            {
              type: "category",
              data: [],
              axisPointer: {
                type: "shadow"
              }
            }
          ],
          yAxis: [
            {
              type: "value",
              name: "--",
              nameTextStyle: {
                align: "left"
              }
            }
          ],
          grid: {
            left: "0",
            right: "0",
            top: "40",
            bottom: "0",
            containLabel: true
          },
          series: []
        }
      }
    };
  },
  watch: {
    params: {
      handler() {
        this.getData();
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async getData() {
      if (!this.params) {
        this.CetChart_1.options.series = [];
        this.CetChart_1.options.yAxis[0].name = "--";
        this.CetChart_1.options.xAxis[0].data = [];
        this.CetChart_1.options = this._.cloneDeep(this.CetChart_1.options);
        return;
      }
      const res = await customApi.realtimeCostRadioTrend(this.params);
      const unit = res?.data?.unitName ?? "--";
      const nowData = res?.data?.nowData ?? [];
      const chainData = res?.data?.chainData ?? [];
      const yoyData = res?.data?.yoyData ?? [];

      const aggregationCycle = this.params?.aggregationCycle;
      const dateType = aggregationCycle == 17 ? "MM" : "DD";
      const xAxisData = [];
      nowData.forEach(item => {
        const xAxisLabel = this.$moment(item.logTime).format(dateType);
        xAxisData.push(xAxisLabel);
      });
      this.unit = unit;

      // 年没有环比
      const series = [
        {
          name: $T("综合成本"),
          type: "bar",
          tooltip: {
            valueFormatter: value => {
              return `${this.valueFormat(value)} ${this.unit}`;
            }
          },
          data: []
        },
        {
          name: $T("同比"),
          type: "line",
          smooth: true,
          showSymbol: false,
          tooltip: {
            valueFormatter: value => {
              return `${this.valueFormat(value)} ${this.unit}`;
            }
          },
          data: [],
          itemStyle: {
            color: this.colorList[1]
          }
        }
      ];
      if (aggregationCycle !== 17) {
        series.push({
          name: $T("环比"),
          type: "line",
          smooth: true,
          showSymbol: false,
          tooltip: {
            valueFormatter: value => {
              return `${this.valueFormat(value)} ${this.unit}`;
            }
          },
          data: [],
          itemStyle: {
            color: this.colorList[2]
          }
        });
      }
      this.CetChart_1.options.series = series;
      this.CetChart_1.options.yAxis[0].name = unit;
      this.CetChart_1.options.xAxis[0].data = xAxisData;
      this.CetChart_1.options.series[0].data = nowData.map(i => i.value);
      this.CetChart_1.options.series[1].data = yoyData.map(i => i.value);
      const barWidth = aggregationCycle == 17 ? 24 : 16;
      this.CetChart_1.options.series[0].barWidth = barWidth;
      this.CetChart_1.options.series[1].barWidth = barWidth;
      if (aggregationCycle !== 17) {
        this.CetChart_1.options.series[2].data = chainData.map(i => i.value);
        this.CetChart_1.options.series[2].barWidth = barWidth;
      }
      this.CetChart_1.options = this._.cloneDeep(this.CetChart_1.options);
    },
    valueFormat(val) {
      if (val == null) return "--";
      return common.formatNum(val.toFixed(2));
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
