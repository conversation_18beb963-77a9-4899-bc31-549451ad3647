import fetch from "eem-base/utils/fetch";

// 能效配置维度配置查询
export function queryEnergyefficiencyset(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/analysis/getEnergyefficiencyset`,
    method: "POST",
    data
  });
}

// 能效对标图表数据
export function queryEnergyEfficiencyDataWithLevel(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/analysis/energyEfficiency/withLevel`,
    method: "POST",
    data
  });
}

// 获取产量数据
export function querySystemDataInput(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/analysis/input/query/new`,
    method: "POST",
    data
  });
}

// 获取周期内能效最大最小和平均值
export function queryMaxMinAvgEnergyEfficiency(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/analysis/energyEfficiency/getMaxMinAvgEnergyEfficiency`,
    method: "POST",
    data
  });
}

// 历史最优值数据
export function queryHistoryOptimumValue(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/analysis/energyEfficiency/getHistoryOptimumValue`,
    method: "POST",
    data
  });
}

// 能效对标数据
export function queryEnergyEfficiencyBenchmark(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/analysis/energyEfficiency/getEnergyEfficiencyBenchmark`,
    method: "POST",
    data
  });
}

// 通过能效配置获取能效数据
export function queryEnergyEfficiencyData(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/analysis/energyEfficiency/query`,
    method: "POST",
    data
  });
}
// 获取节点对比数据
export function queryNodeCompare(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/analysis/query/ef/data`,
    method: "POST",
    data
  });
}
