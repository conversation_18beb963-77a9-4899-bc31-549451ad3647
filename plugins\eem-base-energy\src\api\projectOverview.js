import fetch from "eem-base/utils/fetch";

//查询项目的能源类型
export function getEneryType(params) {
  return fetch({
    url: `/eembaseenergy/v1/energy-type/projectEnergy/order`,
    method: "GET",
    params
  });
}

//多维度用能分析-获取标签数据列表
export function getPropertysList(params) {
  return fetch({
    url: `/eembaseenergy/v1/dimension/queryTagByDimensionId`,
    method: "GET",
    params
  });
}

//多维度用能分析-获取用能分析数据
export function getEachItemized(data) {
  return fetch({
    url: `/eembaseenergy/v1/itemized/energy/energydata/eachItemizedByNode`,
    method: "POST",
    data
  });
}

//查询能耗概览同环比数据
export function getConsumption(params) {
  return fetch({
    url: `/eembaseenergy/v2/energy/consumption/radio`,
    method: "POST",
    data: params
  });
}

// 能耗TOP数据 (查询top排名图表的数据信息)
export function getV2EmergyConsumptionTop(data) {
  return fetch({
    url: `/eembaseenergy/v2/energy/consumption/top`,
    method: "POST",
    data: data
  });
}
