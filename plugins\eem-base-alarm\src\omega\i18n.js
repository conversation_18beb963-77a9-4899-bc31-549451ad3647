import omegaApp from "@omega/app";
import en from "../config/lang/en.json";
import { OmegaI18nPlugin } from "@omega/i18n/plugin";
import baseEn from "eem-base/lang/en.json";

// 获取当前浏览器语言，除中文外默认英文
const browserLanguage = (window.navigator && window.navigator.language) || "en";
const defaultLanguage = browserLanguage.startsWith("zh") ? "zh_cn" : "en";

omegaApp.plugin.register(OmegaI18nPlugin, {
  defaultActive: defaultLanguage,
  en: { ...baseEn, ...en }
  // handler(local) {}
});
