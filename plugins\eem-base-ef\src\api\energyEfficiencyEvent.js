import fetch from "eem-base/utils/fetch";

// 获取能效事件
export function queryEvents(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/event/queryEvents`,
    method: "POST",
    data
  });
}

// 获取事件能效趋势曲线
export function eventAnalysis(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/event/eventAnalysis`,
    method: "POST",
    data
  });
}

// 事件确认
export function confirmEvents(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/event/confirmEvents`,
    method: "POST",
    data
  });
}
