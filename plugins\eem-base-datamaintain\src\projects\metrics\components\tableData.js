const formatNum = () => {
  return (row, column, cellValue, index) => {
    if (cellValue || cellValue === 0) {
      return cellValue;
    } else {
      return "--";
    }
  };
};

export const col = [
  {
    type: "index",
    label: "#",
    showOverflowTooltip: true
  },
  {
    prop: "codename",
    label: $T("能源计量器具代号"),
    headerAlign: "left",
    align: "left",
    showOverflowTooltip: true,
    formatter: formatNum()
  },
  {
    prop: "name",
    label: $T("名称"),
    headerAlign: "left",
    align: "left",
    showOverflowTooltip: true,
    formatter: formatNum()
  },
  {
    prop: "model",
    label: $T("型号规格"),
    headerAlign: "left",
    align: "left",
    showOverflowTooltip: true,
    formatter: formatNum()
  },
  {
    prop: "managementnumber",
    label: $T("管理编号"),
    headerAlign: "left",
    align: "left",
    showOverflowTooltip: true,
    formatter: formatNum()
  },
  {
    prop: "location",
    label: $T("安装使用地点"),
    headerAlign: "left",
    align: "left",
    showOverflowTooltip: true,
    formatter: formatNum()
  },
  {
    prop: "monitorproperty",
    label: $T("测量对象属性"),
    headerAlign: "left",
    align: "left",
    showOverflowTooltip: true,
    formatter: formatNum()
  },
  {
    prop: "backup",
    label: $T("是否备用表计"),
    showOverflowTooltip: true,
    formatter: function (row, column, cellValue, index) {
      if ([true, false].includes(cellValue)) {
        return cellValue === true ? $T("是") : $T("否");
      } else {
        return "--";
      }
    }
  }
];
