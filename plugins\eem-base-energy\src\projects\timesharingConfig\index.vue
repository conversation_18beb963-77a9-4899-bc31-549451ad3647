<template>
  <div class="h-full flex-col flex p-J4 bg-BG1 rounded-Ra box-border">
    <div class="flex-auto flex-col flex">
      <div class="flex flex-row items-center justify-between">
        <div class="flex flex-row items-center">
          <customElSelect
            :prefix_in="$T('所属项目')"
            v-model="ElSelect_2.value"
            v-bind="ElSelect_2"
            v-on="ElSelect_2.event"
            v-show="ElOption_2.options_in?.length > 1"
          >
            <ElOption
              v-for="item in ElOption_2.options_in"
              :key="item[ElOption_2.key]"
              :label="item[ElOption_2.label]"
              :value="item[ElOption_2.value]"
              :disabled="item[ElOption_2.disabled]"
            ></ElOption>
          </customElSelect>
          <customElSelect
            class="ml-J1"
            :prefix_in="$T('能源类型')"
            v-model="ElSelect_1.value"
            v-bind="ElSelect_1"
            v-on="ElSelect_1.event"
          >
            <ElOption
              v-for="item in ElOption_1.options_in"
              :key="item[ElOption_1.key]"
              :label="item[ElOption_1.label]"
              :value="item[ElOption_1.value]"
              :disabled="item[ElOption_1.disabled]"
            ></ElOption>
          </customElSelect>
        </div>
        <div class="flex flex-row">
          <CetButton
            class="mr-J1"
            v-bind="CetButton_delete"
            v-on="CetButton_delete.event"
          ></CetButton>
          <CetButton v-bind="CetButton_3" v-on="CetButton_3.event"></CetButton>
        </div>
      </div>
      <div class="flex-auto mt-J3">
        <CetTable
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
          @expand-change="tableExpandChange"
          @selection-change="tableSelectionChange"
        >
          <ElTableColumn type="selection" width="55"></ElTableColumn>
          <ElTableColumn type="expand">
            <template slot-scope="props">
              <CetTable
                class="expandTable"
                :data.sync="props.row.children"
                :dynamicInput.sync="CetTable_1.dynamicInput"
                v-bind="CetTable_1"
              >
                <el-table-column
                  prop="name"
                  :label="$T('日时段方案')"
                  showOverflowTooltip
                ></el-table-column>
                <el-table-column
                  prop="identificationText"
                  :label="$T('时段')"
                  showOverflowTooltip
                ></el-table-column>
              </CetTable>
            </template>
          </ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_energytype$text"></ElTableColumn>
          <ElTableColumn
            :label="$T('节点信息')"
            width="150"
            header-align="left"
            align="left"
          >
            <template slot-scope="scope">
              <div @click.stop>
                <span
                  class="handle2 fl mr-J3"
                  @click.stop="handleCommand({ type: 'relevance', scope })"
                >
                  {{ $T("关联节点") }}
                </span>
              </div>
            </template>
          </ElTableColumn>
          <ElTableColumn
            :label="$T('操作')"
            :width="enLanguage ? 250 : 200"
            header-align="left"
            align="left"
          >
            <template slot-scope="scope">
              <div @click.stop>
                <span
                  class="handle fl mr-J3"
                  @click.stop="handleCommand({ type: 'detail', scope })"
                >
                  {{ $T("详情") }}
                </span>
                <span
                  class="handle fl mr-J3"
                  @click.stop="handleCommand({ type: 'edit', scope })"
                >
                  {{ $T("编辑") }}
                </span>
                <span
                  class="handle fl mr-J3"
                  @click.stop="handleCommand({ type: 'copy', scope })"
                >
                  {{ $T("复制方案") }}
                </span>
              </div>
            </template>
          </ElTableColumn>
        </CetTable>
      </div>
    </div>
    <AddTimeSharingScheme
      :visibleTrigger_in="AddTimeSharingScheme.visibleTrigger_in"
      :closeTrigger_in="AddTimeSharingScheme.closeTrigger_in"
      :queryId_in="AddTimeSharingScheme.queryId_in"
      :inputData_in="AddTimeSharingScheme.inputData_in"
      :editId_in="AddTimeSharingScheme.editId_in"
      @finishData_out="AddTimeSharingScheme_finishData_out"
      @updateParams="updateAddTimeSharingSchemeParams"
      :timeSchemeColors="timeSchemeColors"
      :rootNodes="ElOption_2.options_in"
    />
    <NodeSelect
      :visibleTrigger_in="NodeSelect.visibleTrigger_in"
      :closeTrigger_in="NodeSelect.closeTrigger_in"
      :inputData_in="NodeSelect.inputData_in"
      :rootNode_in="NodeSelect.rootNode_in"
    />
    <Detail
      v-bind="detail"
      v-on="detail.event"
      :timeSchemeColors="timeSchemeColors"
    />
  </div>
</template>
<script>
import customApi from "@/api/custom";
import AddTimeSharingScheme from "./AddTimeSharingScheme.vue";
import NodeSelect from "./NodeSelect.vue";
import Detail from "./detail";
import omegaI18n from "@omega/i18n";
import omegaTheme from "@omega/theme";
export default {
  name: "timesharingConfig",
  components: {
    AddTimeSharingScheme,
    NodeSelect,
    Detail
  },

  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    enLanguage() {
      return omegaI18n.locale === "en";
    }
  },

  data() {
    let timeSchemeColors = [];
    const currentTheme = omegaTheme.theme;
    // 要与index.vue中的css中的日历单元格对应起来，当前只给了12中配色
    if (currentTheme === "dark") {
      timeSchemeColors = [
        "#0C82F8",
        "#4AFDA7",
        "#FFC531",
        "#41E0E4",
        "#193DFF",
        "#FF8F27",
        "#A4D738",
        "#6D990C",
        "#69B4FF",
        "#FFDD85",
        "#DF7EB7",
        "#048C8E"
      ];
    } else {
      timeSchemeColors = [
        "#70E09E",
        "#4CA6FF",
        "#FFD12F",
        "#7DD9FF",
        "#3166EF",
        "#FF9D09",
        "#B9E177",
        "#6FBE0B",
        "#94C8FB",
        "#FFE69B",
        "#F184C4",
        "#B1F6FF"
      ];
    }
    return {
      timeSchemeColors,
      currentTebelItem: null,
      daySchemeDetail: null,
      ElSelect_1: {
        value: 0,
        style: {},
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: 0,
        style: {},
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {}
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        label: $T("序号"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "70" //绝对宽度
      },
      ElTableColumn_name: {
        prop: "name", // 支持path a[0].b
        label: $T("方案名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "100", //该宽度会自适应
        formatter: function (val) {
          if (val.name) {
            return val.name;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_energytype$text: {
        prop: "energytype$text", // 支持path a[0].b
        label: $T("能源类型"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "100", //该宽度会自适应
        formatter: function (val) {
          if (val.energytype$text) {
            return val.energytype$text;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      CetButton_3: {
        visible_in: true,
        disable_in: false,
        title: $T("新增分时方案"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      CetButton_delete: {
        visible_in: true,
        disable_in: true,
        title: $T("批量删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_delete_statusTrigger_out
        }
      },

      ElTableColumn_identification: {
        //type: "",      // selection 勾选 index 序号
        prop: "identification", // 支持path a[0].b
        label: $T("日时段方案"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "100", //该宽度会自适应
        formatter: function (val) {
          if (val.identification) {
            return val.identification;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_daysharesetText: {
        prop: "daysharesetText ", // 支持path a[0].b
        label: $T("时段"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "100", //该宽度会自适应
        formatter: function (val) {
          if (val.daysharesetText) {
            return val.daysharesetText;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      AddTimeSharingScheme: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        editId_in: 0
      },
      NodeSelect: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        rootNode_in: null
      },
      detail: {
        openTrigger_in: new Date().getTime(),
        inputData_in: null,
        currentNode_in: null,
        event: {}
      }
    };
  },
  watch: {},

  methods: {
    async tableExpandChange(row, expanded) {
      if (expanded.indexOf(row) === -1) {
        return;
      }
      const res = await customApi.timeShareSchemeById(row.id);
      if (res.code !== 0) {
        return;
      }

      const timeshareperiod_model = res.data?.[0]?.timeshareperiod_model || [];
      let tableData = {};
      const formatTimePart = value => String(value).padStart(2, "0");
      timeshareperiod_model.forEach(item => {
        tableData[item.name] ??= [];

        const dayshareset = item.dayshareset_model || [];
        const daysharesetText = dayshareset
          .map(ite => {
            const beginhour = formatTimePart(ite.beginhour);
            const beginminute = formatTimePart(ite.beginminute);
            const endhour = formatTimePart(ite.endhour);
            const endminute = formatTimePart(ite.endminute);
            return `${beginhour}:${beginminute}~${endhour}:${endminute}`;
          })
          .join(";");

        tableData[item.name].push(
          `(${item.identification}:${daysharesetText || "--"})`
        );
      });
      let childrenData = [];
      Object.keys(tableData).forEach(item => {
        childrenData.push({
          name: item,
          identificationText: tableData[item].join("、")
        });
      });
      this.$set(row, "children", childrenData);
    },

    async handleCommand({ type, scope }) {
      this.currentTebelItem = scope.row;
      let daySchemeDetail;
      if (type === "copy") {
        daySchemeDetail = await this.getTimeShareSchemeDetail(scope.row.id);
      }
      const rootNode = this.ElOption_2.options_in.find(
        item => item.id == this.ElSelect_2.value
      );
      switch (type) {
        case "detail":
          this.detail.inputData_in = this._.cloneDeep(this.currentTebelItem);
          this.detail.openTrigger_in = new Date().getTime();
          break;
        case "edit":
          this.AddTimeSharingScheme.inputData_in = null;
          this.AddTimeSharingScheme.editId_in = this.currentTebelItem.id;
          this.AddTimeSharingScheme.visibleTrigger_in = new Date().getTime();
          break;
        case "copy":
          this.AddTimeSharingScheme.editId_in = 0;
          daySchemeDetail.timeshareperiodArr.forEach(item => {
            if (item.data && item.data.length > 0) {
              item.data.forEach(ite => {
                ite.dayset_model = [];
                ite.daysetObj = {};
              });
            }
          });
          daySchemeDetail.id = 0;
          daySchemeDetail.name = $T("{0}-副本", daySchemeDetail.name);
          this.AddTimeSharingScheme.inputData_in = daySchemeDetail;
          this.AddTimeSharingScheme.visibleTrigger_in = new Date().getTime();
          break;

        case "relevance":
          this.NodeSelect.inputData_in = this._.cloneDeep(
            this.currentTebelItem
          );
          this.NodeSelect.rootNode_in = rootNode;
          this.NodeSelect.visibleTrigger_in = new Date().getTime();
          break;
      }
    },
    //获取能源类型
    async getEnergytype() {
      this.ElOption_1.options_in = [];
      const response = await customApi.getProjectEnergyNoStandardized();
      if (response.code !== 0) {
        return;
      }
      const data = response.data || [];
      const selectData = [
        {
          id: 0,
          text: $T("全部")
        },
        ...data.map(item => {
          return {
            id: item.energytype,
            text: item.name
          };
        })
      ];
      this.ElOption_1.options_in = selectData;
      this.ElSelect_1.value = 0;
    },
    async getRootNode() {
      this.ElOption_2.options_in = [];
      const res = await customApi.rootNode();
      const data = res.data || [];
      this.ElOption_2.options_in = data;
      this.ElSelect_2.value = data?.[0]?.id;
    },
    // 根据能源类型和项目获取分时方案
    async getTimeShareScheme() {
      const rootNode = this.ElOption_2.options_in.find(
        item => item.id == this.ElSelect_2.value
      );
      if (!this.ElOption_1.options_in.length || !rootNode) {
        return;
      }

      const queryData = {
        energyType: this.ElSelect_1.value,
        rootNode: {
          id: rootNode.id,
          modelLabel: rootNode.modelLabel
        }
      };
      const res = await customApi.timeShareSchemeList(queryData);
      if (res.code !== 0) {
        return;
      }

      const tableData = res.data || [];
      tableData.forEach(item => {
        let energytypeObj = this.ElOption_1.options_in.find(
          i => i.id == item.energytype
        );
        item.energytype$text = energytypeObj ? energytypeObj.text : "--";
        item.children = [];
      });
      this.CetTable_1.data = tableData;
    },
    // 获取分时方案
    async getTimeShareSchemeDetail(id) {
      this.daySchemeDetail = null;
      const res = await customApi.timeShareSchemeById(id);
      if (res.code !== 0) {
        return;
      }
      const data = res.data[0];
      if (!data) {
        return;
      }
      // 处理年历
      if (data.timeshareperiod_model?.length) {
        data.timeshareperiod_model.forEach(item => {
          if (item.dayset_model && item.dayset_model.length > 0) {
            item.dayset_model.forEach(ite => {
              if (!item.daysetObj) {
                item.daysetObj = {};
              }
              if (!item.daysetObj[this.$moment(ite.day).year()]) {
                item.daysetObj[this.$moment(ite.day).year()] = [ite];
              } else {
                item.daysetObj[this.$moment(ite.day).year()].push(ite);
              }
            });
          }
        });
      }
      // 过滤出日时段方案
      let dayObj = {};
      if (data.timeshareperiod_model?.length) {
        data.timeshareperiod_model.forEach(item => {
          if (!dayObj[item.name]) {
            dayObj[item.name] = [item];
          } else {
            dayObj[item.name].push(item);
          }
        });
      }
      data.timeshareperiodArr = [];
      Object.keys(dayObj).forEach(item => {
        data.timeshareperiodArr.push({
          name: item,
          data: dayObj[item]
        });
      });
      return data;
    },
    AddTimeSharingScheme_finishData_out() {
      this.getTimeShareScheme();
    },
    updateAddTimeSharingSchemeParams(val) {
      Object.assign(this.AddTimeSharingScheme, val);
    },
    // 新增方案
    CetButton_3_statusTrigger_out(val) {
      this.AddTimeSharingScheme.editId_in = 0;
      this.AddTimeSharingScheme.inputData_in = null;
      this.AddTimeSharingScheme.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 获取勾选之后的值
    tableSelectionChange(val) {
      this.deletePlanIds = [];
      this.CetButton_delete.disable_in = true;
      if (val.length) {
        val.forEach(item => {
          this.deletePlanIds.push(item.id);
        });
        this.CetButton_delete.disable_in = false;
      }
    },
    // 进行批量删除操作
    CetButton_delete_statusTrigger_out() {
      const data = this.deletePlanIds;
      this.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        cancelButtonClass: "btn-custom-cancel",
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: async (action, instance, done) => {
          if (action == "confirm") {
            const res = await customApi.timeShareSchemeDelete(data);
            if (res.code !== 0) {
              return;
            }
            this.$message.success($T("删除成功"));
            this.getTimeShareScheme();
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action != "confirm") {
            this.$message({
              type: "info",
              message: $T("取消删除")
            });
          }
        }
      });
    },
    ElSelect_1_change_out() {
      this.getTimeShareScheme();
    },
    ElSelect_2_change_out() {
      this.getTimeShareScheme();
    }
  },
  async mounted() {
    await this.getEnergytype();
    await this.getRootNode();
    this.getTimeShareScheme();
  }
};
</script>
<style lang="scss" scoped>
.expandTable {
  height: 300px;
  margin: var(--J3);
}
.delete {
  @include font_color(Sta3);
}
.handle {
  cursor: pointer;
  @include font_color(ZS);
}
.handle2 {
  cursor: pointer;
  @include font_color(ZS);
  text-decoration: underline;
}
</style>
