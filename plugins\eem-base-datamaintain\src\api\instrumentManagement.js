import fetch from "eem-base/utils/fetch";
const version = "v1";

//计量器具类型
export function typesOfMeasuringInstruments(data) {
  return fetch({
    url: `/eem-service/${version}/dashboard/type`,
    method: "POST",
    data
  });
}
// 仪表台账同步的节点树接口
export function queryPecTree(data) {
  return fetch({
    url: `/eem-service/${version}/dashboard/pec/tree`,
    method: "POST",
    data
  });
}
// 设备通讯
export function queryPecCoreMeterTree(data) {
  return fetch({
    url: `/eem-service/v2/peccore/pecCoreMeterTree`,
    method: "POST",
    data
  });
}
// 仪表台账同步
export function accountSync(data) {
  return fetch({
    url: `/eem-service/${version}/dashboard/sync`,
    method: "POST",
    data
  });
}
// 仪表台账详情--检定信息
export function queryRecordHistory(instrumentId) {
  return fetch({
    url: `/eem-service/${version}/dashboard/record/history`,
    method: "POST",
    params: {
      instrumentId
    }
  });
}
// 仪表台账--表计设备关联的管网信息和安装位置
export function queryDeviceConnectInfo(deviceId) {
  return fetch({
    url: `/eem-service/${version}/dashboard/device/connectInfo`,
    method: "POST",
    params: {
      deviceId
    }
  });
}
