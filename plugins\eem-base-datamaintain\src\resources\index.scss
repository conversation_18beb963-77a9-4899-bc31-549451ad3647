@import "~@omega/theme/index.scss";

// 定制的 elment-ui 样式
@import "eem-base/resources/elementui/elementui-custom.scss";

// 设置表格操作列可选字体样式
.eem-row-handle {
  @include font_color(ZS);
  cursor: pointer;
}
// 设置表格操作列不可选样式
.eem-row-no-handle {
  cursor: not-allowed;
  @include font_color(T6);
}
// 设置表格操作列删除选样式
.eem-row-delete {
  cursor: pointer;
  @include font_color(Sta3);
}

.common-title-H2 {
  @include font_size(H2);
  display: inline-block;
  line-height: 26px;
  font-weight: bold;
}

.eem-group-list {
  overflow: auto;
  .group-item {
    @include padding_left(J2);
    @include padding_right(J2);
    border: 1px solid;
    @include border_color(B1);
    border-radius: 4px;
    @include margin_bottom(J1);
    line-height: 30px;
    height: 30px;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .group-item:hover {
    @include font_color(ZS);
    @include background_color(BG2);
  }
  .active {
    @include font_color(ZS);
    @include background_color(BG4);
  }
}
