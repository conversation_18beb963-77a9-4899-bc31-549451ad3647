/** @type {import("vue-router").RouteConfig[]} */

import main from "./main.vue";

export const appRoutes = [
  {
    path: "/",
    component: main,
    children: [
      {
        path: "/energyAlarmConfig",
        component: () => import("@/projects/energyAlarmConfig/index.vue")
      },
      {
        path: "/cycleconfig",
        component: () => import("@/projects/cycleconfig/index.vue")
      },
      {
        path: "/timesharingConfig",
        component: () => import("@/projects/timesharingConfig/index.vue")
      },
      {
        path: "/publicSchemeConfig",
        component: () => import("@/projects/publicSchemeConfig/index.vue")
      },
      {
        path: "/dimensionConfiguration",
        component: () => import("@/projects/dimensionConfiguration/index.vue")
      },
      {
        path: "/projectOverview",
        component: () => import("@/projects/projectOverview/index.vue")
      },
      {
        path: "/energyQueryAndAnalysis",
        component: () => import("@/projects/energyQueryAndAnalysis/index.vue")
      },
      {
        path: "/subEnergyConsumption",
        component: () => import("@/projects/subEnergyConsumption/index.vue")
      },
      {
        path: "/energyattributionanalysis",
        component: () =>
          import("@/projects/energyattributionanalysis/index.vue")
      },
      {
        path: "/energyFlowAnalysis",
        component: () => import("@/projects/energyFlowAnalysis/index.vue")
      },
      {
        path: "/energyConsumptionEvent",
        component: () => import("@/projects/energyConsumptionEvent/index.vue")
      }
    ]
  }
];

export default {
  // mode: "history",
  routes: appRoutes
};
