import fetch from "eem-base/utils/fetch";

// 能效配置维度配置查询
export function getEfNodeDimTreeConfig(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/ef-node/dim-tree-config`,
    method: "POST",
    data
  });
}

/**
 * 获取多维度节点树 可支持传入末端需要保留的节点
 * @param {*keepNodeTypes} array 传入末端保留的节点的modelLabel，默认不过滤
 * @returns
 */
export function dimensionTreeFilterByEnergytype(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/ef-node/dim/node-tree/filter-by-energytype`,
    method: "POST",
    data
  });
}

// 多维度树
export function getEfNodeTreeAnalysis(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/ef-node/tree/analysis`,
    method: "POST",
    data
  });
}

/**
 * 查询根节点
 */
export function rootNode() {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/ef-node/root-node`,
    method: "GET"
  });
}

/**
 * 查询根节点
 */
export function efNodeTree(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/ef-node/tree`,
    method: "POST",
    data
  });
}

/**
 * 能效分析节点树查询
 */
export function treeByEf(params) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/ef-node/treeByEf`,
    method: "POST",
    params
  });
}

/**
 * 查询多维度节点树的列表
 */
export function dimNodeTree(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/ef-node/dim/node-tree`,
    method: "POST",
    data
  });
}
