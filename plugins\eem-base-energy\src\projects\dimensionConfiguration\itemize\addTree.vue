<template>
  <!-- 弹窗组件 -->
  <CetDialog
    v-bind="CetDialog_pagedialog"
    v-on="CetDialog_pagedialog.event"
    :class="currentTheme"
    :title="isEdit ? $T('编辑多维度节点树') : $T('新建多维度节点树')"
  >
    <CetForm
      ref="form"
      class="flex-col flex p-J4 bg1 content"
      :data.sync="CetForm_1.data"
      v-bind="CetForm_1"
      v-on="CetForm_1.event"
    >
      <el-form-item :label="$T('节点树名称')" prop="name">
        <ElInput
          v-model="CetForm_1.data.name"
          v-bind="ElInput_name"
          v-on="ElInput_name.event"
        ></ElInput>
      </el-form-item>
      <el-form-item
        :label="$T('节点树结构配置')"
        prop="treeConfig"
        class="treeBox"
        style="height: 0; flex: 1"
      >
        <div class="flex-row flex" style="height: calc(100% - 0px)">
          <div
            style="width: 380px"
            class="box pt-J3 pb-J3 pl-J4 pr-J4"
            ref="LevelContain"
            :class="currentTheme ? 'light' : ''"
          >
            <div
              class="treeLevelItem"
              v-for="(item, index) in treeLevelConfIgList"
              :class="'treeLevelItem' + ((index % 7) + 1)"
              :key="index"
            >
              <span class="name">{{ treeLevelNameList[index] }}</span>
              <ElSelect v-model="treeLevelConfIgList[index]" clearable>
                <ElOption
                  v-for="item in ElOption_treeLevel.options_in"
                  :key="item[ElOption_treeLevel.key]"
                  :label="item[ElOption_treeLevel.label]"
                  :value="item[ElOption_treeLevel.value]"
                  :disabled="item[ElOption_treeLevel.disabled]"
                ></ElOption>
              </ElSelect>
              <el-button
                icon="el-icon-close"
                class="deleteBtn"
                v-if="index"
                @click="deleteTreeLevel(index)"
              ></el-button>
              <el-button
                icon="el-icon-plus"
                type="primary"
                class="addBtn"
                v-if="
                  treeLevelConfIgList.length <
                  ElOption_treeLevel.options_in.length
                "
                @click="addTreeLevel(index)"
              ></el-button>
              <div
                class="arrow"
                v-if="index < ElOption_treeLevel.options_in.length - 1"
              ></div>
            </div>
          </div>
          <div style="width: 0; flex: 1" class="box ml-J3 p-J4">
            <CetTree
              :selectNode.sync="CetTree_1.selectNode"
              :checkedNodes.sync="CetTree_1.checkedNodes"
              :searchText_in.sync="CetTree_1.searchText_in"
              v-bind="CetTree_1"
              v-on="CetTree_1.event"
            ></CetTree>
          </div>
        </div>
      </el-form-item>
    </CetForm>

    <template v-slot:footer>
      <span>
        <!-- cancel按钮组件 -->
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <!-- preserve按钮组件 -->
        <CetButton
          class="ml-J1"
          v-bind="CetButton_preserve"
          v-on="CetButton_preserve.event"
        ></CetButton>
      </span>
    </template>
  </CetDialog>
</template>
<script>
import common from "eem-base/utils/common";
import omegaTheme from "@omega/theme";
import customApi from "@/api/custom";

export default {
  name: "addTree",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    currentTheme() {
      return omegaTheme.theme === "light";
    },
    isEdit() {
      return !!this.inputData_in?.id;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    treeDataList: {
      type: Array,
      default: () => []
    },
    rootNode_in: Object
  },
  data() {
    return {
      saveFlag: false, // 是否保存，保存时调用表单节点树结构校验
      treeLevelNameList: [
        $T("一级"),
        $T("二级"),
        $T("三级"),
        $T("四级"),
        $T("五级"),
        $T("六级"),
        $T("七级"),
        $T("八级"),
        $T("九级"),
        $T("十级"),
        $T("十一级"),
        $T("十二级"),
        $T("十三级"),
        $T("十四级"),
        $T("十五级"),
        $T("十六级"),
        $T("十七级"),
        $T("十八级"),
        $T("十九级"),
        $T("二十级")
      ],
      treeLevelConfIgList: [null],
      // treeLevel组件
      ElOption_treeLevel: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetDialog_pagedialog: {
        showClose: true,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        width: "960px",
        top: "5vh",
        appendToBody: true,
        event: {}
      },
      CetForm_1: {
        labelPosition: "top",
        dataMode: "component", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "150px",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入名称"),
              trigger: ["blur", "change"]
            },
            common.check_space,
            common.check_name,
            common.pattern_name,
            {
              validator: (rule, value, callback) => {
                let list = this.treeDataList;
                if (this.isEdit) {
                  list = list.filter(item => item.id !== this.inputData_in.id);
                }
                if (list.some(item => item.name === value)) {
                  return callback(new Error($T("节点树名称已存在")));
                }
                callback();
              }
            }
          ],
          treeConfig: [
            {
              required: true,
              trigger: ["change"],
              validator: (rule, value, callback) => {
                if (!this.saveFlag) callback();
                const emptyLength = this.treeLevelConfIgList.filter(i =>
                  this._.isNil(i)
                ).length;
                if (emptyLength > 0) {
                  return callback(new Error($T("请配置节点树结构")));
                }
                callback();
              }
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      // 1树组件
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        "default-expand-all": true,
        event: {}
      },
      // name组件
      ElInput_name: {
        placeholder: $T("请输入"),
        style: {
          width: "100%"
        },
        event: {}
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    async openTrigger_in(val) {
      this.CetDialog_pagedialog.openTrigger_in = val;
      this.saveFlag = false;

      await this.getTreeLevel();

      if (this.isEdit) {
        this.CetForm_1.data = {
          name: this.inputData_in.name
        };
        const children = this.inputData_in?.children ?? [];
        this.treeLevelConfIgList = children.map(i => i.id);
      } else {
        this.CetForm_1.data = {
          name: ""
        };
        this.treeLevelConfIgList = [null];
      }

      this.CetForm_1.resetTrigger_in = Date.now();
      this.CetTree_1.inputData_in = [];
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = val;
    },
    treeLevelConfIgList: {
      async handler(val) {
        this.ElOption_treeLevel.options_in.forEach(item => {
          const flag = (val || []).includes(item.id);
          item.disabled = flag;
        });
        this.CetTree_1.inputData_in = this.getTreeData();
      }
    }
  },
  methods: {
    async getTreeLevel() {
      if (!this.rootNode_in) {
        return;
      }
      const queryData = {
        id: this.rootNode_in.id,
        modelLabel: this.rootNode_in.modelLabel
      };
      const res = await customApi.unrelatedLevels(queryData);

      const data = res?.data ?? [];
      if (this.isEdit) {
        // 编辑的时候需要将待编辑节点树关联的tag加进来
        const children = this.inputData_in?.children ?? [];
        data.push(...children);
      }
      this.ElOption_treeLevel.options_in = data.map(i => ({
        id: i.id,
        name: i.name,
        disabled: false
      }));
    },
    deleteTreeLevel(index) {
      this.$confirm($T("确定要删除吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(async () => {
          const optionItem = this.ElOption_treeLevel.options_in.find(
            i => i.id === this.treeLevelConfIgList[index]
          );
          this.treeLevelConfIgList.splice(index, 1);
          if (!optionItem) return;
          optionItem.disabled = false;
          this.$refs?.form?.$refs?.cetForm?.validateField("treeConfig");
        })
        .catch(() => {
          this.$message.info($T("已取消删除"));
        });
    },
    addTreeLevel(index) {
      const emptyLength = this.treeLevelConfIgList.filter(i =>
        this._.isNil(i)
      ).length;
      if (emptyLength > 0) {
        this.$message.warning($T("当前存在未配置的节点树层级，请先配置"));
        return;
      }
      this.treeLevelConfIgList.splice(index + 1, 0, null);
      this.$nextTick(() => {
        const scrollDiv = this.$refs.LevelContain;
        if (scrollDiv) {
          // 滚动到最底部
          scrollDiv.scrollTop = scrollDiv.scrollHeight;
        }
      });
    },
    // 组建节点树数据
    getTreeData() {
      const treeMap = new Map();
      const rootNodes = [
        {
          name: this.rootNode_in?.name,
          id: "project" + this.rootNode_in?.id,
          children: []
        }
      ];
      const rootNode = rootNodes[0];

      const configIds = [];
      for (let index = 0; index < this.treeLevelConfIgList.length; index++) {
        const configId = this.treeLevelConfIgList[index];
        if (!configId) {
          continue;
        }

        // 在map存当前节点对象
        treeMap.set(configId, {
          name: this.ElOption_treeLevel.options_in.find(i => i.id === configId)
            .name,
          children: []
        });

        if (!configIds.length) {
          // 首节点
          configIds.push(configId);
          rootNode.children.push(treeMap.get(configId));
          continue;
        }

        const parentId = configIds[configIds.length - 1];
        const parent = treeMap.get(parentId);
        if (parent) {
          parent.children.push(treeMap.get(configId));
        }
        configIds.push(configId);
      }

      return rootNodes;
    },
    async CetForm_1_saveData_out(val) {
      const queryData = {
        rootNodeId: this.rootNode_in.id,
        rootNodeLabel: this.rootNode_in.modelLabel,
        name: val.name,
        levelList: this.treeLevelConfIgList.map((id, index) => {
          return {
            id,
            order: index + 1
          };
        })
      };

      if (this.isEdit) {
        queryData.id = this.inputData_in.id;
      }

      const saveFn = this.isEdit
        ? customApi.editDimension
        : customApi.addDimension;
      const res = await saveFn(queryData);
      if (res?.code !== 0) return;

      this.$emit("finishTrigger_out");
      this.$message.success($T("保存成功"));
      this.CetDialog_pagedialog.closeTrigger_in = val;
    },
    CetButton_preserve_statusTrigger_out(val) {
      this.saveFlag = true;
      this.CetForm_1.localSaveTrigger_in = val;
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = val;
    }
  }
};
</script>
<style lang="scss" scoped>
.content {
  border-radius: var(--Ra1);
  height: 672px;
  box-sizing: border-box;
  .box {
    border-radius: var(--Ra);
    overflow-y: auto;
    background-color: var(--BG2);
  }
  .treeBox :deep(.el-form-item__content) {
    height: 100%;
  }
  .tree,
  :deep(.el-tree) {
    @include themeify {
      background: none;
    }
  }
  @for $i from 1 through 7 {
    .treeLevelItem#{$i} {
      background: url("../assets/darkBG#{$i}.png") no-repeat;
    }
  }
  .light {
    @for $i from 1 through 7 {
      .treeLevelItem#{$i} {
        background: url("../assets/lightBG#{$i}.png") no-repeat;
      }
    }
  }
  .arrow {
    width: 8px;
    height: 40px;
    position: absolute;
    top: 74px;
    left: calc(50% - 10px);
    background: url("../assets/darkArrow.png") no-repeat;
  }
  .light .arrow {
    background: url("../assets/lightArrow.png") no-repeat;
  }
  .treeLevelItem {
    width: 292px;
    height: 114px;
    position: relative;
    .name {
      position: absolute;
      top: 3px;
      left: 16px;
      line-height: 22px;
    }
    .el-select {
      position: absolute;
      top: 32px;
      left: 16px;
      width: calc(100% - 32px) !important ;
    }
    .addBtn,
    .deleteBtn {
      padding: 0;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: var(--Ra);
      :deep(i) {
        @include font_size(H2);
      }
    }
    .deleteBtn {
      position: absolute;
      top: 25px;
      right: -32px;
      width: 24px;
      height: 24px;
      @include border_color(Sta3);
      :deep(i) {
        @include font_color(Sta3);
      }
    }

    .addBtn {
      margin: 0;
      width: 20px;
      height: 20px;
      position: absolute;
      bottom: 12px;
      left: calc(50% - 17px);
      top: 82px;
      z-index: 1;
    }
  }
}
</style>
