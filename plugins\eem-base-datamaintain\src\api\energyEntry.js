import fetch from "eem-base/utils/fetch";

function processRequest(data) {
  return data;
}

function processResponse(response) {
  return response;
}

// 数据录入相关-获取产品数据类型计量参数列表
export function queryProductList() {
  return fetch({
    url: `/eembasedatamaintain/product`,
    method: "GET"
  });
}
// 数据录入相关-获取能耗数据类型计量参数列表
export function queryProjectEnergyList(data, hideNotice) {
  return fetch({
    url: `/eem-service/v1/project/projectEnergy?projectId=${data.projectId}`,
    method: "GET",
    headers: { hideNotice: hideNotice },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

// 数据录入相关-获取能耗数据类型计量参数列表
export function getConvertedStandardCoalCoef(data, hideNotice) {
  return fetch({
    url: `/eem-service/v1/global/setting/convertedStandardCoalCoef?projectId=${data.projectId}`,
    method: "GET",
    headers: { hideNotice: hideNotice },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

// 数据录入相关-查询录入数据，返回格式根据计量参数类型分组
export function querySystemDataInput(data, hideNotice) {
  return fetch({
    url: `/eem-service/v1/system/data/input/query/new`,
    method: "POST",
    headers: { hideNotice: hideNotice },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

/**
 * 查询根节点
 */
export function rootNode() {
  return fetch({
    url: `/eembasedatamaintain/node/root-node`,
    method: "GET"
  });
}

/**
 * 查询多维度配置
 */
export function getMultiConfig() {
  return fetch({
    url: `/eembasedatamaintain/attribute-dimension/tree/config`,
    method: "POST"
  });
}
