<template>
  <div class="h-[calc(100%-104px)] relative">
    <div class="h-full overflow-auto" id="container-wrapper">
      <div id="placeholder"></div>
    </div>
    <div id="container" ref="container"></div>
  </div>
</template>

<script>
import G6 from "@antv/g6";
export default {
  name: "TopologyChart",
  props: {
    inputData_in: {
      type: [Object, Array]
    },
    resize: {
      type: Number
    }
  },
  components: {},
  data() {
    return {
      graph: null,
      lastPosition: [0, 0]
    };
  },
  watch: {
    inputData_in: {
      handler: function (val) {
        this.$nextTick(() => {
          this.paintChart(val?.selectId);
        });
      },
      deep: true,
      immediate: true
    },
    resize(val) {
      if (!_.isEmpty(this.graph)) {
        const width = this.$refs.container.clientWidth;
        const height = this.$refs.container.clientHeight || 500;
        this.graph.changeSize(width, height);
      }
    }
  },
  methods: {
    handlerFocusNode(val) {
      if (!this.graph) return;

      //将所有当前有choose状态的节点的choose状态置为false
      let chooseNodes = this.graph.findAllByState("node", "choose");
      chooseNodes.forEach(node => {
        this.graph.setItemState(node, "choose", false);
      });
      //搜索值为空
      if (!val) {
        return;
      }
      const list = val.split("_");
      const treeId = list[0] + "$" + list[1];
      const findNodes = this.graph.findAll("node", node => {
        return node._cfg.id === treeId;
      });
      if (findNodes.length > 0) {
        //设置当前节点的choose状态为true
        this.graph.setItemState(findNodes[0], "choose", true);

        //定位居中
        const bbox = findNodes[0].getBBox();
        const canvasPos = this.graph.getCanvasByPoint(
          bbox.centerX,
          bbox.centerY
        );
        const container = document.getElementById("container");
        container.scrollTo({
          left: canvasPos.x - container.clientWidth / 2,
          top: canvasPos.y - container.clientHeight / 2,
          behavior: "smooth"
        });
      }
    },
    // 初始化图形配置
    initGraphConf() {
      if (this.graph) return;
      var width = this.$refs.container.scrollWidth;
      var height = this.$refs.container.scrollHeight || 500;
      this.graph = new G6.Graph({
        container: "container",
        width,
        height,
        renderer: "canvas",
        layout: {
          type: "dagre",
          ranksep: 70,
          rankdir: "LR"
        },
        defaultNode: {
          size: [150, 0],
          type: "rect-jsx"
        },
        defaultEdge: {
          type: "line-arrow",
          style: {
            endArrow: {
              path: G6.Arrow.triangle(),
              fill: "#F6BD16"
            },
            lineWidth: 2,
            stroke: "#F6BD16"
          }
        },
        modes: {
          default: [
            "drag-canvas",
            "drag-node",
            "zoom-canvas",
            {
              type: "tooltip",
              formatText: this.formatLabel
            }
          ]
        },
        fitView: true
      });
    },
    // 自动调整画布尺寸
    adjustCanvasSize() {
      const bbox = this.graph.getGroup().getCanvasBBox();
      const padding = 16;

      // 计算所需画布尺寸
      const container = document.getElementById("container");
      const canvasWidth = Math.max(
        container.clientWidth - 12,
        bbox.maxX - bbox.minX + padding * 2
      );
      const canvasHeight = Math.max(
        container.clientHeight - 12,
        bbox.maxY - bbox.minY + padding * 2
      );

      // 更新占位元素尺寸
      const placeholder = document.getElementById("placeholder");
      placeholder.style.height = canvasHeight + "px";
      placeholder.style.width = canvasWidth + "px";

      // 监听滚动事件
      const mountDiv = document.getElementById("container-wrapper");
      mountDiv.addEventListener("scroll", () => {
        const scrollLeft = mountDiv.scrollLeft;
        const scrollTop = mountDiv.scrollTop;
        this.graph.translate(
          -scrollLeft + this.lastPosition[0],
          -scrollTop + this.lastPosition[1]
        );

        this.lastPosition = [scrollLeft, scrollTop];
      });

      // 滚动条居中
      mountDiv.scrollLeft = (canvasWidth - mountDiv.clientWidth) / 2;
      mountDiv.scrollTop = (canvasHeight - mountDiv.clientHeight) / 2;
      this.lastPosition = [mountDiv.scrollLeft, mountDiv.scrollTop];
    },
    formatLabel(params) {
      return params.location + "<br />" + params.label;
    },
    // 获取图表数据
    getChartData() {
      let data = { nodes: [], edges: [] };
      if (!_.isEmpty(this.inputData_in)) {
        data.nodes = this.inputData_in.nodes;
        data.edges = this.inputData_in.edges;
      }
      return data;
    },
    // 绘制图形
    paintChart(selectId = -1) {
      const data = this.getChartData();
      if (this._.isEmpty(this.graph)) this.initGraphConf();
      this.graph.data(data);
      this.graph.render();
      this.deleteTooltip();
      this.adjustCanvasSize();
      if (selectId !== -1) {
        this.handlerFocusNode(selectId);
      }
    },
    deleteTooltip() {
      //删除占位的tooltip
      const tooltip = document.getElementsByClassName("g6-node-tooltip");
      for (let i = 0; i < tooltip.length; i++) {
        tooltip[i].remove();
      }
    },
    setData() {
      const data = this.getChartData();
      this.$nextTick(() => {
        if (this._.isEmpty(this.graph)) this.initGraphConf();
        this.graph.changeData(data);
        this.graph.layout();
      });
    },
    // 超长文本显示
    fittingString(str, maxWidth, fontSize) {
      const ellipsis = "...";
      const ellipsisLength = G6.Util.getTextSize(ellipsis, fontSize)[0];
      let currentWidth = 0;
      let res = str;
      const pattern = new RegExp("[\u4E00-\u9FA5]+"); // distinguish the Chinese charactors and letters
      if (!str) return;
      str.split("").forEach((letter, i) => {
        if (currentWidth > maxWidth - ellipsisLength) return;
        if (pattern.test(letter)) {
          // Chinese charactors
          currentWidth += fontSize;
        } else {
          // get the width of single letter according to the fontSize
          currentWidth += G6.Util.getLetterWidth(letter, fontSize);
        }
        if (currentWidth > maxWidth - ellipsisLength) {
          res = `${str.substr(0, i)}${ellipsis}`;
        }
      });
      return res;
    }
  },
  mounted() {
    const _this = this;
    const currentTheme = localStorage.getItem("omega_theme");
    let textColor = "";
    let textColorT3 = "";
    let bgColor = "";
    if (["dark", "blue"].includes(currentTheme)) {
      textColor = "#f0f1f2";
      textColorT3 = "#9096a8";
      bgColor = "#0e1b47";
    } else {
      textColor = " #242424";
      textColorT3 = "#666666";
      bgColor = " #ffffff";
    }
    G6.registerEdge(
      "line-arrow",
      {
        getPath(points) {
          const startPoint = points[0];
          const endPoint = points[1];
          return [
            ["M", startPoint.x, startPoint.y],
            ["L", startPoint.x + 40, startPoint.y],
            ["L", startPoint.x + 40, endPoint.y],
            ["L", endPoint.x, endPoint.y]
          ];
        }
      },
      "line"
    );
    G6.registerNode("rect-jsx", {
      jsx: cfg => `
          <group>
            <circle style={{
              stroke: '#2D89AE',
              lineWidth: 2,
              height: 100,
              lineDash: ${cfg.virtual ? "[4,2]" : "[4,0]"},
              r: 35,
              marginTop: 70,
              fill:  ${
                cfg.virtual
                  ? "#989898"
                  : cfg.communicationStatus === 1 ||
                    cfg.communicationStatus === 0
                  ? "#06C25D"
                  : "#EB3C3E"
              }
            }} name="circle-item">
                <text style={{ marginTop: -18, fill: '#fff',textAlign: 'center', fontWeight: 'bold',fontSize: '12',}}>{{codename}}</text>
                <rect style={{
                  width: 68,
                  height: 1,
                  marginLeft: -34,
                  marginTop: -16,
                  stroke: '#ffffff',
                  fill: '#ffffff',
                }}>
                </rect>
                <text style={{ marginTop: -15,fill: '#fff',textAlign: 'center',fontWeight: 'bold',}}>{{accuracylevel}}</text>
            </circle>
            <rect style={{
                width: 10,
                height: 1,
                marginLeft: 37,
                marginTop: 0,
                stroke: '#F3E38C',
                fill: '#F3E38C',
              }}>
            </rect>

            <rect style={{
              width: 200,
              height: 40,
              marginTop: -20,
              marginLeft: 48,
              stroke: '#4B7AB1',
              fill: ${bgColor},
            }} name="rect-item">
             <text style={{ marginLeft: 70, marginTop: 3, fill: ${textColorT3}, }} name="position">{{${_this.fittingString(
        cfg.location,
        200,
        14
      )}}}</text>
              <text style={{ marginLeft: 70, marginTop: 3, fill: ${textColor}, }}>{{${_this.fittingString(
        cfg.label,
        200,
        14
      )}}}</text>
            </rect>
          </group>`,
      setState(name, value, item) {
        const group = item.getContainer();
        const circleShape = group.find(e => e.get("name") === "circle-item");
        const rectShape = group.find(e => e.get("name") === "rect-item");
        const positionShape = group.find(e => e.get("name") === "position");
        if (name === "choose" && value) {
          circleShape.attr({
            stroke: "#9AD2FF",
            fill: "#9AD2FF",
            shadowColor: "#0C82F8",
            shadowBlur: 20
          });
          rectShape.attr({
            stroke: "#0c82f8",
            strokeOpacity: 0.5,
            fill: "#0C82F8",
            fillOpacity: 0.4
          });
          positionShape.attr({
            fill: textColor
          });
        } else if (name === "choose" && !value) {
          circleShape.attr({
            stroke: "#2D89AE",
            fill: item._cfg.model.virtual
              ? "#989898"
              : item._cfg.model.communicationStatus === 1 ||
                item._cfg.model.communicationStatus === 0
              ? "#06C25D"
              : "#EB3C3E",
            shadowColor: "",
            shadowBlur: 0
          });
          rectShape.attr({
            stroke: "#4B7AB1",
            strokeOpacity: 1,
            fill: bgColor,
            fillOpacity: 1
          });
          positionShape.attr({
            fill: textColorT3
          });
        }
      },
      getAnchorPoints: function getAnchorPoints() {
        return [
          [0, 0.5],
          [1, 0.5]
        ];
      }
    });
  },
  activated() {},
  deactivated() {},
  beforeDestroy() {}
};
</script>

<style lang="scss" scoped>
#container {
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  position: absolute;
  left: 0;
  top: 0;
  z-index: 999;
}
</style>
<style lang="scss">
.g6-tooltip {
  white-space: nowrap;
  transition: left 0.4s cubic-bezier(0.23, 1, 0.32, 1) 0s,
    top 0.4s cubic-bezier(0.23, 1, 0.32, 1) 0s;
  @include background_color(BG11);
  font-size: 14px;
  border-radius: 4px;
  @include font_color(T1);
  padding: 10px;
  // min-width: 300px;
  // min-height: 200px;
  border: 1px solid;
  @include border_color(B2);
  pointer-events: none;
}
</style>
