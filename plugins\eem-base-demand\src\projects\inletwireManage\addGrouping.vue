<template>
  <!-- 1弹窗组件 -->
  <CetDialog
    v-bind="CetDialog_1"
    v-on="CetDialog_1.event"
    class="min el_dialog"
  >
    <CetForm
      :data.sync="CetForm_1.data"
      v-bind="CetForm_1"
      v-on="CetForm_1.event"
      label-position="top"
    >
      <el-form-item :label="$T('账户组名称')" prop="name">
        <ElInput
          v-model.trim="CetForm_1.data.name"
          v-bind="ElInput_1"
          v-on="ElInput_1.event"
        ></ElInput>
      </el-form-item>
    </CetForm>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        class="ml-J1"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";

export default {
  name: "addGrouping",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },

  computed: {},

  data() {
    return {
      nameOld: "",
      CetDialog_1: {
        title: $T("新建/编辑 分组"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        width: "480px",
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {},
        event: {}
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: ["name"], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入账户组名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      vm.CetForm_1.resetTrigger_in = new Date().getTime();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    inputData_in(val) {
      this.nameOld = "";
      const { fatherId, fatherModelLabel, name, id } = val;
      if (val.id) {
        this.CetDialog_1.title = $T("编辑分组");
        this.CetForm_1.data = { fatherId, fatherModelLabel, name, id };
        this.nameOld = name;
      } else {
        this.CetDialog_1.title = $T("新建分组");
        this.CetForm_1.data = { fatherId, fatherModelLabel, name: "" };
      }
    }
  },

  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    async CetForm_1_saveData_out(val) {
      let data = null;
      if (val?.id) {
        //编辑
        data = {
          id: val.id,
          name: val.name,
          rootNodeId: this.inputData_in.fatherId,
          rootNodeLabel: this.inputData_in.fatherModelLabel
        };
      } else {
        data = {
          name: val.name,
          rootNodeId: this.inputData_in.fatherId,
          rootNodeLabel: this.inputData_in.fatherModelLabel
        };
      }
      if (data.name == this.nameOld) {
        this.$message({
          message: $T("分组名称与新建一致"),
          type: "warning"
        });
        return;
      }
      const response = await customApi.demandGroupUpsert(data);
      if (response.code === 0) {
        this.$message({
          message: $T("保存成功"),
          type: "success"
        });
        this.CetDialog_1.closeTrigger_in = new Date().getTime();
        this.$emit("finishTrigger_out");
      }
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.el_dialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
    border-radius: var(--C1);
  }
}
</style>
