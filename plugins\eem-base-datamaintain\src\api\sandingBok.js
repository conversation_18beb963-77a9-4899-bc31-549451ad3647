import fetch from "eem-base/utils/fetch";
import { getCustomParameter } from "./common.js";
const version = "v1";

// 获取点检到期/超期统计
export function overdueCheck(projectId) {
  return fetch({
    url: `/eem-service/${version}/dashboard/overdueCheck/${projectId}`,
    method: "GET"
  });
}
// 设备台账
// 新增\修改设备台账
export function editDashboard(data) {
  return fetch({
    url: `/eem-service/${version}/dashboard/edit/${data[0].projectid}`,
    method: "POST",
    data: data
  });
}
// 删除设备台账
export function deleteDashboard(data) {
  return fetch({
    url: `/eem-service/${version}/dashboard/delete`,
    method: "DELETE",
    data: data
  });
}
// 单独处理（1）测量对象树节点转换 （2）周期参数
function processExpressions(data) {
  const { expressions = [] } = data;
  const updatedExpressions = expressions.map(item => {
    if (item.prop === "verificationcycle") {
      return { ...item, operator: "EQ" };
    }
    if (item.prop === "metertype") {
      return {
        ...item,
        limit: item?.limit ? [...new Set(item.limit.flat().map(String))] : [],
        operator: "IN"
      };
    }
    return item;
  });

  // 提取 deviceid 对应的节点数据
  const deviceidItem = expressions.find(item => item.prop === "deviceid");
  const newNodes = (deviceidItem?.limit || []).reduce((acc, subArr) => {
    if (!Array.isArray(subArr) || !subArr.length) return acc;

    const lastItem = subArr[subArr.length - 1];
    const idStr = lastItem?.split("_")?.[1];
    const id = Number(idStr);

    if (!isNaN(id)) {
      acc.push({ id, modelLabel: "device" });
    }

    return acc;
  }, []);

  // 处理 monitorNodeList 字段
  const monitorNodeListItem = expressions.find(
    item => item.prop === "monitorNodeList"
  );
  if (monitorNodeListItem?.limit?.length) {
    data.monitorNodeList = monitorNodeListItem.limit.map(i => ({
      id: i.id,
      modelLabel: i.modelLabel
    }));
  }
  // 处理 nodeList 的不同来源
  const outerNodeList = expressions.find(item => item.prop === "outerNodeList");
  const typeItem = expressions.find(item => item.prop === "type");

  const typeValue = typeItem?.limit;
  data.nodeList = [];

  if (typeValue === 1 || typeValue === 2) {
    data.nodeList = outerNodeList?.limit || [];
  } else if (typeValue === 3) {
    data.nodeList = newNodes;
  }
  // 过滤掉指定字段
  const excludeProps = [
    "monitorname",
    "monitorNodeList",
    "deviceid",
    "nodeList",
    "outerNodeList",
    "type"
  ];
  data.expressions = updatedExpressions.filter(
    item => !excludeProps.includes(item.prop)
  );
  return data;
}

// 获取设备台账表格数据
export function getDeviceTableDate(data) {
  let projectId, chooseOverdue;
  const expressions = _.get(data, "rootCondition.filter.expressions");
  if (!_.isEmpty(expressions)) {
    projectId = getCustomParameter(expressions, "projectId");
    chooseOverdue = getCustomParameter(expressions, "chooseOverdue");
  }
  let queryData = {
    expressions: expressions,
    page: data.rootCondition.page,
    projectid: projectId
  };
  if (chooseOverdue) {
    queryData.chooseOverdue = chooseOverdue;
  }
  return fetch({
    url: `/eem-service/${version}/dashboard/query`,
    method: "POST",
    data: processExpressions(queryData)
  });
}

// 台账导入前检查
export function importStandingBookCheck(data, projectId) {
  return fetch({
    url: `/eem-service/${version}/dashboard/importcheck/${projectId}`,
    method: "POST",
    data: data
  });
}
// 设备台账导入
export function importDeviceStandingBook(data, projectId) {
  return fetch({
    url: `/eem-service/${version}/dashboard/import/${projectId}/${true}`,
    method: "POST",
    data: data
  });
}

// 模板管理
// 获取设备台账模板

export function getDeviceTemplate(projectId) {
  return fetch({
    url: `/eem-service/${version}/dashboard/template/query/${projectId}`,
    method: "GET"
  });
}
// 编辑设备台账模板字段
export function editDeviceTemplate(data, projectId) {
  return fetch({
    url: `/eem-service/${version}/dashboard/template/edit/${projectId}`,
    method: "POST",
    data: data
  });
}
// 删除设备台账模板字段
export function deleteDeviceTemplate(data, bool) {
  return fetch({
    url: `/eem-service/${version}/dashboard/template/delete/${bool}`,
    headers: { hideNotice: bool },
    method: "DELETE",
    data: data
  });
}
// 导入系统预制模板字段
export function dashboardImportFixedTemplate(projectId) {
  return fetch({
    url: `/eem-service/${version}/dashboard/importFixedTemplate/${projectId}`,
    method: "POST"
  });
}

// 检定记录
// 检定记录导入
export function importVerificationRecord(data, projectId) {
  return fetch({
    url: `/eem-service/${version}/dashboard/record/import/${projectId}`,
    method: "POST",
    data: data
  });
}
// 获取检定记录台账
export function getRecordTableDate(data) {
  let projectId, nodeList, chooseOverdue;
  const expressions = _.get(data, "rootCondition.filter.expressions");

  if (!_.isEmpty(expressions)) {
    projectId = getCustomParameter(expressions, "projectId");
    nodeList = getCustomParameter(expressions, "nodeList");
    chooseOverdue = getCustomParameter(expressions, "chooseOverdue");
  }
  let queryData = {
    expressions: expressions,
    page: data.rootCondition.page,
    projectid: projectId,
    nodeList
  };
  if (chooseOverdue) {
    queryData.chooseOverdue = chooseOverdue;
  }
  return fetch({
    url: `/eem-service/${version}/dashboard/record/query`,
    method: "POST",
    data: processExpressions(queryData)
  });
}
// 新增\修改检定记录
export function editRecord(data) {
  return fetch({
    url: `/eem-service/${version}/dashboard/record/edit`,
    method: "POST",
    data: data
  });
}
// 删除检定记录
export function deleteRecord(data) {
  return fetch({
    url: `/eem-service/${version}/dashboard/record/delete`,
    method: "DELETE",
    data: data
  });
}
// 根据管网设备新增仪表
export function createPilpeline(data) {
  return fetch({
    url: `/eem-service/${version}/dashboard/pipelineEquipment/create`,
    method: "POST",
    data: data
  });
}
// 删除管网没信息的仪表
export function deletePilpeline() {
  return fetch({
    url: `/eem-service/${version}/dashboard/pipelineEquipment/delete`,
    method: "DELETE"
  });
}
//查询节点树
export function getNodeList(params) {
  return fetch({
    url: `/eem-service/${version}/node/nodeTree/simple`,
    method: "POST",
    data: params
  });
}
