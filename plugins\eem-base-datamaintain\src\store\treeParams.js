import POWER_DEVICE from "./powerdevice.js";
import ELECTRICAL_DEVICE from "./electricaldevice.js";

const TREE_PARAMS = {
  //所有节点列表，包括管理层级和管网层级
  allManagement: [
    { modelLabel: "sectionarea" },
    { modelLabel: "building" },
    { modelLabel: "floor" },
    {
      modelLabel: "room"
    },
    { modelLabel: "manuequipment" },
    { modelLabel: "civicpipe" },
    { modelLabel: "pipeline" },
    { modelLabel: "linesegment" },
    ...ELECTRICAL_DEVICE.map(item => {
      if (item.value === "linesegmentwithswitch") {
        return {
          modelLabel: item.value,
          depth: 2
        };
      } else {
        return {
          modelLabel: item.value
        };
      }
    }),
    ...POWER_DEVICE.map(item => {
      return {
        modelLabel: item.value
      };
    })
  ],
  // 纯管理层级【能耗数据、管理层级趋势曲线】
  management: [
    { modelLabel: "sectionarea" },
    { modelLabel: "building" },
    { modelLabel: "floor" },
    {
      filter: {
        expressions: [{ limit: null, operator: "EQ", prop: "roomtype" }]
      },
      modelLabel: "room"
    },
    { modelLabel: "manuequipment" }
  ],
  timesharingConfig: [
    { modelLabel: "sectionarea" },
    { modelLabel: "building" },
    { modelLabel: "floor" },
    {
      filter: {
        expressions: [{ limit: null, operator: "EQ", prop: "roomtype" }]
      },
      modelLabel: "room"
    },
    { modelLabel: "manuequipment" },
    { modelLabel: "airconditioner" },
    { modelLabel: "civicpipe" },
    { modelLabel: "virtualbuildingnode" },
    { modelLabel: "virtualdevicenode" }
  ],
  eventCallPoliceConfigAuto: [
    { modelLabel: "sectionarea" },
    { modelLabel: "building" },
    { modelLabel: "floor" },
    {
      filter: {
        composemethod: true,
        expressions: [
          {
            limit: null,
            operator: "EQ",
            prop: "roomtype",
            tagid: 2
          }
        ]
      },
      modelLabel: "room"
    },
    { modelLabel: "manuequipment" },
    { modelLabel: "airconditioner" }
  ],
  // 管网对象【管网趋势曲线】
  managementNetwork: [
    {
      modelLabel: "room",
      filter: {
        expressions: [
          {
            limit: null,
            operator: "NE",
            prop: "roomtype"
          }
        ]
      }
    },
    { modelLabel: "civicpipe" },
    { modelLabel: "pipeline" },
    { modelLabel: "linesegment" },
    ...ELECTRICAL_DEVICE.map(item => {
      if (item.value === "linesegmentwithswitch") {
        return {
          modelLabel: item.value,
          depth: 2
        };
      } else {
        return {
          modelLabel: item.value
        };
      }
    }),
    ...POWER_DEVICE.map(item => {
      return {
        modelLabel: item.value
      };
    })
  ],
  // 项目管理层级节点树
  projectConfig: [
    { modelLabel: "sectionarea" },
    { modelLabel: "civicpipe" },
    { modelLabel: "building" },
    { modelLabel: "floor" },
    {
      modelLabel: "room",
      filter: {
        composemethod: true,
        expressions: [
          {
            limit: [3, 4],
            operator: "IN",
            prop: "roomtype",
            tagid: 1
          },
          {
            limit: null,
            operator: "EQ",
            prop: "roomtype",
            tagid: 2
          }
        ]
      }
    },
    { modelLabel: "manuequipment" },
    { modelLabel: "coldwatermainengine" },
    { modelLabel: "windset" },
    { modelLabel: "coolingtower" },
    { modelLabel: "plateheatexchanger" },
    { modelLabel: "plc" },
    { modelLabel: "pump" },
    { modelLabel: "aircompressor" },
    { modelLabel: "colddryingmachine" },
    { modelLabel: "dryingmachine" },
    { modelLabel: "meteorologicalmonitor" },
    { modelLabel: "airconditioner" }
  ],
  // 项目管网层级节点树
  projectConfigNetwork: [
    {
      modelLabel: "room",
      filter: {
        expressions: [
          {
            limit: [1, 2, 5, 6, 7, 8, 9],
            operator: "IN",
            prop: "roomtype"
          }
        ]
      }
    },
    { modelLabel: "pipeline" },
    { modelLabel: "pump" },
    { modelLabel: "plateheatexchanger" },
    { modelLabel: "colddryingmachine" },
    { modelLabel: "dryingmachine" },
    { modelLabel: "boiler" },
    { modelLabel: "linesegment" },
    ...ELECTRICAL_DEVICE.map(item => {
      if (item.value === "linesegmentwithswitch") {
        return {
          modelLabel: item.value,
          depth: 2
        };
      } else {
        return {
          modelLabel: item.value
        };
      }
    })
  ],
  // 包含普通房间、空调机房、空压机房、锅炉房的树【能耗、能效超标报警、分摊方案配置】
  powerEquipment: [
    { modelLabel: "sectionarea" },
    { modelLabel: "building" },
    { modelLabel: "floor" },
    {
      filter: {
        composemethod: true,
        expressions: [
          {
            limit: null,
            operator: "EQ",
            prop: "roomtype",
            tagid: 2
          }
        ]
      },
      modelLabel: "room"
    },
    { modelLabel: "manuequipment" },
    { modelLabel: "airconditioner" }
  ],
  // 单位成本分析、综合成本分析、用电成本分析、能效实绩与对标、单位成本分析
  autoManagement: [
    { modelLabel: "sectionarea" },
    { modelLabel: "building" },
    { modelLabel: "floor" },
    {
      filter: {
        composemethod: true,
        expressions: [
          {
            limit: null,
            operator: "EQ",
            prop: "roomtype",
            tagid: 1
          }
        ]
      },
      modelLabel: "room"
    },
    { modelLabel: "manuequipment" },
    { modelLabel: "airconditioner" }
  ],
  energyQueryAndAnalysis: [
    { modelLabel: "sectionarea" },
    { modelLabel: "building" },
    { modelLabel: "floor" },
    {
      filter: {
        composemethod: true,
        expressions: [
          {
            limit: null,
            operator: "EQ",
            prop: "roomtype",
            tagid: 1
          }
        ]
      },
      modelLabel: "room"
    },
    { modelLabel: "manuequipment" },
    { modelLabel: "airconditioner" },
    { modelLabel: "virtualbuildingnode" },
    { modelLabel: "virtualdevicenode" }
  ],
  // 能耗数据录入
  energyEntry: [
    { modelLabel: "sectionarea" },
    { modelLabel: "building" },
    { modelLabel: "floor" },
    {
      filter: {
        composemethod: true,
        expressions: [
          {
            limit: [3, 4, 5],
            operator: "IN",
            prop: "roomtype",
            tagid: 1
          },
          {
            limit: null,
            operator: "EQ",
            prop: "roomtype",
            tagid: 2
          }
        ]
      },
      modelLabel: "room"
    },
    { modelLabel: "manuequipment" },
    ...POWER_DEVICE.map(item => {
      return {
        modelLabel: item.value
      };
    })
  ],
  // 告警实时诊断、事件明细查询
  distributionEquipment: [
    {
      modelLabel: "room",
      depth: 1,
      filter: {
        expressions: [
          {
            limit: null,
            operator: "NE",
            prop: "roomtype"
          }
        ]
      }
    },
    { modelLabel: "civicpipe", depth: 2 },
    { modelLabel: "pipeline", depth: 2 },
    { modelLabel: "linesegment", depth: 3 },
    ...ELECTRICAL_DEVICE.map(item => {
      if (item.value === "linesegmentwithswitch") {
        return {
          modelLabel: item.value,
          depth: 2
        };
      } else {
        return {
          modelLabel: item.value
        };
      }
    })
  ],
  // 仪表台账信息-测量对象关联
  sandingBok: [
    {
      modelLabel: "room",
      filter: {
        composemethod: false,
        depth: 1,
        expressions: [
          {
            limit: null,
            operator: "NE",
            prop: "roomtype"
          }
        ]
      }
    },
    { modelLabel: "civicpipe", depth: 2 },
    { modelLabel: "pipeline", depth: 2 },
    { modelLabel: "pump", depth: 2 },
    { modelLabel: "coolingtower", depth: 2 },
    { modelLabel: "windset", depth: 2 },
    { modelLabel: "coldwatermainengine", depth: 2 },
    { modelLabel: "aircompressor", depth: 2 },
    { modelLabel: "colddryingmachine", depth: 2 },
    { modelLabel: "dryingmachine", depth: 2 },
    { modelLabel: "boiler", depth: 2 },
    { modelLabel: "steamboiler", depth: 2 },
    { modelLabel: "linesegment", depth: 3 },
    ...ELECTRICAL_DEVICE.map(item => {
      return {
        modelLabel: item.value,
        depth: 2
      };
    })
  ],
  // 采集设备关联节点树
  gatherRelation: [
    { modelLabel: "sectionarea" },
    { modelLabel: "building" },
    { modelLabel: "floor" },
    {
      filter: {
        composemethod: true,
        expressions: [
          {
            limit: [1, 3, 4, 5, 6],
            operator: "IN",
            prop: "roomtype",
            tagid: 1
          },
          {
            limit: null,
            operator: "EQ",
            prop: "roomtype",
            tagid: 2
          }
        ]
      },
      modelLabel: "room"
    },
    { modelLabel: "pipeline" },
    {
      filter: null,
      modelLabel: "linesegment",
      props: []
    },
    ...ELECTRICAL_DEVICE.map(item => {
      if (item.value === "linesegmentwithswitch") {
        return {
          modelLabel: item.value,
          depth: 2
        };
      } else {
        return {
          modelLabel: item.value
        };
      }
    }),
    ...POWER_DEVICE.map(item => {
      return {
        modelLabel: item.value
      };
    })
  ],
  // 关联管网设备节点树
  pipeRelation: [
    { modelLabel: "sectionarea" },
    { modelLabel: "civicpipe" },
    { modelLabel: "building" },
    { modelLabel: "floor" },
    {
      filter: {
        composemethod: true,
        expressions: [
          {
            limit: [3, 4, 5],
            operator: "IN",
            prop: "roomtype",
            tagid: 1
          },
          {
            limit: null,
            operator: "EQ",
            prop: "roomtype",
            tagid: 2
          }
        ]
      },
      modelLabel: "room"
    },
    { modelLabel: "manuequipment" },
    ...POWER_DEVICE.map(item => {
      return {
        modelLabel: item.value
      };
    })
  ],
  // 核算方案-关联对象
  relatedObj: [
    { modelLabel: "sectionarea" },
    { modelLabel: "building" },
    { modelLabel: "floor" },
    {
      filter: {
        composemethod: true,
        expressions: [
          {
            limit: null,
            operator: "EQ",
            prop: "roomtype",
            tagid: 2
          }
        ]
      },
      modelLabel: "room"
    },
    { modelLabel: "manuequipment" },
    { modelLabel: "airconditioner" },
    { modelLabel: "civicpipe" }
  ],
  //成本分析（综合概览、占比分析、变化趋势、排名分析左侧节点树）、成本核算
  costanalysisTree: [
    { modelLabel: "sectionarea" },
    {
      modelLabel: "building"
    },
    {
      modelLabel: "floor"
    },
    {
      modelLabel: "room",
      filter: {
        composemethod: false,
        expressions: [
          {
            limit: null,
            operator: "EQ",
            prop: "roomtype"
          }
        ]
      }
    }
  ],
  //成本分析-均摊对比节点树
  EqualShareComparisonTree: [
    { modelLabel: "sectionarea" },
    {
      modelLabel: "building"
    },
    {
      modelLabel: "floor"
    },
    {
      modelLabel: "room",
      filter: {
        composemethod: false,
        expressions: [
          {
            limit: null,
            operator: "EQ",
            prop: "roomtype"
          }
        ]
      }
    },
    {
      modelLabel: "manuequipment"
    }
  ],
  //用户管理页面-模型列表
  userManageModelLabels: [
    "project",
    "sectionarea",
    "building",
    "floor",
    "room"
  ],
  //用户管理页面-项目节点权限节点树
  userManageProjectTree: [
    { modelLabel: "sectionarea" },
    { modelLabel: "building" },
    { modelLabel: "floor" },
    { modelLabel: "room" }
  ],
  // 用能查询
  energyQuery: [
    { modelLabel: "sectionarea" },
    { modelLabel: "building" },
    { modelLabel: "floor" },
    {
      filter: {
        expressions: [{ limit: null, operator: "EQ", prop: "roomtype" }]
      },
      modelLabel: "room"
    },
    { modelLabel: "manuequipment" },
    { modelLabel: "airconditioner" },
    { modelLabel: "meteorologicalmonitor" }
  ],
  //维修工单、巡检工单、巡检计划页面-维修目标、巡检目标节点树；签到点管理-新增签到点-巡检对象节点树；
  inspectObjTree: [
    { modelLabel: "sectionarea" },
    { modelLabel: "building" },
    { modelLabel: "floor" },
    {
      modelLabel: "room"
    },
    { modelLabel: "manuequipment" },
    { modelLabel: "civicpipe" },
    { modelLabel: "pipeline" },
    { modelLabel: "linesegment" },
    ...ELECTRICAL_DEVICE.map(item => {
      if (item.value === "linesegmentwithswitch") {
        return {
          modelLabel: item.value,
          depth: 2
        };
      } else {
        return {
          modelLabel: item.value
        };
      }
    }),
    ...POWER_DEVICE.map(item => {
      return {
        modelLabel: item.value
      };
    })
  ],
  // 备件管理-同步设备弹框节点树
  synchronizeDialogTree: [
    {
      modelLabel: "room",
      filter: {
        expressions: [
          {
            limit: null,
            operator: "NE",
            prop: "roomtype"
          }
        ]
      }
    },
    { modelLabel: "linesegment" },
    ...ELECTRICAL_DEVICE.map(item => {
      return {
        modelLabel: item.value
      };
    }),
    ...POWER_DEVICE.map(item => {
      return {
        modelLabel: item.value
      };
    })
  ]
};
export default TREE_PARAMS;
