import fetch from "eem-base/utils/fetch";

const version = "v1";

// 查询损耗分摊方案数据
export function getLossShareConfigGroup(data) {
  return fetch({
    url: `/eembaseloss/eem-base/loss/v1/lossShareConfig/group`,
    method: "POST",
    data
  });
}

// 点击操作一栏查询具体方案详情数据
export function getLossShareConfigInfo(id) {
  return fetch({
    url: `/eembaseloss/eem-base/loss/v1/lossShareConfig/info?id=${id}`,
    method: "POST"
  });
}

// 删除损耗分摊方案列表数据
export function deleteLossShareConfig(data, params) {
  return fetch({
    url: `/eembaseloss/eem-base/loss/v1/lossShareConfig/delBatch`,
    method: "DELETE",
    data,
    params
  });
}

// 新增或编辑损耗分摊方案前调用的校验功能
export function checkLossShareConfigSchemeConfig(data) {
  return fetch({
    url: `/eembaseloss/eem-base/loss/v1/lossShareConfig/saveScheme/check`,
    method: "POST",
    data
  });
}

// 新增或编辑保存方案
export function saveLossShareConfigScheme(data) {
  return fetch({
    url: `/eembaseloss/eem-base/loss/v1/lossShareConfig/saveScheme`,
    method: "PUT",
    data
  });
}

// 根据父节点查询下一层子节点的逻辑
export function getLossShareConfigTopologyChild(data) {
  return fetch({
    url: `/eembaseloss/eem-base/loss/v1/lossShareConfig/topologyChild`,
    method: "POST",
    data,
    params: {
      energyType: data.energyType
    }
  });
}

//  获取当前项目重算状态 - 未实现
export function getEnergyReCalcState(projectId) {
  return fetch({
    url: `/eem-service/${version}/system/data/energy/re-calc/state/${projectId}`,
    method: "POST"
  });
}

//  flink执行能耗重算 - 未实现
export function getEnergyReCalc(data) {
  return fetch({
    url: `/eem-service/${version}/system/data/energy/re-calc`,
    method: "POST",
    timeout: 600000,
    data
  });
}
