<template>
  <el-container
    class="fullheight eem-container flex-col flex bg-BG1 rounded-Ra p-J4"
  >
    <el-header height="auto" class="p-0 mb-J3">
      <HandleHeader
        v-bind="headerConfig"
        :standardEnergyTypes="standardEnergyTypes"
        @handleHeader="clickHeader"
        @getEnergyTypeList="getEnergyTypeList"
      ></HandleHeader>
    </el-header>
    <el-main class="flex-auto p-0" style="overflow: auto">
      <FlowAnalysis v-if="showFlowAnalysis" v-bind="flowAnalysisConfig" />
      <SankeyChart v-else-if="showSankeyChart" v-bind="sankeyChartConfig" />
      <div v-else style="height: 100%; width: 100%">
        <div class="noTopology">
          <div class="fl text">
            {{ noChartText }}
          </div>
          <img class="fr img" src="./assets/u1104.png" alt="" />
        </div>
      </div>
    </el-main>
  </el-container>
</template>

<script>
import HandleHeader from "./handleHeader";
import FlowAnalysis from "./flowAnalysis";
import SankeyChart from "./sankeyChart";
import customApi from "@/api/custom";

export default {
  name: "energyFlowAnalysi",
  components: {
    HandleHeader,
    FlowAnalysis,
    SankeyChart
  },
  computed: {
    noChartText() {
      if (this.gotoTopology) {
        return $T("无能耗数据仅显示拓扑图");
      }
      if (this.chartType === 2) {
        return $T("暂无拓扑图");
      }
      return $T("暂无能流图");
    }
  },
  data(vm) {
    return {
      chartType: 1, // 图表类型
      gotoTopology: false, // 引导查看拓扑图
      showSankeyChart: true, // 综合能源桑基图
      showFlowAnalysis: false, // 其余能源类型
      headerConfig: {},
      flowAnalysisConfig: {
        inputData_in: null,
        imageName: "",
        params: null
      },
      sankeyChartConfig: {
        inputData_in: null,
        imageName: "",
        isTotalEnergyType: true,
        energyTypeList: [],
        params: null
      },
      standardEnergyTypes: []
    };
  },
  watch: {},

  methods: {
    async getStandardEnergyTypes() {
      const res = await customApi.queryStandardEnergyTypes();
      if (res.code === 0) {
        this.standardEnergyTypes = res.data || [];
      }
      this.standardEnergyTypes.reverse();
    },
    //头部条件改变触发
    clickHeader(val) {
      if (!val) return;
      const flag = this.standardEnergyTypes.includes(val.energyType); // 综合能耗和碳能耗
      this.showSankeyChart = flag;
      this.showFlowAnalysis = !flag;

      this.chartType = val.type;
      if (val.type === 2) {
        this.showSankeyChart = false;
        this.showFlowAnalysis = true;
      } else {
        this.showSankeyChart = true;
        this.showFlowAnalysis = false;
      }
      const typeKey = {
        12: "yyyy-MM-dd",
        14: "yyyy-MM",
        17: "yyyy"
      };
      let format = typeKey[val.aggregationCycle] || "YYYY-MM-DD";
      const imageName = `${val.energyTypeName || "--"}_${this.$moment(
        val.time
      ).format(format)}`;
      this.flowAnalysisConfig.imageName = imageName;
      this.sankeyChartConfig.imageName = imageName;
      this.sankeyChartConfig.isTotalEnergyType = flag;
      this.gotoTopology = false;
      const { selectId, ...newVal } = val;
      const list = selectId?.split("_");
      newVal.searchNode = selectId
        ? {
            id: Number(list[1]),
            modelLabel: list[0]
          }
        : null;
      customApi.queryEnergyFlow(newVal).then(res => {
        if (res.code === 0) {
          const energyFlowNodeDataList =
            this._.get(res, "data.energyFlowNodeDataList", []) || [];
          const linkNodeList = this._.get(res, "data.linkNodeList", []) || [];
          if (!energyFlowNodeDataList.length || !linkNodeList.length) {
            this.showSankeyChart = false;
            this.showFlowAnalysis = false;
          }
          if (this.showSankeyChart) {
            //linkNodeList 里的值如果都是0，则展示 暂无损耗图
            let findData = linkNodeList.filter(
              item => ![0, null, undefined, NaN].includes(item.value)
            );
            if (findData.length === 0) {
              this.showSankeyChart = false;
              this.gotoTopology = true;
            }
          }
          const nodeId = selectId ? list[1] + "_" + list[0] : null;
          this.flowAnalysisConfig.params = newVal;
          this.sankeyChartConfig.params = newVal;
          this.flowAnalysisConfig.inputData_in = {
            ...res.data,
            selectId: nodeId
          };
          this.sankeyChartConfig.inputData_in = {
            ...res.data,
            selectId: nodeId
          };
        }
      });
    },
    getEnergyTypeList(val) {
      this.sankeyChartConfig.energyTypeList = val;
    },
    init() {
      this.showSankeyChart = false;
      this.showFlowAnalysis = false;
    }
  },
  mounted() {
    this.getStandardEnergyTypes();
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.noTopology {
  width: 725px;
  height: 315px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  .img {
    height: 315px;
  }
  .text {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: #2e95ff;
    font-size: 32px;
    font-weight: 700;
  }
}
.energyType {
  display: inline-block;
}
.title.text {
  font-size: 24px;
  margin-left: 10px;
  vertical-align: middle;
}
</style>
