<template>
  <div class="page">
    <div
      class="cont"
      :style="{ height: isExpand ? 'calc(100% - 338px)' : 'calc(100% - 85px)' }"
    >
      <div class="h-[32px]">
        <div class="fl lh32 fs16">
          {{ energyTypeObj && energyTypeObj.name }}{{ en ? " " : ""
          }}{{ $T("计量网络图") }}
        </div>
        <CetButton
          class="fr"
          v-bind="CetButton_import"
          v-on="CetButton_import.event"
        ></CetButton>
        <CetButton
          class="fr mr-J3"
          v-bind="CetButton_All"
          v-on="CetButton_All.event"
        ></CetButton>
        <CetButton
          class="fr mr-J3"
          v-bind="CetButton_next"
          v-on="CetButton_next.event"
        ></CetButton>
        <customElSelect
          v-model="ElSelect_energyType.value"
          v-bind="ElSelect_energyType"
          v-on="ElSelect_energyType.event"
          class="fr mr-J3"
          :prefix_in="$T('能源类型')"
        >
          <ElOption
            v-for="item in ElOption_energyType.options_in"
            :key="item[ElOption_energyType.key]"
            :label="item[ElOption_energyType.label]"
            :value="item[ElOption_energyType.value]"
            :disabled="item[ElOption_energyType.disabled]"
          ></ElOption>
        </customElSelect>
        <el-autocomplete
          ref="autocomplete"
          popper-class="tree-popper"
          v-model="filterValue"
          suffix-icon="el-icon-search"
          :fetch-suggestions="querySearch"
          :placeholder="$T('请输入节点或安装地点名称进行搜索')"
          clearable
          @select="handleSelect"
          @clear="handleClear"
          class="w-[320px] fr mr-J3"
        >
          <template slot-scope="{ item }">
            <div class="tree-wrapper">
              <el-tree
                ref="selectTree"
                :data="filterTreeData"
                :props="treeProps"
                :filter-node-method="filterNode"
                node-key="tree_id"
                highlight-current
                @node-click="handleNodeClick"
              />
            </div>
          </template>
        </el-autocomplete>
        <div
          class="text-sm fr state"
          v-for="(item, index) in stateList"
          :key="index"
        >
          <div :style="{ 'background-color': item.color }" class="dot"></div>
          <div>{{ $T(item.text) }}</div>
        </div>
      </div>
      <meterInfo :energyType="ElSelect_energyType.value" />
      <measureTopologyChart
        v-if="showTopology"
        v-bind="measureTopologyChartConfig"
      />
    </div>
    <div class="mt-J3 cont">
      <div v-show="isExpand" class="fs16 mb-J3">{{ $T("设备详情") }}</div>
      <div v-show="isExpand" class="h-[200px] mb-J3">
        <el-table
          :data="tableData"
          :spanMethod="spanMethod"
          border
          height="100%"
        >
          <el-table-column
            v-for="item in columns"
            v-bind="item"
            :key="item.prop"
          />
        </el-table>
      </div>
      <div v-if="!isExpand" class="expand" @click="handleExpand(true)">
        {{ $T("展开设备详情") }}
        <i class="el-icon-arrow-down ml-J1" />
      </div>
      <div v-else class="expand" @click="handleExpand(false)">
        {{ $T("收起设备详情") }}
        <i class="el-icon-arrow-up ml-J1" />
      </div>
    </div>
    <UploadDialog
      v-bind="uploadDialog"
      v-on="uploadDialog.event"
    ></UploadDialog>
  </div>
</template>
<script>
import commonApi from "@/api/custom.js";
import common from "eem-base/utils/common.js";
import measureTopologyChart from "./measureTopologyChart.vue";
import UploadDialog from "eem-base/components/uploadDialog";
import meterInfo from "./components/meterInfo.vue";
import { col } from "./components/tableData.js";
export default {
  components: { measureTopologyChart, UploadDialog, meterInfo },
  data() {
    const language = window.localStorage.getItem("omega_language") === "en";
    return {
      filterValue: undefined,
      treeProps: {
        children: "children",
        label: "nodeName",
        id: "tree_id"
      },
      filterTreeData: [],
      selectNode: null,
      isExpand: false,
      stateList: [
        { text: "未关联", color: "#D8DAE1" },
        { text: "离线", color: "#FF5C5C" },
        { text: "在线", color: "#49BC79" }
      ],
      tableData: null,
      hierarchy: 2,
      showTopology: false,
      measureTopologyChartConfig: {
        inputData_in: null,
        resize: Date.now()
      },
      // energyType组件
      ElSelect_energyType: {
        value: "",
        style: {
          width: language ? "260px" : "200px"
        },
        event: {
          change: this.ElSelect_energyType_change_out
        }
      },
      // energyType组件
      ElOption_energyType: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      CetButton_import: {
        visible_in: true,
        disable_in: false,
        title: $T("导入"),
        type: "primary",
        size: "",
        primary: true,
        event: {
          statusTrigger_out: this.CetButton_import_statusTrigger_out
        }
      },
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: $T("加载下一层"),
        type: "primary",
        size: "",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      CetButton_All: {
        visible_in: true,
        disable_in: false,
        title: $T("加载全部"),
        type: "primary",
        size: "",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_All_statusTrigger_out
        }
      },
      columns: null,
      uploadDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        extensionNameList_in: [".xlsx", ".xls"],
        hideDownload: false,
        dialogTitle: $T("导入数据"),
        event: {
          download: this.uploadDialog_download,
          uploadFile: this.uploadDialog_uploadFile
        }
      }
    };
  },
  computed: {
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    },
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    energyTypeObj() {
      return this.ElOption_energyType.options_in.find(
        item => item.energytype == this.ElSelect_energyType.value
      );
    },
    mergeArr() {
      if (!this.tableData.length) {
        return [];
      }
      var wrap = this.tableData[0].codename;
      var arr = [];
      this.tableData.forEach((item, index) => {
        if (item.codename !== wrap) {
          wrap = item.codename;
          arr.push(index);
        }
      });
      return arr;
    } // 合并的行数下标
  },
  watch: {},
  methods: {
    handleClear() {
      if (!this.selectNode) {
        this.$refs.autocomplete.$refs.input.blur();
        return;
      }

      this.$refs.selectTree.setCurrentKey(null);
      const node = this.$refs.selectTree.getNode(this.selectNode);
      this.handleNodeClick(node.data);
    },
    //清空选中树节点
    clearSelectNode() {
      this.filterValue = undefined;
      if (!this.$refs.selectTree) return;
      this.$refs.selectTree.setCurrentKey(null);
      this.selectNode = null;
    },
    //获取下拉选项节点树
    async getNodeTree() {
      if (!this.ElSelect_energyType.value) {
        this.filterTreeData = [];
        return;
      }
      const data = {
        projectId: this.$store.state.projectId,
        energyType: this.ElSelect_energyType.value
      };
      const res = await commonApi.getNetWorkTree(data);
      if (res.code === 0 && !_.isEmpty(res.data)) {
        this.filterTreeData = res.data;
      } else {
        this.filterTreeData = [];
      }
      this.clearSelectNode();
    },
    //模糊搜索过滤
    querySearch(str, cb) {
      this.$refs.selectTree?.filter(str);
      cb([{}]);
    },
    //选中
    handleSelect(item) {},
    //下拉树选项过滤节点
    filterNode(value, data) {
      if (!value) return true;
      return (
        data.nodeName.toLowerCase().includes(value.toLowerCase()) ||
        data.location.toLowerCase().includes(value.toLowerCase())
      );
    },
    //点击树节点
    handleNodeClick(node) {
      //可点击取消选中
      if (this.selectNode && this.selectNode === node.tree_id) {
        this.$refs.selectTree.setCurrentKey(null);
        this.selectNode = null;
        this.filterValue = undefined;
        this.hierarchy = 2;
      } else {
        this.selectNode = node.tree_id;
        this.filterValue = node.nodeName;
        this.hierarchy = node.layer + 1;
      }
      this.$refs.selectTree?.filter(this.filterValue);
      this.getTopologyData(this.selectNode);
      this.getTableData(this.selectNode);
    },
    async getTableData(val = null) {
      const params = {
        projectId: this.projectId,
        energyType: this.ElSelect_energyType.value,
        index: this.hierarchy,
        treeId: val
      };
      const res = await commonApi.getMeasureTable(params);
      let resData = [];
      if (res.code === 0) {
        resData = res.data;
      }
      this.tableData = resData;
    },
    handleExpand(val) {
      this.isExpand = val;
      this.measureTopologyChartConfig.resize = Date.now();
    },
    init() {
      commonApi.getProjectEnergy().then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []).filter(
            item =>
              item.energytype !== 13 &&
              item.energytype !== 18 &&
              item.energytype !== 22
          );
          this.ElOption_energyType.options_in = data;
          this.ElSelect_energyType.value =
            this.ElOption_energyType.options_in[0]?.energytype;
          this.getNodeTree();
          this.getData();
        }
      });
    },
    reset() {
      this.$nextTick(() => {
        this.$refs.upload.clearFiles();
      });
    },
    getData() {
      this.hierarchy = 2;
      this.measureTopologyChartConfig.inputData_in = null;
      this.tableData = [];
      if (!this.ElSelect_energyType.value) {
        return;
      }
      this.getTopologyData();
      this.getTableData();
    },
    CetButton_next_statusTrigger_out(val) {
      // 加载全部后点击加载下一层时不恢复为两层，保持展示全部
      if (!this.hierarchy) {
        this.measureTopologyChartConfig.inputData_in = null;
      } else {
        this.hierarchy++;
      }
      this.getTopologyData(this.selectNode);
      this.getTableData(this.selectNode);
    },
    CetButton_All_statusTrigger_out(val) {
      this.hierarchy = 0;
      this.clearSelectNode();
      this.getTopologyData();
      this.getTableData();
    },
    getTopologyData(id = null) {
      commonApi
        .getMeasureTopology({
          energyType: this.ElSelect_energyType.value,
          projectId: this.projectId,
          index: this.hierarchy,
          treeId: id
        })
        .then(response => {
          if (response.code === 0) {
            var val = this._.get(response, "data", {});
            if (val && val.dataInfo && val.dataInfo.length > 0) {
              this.showTopology = true;
              const dataInfo = val.dataInfo.map(item => {
                var obj = {
                  id: item.name,
                  label: item.nodeName || "",
                  location: item.location,
                  communicationStatus: item.communicationStatus,
                  accuracylevel: " ",
                  codename: " "
                };
                // 有实际设备、表计才展示状态，其他归为虚拟表计
                if (
                  !item.deviceId ||
                  !item.instruments ||
                  item.instruments.length === 0
                ) {
                  if (item.instruments && item.instruments.length > 0) {
                    obj.codename = item.instruments[0].codename || "--";
                  }
                  obj.virtual = true;
                } else {
                  if (item.instruments && item.instruments.length > 0) {
                    obj.accuracylevel =
                      item.instruments[0].accuracylevel || "--";
                    obj.codename = item.instruments[0].codename || "--";
                  }
                }
                return obj;
              });
              const dataLink = val.dataLink || [];
              // if (!this.measureTopologyChartConfig.inputData_in || !this.measureTopologyChartConfig.inputData_in.nodes.length) {
              this.measureTopologyChartConfig.inputData_in = {
                nodes: dataInfo,
                edges: dataLink,
                selectId: id
              };
              // } else {
              //   this.measureTopologyChartConfig.inputData_in.nodes.push(...dataInfo);
              //   this.measureTopologyChartConfig.inputData_in.edges.push(...dataLink);
              // }
            } else if (this.hierarchy === 0 || this.hierarchy === 2) {
              this.showTopology = false;
            }
          } else {
            this.showTopology = false;
          }
        });
    },
    CetButton_import_statusTrigger_out(val) {
      this.uploadDialog.openTrigger_in = new Date().getTime();
    },
    uploadDialog_uploadFile(val) {
      const formData = new FormData();
      formData.append("file", val.file);
      var queryData = {
        projectId: this.projectId
      };
      commonApi
        .networkImportNodeAndConnection(formData, queryData)
        .then(response => {
          if (response.code === 0) {
            this.$message({
              type: "success",
              message: $T("导入成功！")
            });
            // 刷新
            this.uploadDialog.closeTrigger_in = Date.now();
            this.getData();
          }
        });
    },
    uploadDialog_download() {
      if (!this.ElSelect_energyType.value) {
        return;
      }

      common.downExcelGET(
        `/eem-service/v1/topology/network/exportNodeAndConnection/${this.projectId}`,
        {},
        this.token
      );
    },
    ElSelect_energyType_change_out(val) {
      this.getNodeTree();
      this.getData();
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 4 || columnIndex === 5) {
        if (!this.mergeArr.length) {
          return {
            rowspan: 1,
            colspan: 1
          };
        }
        if (!rowIndex) {
          return {
            rowspan: this.mergeArr[0],
            colspan: 1
          };
        }
        var index = this.mergeArr.indexOf(rowIndex);
        if (index !== -1) {
          if (this.mergeArr[index + 1]) {
            var indexLength = this.mergeArr[index + 1] - this.mergeArr[index];
            return {
              rowspan: indexLength,
              colspan: 1
            };
          } else {
            return {
              rowspan: this.tableData.length - this.mergeArr[index],
              colspan: 1
            };
          }
        }
        return {
          rowspan: 0,
          colspan: 0
        };
      }
    }
  },
  activated: function () {
    this.isExpand = false;
    this.filterValue = undefined;
    this.init();
  },
  mounted() {
    this.columns = col;
    this.isExpand = false;
    this.filterValue = undefined;
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.page {
  height: 100%;
  overflow: hidden;
}
.state {
  height: 32px;
  display: flex;
  align-items: center;
  margin-right: 16px;
  .dot {
    width: 8px;
    height: 8px;
    border-radius: 4px;
    margin-right: 8px;
  }
}
.cont {
  padding: 24px !important;
  overflow: hidden;
  box-sizing: border-box;
  background-color: var(--BG1);
}
.expand {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 16px;
  @include font_color(ZS);
  i {
    font-size: 16px;
  }
}
.Tooltip {
  position: absolute;
  height: 50px;
  line-height: 50px;
  z-index: 1;
  & > span {
    display: inline-block;
    padding-left: 40px;
    margin-right: 30px;
  }
  & > span:nth-child(1) {
    background: url("./assets/img1.png") no-repeat left center;
  }
  & > span:nth-child(2) {
    background: url("./assets/img2.png") no-repeat left center;
  }
  & > span:nth-child(3) {
    background: url("./assets/img3.png") no-repeat left center;
  }
}
:deep(.el-icon-circle-close) {
  color: var(--T3);
}
:deep(.el-icon-circle-close:hover) {
  color: var(--ZS);
}
</style>
<style lang="scss">
.tree-popper {
  li {
    padding: 0;
    overflow: visible;
  }
}
</style>
