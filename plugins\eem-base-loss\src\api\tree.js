import fetch from "eem-base/utils/fetch";
/**
 * 获取节点树
 */
export function getNodeTree(data) {
  return fetch({
    url: `/eembaseloss/eem-base/loss/v1/node/tree`,
    method: "POST",
    data
  });
}

/**
 * 查询根节点
 */
export function rootNode() {
  return fetch({
    url: `/eembaseloss/eem-base/loss/v1/node/root-node`,
    method: "GET"
  });
}

export function lossConfigProjectTreeWithoutgroup(data, params) {
  return fetch({
    url: `/eembaseloss/eem-base/loss/v1/config/tree/without/group`,
    method: "POST",
    data,
    params
  });
}
