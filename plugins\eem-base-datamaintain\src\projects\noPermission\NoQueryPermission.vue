<template>
  <el-empty
    class="no-permission bg1 brC"
    :image="imageData"
    :description="$T('您当前无数据访问或操作权限，请联系管理员分配！')"
    :image-size="432"
  ></el-empty>
</template>

<script>
import omegaTheme from "@omega/theme";
import permissionDark from "./assets/noQueryPermission-dark.png";
import permissionLight from "./assets/noQueryPermission-light.png";

export default {
  name: "NoQueryPermission",
  computed: {
    imageData() {
      if (omegaTheme.theme === "light") {
        return permissionLight;
      }
      return permissionDark;
    }
  }
};
</script>
<style lang="scss" scoped>
.no-permission {
  position: relative;
  width: 100%;
  height: 100%;
  :deep(.el-empty__description) {
    margin-top: 50px;
  }
}
</style>
