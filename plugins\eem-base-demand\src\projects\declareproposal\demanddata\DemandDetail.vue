<template>
  <!-- 1弹窗组件 -->
  <CustomElDrawer
    class="drawer"
    :title="CetDialog_1.title"
    :visible.sync="openDrawer"
    destroy-on-close
    size="640px"
  >
    <el-container class="h-full m-J1 flex flex-auto">
      <el-header height="32px" class="mb-J2 h-8 p-0 leading-8">
        <el-row class="text-Aa font-bold flex items-center">
          <span>{{ $T("进线名称") }}：</span>
          <span class="mr-J1 text-over flex-auto">
            <el-tooltip effect="light" :content="name">
              <span>{{ name }}</span>
            </el-tooltip>
          </span>
          <span>{{ $T("户号") }}：</span>
          <span class="mr-J1 text-over flex-auto">
            <el-tooltip effect="light" :content="accountno">
              <span>{{ accountno }}</span>
            </el-tooltip>
          </span>
        </el-row>
      </el-header>
      <CetTable
        class="flex-auto"
        :data.sync="CetTable_1.data"
        :dynamicInput.sync="CetTable_1.dynamicInput"
        v-bind="CetTable_1"
        v-on="CetTable_1.event"
      >
        <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
        <ElTableColumn v-bind="ElTableColumn_logtimeText"></ElTableColumn>
        <ElTableColumn v-bind="ElTableColumn_volumeafterNum"></ElTableColumn>
        <ElTableColumn
          v-bind="ElTableColumn_chargingwayafterText"
        ></ElTableColumn>
        <ElTableColumn v-bind="ElTableColumn_effectivedateText"></ElTableColumn>
      </CetTable>
      <el-pagination
        class="mt-J1"
        style="text-align: right"
        @size-change="handleSizeChange"
        @current-change="handleCurrentPageChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :total="totalCount"
        layout="total,sizes, prev, pager, next, jumper"
        :pageSizes="[10, 20, 50, 100]"
      ></el-pagination>
    </el-container>
  </CustomElDrawer>
</template>
<script>
import customApi from "@/api/custom";
import CustomElDrawer from "eem-base/components/customElComponent/customElDrawer.vue";
import omegaI18n from "@omega/i18n";

export default {
  name: "DemandDetail",
  components: { CustomElDrawer },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },

  computed: {
    language() {
      return omegaI18n.locale === "en";
    }
  },

  data(vm) {
    return {
      openDrawer: false,
      copyTableData: [],
      // 分页
      currentPage: 1,
      pageSize: 20,
      totalCount: 0,
      name: "",
      accountno: "",
      CetDialog_1: {
        title: $T("容量调整与计费方式调整记录"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: $T("序号"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: vm.language ? "100" : "65" //绝对宽度
      },
      ElTableColumn_logtimeText: {
        type: "", // selection 勾选 index 序号
        prop: "logtimeText", // 支持path a[0].b
        label: $T("调整时间"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      ElTableColumn_volumeafterNum: {
        type: "", // selection 勾选 index 序号
        prop: "volumeafterNum", // 支持path a[0].b
        label: $T("容量"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      ElTableColumn_chargingwayafterText: {
        type: "", // selection 勾选 index 序号
        prop: "chargingwayafterText", // 支持path a[0].b
        label: $T("计费方式"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      ElTableColumn_effectivedateText: {
        type: "", // selection 勾选 index 序号
        prop: "effectivedateText", // 支持path a[0].b
        label: $T("生效日期"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.openDrawer = true;
      this.getTabelData();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    inputData_in(val) {}
  },

  methods: {
    async getTabelData() {
      this.CetTable_1.data = [];
      this.name = "";
      this.accountno = "";
      const response = await customApi.getVolumeOrChargingChangeRecord({
        id: this.queryId_in
      });
      const { code, data } = response;
      if (code === 0 && data && data.length > 0) {
        const { name, accountno, volumeorchargingchangerecord_model } = data[0];
        this.name = name;
        this.accountno = accountno;
        const len = volumeorchargingchangerecord_model?.length;
        if (volumeorchargingchangerecord_model && len > 0) {
          for (var i = 0; i < len; i++) {
            const { chargingwaybefore, chargingwayafter, volumeafter } =
              volumeorchargingchangerecord_model[i];
            let map = {
              1: $T("容量计费"),
              2: $T("需量计费")
            };
            let item = data[0].volumeorchargingchangerecord_model[i];
            item.chargingwaybeforeText = map[chargingwaybefore] || $T("未调整");
            item.chargingwayafterText = map[chargingwayafter] || $T("未调整");
            item.volumeafterNum = volumeafter ? volumeafter.toFixed2(2) : "--";
            item.effectivedateText = this.$moment(
              volumeorchargingchangerecord_model[i].effectivedate
            ).format("YYYY-MM-DD");
            item.logtimeText = this.$moment(
              volumeorchargingchangerecord_model[i].logtime
            ).format("YYYY-MM-DD HH:mm:ss");
          }
          volumeorchargingchangerecord_model.sort(
            (a, b) =>
              Math.abs(Number(b.effectivedate)) -
              Math.abs(Number(a.effectivedate))
          );
          // 保存数据，用于分页
          this.copyTableData = this._.cloneDeep(
            volumeorchargingchangerecord_model
          );
          this.handleCurrentPageChange(1);
          this.totalCount = this.copyTableData.length;
        }
      }
    },
    //分页大小变化
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.handleCurrentPageChange(1);
    },
    //分页当前页变化
    handleCurrentPageChange(val) {
      this.currentPage = val;
      this.CetTable_1.data = this.copyTableData.slice(
        (this.currentPage - 1) * this.pageSize,
        this.currentPage * this.pageSize
      );
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog .el-dialog__body) {
  padding: 0 20px;
}
.CetDialog {
  :deep(.el-dialog__body) {
    @include padding(J1);
    @include background_color(BG);
  }
}
.text-over {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
</style>
