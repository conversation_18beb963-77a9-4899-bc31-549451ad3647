import TopSerach from "./TopSerach";
import TableDetails from "./TableDetails";
import Synchronous from "./Synchronous";
import omegaTheme from "@omega/theme";
import moment from "moment";

function getInstrumentData(data = {}) {
  const themeConfig = {
    light: [
      {
        id: 1,
        img: "bgLigth1",
        selectedImg: "noBgLigth1",
        textShadow: "0 0 4px #FF5D5D",
        background: "linear-gradient(180deg, #424E5F 27.27%, #F53F3F 77.27%)"
      },
      {
        id: 2,
        img: "bgLigth2",
        selectedImg: "noBgLigth2",
        textShadow: "0 0 4px #FF9C3F",
        background: "linear-gradient(180deg, #424E5F 27.27%, #FF9D09 77.27%)",
        time: [
          moment().startOf("d").valueOf(),
          moment().startOf("d").add(6, "d").valueOf()
        ]
      },
      {
        id: 3,
        img: "bgLigth3",
        selectedImg: "noBgLigth3",
        textShadow: "0 0 4px #FFC531",
        background: "linear-gradient(180deg, #424E5F 27.27%, #FFC531 77.27%)",
        time: [
          moment().startOf("d").valueOf(),
          moment().startOf("d").add(15, "d").valueOf()
        ]
      },
      {
        id: 4,
        img: "bgLigth4",
        selectedImg: "noBgLigth4",
        textShadow: "0 0 4px #0C82F8",
        background: "linear-gradient(180deg, #424E5F 27.27%, #4CA6FF 77.27%)",
        time: [
          moment().startOf("d").valueOf(),
          moment().startOf("d").add(30, "d").valueOf()
        ]
      }
    ],
    dark: [
      {
        id: 1,
        img: "bgDark1",
        selectedImg: "noBgDark1",
        textShadow: "0 0 4px #FF5D5D",
        background: "linear-gradient(180deg, #FFF 27.27%, #FFBDBD 77.27%)"
      },
      {
        id: 2,
        img: "bgDark2",
        selectedImg: "noBgDark2",
        textShadow: "0 0 4px #FF9C3F",
        background: "linear-gradient(180deg, #FFF 27.27%, #FFD4AC 77.27%)",
        time: [
          moment().startOf("d").valueOf(),
          moment().startOf("d").add(6, "d").valueOf()
        ]
      },
      {
        id: 3,
        img: "bgDark3",
        selectedImg: "noBgDark3",
        textShadow: "0 0 4px #FFC531",
        background: "linear-gradient(180deg, #FFF 27.27%, #FFE5A3 77.27%)",
        time: [
          moment().startOf("d").valueOf(),
          moment().startOf("d").add(15, "d").valueOf()
        ]
      },
      {
        id: 4,
        img: "bgDark4",
        selectedImg: "noBgDark4",
        textShadow: "0 0 4px #0C82F8",
        background: "linear-gradient(180deg, #FFF 27.27%, #8EC7FF 77.27%)",
        time: [
          moment().startOf("d").valueOf(),
          moment().startOf("d").add(30, "d").valueOf()
        ]
      }
    ]
  };

  const items = themeConfig[omegaTheme.theme] ?? themeConfig.light;
  const keyMap = ["expired", "expiresin7d", "expiresin15d", "expiresin30d"];

  return items.map((item, index) => {
    const value = data[keyMap[index]];
    return {
      ...item,
      value: value ? value : 0
    };
  });
}

function getTagDetail(data = {}) {
  const baseConfig = [
    {
      text: $T("已检定"),
      unit: $T("次"),
      light: { bgColor: "rgba(106,222,154,0.2)", color: "#6ADE9A" },
      dark: { bgColor: "rgba(12, 130, 248, 0.2)", color: "#0C82F8" }
    },
    {
      text: $T("检定周期"),
      light: { bgColor: "rgba(76,166,255,0.2)", color: "#4CA6FF" },
      dark: { bgColor: "rgba(24, 221, 127, 0.2)", color: "#18dd7f" }
    },
    {
      text: $T("上次检定时间"),
      light: { bgColor: "rgba(255,206,32,0.2)", color: "#FFCE20" },
      dark: { bgColor: "rgba(255, 197, 49, 0.2)", color: "#ffc531" }
    },
    {
      text: $T("下次检定时间"),
      light: { bgColor: "rgba(125,217,255,0.2)", color: "#7DD9FF" },
      dark: { bgColor: "rgba(65, 224, 228, 0.2)", color: "#41e0e4" }
    }
  ];

  const theme = omegaTheme.theme === "dark" ? "dark" : "light";
  const keyMap = [
    "number",
    "plannedverificationcycle",
    "lastoverhauldate",
    "nextoverhauldate"
  ];

  return baseConfig.map((item, index) => {
    const colors = item[theme] || item.light;
    const value = data[keyMap[index]];

    return {
      text: item.text,
      unit: index === 1 ? data.verificationcycle : item.unit,
      bgColor: colors.bgColor,
      color: colors.color,
      value: value ?? "--"
    };
  });
}

export {
  TopSerach,
  TableDetails,
  Synchronous,
  getTagDetail,
  getInstrumentData
};
