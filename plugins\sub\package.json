{"name": "sub", "version": "1.0.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "release": "npx @altair/pack build"}, "vuePlugins": {"resolveFrom": "../../base"}, "devDependencies": {"@altair/pack": "^1.0.2", "@babel/core": "7.11.0", "@babel/eslint-parser": "^7.16.0", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-service": "~5.0.8", "compression-webpack-plugin": "^10.0.0", "core-js": "^3.36.0", "css-loader": "3.6.0", "eslint": "^8.11.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-vue": "^9.3.0", "postcss-import": "^14.1.0", "prettier": "^2.4.1", "sass": "1.54.0", "sass-loader": "^13.0.2", "svg-sprite-loader": "^6.0.11", "tailwindcss": "^3.1.6", "vue-eslint-parser": "^8.0.1", "vue-loader": "15.10.0"}, "dependencies": {"@altair/knight": "^1.2.5", "@omega/app": "^1.10.1", "@omega/cli-codestd": "0.0.2", "@omega/cli-devops": "^1.2.6", "@omega/cli-devserver": "^1.2.0", "@omega/dashboard": "^1.2.8", "@omega/http": "^1.7.1", "@omega/i18n": "^1.3.0", "@omega/icon": "^1.40.0", "@omega/theme": "^1.12.12", "@omega/trend": "^1.2.9", "@omega/widget": "^1.0.3", "cet-chart": "^1.7.5", "cet-common": "^1.7.4", "element-ui": "^2.15.6", "jquery": "^3.7.1", "lodash": "^4.17.21", "moment": "^2.29.1", "nprogress": "^0.2.0", "vue": "^2.7.8", "vue-router": "^3.5.3", "vuex": "^3.5.1", "driver.js": "^1.3.1", "eem-base": "workspace:^", "crypto-js": "^4.1.1"}}