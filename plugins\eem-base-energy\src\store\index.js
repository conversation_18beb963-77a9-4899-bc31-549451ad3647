import importProgress from "./modules/importProgress.js";
import { modules, state, mutations, actions } from "eem-base/store/index.js";
import customApi from "@/api/custom";
export default {
  modules: { ...modules, importProgress },
  state: {
    ...state,
    multidimensional: true,
    systemCfg: {
      //用能分析页面中能耗切换以及总能耗详情tab页面是否进行展示
      totalEnergyConsumptionDetail: true,
      //能耗查询页面分时分析tab页默认的堆叠图chart类型，支持
      energyQueryAndAnalysisTimeShareChartType: "line",
      //节点对比限制上限
      numberOfNodesCompared: 4,
      //能耗查询与分析页面分时分析峰平谷配置
      customTimeComparisonRule: [
        {
          denominator1: "峰",
          denominator2: "峰",
          molecule1: "谷",
          molecule2: "平"
        }
      ],
      //属性关联页面，模型类型下拉框列表配置
      metricalNodeModelList: [
        "sectionarea",
        "building",
        "floor",
        "room",
        "manuequipment"
      ],
      //多维度用能分析页面中的左侧树节点展示数据
      measureLabelList: [
        "sectionarea",
        "building",
        "floor",
        "room",
        "manuequipment"
      ],
      ///属性关联页面，是否隐藏历史记录
      hideHistoryBtn: false,
      ///能源流向需要隐藏的节点模型
      energyFlowAnalysisHideTreeModelLabels: ["project"]
    },
    alarmConfigTypes: [1, 2]
  },
  mutations: {
    ...mutations,
    setSystemCfg(state, val) {
      state.systemCfg = val;
    },
    setAlarmConfigTypes(state, val) {
      state.alarmConfigTypes = val;
    }
  },
  actions: {
    ...actions,
    async getConfig({ commit }) {
      const res = await customApi.configConfigInfo();
      const config = res.data || {};
      commit("setSystemCfg", config.systemCfg);
      commit("setAlarmConfigTypes", config.alarmConfigTypes);
    }
  }
};
