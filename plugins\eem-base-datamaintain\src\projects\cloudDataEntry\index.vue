<template>
  <div class="page">
    <CetAside class="cet-aside">
      <template #aside>
        <ProjectTree
          @currentNode_out="updateTreeNode"
          @treeData_out="projectTree_treeData_out"
          :queryTimeParams="queryTimeParams"
          :refreshTrigger="refreshProjectTree"
          :currentTab="ElSelect_type.curValue"
          @dimTreeConfigId_out="dimTreeConfigId_out"
          :dimTreeRefreshTrigger="dimTreeRefresh"
        />
      </template>
      <template #container>
        <div class="mb-J3">
          <el-tooltip
            :content="currentNode && currentNode.name"
            effect="light"
            placement="top-start"
          >
            <span class="common-title-H2 title-width text-ellipsis">
              {{ currentNode && currentNode.name }}
            </span>
          </el-tooltip>
        </div>
        <div class="flex flex-col flex-auto bg-BG1">
          <el-tabs
            class="box-border"
            v-model="ElSelect_type.curValue"
            @tab-click="ElSelect_type_change_out"
          >
            <el-tab-pane
              v-for="item in dataEntryList"
              :label="item.label"
              :name="item.name"
              :key="item.name"
            ></el-tab-pane>
          </el-tabs>
          <div class="flex flex-col flex-auto p-J4">
            <div class="clearfix mb-J3">
              <el-tooltip
                effect="light"
                :disabled="!taskImportStatus"
                :content="$T('正在导入产量数据，请等到导入完成后再操作！')"
                placement="top"
              >
                <div class="inline-block fr">
                  <CetButton
                    v-if="isShowEdit"
                    class="fr"
                    v-bind="CetButton_4"
                    v-on="CetButton_4.event"
                    :disable_in="CetButton_4.disable_in || taskImportStatus"
                  ></CetButton>
                </div>
              </el-tooltip>
              <CetButton
                v-if="isShowEdit"
                class="fr mr-J1"
                v-bind="CetButton_5"
                v-on="CetButton_5.event"
              ></CetButton>
              <el-tooltip
                effect="light"
                :disabled="!taskImportStatus"
                :content="$T('正在导入产量数据，请等到导入完成后再操作！')"
                placement="top"
              >
                <div class="inline-block fr">
                  <CetButton
                    class="fr mr-J1"
                    v-show="ElSelect_type.value !== '4'"
                    v-bind="CetButton_export"
                    v-on="CetButton_export.event"
                    :disable_in="
                      CetButton_export.disable_in || taskImportStatus
                    "
                  ></CetButton>
                </div>
              </el-tooltip>
              <el-tooltip
                effect="light"
                :disabled="!taskImportStatus"
                :content="$T('正在导入产量数据，请等到导入完成后再操作！')"
                placement="top"
              >
                <div class="inline-block fr">
                  <CetButton
                    class="fr mr-J1"
                    v-permission="'energyplan_export'"
                    v-show="ElSelect_type.value === '4'"
                    v-bind="CetButton_1"
                    v-on="CetButton_1.event"
                    :disable_in="CetButton_1.disable_in || taskImportStatus"
                  ></CetButton>
                </div>
              </el-tooltip>
              <el-tooltip
                effect="light"
                :disabled="!taskImportStatus"
                :content="$T('正在导入产量数据，请等到导入完成后再操作！')"
                placement="top"
              >
                <div class="inline-block fr">
                  <CetButton
                    class="fr mr-J1"
                    v-show="isShowImportOrEdit"
                    v-bind="CetButton_import"
                    v-on="CetButton_import.event"
                    :title="taskImportStatus ? $T('导入中') : $T('导入')"
                    :disable_in="
                      CetButton_import.disable_in || taskImportStatus
                    "
                  ></CetButton>
                </div>
              </el-tooltip>
              <div
                class="basic-box fr mr-J1"
                v-if="ElSelect_type.value === '3'"
              >
                <div class="basic-box-label">{{ $T("导入类型") }}</div>
                <ElSelect v-model="ElSelect_4.value" v-bind="ElSelect_4">
                  <ElOption
                    v-for="item in ElOption_4.options_in"
                    :key="item[ElOption_4.key]"
                    :label="item[ElOption_4.label]"
                    :value="item[ElOption_4.value]"
                    :disabled="item[ElOption_4.disabled]"
                  ></ElOption>
                </ElSelect>
              </div>
              <div
                :key="ElSelect_type.value + '12'"
                class="year-range fr mr-J1"
                v-show="ElSelect_3.value === 12"
              >
                <el-date-picker
                  ref="dayPicker"
                  v-model="curDayInfo"
                  type="daterange"
                  @change="timeChanged"
                  :clearable="false"
                  :pickerOptions="dayPick"
                ></el-date-picker>
              </div>
              <CetDateSelect
                class="CetDateSelect fr mr-J1"
                :key="ElSelect_type.value + '15'"
                v-show="ElSelect_3.value === 15"
                v-bind="CetDateSelect_time"
                v-on="CetDateSelect_time.event"
              ></CetDateSelect>
              <el-date-picker
                ref="seasonPicker"
                :key="ElSelect_type.value + '14'"
                v-if="ElSelect_3.value === 14"
                class="fr mr10"
                v-model="CetDatePicker_2.curVal"
                v-bind="CetDatePicker_2.config"
                :pickerOptions="
                  ElSelect_type.value === '4'
                    ? {}
                    : CetDatePicker_2.pickerOptions
                "
                @change="timeChanged"
                :placeholder="$T('选择日期')"
              ></el-date-picker>
              <div
                class="year-range fr mr-J1"
                :key="ElSelect_type.value + '17'"
                v-show="ElSelect_3.value === 17"
              >
                <yearPicker
                  class="w300"
                  ref="basePicker"
                  :dateList="curDateList"
                  @updateTimeRange="updateStatisticYear"
                  :optionPicker="optionPicker"
                />
              </div>
              <customElSelect
                v-model="ElSelect_3.curValue"
                v-bind="ElSelect_3"
                v-on="ElSelect_3.event"
                class="fr mr-J1"
                :prefix_in="$T('间隔时间')"
              >
                <ElOption
                  v-for="item in ElOption_3.options_in"
                  v-show="item.visible"
                  :key="item[ElOption_3.key]"
                  :label="item[ElOption_3.label]"
                  :value="item[ElOption_3.value]"
                  :disabled="item[ElOption_3.disabled]"
                ></ElOption>
              </customElSelect>
            </div>
            <div class="flex-auto clearfix">
              <CetTable
                :data.sync="CetTable_1.data"
                :dynamicInput.sync="CetTable_1.dynamicInput"
                v-bind="CetTable_1"
                v-on="CetTable_1.event"
                :empty-text="
                  isShowTableEmptyText
                    ? $T('无计划产量查询权限')
                    : $T('暂无数据')
                "
              >
                <el-table-column
                  type="index"
                  :label="$T('序号')"
                  :fixed="true"
                  align="left"
                  :width="language ? 90 : 65"
                ></el-table-column>
                <el-table-column
                  prop="energyTypeName"
                  :fixed="true"
                  align="left"
                  :label="labelname"
                  minWidth="160"
                >
                  <template slot-scope="scope">
                    <span>
                      <span v-if="showEmpty(ElSelect_type.value, scope.row)">
                        &nbsp;&nbsp;&nbsp;&nbsp;
                      </span>
                      {{ tableName(ElSelect_type.value, scope.row) }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  :fixed="true"
                  align="left"
                  :label="$T('计量单位')"
                  v-if="ElSelect_type.value === '3'"
                  minWidth="160"
                >
                  <template slot-scope="scope">
                    <span>{{ formatSymbol(scope.row.unit, 2) }}</span>
                  </template>
                </el-table-column>
                <ElTableColumn
                  v-for="(item, index) in columnArr"
                  :key="index"
                  v-bind="item"
                  align="left"
                >
                  <template slot-scope="scope">
                    <div
                      v-if="
                        canEdit(
                          scope.row.aggregationCycle,
                          item.prop,
                          scope.row
                        )
                      "
                    >
                      <el-input
                        v-model="scope.row[item.prop]"
                        @keyup.native="handleNum(scope.row, item.prop, 2)"
                        :placeholder="placeHolderByType(scope.row.energytype)"
                      ></el-input>
                    </div>

                    <div v-else>
                      <span>
                        {{
                          scope.row[item.prop] === null
                            ? "--"
                            : scope.row[item.prop]
                        }}
                      </span>
                    </div>
                  </template>
                </ElTableColumn>
              </CetTable>
            </div>
          </div>
        </div>
      </template>
    </CetAside>

    <exportWin v-bind="exportWin" @updata_out="updataOut" />
    <UploadDialog
      v-bind="uploadDialog"
      v-on="uploadDialog.event"
    ></UploadDialog>
  </div>
</template>
<script>
import custom from "@/api/custom";
import common from "eem-base/utils/common.js";
import ProjectTree from "./ProjectTree.vue";
import moment from "moment";
import yearPicker from "./year-range-picker.vue";
import exportWin from "./exportWin.vue";
import { httping } from "@omega/http";
import UploadDialog from "eem-base/components/uploadDialog";

const DateConfig = [
  {
    id: 7,
    text: $T("小时"),
    type: "hour",
    sub: "h",
    format: "hh:mm"
  },
  {
    id: 12,
    text: $T("日"),
    type: "date",
    sub: "d",
    format: "YYYY-MM-DD"
  },
  {
    id: 13,
    text: $T("周"),
    type: "week",
    sub: "w",
    format: "YYYY-MM-DD WW"
  },
  {
    id: 14,
    text: $T("月"),
    type: "month",
    sub: "M",
    format: "YYYY-MM"
  },
  {
    id: 17,
    text: $T("年"),
    type: "year",
    sub: "y",
    format: "YYYY"
  }
];
export default {
  name: "energyConsumptionDataEntry",
  components: {
    ProjectTree,
    yearPicker,
    exportWin,
    UploadDialog
  },
  computed: {
    dayPick() {
      return this.ElSelect_type.value === "4"
        ? "{}"
        : this.CetDatePicker_2.pickerOptions;
    },
    isShowEdit() {
      return (
        this.ElSelect_type.value == "1" ||
        (this.ElSelect_type.value == "2" &&
          [14].includes(this.ElSelect_3.value)) ||
        this.ElSelect_type.value == "5" ||
        (this.ElSelect_type.value == "4" &&
          (this.ElSelect_3.value === 14 || this.ElSelect_3.value === 12) &&
          (this.$checkPermission("energyplan_create") ||
            this.$checkPermission("energyplan_import")))
      );
    },
    projectId() {
      return this.$store.state.projectId;
    },
    optionPicker() {
      if (this.ElSelect_type.value === "4") {
        return val => true;
      } else {
        return val => {
          return (
            this.$moment(val).valueOf() <=
            this.$moment().endOf("year").valueOf()
          );
        };
      }
    },
    dataEntryList() {
      const initList = [
        {
          name: "1"
        },
        {
          name: "2"
        },
        // {
        //   name: "3"
        // },
        {
          name: "4"
        },
        {
          name: "5"
        }
      ];
      const keyArr = [
        "",
        $T("实际产量"),
        $T("账单能耗"),
        $T("台账"),
        $T("计划产量"),
        $T("能耗")
      ];
      let dataEntryList =
        this.$store.state.systemCfg?.dataEntryList || initList;
      dataEntryList.forEach(item => {
        item.label = keyArr[item.name];
      });

      return dataEntryList;
    },
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    },
    taskImportStatus() {
      return (
        this.$store.getters["importProgress/importing"](3) ||
        this.$store.getters["importProgress/importing"](4)
      );
    },
    isShowImportOrEdit() {
      return (
        this.ElSelect_type.value !== "4" ||
        (this.ElSelect_type.value === "4" &&
          (this.$checkPermission("energyplan_create") ||
            this.$checkPermission("energyplan_import")))
      );
    },
    isShowTableEmptyText() {
      return (
        this.ElSelect_type.value === "4" &&
        !this.$checkPermission("energyplan_browser")
      );
    },
    queryTimeParams() {
      let params = {
        aggregationCycle: this.ElSelect_3.value
      };
      switch (this.ElSelect_3.value) {
        case 12:
          params.startTime = this.$moment(this.dayInfo[0])
            .startOf("day")
            .valueOf();
          params.endTime =
            this.$moment(this.dayInfo[1]).endOf("day").valueOf() + 1;
          break;
        case 14:
          params.startTime = this.$moment(this.CetDatePicker_2.val[0])
            .startOf("month")
            .valueOf();
          params.endTime =
            this.$moment(this.CetDatePicker_2.val[1]).endOf("month").valueOf() +
            1;
          break;
        case 15:
          params.startTime = this.$moment(this.quarterTime[0])
            .startOf("quarter")
            .valueOf();
          params.endTime =
            this.$moment(this.quarterTime[1]).endOf("quarter").valueOf() + 1;
          break;
        case 17:
          params.startTime = this.dateList[0];
          params.endTime = this.dateList[1];
          break;
        default:
          break;
      }
      return params;
    }
  },
  data(vm) {
    return {
      sideWidth: "315px",
      dataEntryType: {
        1: 1,
        4: 4,
        5: 2
      },
      refreshProjectTree: Date.now(),
      dimTreeRefresh: Date.now(),
      curDayInfo: [],
      dayInfo: [],
      labelname: $T("能源类型"),
      ExcelHeaderData: [], //台账表头数据
      updateFlag: false,
      products: [], //产品
      energys: [], //账单能耗
      editFlag: false, //是否修改
      currentNode: null,
      ElSelect_type: {
        curValue: "1", //便于处理确认取消操作
        value: "1",
        style: {
          width: "150px"
        },
        event: {
          change: this.ElSelect_type_change_out
        }
      },
      ElSelect_3: {
        value: 14,
        curValue: 14,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_3_change_out
        }
      },
      ElOption_3: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_4: {
        value: false,
        style: {
          width: "200px"
        }
        // event: {
        //   change: this.ElSelect_4_change_out
        // }
      },
      ElOption_4: {
        options_in: [
          {
            type: true,
            text: $T("覆盖导入")
          },
          {
            type: false,
            text: $T("增量导入")
          }
        ],
        key: "type",
        value: "type",
        label: "text",
        disabled: "disabled"
      },
      CetDatePicker_2: {
        disable_in: false,
        val: [],
        curVal: [],
        config: {
          valueFormat: "timestamp",
          type: "monthrange",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          clearable: false,
          style: {
            width: "300px"
          }
        },
        pickerOptions: {
          // 限制预约时间
          disabledDate(time) {
            let timeStr = moment(new Date()).startOf("day").valueOf();
            return time.getTime() > timeStr;
          }
        }
      },
      dateList: [
        vm.$moment().subtract(1, "year").startOf("year").valueOf(),
        vm.$moment().endOf("year").valueOf()
      ],
      curDateList: [],
      CetDateSelect_time: {
        typeList: ["season"],
        showOption: false,
        value: {
          dateType: "4",
          value: vm.$moment().valueOf()
        },
        event: {
          date_out: this.CetDateSelect_time_date_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "", //查询按钮触发  trigger  ,或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out
        }
      },
      columnArr: [],
      CetButton_4: {
        visible_in: true,
        disable_in: true,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_4_statusTrigger_out
        }
      },
      CetButton_5: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_5_statusTrigger_out
        }
      },
      CetButton_export: {
        visible_in: true,
        disable_in: true,
        title: $T("导出"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      productList: [],
      unitMap: {},
      CetButton_import: {
        visible_in: true,
        disable_in: false,
        title: $T("导入"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_import_statusTrigger_out
        }
      },
      exportWin: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        treeData: null,
        urlStr: ""
      },
      uploadDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        extensionNameList_in: [".xlsx"],
        downloadPermission: null,
        hideDownload: true,
        dialogTitle: $T("导入数据"),
        event: {
          download: this.uploadDialog_download,
          uploadFile: this.uploadDialog_uploadFile
        }
      },
      // 按照能管通用3.5迭代要求，需要另一个用户也判断不可导入处理
      dimTreeConfigId: -1
    };
  },

  methods: {
    canEdit(cycleInfo, timeStamp, row) {
      if (
        (this.ElSelect_type.value === "2" && row.energytype == 13) ||
        (this.ElSelect_type.value === "5" && row.measureType == 13)
      ) {
        return false;
      }
      if (!this.isShowEdit) {
        return false;
      } else if (this.ElSelect_type.value != "4") {
        return true;
      } else {
        let curTime = this.$moment();
        if (cycleInfo === 12) {
          curTime = curTime.startOf("day").valueOf();
        } else if (cycleInfo === 14) {
          curTime = curTime.startOf("month").valueOf();
        } else {
          return false;
        }
        return timeStamp >= curTime;
      }
    },
    placeHolderByType(energyType) {
      if (this.ElSelect_type.value === "1") {
        return $T("请输入产品值");
      } else if (this.ElSelect_type.value === "4") {
        return $T("请输入计划产值");
      } else if (this.ElSelect_type.value === "5") {
        return $T("请输入能耗数值");
      } else if (
        this.ElSelect_type.value === "2" &&
        energyType !== 13 &&
        this.ElSelect_3.value === 14
      ) {
        return $T("请输入账单能耗值");
      }
    },
    updateStatisticYear(yearData) {
      this.curDateList = [yearData.startYear, yearData.endYear];
      this.timeChanged();
    },
    init() {
      this.currentNode = null;
      this.CetDatePicker_2.val = [
        this.$moment().subtract(3, "month").valueOf(),
        this.$moment().subtract(1, "month").valueOf()
      ];
      this.CetDatePicker_2.curVal = this.CetDatePicker_2.val;
      this.dateList = [
        this.$moment().subtract(1, "year").startOf("year").valueOf(),
        this.$moment().endOf("year").valueOf()
      ];
      this.curDateList = this.dateList;
      this.$refs.basePicker.setYear(this.curDateList[0], this.curDateList[1]);

      this.CetDateSelect_time.value.value = this.$moment().valueOf();
      this.quarterTime = [
        this.$moment().startOf("quarter").valueOf(),
        this.$moment().endOf("quarter").valueOf()
      ];
      this.queryProductList_out();
    },
    showEmpty(value, data) {
      let row = [20, 22, 26, 38, 39, 40];
      if (
        value === "3" &&
        (row.indexOf(data.index) > -1 ||
          (data.index % 2 === 1 && data.index < 36 && data.index > 3))
      ) {
        return true;
      } else {
        return false;
      }
    },
    tableName(value, data) {
      if (value === "1" || value === "4" || value === "5") {
        return data.productName + this.formatSymbol(data.unit, 1);
      } else if (value === "2") {
        return data.energyTypeName + this.formatSymbol(data.unit, 1);
      } else if (value === "3") {
        return data.desc;
      } else {
        return "";
      }
    },
    getProjectEnergy(res) {
      custom.getProjectEnergy().then(response => {
        if (response.code === 0) {
          this.energys = response.data || [];
        } else {
          this.energys = [];
        }
        res();
      });
    },
    getProjectProduct(res) {
      httping({
        url: "/eembasedatamaintain/product/config",
        method: "GET"
      }).then(response => {
        if (response.code === 0) {
          this.products = response.data || [];
        } else {
          this.products = [];
        }
        res();
      });
    },
    getTableData() {
      if (!this.currentNode) {
        this.columnArr = [];
        this.CetTable_1.data = [];
        return;
      }
      if (
        this.ElSelect_type.value === "1" ||
        this.ElSelect_type.value === "2" ||
        this.ElSelect_type.value === "4"
      ) {
        if (
          (this.ElSelect_type.value === "1" ||
            this.ElSelect_type.value === "4" ||
            this.ElSelect_type.value === "5") &&
          !this.products.length
        ) {
          this.columnArr = [];
          this.CetTable_1.data = [];
          return;
        } else if (!this.energys.length) {
          this.columnArr = [];
          this.CetTable_1.data = [];
          return;
        }
      }
      this.getData();
    },

    getFileName() {
      if (this.ElSelect_3.value === 12) {
        return $T("日度报表");
      } else if (this.ElSelect_3.value === 14) {
        return $T("月度报表");
      } else if (this.ElSelect_3.value === 15) {
        return $T("季度报表");
      } else if (this.ElSelect_3.value === 17) {
        return $T("年度报表");
      }
      return $T("普通报表");
    },
    getData() {
      this.unitMap = {};
      if (!this.judgeTimeSection()) {
        return;
      }
      let inputInfo = {
        aggregationCycle: this.ElSelect_3.value
      };
      let nodeInfo = {
        id: this.currentNode.id,
        modelLabel: this.currentNode.modelLabel
      };

      let columnArr = [];
      let text = "month";
      switch (this.ElSelect_3.value) {
        case 12:
          inputInfo.startTime = this.$moment(this.dayInfo[0])
            .startOf("day")
            .valueOf();
          inputInfo.endTime =
            this.$moment(this.dayInfo[1]).endOf("day").valueOf() + 1;
          text = "day";
          break;
        case 14:
          inputInfo.startTime = this.$moment(this.CetDatePicker_2.val[0])
            .startOf("month")
            .valueOf();
          inputInfo.endTime =
            this.$moment(this.CetDatePicker_2.val[1]).endOf("month").valueOf() +
            1;
          text = "month";
          break;
        case 15:
          inputInfo.startTime = this.$moment(this.quarterTime[0])
            .startOf("quarter")
            .valueOf();
          inputInfo.endTime =
            this.$moment(this.quarterTime[1]).endOf("quarter").valueOf() + 1;
          text = "quarter";
          break;
        case 17:
          inputInfo.startTime = this.dateList[0];
          inputInfo.endTime = this.dateList[1];
          text = "year";
          break;
        default:
          break;
      }

      // 根据查询时间生成表头
      let copyStartTime = inputInfo.startTime,
        copyEndTime = inputInfo.endTime;

      //获取当前月所在季度的开始月
      let getQuarterStartMonth = month => {
        let quarterStartMonth = 0;
        if (month < 3) {
          quarterStartMonth = 1;
        }
        if (3 <= month && month < 6) {
          quarterStartMonth = 2;
        }
        if (6 <= month && month < 9) {
          quarterStartMonth = 3;
        }
        if (month >= 9) {
          quarterStartMonth = 4;
        }
        return quarterStartMonth;
      };

      let getHead = iTime => {
        if (this.ElSelect_3.value === 12) {
          let formatStr = this.language ? "MM-DD" : "MM月DD日";
          return this.$moment(iTime).format(formatStr);
        } else if (this.ElSelect_3.value === 14) {
          let formatStr = this.language ? "YYYY-MM" : "YYYY年MM月";
          return this.$moment(iTime).format(formatStr);
        } else if (this.ElSelect_3.value === 15) {
          let month = this.$moment(iTime).month() + 1;
          let quarter = getQuarterStartMonth(month);
          let val = "";
          if (this.language) {
            val = quarter + " " + $T("季度");
          } else {
            val = "第" + quarter + "季度";
          }
          return val;
        } else if (this.ElSelect_3.value === 17) {
          let formatStr = this.language ? "YYYY" : $T("YYYY年");
          return this.$moment(iTime).format(formatStr);
        }
      };
      while (copyStartTime < copyEndTime) {
        columnArr.push({
          prop: String(copyStartTime),
          label: getHead(copyStartTime),
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: 160
        });
        copyStartTime = this.$moment(copyStartTime).add(1, text).valueOf();
      }
      this.columnArr = columnArr;
      if (this.ElSelect_type.value === "1") {
        this.labelname = $T("产品类型");
        inputInfo.measureTypes = this.products.map(i => i.id);
        inputInfo = Object.assign(inputInfo, nodeInfo);
        inputInfo.dataEntryType = 1; //服务器让传的伞兵类型
      } else if (this.ElSelect_type.value === "2") {
        this.labelname = $T("能源类型");
        inputInfo.cycle = this.ElSelect_3.value;
        inputInfo.node = nodeInfo;
        inputInfo.energyTypes = this.energys.map(i => i.energytype);
      } else if (this.ElSelect_type.value === "4") {
        this.labelname = $T("计划产量");
        inputInfo.measureTypes = this.products.map(i => i.id);
        inputInfo.dataEntryType = 4;
        inputInfo = Object.assign(inputInfo, nodeInfo);
      } else if (this.ElSelect_type.value === "5") {
        this.labelname = $T("能源类型");
        inputInfo.dataEntryType = 2;
        inputInfo.measureTypes = this.energys.map(i => i.energytype);
        inputInfo = Object.assign(inputInfo, nodeInfo);
      }

      custom.getProductData(this.ElSelect_type.value, inputInfo).then(res => {
        if (res.code === 0) {
          if (["1", "4", "5"].includes(this.ElSelect_type.value)) {
            let val = this._.get(res, "data", []);
            // 根据返回有多少个计量参数来生成列
            val.forEach(item => {
              item.details.forEach(i => {
                item[i.logTime] = i.value === null ? null : i.value.toFixed(2);
                item[i.logTime + "_id"] = i.id;
                item.productName = item.measureTypeName;
              });
              this.unitMap[item.measureType] = item.unit;
            });
            this.CetTable_1.data = val;
          } else if (this.ElSelect_type.value === "2") {
            let data = res.data || [];
            // 返回的data不能直接用需要经过处理
            // 先取出查询的能源类型对应的一个数据
            let resData = [];
            inputInfo.energyTypes.forEach(item => {
              let obj = data.find(i => i.energytype === item);
              // 往单个对象添加每个日期的数据
              data.forEach(item2 => {
                if (item2.energytype === item) {
                  obj[item2.logtime] =
                    item2.totalusage === null
                      ? null
                      : item2.totalusage.toFixed(2);
                }
              });
              resData.push(obj);
            });

            this.CetTable_1.data = this._.cloneDeep(resData);
          } else {
            let val = res.data || [];
            val.forEach(item => {
              if (item.valueList) {
                item.valueList.forEach(i => {
                  item[i.logTime] =
                    i.value || i.value === 0 ? i.value.toFixed(2) : null;
                  item[i.logTime + "_id"] = i.id;
                });
              }
            });
            this.CetTable_1.data = val;
          }
        }
        this.editFlag = false;
      });
    },
    // 判断时间是否超过限制
    judgeTimeSection() {
      let flag = true;
      switch (this.ElSelect_3.value) {
        case 12:
          if (
            this.$moment(this.dayInfo[0]).add(31, "day").valueOf() <
            this.$moment(this.dayInfo[1]).valueOf()
          ) {
            this.$message({
              type: "warning",
              message: $T("起止时间不能相差超过31天")
            });

            this.dayInfo[0] = this.$moment(this.dayInfo[1])
              .subtract(30, "day")
              .valueOf();
            this.curDayInfo = this.dayInfo;
          }
          break;
        case 14:
          if (
            this.$moment(this.CetDatePicker_2.val[0])
              .add(11, "month")
              .startOf("month")
              .valueOf() <
            this.$moment(this.CetDatePicker_2.val[1]).startOf("month").valueOf()
          ) {
            this.$message({
              type: "warning",
              message: $T("起止时间不能相差超过12个月")
            });
            this.CetDatePicker_2.val[0] = this.$moment(
              this.CetDatePicker_2.val[1]
            )
              .startOf("month")
              .subtract(11, "month")
              .valueOf();
            // flag = false;
            this.CetDatePicker_2.curVal = this.CetDatePicker_2.val;
          }
          break;
        case 17:
          if (
            this.$moment(this.dateList[0])
              .add(10, "year")
              .endOf("year")
              .valueOf() <= this.dateList[1]
          ) {
            this.$message({
              type: "warning",
              message: $T("起止时间不能相差超过10年")
            });
            this.dateList = [
              this.$moment(this.dateList[1])
                .startOf("year")
                .subtract(10, "year")
                .valueOf(),
              this.dateList[1]
            ];
            this.curDateList = this.dateList;
            // flag = false;
          }
          break;
        default:
          break;
      }
      return flag;
    },

    timeChanged() {
      const vm = this;

      if (vm.editFlag) {
        vm.$confirm($T("数据有修改,是否保存？"), $T("提示"), {
          distinguishCancelAndClose: true,
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        })
          .then(() => {
            this.CetButton_4_statusTrigger_out(); //TODO保存和读取有冲突，
            this.dayInfo = this.curDayInfo;
            this.CetDatePicker_2.val = this.CetDatePicker_2.curVal;
            this.dateList = this.curDateList;
            this.checkRefreshTreeOrGetTableData();
            return;
          })
          .catch(action => {
            if (action === "cancel") {
              this.dayInfo = this.curDayInfo;
              this.dateList = this.curDateList;
              this.CetDatePicker_2.val = this.CetDatePicker_2.curVal;
              this.checkRefreshTreeOrGetTableData();
            } else {
              this.curDayInfo = this.dayInfo;
              this.curDateList = this.dateList;
              this.CetDatePicker_2.curVal = this.CetDatePicker_2.val;
            }
          });
      } else {
        this.dayInfo = this.curDayInfo;
        this.CetDatePicker_2.val = this.CetDatePicker_2.curVal;
        this.dateList = this.curDateList;
        this.checkRefreshTreeOrGetTableData();
      }
    },
    CetButton_1_statusTrigger_out(val) {
      // this.exportExcel(false);
      this.CetButton_export_statusTrigger_out(new Date().getTime());
    },
    //获取产品计量参数列表
    queryProductList_out(callback) {
      custom.queryProductList().then(response => {
        if (response.code === 0) {
          this.productList = response.data || [];
        } else {
          this.productList = [];
        }
        callback && callback();
      });
    },
    //导出Excel
    exportExcel(type = true) {
      if (!this.currentNode) {
        this.$message.warning($T("暂无节点信息"));
        return;
      }
      let url = `/eem-service/v1/energy/plan/export?exportTemplate=${type}`;
      const timeType = this.ElSelect_3.value;
      let startTime, endTime;
      switch (this.ElSelect_3.value) {
        case 12:
          startTime = this.$moment(this.dayInfo[0]).startOf("day").valueOf();
          endTime = this.$moment(this.dayInfo[1]).endOf("day").valueOf() + 1;
          break;
        case 14:
          startTime = this.$moment(this.CetDatePicker_2.val[0])
            .startOf("month")
            .valueOf();
          endTime =
            this.$moment(this.CetDatePicker_2.val[1]).endOf("month").valueOf() +
            1;
          break;
        case 15:
          startTime = this.$moment(this.quarterTime[0])
            .startOf("quarter")
            .valueOf();
          endTime =
            this.$moment(this.quarterTime[1]).endOf("quarter").valueOf() + 1;
          break;
        case 17:
          startTime = this.dateList[0];
          endTime = this.dateList[1];
          break;
        default:
          break;
      }
      const list = this.productList || [];
      let idArr = list.map(item => item.producttype);
      if (this.ElSelect_type.value === "4") {
        const params = {
          unitMap: this.unitMap,
          types: idArr,
          startTime: startTime,
          endTime: endTime,
          aggregationCycles: {
            totalData: timeType === 12 ? 14 : 17,
            singleData: timeType
          },
          node: {
            id: this.currentNode.id,
            name: this.currentNode.name,
            modelLabel: this.currentNode.modelLabel
          },
          modelLabel: "productionplan"
        };
        common.downExcel(url, params);
      }
    },
    CetButton_export_statusTrigger_out(val) {
      let params = {
        aggregationCycle: this.ElSelect_3.value,
        dataEntryType: this.dataEntryType[this.ElSelect_type.value]
      };
      switch (this.ElSelect_3.value) {
        case 12:
          params.startTime = this.$moment(this.dayInfo[0])
            .startOf("day")
            .valueOf();
          params.endTime =
            this.$moment(this.dayInfo[1]).endOf("day").valueOf() + 1;
          break;
        case 14:
          params.startTime = this.$moment(this.CetDatePicker_2.val[0])
            .startOf("month")
            .valueOf();
          params.endTime =
            this.$moment(this.CetDatePicker_2.val[1]).endOf("month").valueOf() +
            1;
          break;
        case 15:
          params.startTime = this.$moment(this.quarterTime[0])
            .startOf("quarter")
            .valueOf();
          params.endTime =
            this.$moment(this.quarterTime[1]).endOf("quarter").valueOf() + 1;
          break;
        case 17:
          params.startTime = this.dateList[0];
          params.endTime = this.dateList[1];
          break;
        default:
          break;
      }
      let msType = this.products.map(i => i.id);
      let energyTypes = this.energys.map(i => i.energytype);
      if (
        this.ElSelect_type.value === "1" ||
        this.ElSelect_type.value === "4"
      ) {
        params.measureTypes = msType;
      } else if (this.ElSelect_type.value === "5") {
        params.measureTypes = energyTypes;
      } else {
        this.CetButton_export_statusTrigger_out11();
        return;
      }
      if (this.ElSelect_type.value === "4") {
        this.exportWin.urlStr =
          "/eembasedatamaintain/energy/plan/export/multiNodes";
      } else {
        this.exportWin.urlStr =
          "/eembasedatamaintain/system/data/export/multiNodes";
      }
      this.exportWin.inputData_in = this._.cloneDeep(params);
      this.exportWin.visibleTrigger_in = this._.cloneDeep(val);
    },
    updataOut(val) {},
    //台账和账单能耗导出
    CetButton_export_statusTrigger_out11(val) {
      if (!this.currentNode) {
        return;
      }
      let inputInfo = {
        aggregationCycle: this.ElSelect_3.value,
        dataEntryType: this.dataEntryType[this.ElSelect_type.value]
      };

      let nodeInfo = {
        id: this.currentNode.id,
        modelLabel: this.currentNode.modelLabel
      };

      let timeStr = "";

      switch (this.ElSelect_3.value) {
        case 12:
          inputInfo.startTime = this.$moment(this.dayInfo[0])
            .startOf("day")
            .valueOf();
          inputInfo.endTime =
            this.$moment(this.dayInfo[1]).endOf("day").valueOf() + 1;
          break;
        case 14:
          inputInfo.startTime = this.$moment(this.CetDatePicker_2.val[0])
            .startOf("month")
            .valueOf();
          inputInfo.endTime =
            this.$moment(this.CetDatePicker_2.val[1]).endOf("month").valueOf() +
            1;
          break;
        case 15:
          inputInfo.startTime = this.$moment(this.quarterTime[0])
            .startOf("quarter")
            .valueOf();
          inputInfo.endTime =
            this.$moment(this.quarterTime[1]).endOf("quarter").valueOf() + 1;

          timeStr =
            Math.floor(
              this.$moment(this.CetDateSelect_time.value.value).month() / 3
            ) + 1;
          break;
        case 17:
          inputInfo.startTime = this.dateList[0];
          inputInfo.endTime = this.dateList[1];
          break;
        default:
          break;
      }

      if (this.ElSelect_3.value !== 15) {
        let marginDate = DateConfig.find(
          item => item.id === Number(this.ElSelect_3.value)
        );
        timeStr =
          this.$moment(inputInfo.startTime).format(marginDate.format) +
          "-" +
          this.$moment(inputInfo.endTime - 1).format(marginDate.format);
      }
      let msType = this.products.map(i => i.id);
      if (this.ElSelect_type.value === "4") {
        custom
          .downloadPlanExport(this.projectId, inputInfo, nodeInfo, msType)
          .then(res => {
            let fileName = `${this.currentNode.name}-${$T(
              "计划产量"
            )}-${timeStr}`;
            this.endDownloadFile(res, fileName);
          })
          .catch(error => {
            this.endErrorMsg($T("请检查网络是否连接正常"));
          });
      } else if (this.ElSelect_type.value === "1") {
        custom
          .downloadProductExport(this.projectId, inputInfo, nodeInfo, msType)
          .then(res => {
            let fileName = `${this.currentNode.name}-${$T("产品")}-${timeStr}`;
            this.endDownloadFile(res, fileName);
          })
          .catch(error => {
            this.endErrorMsg($T("请检查网络是否连接正常"));
          });
      } else if (this.ElSelect_type.value === "5") {
        let energyTypes = this.energys.map(i => i.energytype);
        custom
          .downloadProductExport(
            this.projectId,
            inputInfo,
            nodeInfo,
            energyTypes
          )
          .then(res => {
            let fileName = `${this.currentNode.name}-${$T("能耗")}-${timeStr}`;
            this.endDownloadFile(res, fileName);
          })
          .catch(error => {
            this.endErrorMsg($T("请检查网络是否连接正常"));
          });
      } else if (this.ElSelect_type.value === "2") {
        inputInfo.cycle = this.ElSelect_3.value;
        let energyTypes = this.energys.map(i => i.energytype);
        custom
          .downloadPowerExport(this.projectId, inputInfo, nodeInfo, energyTypes)
          .then(res => {
            let fileName = `${this.currentNode.name}-${$T(
              "账单能耗"
            )}-${timeStr}`;
            this.endDownloadFile(res, fileName);
          })
          .catch(error => {
            this.endErrorMsg($T("请检查网络是否连接正常"));
          });
      } else if (this.ElSelect_type.value === "3") {
        inputInfo.cycle = this.ElSelect_3.value;
        inputInfo.objectId = nodeInfo.id;
        inputInfo.modelLabel = nodeInfo.modelLabel;
        let energyTypes = this.energys.map(i => i.energytype);
        custom
          .downloadBillExport(this.projectId, inputInfo, nodeInfo, energyTypes)
          .then(res => {
            let fileName = "";
            if (this.language) {
              fileName = this.currentNode.name + " " + this.getFileName();
            } else {
              fileName = this.currentNode.name + this.getFileName();
            }
            this.endDownloadFile(res.data, fileName);
          })
          .catch(error => {
            this.endErrorMsg($T("请检查网络是否连接正常"));
          });
      }
    },
    endErrorMsg(msg) {
      this.$message({
        message: msg,
        showClose: true,
        type: "error"
      });
    },
    endDownloadFile(data, fileNameStr) {
      const url = window.URL.createObjectURL(
        new Blob([data], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        })
      );
      const link = document.createElement("a");
      link.style.display = "none";
      link.href = url;

      let fileName = $T("导出文件");
      if (fileNameStr) {
        fileName = fileNameStr; // + //this.$moment(Date.now()).format('YYYYMM:DD:HH:mm:ss')
      }
      fileName = common.checkFileName(fileName);
      link.setAttribute("download", fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    uploadResult(res, callback) {
      if (res.code === 0) {
        if (
          this.ElSelect_type.value == "1" ||
          this.ElSelect_type.value == "4"
        ) {
          this.$message.warning(res.msg);
        } else {
          this.$message.success($T("上传成功"));
          this.getTableData();
        }
        callback();
      } else {
        this.$message({
          dangerouslyUseHTMLString: true,
          type: "error",
          message:
            "<span style='color:#f00'>" +
            res.msg.replace(/\\n/g, "<br/>") +
            "</span>"
        });
      }
    },
    uploadResultHandle(res, callback) {
      if (res.code !== 0) {
        this.showMsg("error", res.msg);
        return;
      }
      callback();
      if (res.msg) {
        this.showMsg("info", res.msg);
        this.getTableData();
      } else {
        this.$message.success($T("上传成功"));
        this.getTableData();
      }
    },
    showMsg(type, msg) {
      this.$message({
        dangerouslyUseHTMLString: true,
        type: type,
        message:
          "<span style='color:#f00'>" + msg.replace(/\\n/g, "<br/>") + "</span>"
      });
    },

    httpRequest(val, callback) {
      if (["1", "5"].includes(this.ElSelect_type.value)) {
        this.$confirm($T("是否确定上传该文件？"), $T("提示"), {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        })
          .then(() => {
            if (this.ElSelect_type.value == "1") {
              custom.importProductionData(val.file).then(res => {
                this.uploadResult(res, callback);
                this.CetButton_import.disable_in = true;
                this.CetButton_import.title = $T("导入中");
                // 导入之后消息通知提示进度
                this.$store.dispatch("importProgress/noticeProgress", {
                  vm: this,
                  initialProcessInfo: res.data,
                  cb: () => {
                    this.getTableData();
                  }
                });
                setTimeout(() => {
                  this.CetButton_import.disable_in = false;
                }, 1000);
              });
            } else {
              const params = {
                dataEntryType: this.dataEntryType[this.ElSelect_type.value]
              };
              custom
                .importMultiNodes(params, val.file, this.projectId)
                .then(res => {
                  this.uploadResult(res, callback);
                });
            }
          })
          .catch(() => {
            this.$message.info($T("已取消"));
          });
      } else if (this.ElSelect_type.value === "2") {
        this.$confirm(
          $T("文件中若包含季度、年账单能耗,则会被忽略?"),
          $T("提示"),
          {
            confirmButtonText: $T("确定"),
            cancelButtonText: $T("取消"),
            type: "warning"
          }
        )
          .then(() => {
            custom
              .powerUseImport(
                this.projectId,
                this.currentNode.id,
                this.currentNode.modelLabel,
                val.file
              )
              .then(res => {
                this.uploadResultHandle(res, callback);
              });
          })
          .catch(() => {
            this.$message.info($T("已取消"));
          });
      } else if (this.ElSelect_type.value === "3") {
        let infoList = {
          objectId: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          overwrite: this.ElSelect_4.value
        };

        this.$confirm($T("是否确定上传该文件？"), $T("提示"), {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        })
          .then(() => {
            custom
              .powerBillImport(
                this.projectId,
                this.currentNode.id,
                this.currentNode.modelLabel,
                val.file,
                infoList
              )
              .then(res => {
                this.uploadResult(res, callback);
              });
          })
          .catch(() => {
            this.$message.info($T("已取消"));
          });
      } else if (this.ElSelect_type.value === "4") {
        this.$confirm($T("是否确定上传该文件？"), $T("提示"), {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        })

          .then(() => {
            // custom
            //   .planImport("productionplan", val.file, this.projectId)
            //   .then(res => {
            //     this.uploadResult(res, callback);
            //   });
            custom.planImportAsync(val.file).then(res => {
              this.uploadResult(res, callback);
              this.CetButton_import.disable_in = true;
              this.CetButton_import.title = $T("导入中");
              // 导入之后消息通知提示进度
              this.$store.dispatch("importProgress/noticeProgress", {
                vm: this,
                initialProcessInfo: res.data,
                cb: () => {
                  this.getTableData();
                }
              });
              setTimeout(() => {
                this.CetButton_import.disable_in = false;
              }, 1000);
            });
          })
          .catch(() => {
            this.$message.info($T("已取消"));
          });
      }
    },
    CetButton_4_statusTrigger_out(val) {
      let startTime, endTime;
      switch (this.ElSelect_3.value) {
        case 12:
          startTime = this.$moment(this.dayInfo[0]).startOf("day").valueOf();
          endTime = this.$moment(this.dayInfo[1]).endOf("day").valueOf() + 1;
          break;
        case 14:
          startTime = this.$moment(this.CetDatePicker_2.val[0])
            .startOf("month")
            .valueOf();
          endTime =
            this.$moment(this.CetDatePicker_2.val[1]).endOf("month").valueOf() +
            1;
          break;
        case 15:
          startTime = this.$moment(this.quarterTime[0])
            .startOf("quarter")
            .valueOf();
          endTime =
            this.$moment(this.quarterTime[1]).endOf("quarter").valueOf() + 1;
          break;
        case 17:
          startTime = this.dateList[0];
          endTime = this.dateList[1];
          break;
        default:
          break;
      }

      let data = {};

      if (["1", "4", "5"].includes(this.ElSelect_type.value)) {
        data = {
          dataEntryType: this.dataEntryType[this.ElSelect_type.value],
          aggregationCycle: this.ElSelect_3.value,
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          startTime: startTime,
          endTime: endTime,
          dataNodes: []
        };
      } else if (this.ElSelect_type.value === "2") {
        data = {
          objectId: this.currentNode.id,
          objectLabel: this.currentNode.modelLabel,
          billDataList: []
        };
      }
      this.CetTable_1.data.forEach(item => {
        Object.keys(item).forEach(i => {
          if (String(Number(i)) !== "NaN") {
            if (item[i]) {
              item[i] = Number(item[i]);
            }
            if (
              this.ElSelect_type.value === "1" ||
              this.ElSelect_type.value === "4" ||
              this.ElSelect_type.value === "5"
            ) {
              data.dataNodes.push({
                logTime: Number(i),
                value: !item[i] && item[i] !== 0 ? null : item[i],
                id: item[`${i}_id`],
                measureType: item.measureType
              });
            } else {
              data.billDataList.push({
                aggregationcycle: this.ElSelect_3.value,
                energytype: item.energytype,
                logtime: Number(i),
                // projectid: this.projectId,
                totalusage: !item[i] && item[i] !== 0 ? null : item[i]
              });
            }
          }
        });
      });
      if (
        this.ElSelect_type.value === "1" ||
        this.ElSelect_type.value === "5"
      ) {
        this.confirm(data);
      } else if (this.ElSelect_type.value === "4") {
        this.confirm3(data);
      } else {
        this.confirm2(data);
      }
    },
    CetButton_5_statusTrigger_out(val) {
      this.editFlag = false;
      this.getTableData();
    },
    confirm(params) {
      this.editFlag = false;
      custom.saveProduct(params).then(res => {
        if (res.code === 0) {
          this.$message({
            type: "success",
            message: $T("保存成功")
          });
          this.getTableData();
        }
      });
    },
    confirm2(params) {
      this.editFlag = false;

      custom.saveBill(params).then(res => {
        if (res.code === 0) {
          this.$message({
            type: "success",
            message: $T("保存成功")
          });
          this.getTableData();
        }
      });
    },
    confirm3(params) {
      this.editFlag = false;

      custom.saveProductPlan(params).then(res => {
        if (res.code === 0) {
          this.$message({
            type: "success",
            message: $T("保存成功")
          });
          this.getTableData();
        }
      });
    },
    ElSelect_type_change_out(val) {
      const vm = this;
      if (vm.editFlag) {
        vm.$confirm($T("数据有修改,是否保存？"), $T("提示"), {
          distinguishCancelAndClose: true,
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        })
          .then(() => {
            this.CetButton_4_statusTrigger_out();
            vm.ElSelect_type.value = vm.ElSelect_type.curValue;
            vm.typeChange();
            return;
          })
          .catch(action => {
            if (action === "cancel") {
              vm.editFlag = false;
              vm.ElSelect_type.value = vm.ElSelect_type.curValue;
              vm.typeChange();
            } else {
              vm.ElSelect_type.curValue = vm.ElSelect_type.value;
            }
          });
      } else {
        vm.ElSelect_type.value = vm.ElSelect_type.curValue;
        vm.typeChange();
      }
    },
    typeChange() {
      this.CetTable_1.data = [];
      this.ElOption_3.options_in = [
        {
          id: 12,
          visible: this.ElSelect_type.value !== "2",
          text: $T("日")
        },
        {
          id: 14,
          visible: true,
          text: $T("月")
        },
        {
          id: 15,
          visible: this.ElSelect_type.value === "2",
          text: $T("季度")
        },
        {
          id: 17,
          visible: true,
          text: $T("年")
        }
      ];
      this.uploadDialog.hideDownload = true;
      this.uploadDialog.downloadPermission = null;
      if (this.ElSelect_type.value === "1") {
        this.labelname = $T("产品类型");
      } else if (this.ElSelect_type.value === "2") {
        this.labelname = $T("能源类型");
      } else if (this.ElSelect_type.value === "4") {
        // 3.5迭代中按照骆海瑞要求隐藏导入弹窗中的下载模板
        this.uploadDialog.hideDownload = true;
        this.uploadDialog.downloadPermission = "energyplan_export";
        this.labelname = $T("计划产量");
      } else if (this.ElSelect_type.value === "5") {
        this.labelname = $T("能源类型");
      }

      this.dateList = [
        this.$moment().subtract(1, "year").startOf("year").valueOf(),
        this.$moment().endOf("year").valueOf()
      ];
      this.curDateList = this.dateList;

      if (
        this.ElSelect_type.value === "1" ||
        this.ElSelect_type.value === "5"
      ) {
        this.ElSelect_3.value = 12;
        this.ElSelect_3.curValue = 12;
        this.timeTypeChange(12);
      } else {
        this.ElSelect_3.value = 14;
        this.ElSelect_3.curValue = 14;
        this.timeTypeChange(14);
      }
    },
    ElSelect_3_change_out(val) {
      if (this.editFlag) {
        this.$confirm($T("数据有修改,是否保存？"), $T("提示"), {
          distinguishCancelAndClose: true,
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        })
          .then(() => {
            this.CetButton_4_statusTrigger_out();
            this.ElSelect_3.value = this.ElSelect_3.curValue;
            this.timeTypeChange();
            return;
          })
          .catch(action => {
            if (action === "cancel") {
              this.editFlag = false;
              this.ElSelect_3.value = this.ElSelect_3.curValue;
              this.timeTypeChange();
            } else {
              this.ElSelect_3.curValue = this.ElSelect_3.value;
            }
          });
      } else {
        this.ElSelect_3.value = this.ElSelect_3.curValue;
        this.timeTypeChange();
      }
    },
    timeTypeChange(val) {
      //0,1位置的数字表示日月的偏移量，2,3位置的表示年的偏移量
      let startEndList = [-3, -1, -1, 0];
      if (this.ElSelect_type.value == "4") {
        startEndList = [0, 2, 0, 1];
      }
      if (this.ElSelect_3.value === 12) {
        this.dayInfo = [
          this.$moment().startOf("day").add(startEndList[0], "day").valueOf(),
          this.$moment().startOf("day").add(startEndList[1], "day").valueOf()
        ];
        this.curDayInfo = this.dayInfo;
      } else if (this.ElSelect_3.value === 14) {
        this.CetDatePicker_2.val = [
          this.$moment().add(startEndList[0], "month").valueOf(),
          this.$moment().add(startEndList[1], "month").valueOf()
        ];
        this.CetDatePicker_2.curVal = this.CetDatePicker_2.val;
      } else if (this.ElSelect_3.value === 15) {
        this.$set(this.CetDateSelect_time, "value", {
          dateType: "4",
          value: this.$moment().valueOf()
        });
        this.quarterTime = [
          this.$moment().startOf("quarter").valueOf(),
          this.$moment().endOf("quarter").valueOf()
        ];
      } else if (this.ElSelect_3.value === 17) {
        this.dateList = [
          this.$moment().add(startEndList[2], "year").startOf("year").valueOf(),
          this.$moment().add(startEndList[3], "year").endOf("year").valueOf()
        ];
        this.curDateList = this.dateList;
        this.$refs.basePicker.setYear(this.dateList[0], this.dateList[1]);
      }
      this.checkRefreshTreeOrGetTableData();
    },
    updateTreeNode(val) {
      // 按照张壮要求，在通用3.5迭代中，重新添加成点击弹窗处理
      if (val?.childSelectState === 2) {
        return this.$message.warning($T("没有该节点权限"));
      }
      this.currentNode = this._.cloneDeep(val);
      this.getTableData();
    },
    projectTree_treeData_out(data) {
      this.exportWin.treeData = data ?? [];
    },
    CetDateSelect_time_date_out(val) {
      this.quarterTime = val;
      this.getTableData();
    },
    CetTable_1_record_out(val) {
      if (val.id == -1) {
        this.CetButton_4.disable_in = true;
        this.CetButton_export.disable_in = true;
      } else {
        this.CetButton_4.disable_in = false;
        this.CetButton_export.disable_in = false;
      }
    },
    // 输入数字控制
    handleNum(row, key, num) {
      this.editFlag = true;
      var value;
      if (typeof row[key] === "object") {
        value = row[key].val;
      } else {
        value = row[key];
      }
      var reg = new RegExp("^(\\-)*(\\d+)\\.(\\d{0," + num + "}).*$");
      var reg2 = new RegExp("^(\\d{0,12})(\\d+)\\.(\\d{0," + num + "}).*$");
      var reg3 = new RegExp("^(\\d{0,12})(\\d+)$");
      var val = String(value);
      if (val) {
        val = val.replace(/[^\d.]/g, ""); //清除数字和'.'以外的字符
        val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", "."); //只保留顺位第一的'.'
        val = val.replace(reg, "$1$2.$3"); //只能输入两位位小数
      }
      if (val.length >= 2 && val.indexOf(".") !== 1 && val[0] == 0) {
        //在非小数的时候清除前导0
        val = val.replace(/0/, "");
      }
      if (val.length >= 2 && val.indexOf(".") === 0) {
        // 在先输入小数点时补0
        val = Number(val);
      }
      if (typeof row[key] === "object") {
        if (String(val).indexOf(".") !== -1 && Number(val) > 999999999999.99) {
          val = val.replace(reg2, "$1.$3");
        } else if (String(val).indexOf(".") === -1 && String(val).length > 12) {
          val = val.replace(reg3, "$1");
        }
        row[key].val = val;
      } else {
        if (String(val).indexOf(".") !== -1 && Number(val) > 999999999999.99) {
          val = val.replace(reg2, "$1.$3");
        } else if (String(val).indexOf(".") === -1 && String(val).length > 12) {
          val = val.replace(reg3, "$1");
        }
        row[key] = val;
      }

      if (
        this.ElSelect_type.value === "1" ||
        this.ElSelect_type.value === "4" ||
        this.ElSelect_type.value === "5"
      ) {
        this.CetTable_1.data.find(item => item.measureType == row.measureType)[
          key
        ] = row[key];
      } else {
        this.CetTable_1.data.find(item => item.energytype == row.energytype)[
          key
        ] = row[key];
      }
    },
    // 处理后端返回的单位,比如M3转成M³
    formatSymbol(symbol, type) {
      if (!symbol) {
        return "(--)";
      }
      var value = "";
      switch (symbol) {
        case "m3":
          value = "m³";
          break;
        case "M3":
          value = "m³";
          break;

        default:
          value = symbol;
          break;
      }
      if (value) {
        if (type == 1) {
          value = `(${value})`;
        }
      }
      return value;
    },
    createLocalFile(data, fileNameStr) {},
    CetButton_import_statusTrigger_out() {
      this.uploadDialog.openTrigger_in = new Date().getTime();
    },
    uploadDialog_uploadFile(val) {
      this.httpRequest(val, () => {
        this.uploadDialog.closeTrigger_in = Date.now();
      });
    },
    uploadDialog_download() {
      if (this.ElSelect_type.value === "4") {
        this.exportExcel(true);
        return;
      }
    },
    checkRefreshTreeOrGetTableData() {
      if (this.dimTreeConfigId < 0) {
        this.getTableData();
        return;
      }
      // 通知节点树刷新
      this.dimTreeRefresh = Date.now();
    },
    dimTreeConfigId_out(val) {
      this.dimTreeConfigId = val;
    }
  },
  created() {},

  mounted: function () {
    this.editFlag = false;
    this.init();
    this.refreshProjectTree = Date.now();
    Promise.all([
      new Promise((res, err) => {
        this.getProjectEnergy(res);
      }),
      new Promise((res, err) => {
        this.getProjectProduct(res);
      })
    ]).then(() => {
      const tabVal = this._.get(this.dataEntryList, "[0].name", "1");
      this.ElSelect_type.curValue = tabVal;
      this.ElSelect_type.value = tabVal;
      this.ElSelect_type_change_out();
    });
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  :deep(.cet-content-aside-container) {
    padding: 0;
    display: flex;
    flex-direction: column;
  }
}

.page-body {
  padding: 0;
  height: 100%;
}

.year-range {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 300px;
  padding: 0px;
  border-radius: 4px;
  box-sizing: border-box;
  :deep(.el-input__inner) {
    text-align: center;
  }
  :deep(input) {
    border: none;
    height: 32px;
  }
  & > div:last-child {
    :deep(.el-input__prefix) {
      display: none;
    }
  }
}

.CetDateSelect {
  :deep(.el-card.box-card.is-always-shadow) {
    right: 0 !important;
  }
  :deep(.el-select) {
    width: 100px !important;
  }
}
.title-width {
  max-width: 50%;
}
.box-border {
  box-sizing: border-box;
  :deep(.el-tabs__item) {
    height: 48px;
    line-height: 48px;
  }
  :deep(.el-tabs__item.is-top:nth-child(2)) {
    padding-left: 16px;
  }
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tab-pane) {
    border-top: 1px solid;
    @include border_color(B2);
  }
}
</style>
