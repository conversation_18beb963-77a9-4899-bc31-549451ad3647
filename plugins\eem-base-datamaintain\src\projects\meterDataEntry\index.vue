<template>
  <div class="page">
    <CetAside class="cet-aside">
      <template #aside>
        <ProjectTree
          ref="projectTree"
          @currentNode_out="updateTreeNode"
          @communicate_out="communicateChange"
          @project_node="updateProject"
          :refreshTrigger="refreshProjectTree"
        />
        <!-- 暂时注释重算 -->
        <!-- <CetButton
          class="fr mt-J1"
          v-bind="CetButton_recalculation"
          v-on="CetButton_recalculation.event"
          v-permission="'devicerepairdatalog_update'"
        ></CetButton> -->
      </template>
      <template #container>
        <div
          class="eem-min-width-mini"
          v-if="communicate !== 'virtual' && showRight"
        >
          <el-tabs
            class="eem-tabs-custom"
            v-model="ElSelect_type.curValue"
            @tab-click="ElSelect_type_change_out"
          >
            <el-tab-pane
              v-for="item in dataEntryList"
              :label="item.label"
              :name="item.id"
              :key="item.id"
            ></el-tab-pane>
          </el-tabs>
        </div>
        <div class="flex flex-col flex-auto p-J4 eem-min-width-mini">
          <template v-if="showRight">
            <div class="clearfix mb-J3">
              <div class="titleBox">
                <div
                  v-if="ElSelect_1.value === 2"
                  style="display: inline-block; line-height: 32px"
                >
                  <span class="fs16" style="font-weight: bold">
                    {{ $T("累计差值录入表") }}
                  </span>
                  <el-popover
                    class="popover ml-J1"
                    placement="bottom-start"
                    trigger="hover"
                    :content="
                      $T(
                        '累计差值录入不支持可以采集数据的表计，请勿针对此类表计录入！'
                      )
                    "
                  >
                    <i slot="reference" class="el-icon-question"></i>
                  </el-popover>
                  <span class="span_title ml-J0">{{ $T("录入规则") }}</span>
                </div>
                <div
                  v-if="ElSelect_1.value !== 2"
                  style="display: inline-block; line-height: 32px"
                >
                  <span class="fs16" style="font-weight: bold">
                    {{ $T("表码值") }}
                  </span>
                  <!-- <el-popover
                  class="popover ml-J1"
                  placement="bottom-start"
                  trigger="hover"
                  v-if="ElSelect_type.curValue == 31"
                  :content="$T('换表时间选择为更新新表之后的第一个点')"
                >
                  <i slot="reference" class="el-icon-question"></i>
                </el-popover> -->
                  <el-tooltip
                    effect="light"
                    placement="bottom"
                    popper-class="tooltip_img"
                    v-if="ElSelect_type.curValue == 31"
                    :popper-class="
                      themeLight
                        ? 'meterDataEntry_tooltip_light'
                        : 'meterDataEntry_tooltip_dark'
                    "
                  >
                    <div slot="content">
                      <img
                        :src="dynamicImg"
                        alt=""
                        style="height: 300px; width: 450px"
                      />
                    </div>
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  <span
                    class="span_title ml-J0"
                    v-if="ElSelect_type.curValue == 31"
                  >
                    {{ $T("换表操作帮助") }}
                  </span>
                </div>
              </div>
              <div class="btnBox">
                <el-dropdown
                  class="ml-J3 more fr"
                  @command="handleCommand"
                  v-if="ElSelect_1.value === 1"
                >
                  <span class="el-dropdown-link">
                    {{ $T("更多") }}
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      command="import"
                      :disabled="!CetButton_edit.visible_in"
                      v-permission="'devicerepairdatalog_update'"
                    >
                      {{ $T("导入") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      command="export"
                      :disabled="!CetButton_edit.visible_in"
                    >
                      {{ $T("导出") }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <CetButton
                  class="fr ml-J1"
                  v-bind="CetButton_save"
                  v-on="CetButton_save.event"
                ></CetButton>
                <CetButton
                  class="fr ml-J1"
                  v-bind="CetButton_cancel"
                  v-on="CetButton_cancel.event"
                ></CetButton>
                <CetButton
                  class="fr ml-J1"
                  v-if="ElSelect_1.value === 31"
                  v-bind="CetButton_changeRecord"
                  v-on="CetButton_changeRecord.event"
                ></CetButton>
                <CetButton
                  class="fr ml-J1"
                  v-if="
                    ElSelect_1.value !== 31 &&
                    $checkPermission('devicerepairdatalog_update')
                  "
                  v-bind="CetButton_edit"
                  v-on="CetButton_edit.event"
                ></CetButton>
                <CetButton
                  class="fr ml-J1"
                  v-if="ElSelect_1.value === 2"
                  v-bind="CetButton_export"
                  v-on="CetButton_export.event"
                ></CetButton>
                <CetButton
                  class="fr ml-J1"
                  v-if="
                    ElSelect_1.value === 2 &&
                    $checkPermission('devicerepairdatalog_update')
                  "
                  v-show="showUpload"
                  v-bind="CetButton_import"
                  v-on="CetButton_import.event"
                ></CetButton>
                <CetButton
                  class="fr ml-J1"
                  v-if="ElSelect_1.value === 1 && CetButton_edit.visible_in"
                  v-bind="CetButton_preview"
                  v-on="CetButton_preview.event"
                ></CetButton>
                <CetButton
                  class="fr ml-J1"
                  v-if="false"
                  v-bind="CetButton_add"
                  v-on="CetButton_add.event"
                ></CetButton>
                <CetButton
                  class="fr ml-J0 custom—square"
                  v-bind="CetButton_next"
                  v-on="CetButton_next.event"
                  :disable_in="disbackToTime"
                  v-if="ElSelect_1.value === 2"
                ></CetButton>
                <div class="year-range fr">
                  <el-date-picker
                    ref="dayPicker"
                    v-model="dayInfo"
                    type="daterange"
                    unlink-panels
                    v-if="tabTimeValue === 12"
                    @change="timeChanged"
                    :clearable="false"
                    :pickerOptions="CetDatePicker_2.pickerOptions"
                  ></el-date-picker>
                </div>
                <el-date-picker
                  ref="seasonPicker"
                  class="fr"
                  v-if="tabTimeValue === 14"
                  v-model="CetDatePicker_2.val"
                  v-bind="CetDatePicker_2.config"
                  @change="timeChanged"
                  :pickerOptions="CetDatePicker_2.pickerOptions"
                ></el-date-picker>
                <CetButton
                  class="fr custom—square mr-J0"
                  v-bind="CetButton_prv"
                  v-on="CetButton_prv.event"
                  v-if="ElSelect_1.value === 2"
                ></CetButton>
                <!-- <customElSelect
                v-model="ElSelect_3.value"
                v-bind="ElSelect_3"
                v-on="ElSelect_3.event"
                v-if="ElSelect_1.value !== 1"
                class="fr mr-J1"
                prefix_in="查询时段"
              >
                <ElOption
                  v-for="item in ElOption_3.options_in"
                  v-show="item.visible"
                  :key="item[ElOption_3.key]"
                  :label="item[ElOption_3.label]"
                  :value="item[ElOption_3.value]"
                  :disabled="item[ElOption_3.disabled]"
                ></ElOption>
              </customElSelect> -->
                <el-radio-group
                  v-model="tabTimeValue"
                  @change="tabRadioChange"
                  class="fr mr-J1"
                  v-if="ElSelect_1.value === 2"
                >
                  <el-radio-button :label="14">
                    {{ $T("月") }}
                  </el-radio-button>
                  <el-radio-button :label="12">
                    {{ $T("日") }}
                  </el-radio-button>
                </el-radio-group>
                <customElSelect
                  v-model="ElSelect_1.value"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                  v-show="false"
                  class="fr mr-J1 groupSelect"
                  :prefix_in="$T('录入类型')"
                >
                  <el-option-group
                    v-for="group in ElOption_1.options_in"
                    :key="group[ElOption_1.key]"
                    :label="group[ElOption_1.label]"
                    :value="group[ElOption_1.value]"
                    :disabled="group[ElOption_1.disabled]"
                  >
                    <el-option
                      v-for="item in group.options"
                      :key="item[ElOption_1.key]"
                      :label="item[ElOption_1.label]"
                      :value="item[ElOption_1.value]"
                      :disabled="item[ElOption_1.disabled]"
                    ></el-option>
                  </el-option-group>
                </customElSelect>
                <customElSelect
                  v-model="ElSelect_2.value"
                  v-bind="ElSelect_2"
                  v-on="ElSelect_2.event"
                  class="fr mr-J1"
                  :prefix_in="$T('数据类型')"
                >
                  <ElOption
                    v-for="item in ElOption_2.options_in"
                    :key="item[ElOption_2.key]"
                    :label="item[ElOption_2.label]"
                    :value="item[ElOption_2.value]"
                    :disabled="item[ElOption_2.disabled]"
                  ></ElOption>
                </customElSelect>
              </div>
            </div>
            <div class="flex-auto clearfix">
              <CetTable
                :data.sync="CetTable_1.data"
                :dynamicInput.sync="CetTable_1.dynamicInput"
                v-bind="CetTable_1"
                v-on="CetTable_1.event"
                style="height: calc(100% - 32px)"
                :cell-style="tableCellStyle"
                v-if="ElSelect_1.value !== 31"
              >
                <el-table-column
                  type="index"
                  :label="$T('序号')"
                  :fixed="true"
                  align="left"
                  :width="62"
                ></el-table-column>
                <el-table-column
                  prop="logtime"
                  :fixed="true"
                  align="left"
                  :label="$T('时间')"
                  minWidth="180"
                  :formatter="timeFormat"
                ></el-table-column>
                <template v-for="(item, index) in columnArr">
                  <ElTableColumn
                    v-if="ElSelect_1.value === 2"
                    :key="index"
                    v-bind="item"
                    align="left"
                  >
                    <template slot-scope="scope">
                      <div v-if="!CetButton_edit.visible_in">
                        <el-input
                          v-model="scope.row[item.prop]"
                          :disabled="!scope.row.edit"
                          :placeholder="$T('请输入')"
                          @keyup.native="handleNum(scope.row, item.prop, 2)"
                        ></el-input>
                      </div>
                      <div v-else>
                        {{ valueFormat2(scope.row[item.prop]) }}
                      </div>
                    </template>
                  </ElTableColumn>
                  <ElTableColumn
                    :key="index"
                    v-bind="item"
                    align="left"
                    v-if="ElSelect_1.value === 1 && item.children"
                  >
                    <template v-for="col in item.children">
                      <ElTableColumn :key="col.key" v-bind="col">
                        <template slot-scope="scope">
                          <div v-if="col.showInput">
                            <div v-if="!CetButton_edit.visible_in">
                              <el-input
                                :style="setStyle(scope)"
                                v-model="scope.row[col.prop]"
                                :disabled="!scope.row.edit"
                                :placeholder="$T('请输入')"
                                @keyup.native="
                                  handleNum(scope.row, col.prop, 2)
                                "
                              ></el-input>
                            </div>
                            <div v-else>
                              {{ valueFormat2(scope.row[col.prop]) }}
                            </div>
                          </div>
                          <div v-else>
                            {{ valueFormat(scope.row[col.prop]) }}
                          </div>
                        </template>
                      </ElTableColumn>
                    </template>
                  </ElTableColumn>
                </template>
              </CetTable>
              <div
                class="text-right mt-J0"
                style="height: 32px"
                v-if="ElSelect_1.value !== 31"
              >
                <el-pagination
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage"
                  :page-sizes="[10, 20, 50, 100]"
                  :page-size="pageSize"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="totalCount"
                ></el-pagination>
              </div>
              <div
                class="fullheight"
                v-if="ElSelect_1.value === 31 && dialogShow"
              >
                <manualChangeChart
                  v-bind="manualChangeChart"
                  @buttomClick="buttomClick"
                ></manualChangeChart>
              </div>
            </div>
          </template>
          <div v-if="!showRight" class="text-center" style="margin-top: 80px">
            <span class="fs22">{{ showLeftName }}</span>
          </div>
        </div>
      </template>
    </CetAside>
    <exportWin
      :visibleTrigger_in="exportWin.visibleTrigger_in"
      :closeTrigger_in="exportWin.closeTrigger_in"
      :inputData_in="exportWin.inputData_in"
      :connected="exportWin.connected"
    />
    <addMeter
      :visibleTrigger_in="addMeter.visibleTrigger_in"
      :closeTrigger_in="addMeter.closeTrigger_in"
      :inputData_in="addMeter.inputData_in"
      :columnArr="addMeter.meterColumnArr"
      :showData="addMeter.showData"
      @updata_out="updata_out"
    ></addMeter>
    <recalculation
      v-bind="recalculation"
      @updataReCalcState_out="updataReCalcState_out"
    ></recalculation>
    <UploadDialog
      v-bind="uploadDialog"
      v-on="uploadDialog.event"
    ></UploadDialog>
    <dataPreview v-bind="dataPreview"></dataPreview>
    <changeRecord
      v-bind="changeRecord"
      @delete_sucessful="delete_sucessful"
    ></changeRecord>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import ProjectTree from "./subcomponents/ProjectTree.vue";
import moment from "moment";
import exportWin from "./subcomponents/exportWin.vue";
import addMeter from "./subcomponents/add.vue";
import recalculation from "./subcomponents/recalculation.vue";
import UploadDialog from "eem-base/components/uploadDialog";
import dataPreview from "./subcomponents/dataPreview.vue";
import manualChangeChart from "./subcomponents/manualChangeChart.vue";
import changeRecord from "./subcomponents/changeRecord.vue";
import { httping } from "@omega/http";
import omegaTheme from "@omega/theme";

import { api } from "@altair/knight";

export default {
  name: "meterConsumptionDataEntry",
  components: {
    ProjectTree,
    exportWin,
    addMeter,
    recalculation,
    UploadDialog,
    dataPreview,
    manualChangeChart,
    changeRecord
  },
  computed: {
    dynamicImg() {
      const isEnglish = window.localStorage.getItem("omega_language") === "en";
      const isLightTheme =
        window.localStorage.getItem("omega_theme") === "light";

      const language = isEnglish ? "en" : "zh";
      const theme = isLightTheme ? "dark" : "light";

      return require(`./assets/${language}/meterDataEntry_${theme}.png`);
    },
    themeLight() {
      return window.localStorage.getItem("omega_theme") === "light";
    },
    projectId() {
      return this.$store.state.projectId;
    },
    // 判断时间范围右键是否禁用
    disbackToTime() {
      if (this.tabTimeValue === 12) {
        let nowDate = this.$moment().startOf("day").valueOf();
        return nowDate <= this.dayInfo[1];
      } else if (this.tabTimeValue === 14) {
        let nowDate = this.$moment().startOf("month").valueOf();
        return nowDate <= this.CetDatePicker_2.val[1];
      }
      return "";
    },
    // 判断表格输入框是否正确验证
    setStyle() {
      return function (data) {
        let row = data.row;
        let column = data.column;
        if (row.logicalValues.length) {
          let id = column.property.split("_")[1];
          let name = "datalogValue_" + id;
          if (
            row[column.property] !== null &&
            row[name] !== null &&
            row[column.property] != row[name]
          ) {
            if (omegaTheme.theme === "dark") {
              return "border: 1px solid #ff842b;border-radius: 4px;";
            } else {
              return "border: 1px solid #FCB92C;border-radius: 4px;";
            }
          }
        }
        return;
      };
    }
  },
  data() {
    return {
      sideWidth: "315px",
      // 树节点刷新
      refreshProjectTree: Date.now(),
      // 左侧树节点获取节点数据
      currentNode: null,
      // 按照天数选择信息
      dayInfo: [],
      // 控制右侧表格显示
      showRight: false,
      // 判断表单数据是否进行更改
      editFlag: false,
      // 数据不存在时标题展示
      showLeftName: $T("请选择表计设备"),
      // 是否展示导入
      showUpload: true,
      // 表头数据
      columnArr: [],
      CetTable_1: {
        //组件模式设置项
        queryMode: "", //查询按钮触发  trigger  ,或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          //   record_out: this.CetTable_1_record_out
        }
      },
      ElSelect_1: {
        value: 2,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_3: {
        value: 12,
        style: {
          width: "160px"
        },
        event: {
          change: this.ElSelect_3_change_out
        }
      },
      ElOption_3: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetButton_edit: {
        visible_in: true,
        disable_in: false,
        title: $T("编辑"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_edit_statusTrigger_out
        }
      },
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      CetButton_import: {
        visible_in: true,
        disable_in: false,
        title: $T("导入"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_import_statusTrigger_out
        }
      },
      CetButton_recalculation: {
        visible_in: true,
        disable_in: false,
        title: $T("重算"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_recalculation_statusTrigger_out
        }
      },
      CetDatePicker_2: {
        disable_in: false,
        val: [],
        config: {
          valueFormat: "timestamp",
          type: "monthrange",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          clearable: false,
          style: {
            width: "300px"
          }
        },
        pickerOptions: {
          // 限制预约时间
          disabledDate(time) {
            let timeStr = moment(new Date()).startOf("day").valueOf();
            return time.getTime() > timeStr;
          }
        }
      },
      CetButton_save: {
        visible_in: false,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_save_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: false,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_changeRecord: {
        visible_in: true,
        disable_in: false,
        title: $T("换表清单"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_changeRecord_statusTrigger_out
        }
      },
      CetButton_add: {
        visible_in: false,
        disable_in: false,
        title: $T("换表"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      CetButton_preview: {
        visible_in: true,
        disable_in: false,
        title: $T("数据预览"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_preview_statusTrigger_out
        }
      },
      // 导出按钮的弹窗
      exportWin: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        connected: null
      },
      // 录入类型固定数据
      typeofEntryData: [
        {
          text: $T("手动设值"),
          options: [
            {
              id: 2,
              text: $T("累计差值")
            },
            // 由于后端在表码值上有点问题，根据曹雪莹那边要求先对表码值进行隐藏
            {
              id: 1,
              text: $T("表码值")
            }
          ]
        },
        {
          text: $T("手动换表"),
          options: [
            {
              id: 31,
              text: $T("手动换表")
            }
          ]
        }
      ],
      // 查询时段数据
      inquiryPeriodData: [
        {
          id: 12,
          visible: true,
          text: $T("日")
        },
        {
          id: 14,
          visible: true,
          text: $T("月")
        }
      ],
      // 表格数据录入
      tableData: [],
      // 数据查询
      params: {
        deviceid: null,
        dataid: null,
        startTime: null,
        endTime: null,
        status: 30,
        meterType: 2,
        aggregationcycle: 12,
        page: {
          index: 0,
          limit: 100
        }
      },
      // 换表
      addMeter: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        meterColumnArr: null,
        showData: null
      },
      // 重算
      recalculation: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      // 项目节点信息
      projectNode: {
        objectId: 1,
        objectLabel: "project"
      },
      // 分页标签
      currentPage: 1,
      pageSize: 100,
      totalCount: 0,
      uploadDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        extensionNameList_in: [".xlsx", ".xls"],
        hideDownload: true,
        dialogTitle: $T("导入数据"),
        maxFlinkCount: 1000,
        event: {
          download: this.uploadDialog_download,
          uploadFile: this.uploadDialog_uploadFile
        }
      },
      communicate: "virtual",
      tabTimeValue: "12",
      // 物理表计tab页面切换
      ElSelect_type: {
        curValue: "1", //便于处理确认取消操作
        value: "1",
        style: {
          width: "150px"
        },
        event: {
          change: this.ElSelect_type_change_out
        }
      },
      dataEntryList: [
        {
          id: "1",
          label: $T("数据补录")
        },
        {
          id: "31",
          label: $T("手动换表")
        }
      ],
      // 数据预览弹窗
      dataPreview: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      // 换表页面
      manualChangeChart: {
        paramData: null
      },
      // 换表记录弹窗
      changeRecord: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      // 时间范围左右按钮
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      CetButton_next: {
        visible_in: true,
        // disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      // 用于存储节点树上一次点击的节点数据
      lastClickNodeData: null,
      // 用于判断弹窗是否点击的是关闭按钮
      closeConfim: false,
      // 用于判断弹窗是否存在
      dialogClose: true,
      // 用于判断手动换表是否进行展示
      dialogShow: true
    };
  },
  methods: {
    init() {
      const removeId = this.communicate === "virtual" ? 1 : 2;
      let options = this._.cloneDeep(this.typeofEntryData);
      options[0].options = options[0].options.filter(i => i.id !== removeId);
      this.ElOption_1.options_in = options;
      if (this.ElSelect_1.value === removeId) {
        this.ElSelect_1.value = this._.get(options, "[0].options[0].id", 2);
      }

      this.ElOption_3.options_in = this._.cloneDeep(this.inquiryPeriodData);
      // this.ElSelect_3.value = this._.get(this.inquiryPeriodData, "[0].id", 12);
      this.tabTimeValue = this._.get(this.inquiryPeriodData, "[0].id", 12);
      this.timeInit();
    },
    timeInit() {
      if (this.ElSelect_1.value === 2) {
        this.dayInfo = [
          this.$moment().subtract(30, "day").startOf("day").valueOf(),
          this.$moment().endOf("day").valueOf()
        ];
      } else {
        this.dayInfo = [
          this.$moment().startOf("day").valueOf(),
          this.$moment().endOf("day").valueOf()
        ];
      }
      this.CetDatePicker_2.val = [
        this.$moment().startOf("month").valueOf(),
        this.$moment().endOf("month").valueOf()
      ];
    },
    // 时间值校验
    timeFormat(row, column, cellValue, index) {
      let timeString;
      if (this.ElSelect_1.value === 2) {
        // timeString = this.ElSelect_3.value === 12 ? "YYYY-MM-DD" : "YYYY-MM";
        timeString = this.tabTimeValue === 12 ? "YYYY-MM-DD" : "YYYY-MM";
      } else {
        timeString = "YYYY-MM-DD HH:mm:ss";
      }
      return this.$moment(cellValue).format(timeString);
    },
    valueFormat(cellValue) {
      return cellValue != null ? cellValue.toFixed(2) : "--";
    },
    valueFormat2(cellValue) {
      return cellValue != null ? cellValue : "--";
    },
    CetButton_export_statusTrigger_out(val) {
      let param = {
        startTime: this.params.startTime,
        endTime: this.params.endTime,
        status: this.params.status,
        meterType: this.params.meterType,
        aggregationcycle: this.params.aggregationcycle,
        dataid: this.params.dataid
      };
      this.exportWin.inputData_in = this._.cloneDeep(param);
      this.exportWin.connected = this.communicate === "virtual" ? false : true;
      this.exportWin.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 进行编辑操作
    CetButton_edit_statusTrigger_out() {
      this.CetButton_edit.visible_in = false;
      this.CetButton_save.visible_in = true;
      this.CetButton_cancel.visible_in = true;
      this.CetButton_export.disable_in = true;
      this.CetButton_import.disable_in = true;
    },
    // 时间控件选择时间触发
    timeChanged() {
      if (!this.judgeTimeSection()) {
        return;
      }
      this.currentPage = 1;
      this.params.page.index = 0;
      this.isEditFlag();
    },
    // 表格中输入数字控制
    handleNum(row, key, num) {
      this.editFlag = true;
      var value;
      if (typeof row[key] === "object") {
        value = row[key].val;
      } else {
        value = row[key];
      }
      var reg = new RegExp("^(\\-)*(\\d+)\\.(\\d{0," + num + "}).*$");
      var reg2 = new RegExp("^(\\d{0,11})(\\d+)\\.(\\d{0," + num + "}).*$");
      var reg3 = new RegExp("^(\\d{0,11})(\\d+)$");
      var val = String(value);
      if (val) {
        val = val.replace(/[^\d.]/g, ""); //清除数字和'.'以外的字符
        val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", "."); //只保留顺位第一的'.'
        val = val.replace(reg, "$1$2.$3"); //只能输入两位位小数
      }
      if (val.length >= 2 && val.indexOf(".") !== 1 && val[0] == 0) {
        //在非小数的时候清除前导0
        val = val.replace(/0/, "");
      }
      if (val.length >= 2 && val.indexOf(".") === 0) {
        // 在先输入小数点时补0
        val = Number(val);
      }
      if (typeof row[key] === "object") {
        if (String(val).indexOf(".") !== -1 && Number(val) > 99999999999.99) {
          val = val.replace(reg2, "$1.$3");
        } else if (String(val).indexOf(".") === -1 && String(val).length > 11) {
          val = val.replace(reg3, "$1");
        }
        row[key].val = val;
      } else {
        if (String(val).indexOf(".") !== -1 && Number(val) > 99999999999.99) {
          val = val.replace(reg2, "$1.$3");
        } else if (String(val).indexOf(".") === -1 && String(val).length > 11) {
          val = val.replace(reg3, "$1");
        }
        row[key] = val;
      }
      this.CetTable_1.data.find(item => item.logtime == row.logtime)[key] =
        row[key];
    },
    // 树节点选中之后更新
    updateTreeNode(val) {
      if (_.isEmpty(val)) {
        this.showRight = false;
        this.showLeftName = $T("请选择表计设备");
        return;
      }
      if (this.CetButton_edit.visible_in) {
        this.lastClickNodeData = this._.cloneDeep(val);
      }
      const callback = () => {
        this.currentNode = this._.cloneDeep(val);
        if (val.nodeType === 269619472) {
          this.params.deviceid = [val.nodeId];
          this.getDataType(val.nodeId);
          this.init();
          this.currentPage = 1;
          this.params.page.index = 0;
        } else {
          this.showRight = false;
          this.showLeftName = $T("请选择表计设备");
        }
      };
      this.isEditFlag("node", callback);
      // this.currentNode = this._.cloneDeep(val);
      //   if (val.nodeType === 269619472) {
      //     this.params.deviceid = [val.nodeId];
      //     this.getDataType(val.nodeId);
      //     this.init();
      //   } else {
      //     this.showRight = false;
      //     this.showLeftName = "请选择表计设备";
      //   }
    },
    // 获取数据类型的数据
    getDataType(deviceId) {
      customApi.getPoints(deviceId).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.ElOption_2.options_in = this._.cloneDeep(data);
          if (data.length == 0) {
            this.showRight = false;
            this.showLeftName = $T(
              "当前设备无正向有功电能、反向有功电能或者能源累计值定时记录测点，请在PecConfig中配置定时记录映射方案！"
            );
          } else {
            this.ElSelect_2.value = data[0].id;
            this.getTableData();
            this.showRight = true;
          }
        }
      });
    },
    // 查询时段数据切换
    tabRadioChange(val) {
      // this.ElSelect_3.value = val;
      this.tabTimeValue = val;
      if (val === 12) {
        this.CetDatePicker_2.val = [
          this.$moment().startOf("month").valueOf(),
          this.$moment().endOf("month").valueOf()
        ];
      } else if (val === 14) {
        if (this.ElSelect_1.value === 2) {
          this.dayInfo = [
            this.$moment().subtract(30, "day").startOf("day").valueOf(),
            this.$moment().endOf("day").valueOf()
          ];
        } else {
          this.dayInfo = [
            this.$moment().startOf("day").valueOf(),
            this.$moment().endOf("day").valueOf()
          ];
        }
      }
      this.currentPage = 1;
      this.params.page.index = 0;
      this.isEditFlag();
    },
    // 对时间控制选择的时间进行判断是否合规
    judgeTimeSection() {
      let flag = true;
      switch (this.tabTimeValue) {
        case 12:
          if (this.ElSelect_1.value === 1) {
            if (
              this.$moment(this.dayInfo[0]).add(6, "day").valueOf() <
              this.$moment(this.dayInfo[1]).valueOf()
            ) {
              this.$message({
                type: "warning",
                message: $T("起止时间不能相差超过7天")
              });

              this.dayInfo[0] = this.$moment(this.dayInfo[1])
                .subtract(6, "day")
                .valueOf();
            }
          } else if (this.ElSelect_1.value === 31) {
            if (
              this.$moment(this.dayInfo[0]).add(2, "day").valueOf() <
              this.$moment(this.dayInfo[1]).valueOf()
            ) {
              this.$message({
                type: "warning",
                message: $T("起止时间不能相差超过3天")
              });

              this.dayInfo[0] = this.$moment(this.dayInfo[1])
                .subtract(2, "day")
                .valueOf();
            }
          } else {
            if (
              this.$moment(this.dayInfo[0]).add(93, "day").valueOf() <
              this.$moment(this.dayInfo[1]).valueOf()
            ) {
              this.$message({
                type: "warning",
                message: $T("起止时间不能相差超过1个季度")
              });

              this.dayInfo[0] = this.$moment(this.dayInfo[1])
                .subtract(93, "day")
                .valueOf();
            }
          }
          break;
        case 14:
          if (
            this.$moment(this.CetDatePicker_2.val[0])
              .add(11, "month")
              .startOf("month")
              .valueOf() <
            this.$moment(this.CetDatePicker_2.val[1]).startOf("month").valueOf()
          ) {
            this.$message({
              type: "warning",
              message: $T("起止时间不能相差超过12个月")
            });
            this.CetDatePicker_2.val[0] = this.$moment(
              this.CetDatePicker_2.val[1]
            )
              .startOf("month")
              .subtract(11, "month")
              .valueOf();
          }
          break;
        default:
          break;
      }
      return flag;
    },
    // 录入类型数据切换
    ElSelect_1_change_out(val) {
      this.CetButton_add.visible_in = false;
      if (val === 1) {
        // this.ElSelect_3.value = 12;
        this.tabTimeValue = 12;
      } else if (val === 31) {
        this.CetButton_add.visible_in = true;
      }
      this.timeInit();
      this.currentPage = 1;
      this.params.page.index = 0;
      this.isEditFlag();
    },
    // 数据类型切换
    ElSelect_2_change_out(val) {
      this.currentPage = 1;
      this.params.page.index = 0;
      this.isEditFlag();
    },
    getParams() {
      this.params.dataid = this.ElSelect_2.value;
      this.params.meterType = this.ElSelect_1.value;
      this.params.status = this.ElSelect_1.value === 31 ? 31 : 30;
      // this.params.aggregationcycle = this.ElSelect_3.value;
      this.params.aggregationcycle = this.tabTimeValue;
      switch (this.tabTimeValue) {
        case 12:
          this.params.startTime = this.$moment(this.dayInfo[0])
            .startOf("day")
            .valueOf();
          this.params.endTime =
            this.$moment(this.dayInfo[1]).endOf("day").valueOf() + 1;
          break;
        case 14:
          this.params.startTime = this.$moment(this.CetDatePicker_2.val[0])
            .startOf("month")
            .valueOf();
          this.params.endTime =
            this.$moment(this.CetDatePicker_2.val[1]).endOf("month").valueOf() +
            1;
          break;
        default:
          break;
      }
    },
    // 获取表格查询数据
    getTableData() {
      this.getParams();
      let param = this.params;
      if (this.ElSelect_1.value === 31) {
        let paramData = this._.cloneDeep(param);
        delete paramData.page;
        this.manualChangeChart.paramData = paramData;
      } else {
        customApi.getFlinkDatalogData(param).then(response => {
          if (response.code === 0) {
            var data = this._.get(response, "data", []);
            data.forEach(item => {
              item.logicalValues.forEach(i => {
                item["value_" + i.logicalid] =
                  i.value === null ? null : i.value.toFixed(2);
                item["datalogValue_" + i.logicalid] = i.datalogValue;
              });
            });
            let columnArr = [];
            if (data && data.length) {
              data[0].logicalValues.forEach(i => {
                if (this.ElSelect_1.value === 1) {
                  // columnArr.push({
                  //   prop: "datalogValue_" + i.logicalid,
                  //   label: "实际数据",
                  //   headerAlign: "left",
                  //   align: "left",
                  //   showOverflowTooltip: true,
                  //   minWidth: 120
                  // });
                  columnArr.push({
                    label: $T("回路{0}", i.logicalid),
                    headerAlign: "left",
                    align: "left",
                    children: [
                      {
                        prop: "datalogValue_" + i.logicalid,
                        label: $T("实际数据"),
                        key: $T("实际数据") + i.logicalid,
                        headerAlign: "left",
                        align: "left",
                        showOverflowTooltip: true,
                        minWidth: 120
                      },
                      {
                        prop: "value_" + i.logicalid,
                        label: $T("录入数据"),
                        key: $T("录入数据") + i.logicalid,
                        headerAlign: "left",
                        align: "left",
                        showOverflowTooltip: true,
                        minWidth: 160,
                        showInput: true
                      }
                    ]
                  });
                } else {
                  columnArr.push({
                    prop: "value_" + i.logicalid,
                    label: this.getHeadText(i.logicalid),
                    headerAlign: "left",
                    align: "left",
                    showOverflowTooltip: true,
                    minWidth: 160,
                    showInput: true
                  });
                }
              });
            }
            this.columnArr = columnArr;
            this.totalCount = response.total;
            this.findSmallerData(data);
            this.CetTable_1.data = this._.cloneDeep(data);
          }
          this.editFlag = false;
        });
      }
    },
    // 表格头部名称
    getHeadText(iText) {
      return this.ElSelect_1.value === 2
        ? $T("回路{0}累计值差值", iText)
        : $T("回路{0}录入数据", iText);
    },
    // 换表数据
    CetButton_add_statusTrigger_out() {
      this.addMeter.visibleTrigger_in = new Date().getTime();
      this.addMeter.meterColumnArr = this._.cloneDeep(this.columnArr);
      let param = {
        deviceid: this.params.deviceid[0],
        dataid: this.params.dataid,
        status: this.params.status,
        meterType: this.params.meterType
      };
      this.addMeter.inputData_in = this._.cloneDeep(param);
    },
    // 保存按钮
    CetButton_save_statusTrigger_out() {
      let oldData = this._.cloneDeep(this.CetTable_1.data);
      const callback = () => {
        oldData.forEach(item => {
          item.logicalValues.forEach(i => {
            i.value = item["value_" + i.logicalid];
            item.delete = this.isEmpty(item["value_" + i.logicalid], 1);
            delete item["value_" + i.logicalid];
          });
        });
        let data = this._.cloneDeep(oldData);
        this.editFlag = false;
        customApi.getFlinkEdit(data).then(response => {
          if (response.code === 0) {
            this.$message({
              type: "success",
              message: $T("保存成功")
            });
            this.getTableData();
          }
        });
        this.cancelEdit();
      };
      if (this.ElSelect_1.value === 1) {
        this.disOrdering(oldData, callback);
        return;
      } else {
        callback();
      }
      // this.CetTable_1.data.forEach(item => {
      //   item.logicalValues.forEach(i => {
      //     i.value = item["value_" + i.logicalid];
      //     delete item["value_" + i.logicalid];
      //   });
      // });
      // let data = this._.cloneDeep(this.CetTable_1.data);
      // this.editFlag = false;
      // customApi.getFlinkEdit(data).then(response => {
      //   if (response.code === 0) {
      //     this.$message({
      //       type: "success",
      //       message: $T("保存成功")
      //     });
      //     this.getTableData();
      //   }
      // });
    },
    // 判断数值是否为空
    isEmpty(val, id) {
      if (typeof val === "string" && val.length === 0 && id === 1) {
        return true;
      }
      return false;
    },
    // 取消按钮
    CetButton_cancel_statusTrigger_out() {
      if (this.editFlag) {
        this.$confirm($T("是否取消数据保存?"), $T("提示"), {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("关闭"),
          type: "warning"
        })
          .then(() => {
            this.editFlag = false;
            this.isEditFlag();
            this.$message({
              type: "success",
              message: $T("取消成功!")
            });
            this.cancelEdit();
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: $T("已关闭取消")
            });
          });
      } else {
        this.$message({
          type: "info",
          message: $T("暂未有数据进行了修改")
        });
        this.cancelEdit();
      }
    },
    // 检查表格数据是否存在修改
    isEditFlag(type, callback) {
      const vm = this;
      // 判断是否进行了关闭按钮取消跳转处理
      if (vm.closeConfim) {
        vm.closeConfim = false;
        return;
      }
      if (vm.editFlag) {
        // if (!vm.dialogClose) {
        //   return;
        // }
        vm.dialogClose = false;
        vm.dialogShow = false;
        vm.$confirm($T("数据有修改,是否保存？"), $T("提示"), {
          distinguishCancelAndClose: true,
          closeOnClickModal: false,
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          showClose: type === "node",
          type: "warning"
        })
          .then(() => {
            vm.dialogClose = true;
            vm.dialogShow = true;
            this.CetButton_save_statusTrigger_out(); //TODO保存和读取有冲突
            if (typeof callback === "function") {
              callback();
            } else {
              vm.getTableData();
            }
            return;
          })
          .catch(action => {
            vm.dialogClose = true;
            vm.dialogShow = true;
            if (action === "cancel") {
              vm.editFlag = false;
              vm.cancelEdit();
              if (typeof callback === "function") {
                callback();
              } else {
                vm.getTableData();
              }
            } else if (action === "close") {
              this.closeConfim = true;
              if (type === "node") {
                this.currentNode = this._.cloneDeep(this.lastClickNodeData);
                this.$refs.projectTree.getSelectNode(this.lastClickNodeData);
              } else if (type === "communicate") {
                this.$refs.projectTree.getTabValue(this.communicate);
                this.currentNode = this._.cloneDeep(this.lastClickNodeData);
                this.$refs.projectTree.getSelectNode(this.lastClickNodeData);
              }
            }
          });
      } else {
        if (typeof callback === "function") {
          if (!vm.CetButton_edit.visible_in) {
            this.$message({
              type: "info",
              message: $T("暂未有数据进行了修改")
            });
            vm.cancelEdit();
          }
          callback();
        } else {
          vm.getTableData();
        }
      }
    },
    // 取消编辑之后的按钮变化
    cancelEdit() {
      this.CetButton_edit.visible_in = true;
      this.CetButton_save.visible_in = false;
      this.CetButton_cancel.visible_in = false;
      this.CetButton_export.disable_in = false;
      this.CetButton_import.disable_in = false;
    },
    updata_out() {
      this.getTableData();
    },
    // 分段操作
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.params.page.index = 0;
      this.params.page.limit = val;
      this.getTableData();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.params.page.index = (val - 1) * this.pageSize;
      this.getTableData();
    },
    // 是否导入判断
    CetButton_import_statusTrigger_out() {
      this.uploadDialog.openTrigger_in = new Date().getTime();
    },
    // 导入成功后重新请求
    refresh() {
      this.getTableData();
    },
    updateProject(val) {
      this.projectNode.objectId = this.projectId;
    },
    CetButton_recalculation_statusTrigger_out(val) {
      this.recalculation.visibleTrigger_in = this._.cloneDeep(val);
      this.recalculation.inputData_in = this._.cloneDeep(this.projectNode);
    },
    // 获取当前项目的重算状态并展示进度值
    getReCalcState() {
      customApi.getEnergyReCalcState(this.projectId).then(response => {
        if (response.code === 0) {
          if (response.data && response.data.state === 2) {
            let num = response.data.ratio.toFixed(1);
            this.CetButton_recalculation.disable_in = true;
            this.CetButton_recalculation.title = `${$T("重算中（{0}%）", num)}`;
          } else {
            this.CetButton_recalculation.disable_in = false;
            this.CetButton_recalculation.title = $T("重算");
          }
        }
      });
    },
    // 更新项目的重算状态
    updataReCalcState_out() {
      this.CetButton_recalculation.disable_in = true;
      setTimeout(() => {
        this.getReCalcState();
      }, 2500);
    },
    // 进行判断是否存在错序的问题
    disOrdering(num, callback) {
      let arr = [];
      let bool = true;
      if (num && num[0].logicalValues && num[0].logicalValues.length) {
        num[0].logicalValues.forEach(key => {
          num.forEach(item => {
            if (item["value_" + key.logicalid]) {
              arr.push(item["value_" + key.logicalid]);
            }
          });
          if (!this.isSomeArray(arr)) {
            bool = false;
            return;
          }
        });
      }
      if (!bool) {
        this.$confirm(
          $T("存在表码值时间越后的值比前面时间的值更小的问题，是否继续保存?"),
          $T("提示"),
          {
            confirmButtonText: $T("确定"),
            cancelButtonText: $T("关闭"),
            type: "warning"
          }
        )
          .then(() => {
            typeof callback === "function" && callback();
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: $T("已取消保存")
            });
          });
      } else {
        typeof callback === "function" && callback();
      }
    },
    // 判断两个数组是否相同
    isSomeArray(arr) {
      let b = this._.cloneDeep(arr);
      arr.sort((a, b) => a - b);
      return b.join("") === arr.join("") ? true : false;
    },
    uploadDialog_uploadFile(val) {
      const formData = new FormData();
      let meterType = this.params.meterType;
      formData.append("file", val.file);
      httping({
        url: "/eembasedatamaintain/flink/datalog/import?meterType=" + meterType,
        method: "POST",
        data: formData
      }).then(response => {
        if (response.code === 0) {
          this.$message({
            message: `${$T("上传成功")} ${response.data || ""}`,
            type: "success"
          });
          this.uploadDialog.closeTrigger_in = Date.now();
          this.getTableData();
        } else {
          this.$message({
            type: "error",
            message: response.msg
          });
        }
      });
    },
    uploadDialog_download() {},
    // 通讯类型选择
    communicateChange(val) {
      // this.isEditFlag();
      const callback = () => {
        this.communicate = val;
        this.ElSelect_1.value = this.communicate === "virtual" ? 2 : 1;
        this.ElSelect_type.curValue =
          this.communicate === "virtual" ? "31" : "1";
      };
      this.isEditFlag("communicate", callback);
    },
    // tab页面切换
    ElSelect_type_change_out() {
      this.ElSelect_1.value = this.ElSelect_type.curValue == 31 ? 31 : 1;
      this.ElSelect_1_change_out(this.ElSelect_1.value);
    },
    // 更多
    handleCommand(command) {
      switch (command) {
        case "import":
          this.CetButton_import_statusTrigger_out();
          break;
        case "export":
          this.CetButton_export_statusTrigger_out(new Date().getTime());
          break;
        default:
          return;
      }
    },
    // 数据预览
    CetButton_preview_statusTrigger_out() {
      this.dataPreview.openTrigger_in = new Date().getTime();
      this.dataPreview.inputData_in = this._.cloneDeep(this.CetTable_1.data);
    },
    // 物理表计中实际数据和录入数据不一致进行颜色区分
    tableCellStyle({ row, column, rowIndex, columnIndex }) {
      // console.log(row, column, rowIndex, columnIndex, "row");
      if (
        (column.label === $T("录入数据") || column.label === "录入数据") &&
        this.ElSelect_1.value === 1 &&
        this.CetButton_edit.visible_in
      ) {
        if (row.logicalValues.length) {
          let id = column.property.split("_")[1];
          let name = "datalogValue_" + id;
          if (
            row[column.property] !== null &&
            row[name] !== null &&
            row[column.property] != row[name]
          ) {
            if (!_.isEmpty(row.test) && row.test[column.property]) {
              return omegaTheme.theme === "light"
                ? "background: #FFF8EA;color: red"
                : "background: #262544;color: red";
            } else {
              return omegaTheme.theme === "light"
                ? "background: #FFF8EA;"
                : "background: #262544;";
            }
          }
          if (!_.isEmpty(row.test) && row.test[column.property]) {
            return "color: red";
          }
        }
      }
      return;
    },
    // 进行换表记录
    CetButton_changeRecord_statusTrigger_out() {
      this.changeRecord.openTrigger_in = new Date().getTime();
      this.changeRecord.inputData_in = this._.cloneDeep(this.params);
    },
    buttomClick(data) {
      this.addMeter.showData = this._.cloneDeep(data);
      this.CetButton_add_statusTrigger_out();
    },
    // 删除成功
    delete_sucessful() {
      this.getTableData();
    },
    // 处理表格数据，找出时间后面的值比前面的值小的数据
    findSmallerData(data) {
      let startNum = [];
      if (data.length) {
        data[0].logicalValues.forEach((item, index) => {
          startNum[index] = null;
        });
      }
      for (let i = 1; i < data.length; i++) {
        data[i].test = {};
        data[i].logicalValues.forEach((item, index) => {
          let name = "value_" + item.logicalid;
          startNum[index] = data[i - 1][name]
            ? Number(data[i - 1][name])
            : startNum[index];
          if (
            data[i][name] !== null &&
            startNum[index] !== null &&
            Number(data[i][name]) < startNum[index]
          ) {
            data[i].test[name] = true;
          }
          if (data[i][name] !== null) {
            startNum[index] = Number(data[i][name]);
          }
        });
      }
    },
    // 左边按钮
    CetButton_prv_statusTrigger_out() {
      // let date = this.CetDatePicker_time.val;
      // this.CetDatePicker_time.val = [
      //   this.$moment(date[0]).subtract(1, "d").valueOf(),
      //   this.$moment(date[1]).subtract(1, "d").valueOf()
      // ];
      if (this.tabTimeValue === 12) {
        let date = this.dayInfo;
        if (
          this.$moment(date[0]).valueOf() == this.$moment(date[1]).valueOf()
        ) {
          this.dayInfo = [
            this.$moment(date[0]).subtract(1, "d").valueOf(),
            this.$moment(date[1]).subtract(1, "d").valueOf()
          ];
        } else {
          const timeDiff = this.$moment(date[1]).diff(
            this.$moment(date[0]),
            "days"
          );
          this.dayInfo = [
            this.$moment(date[0]).subtract(timeDiff, "d").valueOf(),
            date[0]
          ];
        }
      } else if (this.tabTimeValue === 14) {
        let date = this.CetDatePicker_2.val;
        if (
          this.$moment(date[0]).startOf("month").valueOf() ==
          this.$moment(date[1]).startOf("month").valueOf()
        ) {
          this.CetDatePicker_2.val = [
            this.$moment(date[0]).subtract(1, "month").valueOf(),
            this.$moment(date[1]).subtract(1, "month").valueOf()
          ];
        } else {
          const timeDiff = this.$moment(date[1]).diff(
            this.$moment(date[0]),
            "month"
          );
          this.CetDatePicker_2.val = [
            this.$moment(date[0]).subtract(timeDiff, "month").valueOf(),
            date[0]
          ];
        }
      }
      this.timeChanged();
      // this.getTableData();
    },
    CetButton_next_statusTrigger_out() {
      // this.timeChanged();
      // let date = this.CetDatePicker_time.val;
      // this.CetDatePicker_time.val = [
      //   this.$moment(date[0]).add(1, "d").valueOf(),
      //   this.$moment(date[1]).add(1, "d").valueOf()
      // ];
      if (this.tabTimeValue === 12) {
        let date = this.dayInfo;
        if (
          this.$moment(date[0]).valueOf() == this.$moment(date[1]).valueOf()
        ) {
          this.dayInfo = [
            this.$moment(date[0]).add(1, "d").valueOf(),
            this.$moment(date[1]).add(1, "d").valueOf()
          ];
        } else {
          const timeDiff = this.$moment(date[1]).diff(
            this.$moment(date[0]),
            "days"
          );
          let lastDate = this.$moment(date[1]).add(timeDiff, "d").valueOf();
          if (lastDate > this.$moment().endOf("day").valueOf()) {
            lastDate = this.$moment().endOf("day").valueOf();
          }
          this.dayInfo = [date[1], lastDate];
        }
      } else if (this.tabTimeValue === 14) {
        let date = this.CetDatePicker_2.val;
        if (
          this.$moment(date[0]).startOf("month").valueOf() ==
          this.$moment(date[1]).startOf("month").valueOf()
        ) {
          this.CetDatePicker_2.val = [
            this.$moment(date[0]).add(1, "month").valueOf(),
            this.$moment(date[1]).add(1, "month").valueOf()
          ];
        } else {
          const timeDiff = this.$moment(date[1]).diff(
            this.$moment(date[0]),
            "month"
          );
          let lastDate = this.$moment(date[1]).add(timeDiff, "month").valueOf();
          if (lastDate > this.$moment().endOf("month").valueOf()) {
            lastDate = this.$moment().endOf("month").valueOf();
          }
          this.CetDatePicker_2.val = [date[1], lastDate];
        }
      }
      this.timeChanged();
      // this.getTableData();
    },
    // 获取导入数据数据配置
    getFlinkCount() {
      customApi.getFlinkMaxExportCount().then(response => {
        if (response.code === 0) {
          this.uploadDialog.maxFlinkCount =
            this._.get(response, "data.maxCount", 1000) || 1000;
        }
      });
    }
  },

  mounted: function () {
    this.editFlag = false;
    this.showRight = false;
    this.closeConfim = false;
    this.dialogClose = true;
    this.showLeftName = $T("请选择表计设备");
    this.CetButton_add.visible_in = false;
    this.CetButton_recalculation.disable_in = true;
    this.refreshProjectTree = Date.now();
    this.init();
    this.getFlinkCount();
    // 定时器 暂时注释
    // this.getReCalcState();
    // this.setInterval = setInterval(() => {
    //   this.getReCalcState();
    // }, 60000);
  },
  destroyed: function () {
    // 关闭定时器
    clearInterval(this.setInterval);
  },
  deactivated: function () {
    // 关闭定时器
    clearInterval(this.setInterval);
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  :deep(.cet-content-aside-container) {
    display: flex;
    flex-direction: column;
    @include background_color(BG1);
    padding: 0;
  }
}

.page-body {
  padding: 0;
  height: 100%;
}

.upload {
  :deep .el-upload-list {
    display: none;
    font-size: 0;
  }
}

.year-range {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 300px;
  padding: 0px;
  border-radius: 4px;
  box-sizing: border-box;
  :deep .el-input__inner {
    text-align: center;
  }
  :deep input {
    border: none;
    height: 32px;
  }
  & > div:last-child {
    :deep .el-input__prefix {
      display: none;
    }
  }
}

.CetDateSelect {
  :deep .el-card.box-card.is-always-shadow {
    right: 0 !important;
  }
  :deep .el-select {
    width: 100px !important;
  }
}
.title-width {
  max-width: 50%;
}
.span_title {
  font-size: 14px;
  @include font_color("T3");
}
.more {
  line-height: 32px;
}
.eem-tabs-custom {
  :deep(.el-tabs__item) {
    height: 48px;
    line-height: 48px;
  }
  :deep(.el-tabs__item.is-top:nth-child(2)) {
    padding-left: 16px;
  }
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tab-pane) {
    border-top: 1px solid;
    @include border_color(B2);
  }
}
</style>
<style lang="scss">
.tooltip_img {
  padding: 0 !important;
}
.el-tooltip__popper.meterDataEntry_tooltip_light {
  background: #353535 !important;
}
.el-tooltip__popper.meterDataEntry_tooltip_dark {
  background: #fff !important;
}
</style>
