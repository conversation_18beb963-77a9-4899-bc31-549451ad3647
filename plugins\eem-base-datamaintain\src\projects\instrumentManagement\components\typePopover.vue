<template>
  <div class="customHeader" style="display: inline-block">
    <el-popover
      placement="bottom-start"
      trigger="click"
      :visible-arrow="false"
      :ref="`popover-${columnIndex}`"
      popper-class="type-popover"
      @show="handlerShow"
    >
      <!-- table表头文字-->
      <span slot="reference" class="flex items-center">
        <span :class="{ fcZS: initValue || initValue === false }" class="mr-J0">
          {{ column.alias }}
        </span>
        <omega-icon
          :symbolId="getIconId()"
          class="cursor-pointer"
          :class="getIconId()"
        />
      </span>
      <div>
        <!-- 测量对象使用级联选择面板 -->
        <el-cascader-panel
          v-if="column.name == 'monitorname' || column.name == 'deviceid'"
          v-model="value"
          :props="column.name == 'monitorname' ? simpleProps : props"
          :options="column.name == 'monitorname' ? simpleList : options"
          :border="false"
          @expand-change="handlerExpandChange"
        >
          <template slot-scope="{ node, data }">
            <el-tooltip
              :content="data.text || data.name"
              placement="top-start"
              :disabled="(data.text || data.name)?.length < 12"
            >
              <span>{{ data.text || data.name }}</span>
            </el-tooltip>
          </template>
        </el-cascader-panel>
        <ElInput
          v-else-if="column.datatype == 'string'"
          v-model.trim="value"
          suffix-icon="el-icon-search"
          v-bind="ElInput_string"
          v-on="ElInput_string.event"
          class="filter"
        ></ElInput>
        <ElInputNumber
          v-else-if="
            ['int4', 'int8'].includes(column.datatype) &&
            column.name !== 'plannedverificationcycle'
          "
          v-model="value"
          suffix-icon="el-icon-search"
          v-bind="ElInputNumber_num1"
          v-on="ElInputNumber_num1.event"
          class="filter"
        ></ElInputNumber>
        <div
          v-else-if="
            column.datatype === 'int4' &&
            column.name == 'plannedverificationcycle'
          "
        >
          <ElInputNumber
            v-model="value"
            suffix-icon="el-icon-search"
            v-bind="ElInputNumber_num1"
            v-on="ElInputNumber_num1.event"
            :min="1"
            class="filter"
          ></ElInputNumber>
          <el-select
            v-model="verificationcycle"
            style="width: 64px"
            class="cycleSelect"
            clearable
            placeholder=""
          >
            <ElOption
              v-for="item in cycleList"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            ></ElOption>
          </el-select>
        </div>
        <ElInputNumber
          v-else-if="column.datatype == 'float'"
          v-model="value"
          suffix-icon="el-icon-search"
          v-bind="ElInputNumber_num2"
          v-on="ElInputNumber_num2.event"
          class="filter"
        ></ElInputNumber>
        <datePanel :value.sync="value" v-else-if="column.datatype == 'date'" />
        <singleChoiceList
          v-else-if="column.datatype == 'boolean'"
          :value.sync="value"
        />
        <singleChoiceList
          v-else-if="
            (column.datatype == 'enum' ||
              (column.enumerationvalue && column.enumerationvalue.length)) &&
            column.name !== 'metertype'
          "
          :value.sync="value"
          :type="2"
          :options="column.enumerationvalue"
        />
        <el-cascader-panel
          v-if="column.datatype == 'enum' && column.name === 'metertype'"
          clearable
          v-model="value"
          :props="metertypeProps"
          :options="metertypeOptions"
          collapse-tags
        ></el-cascader-panel>
      </div>
      <div
        class="flex justify-end btns"
        :class="{ 'cascader-btns': column.name == 'monitorname' }"
      >
        <el-button type="text" size="medium" class="fcT3" @click="handlerReset">
          {{ $T("重置") }}
        </el-button>
        <el-button
          type="text"
          size="medium"
          class="mr-J3"
          @click="handlerQuery"
        >
          {{ $T("查询") }}
        </el-button>
      </div>
    </el-popover>
  </div>
</template>
<script>
import common from "eem-base/utils/common.js";
import datePanel from "./datePanel.vue";
import singleChoiceList from "./singleChoiceList.vue";

export default {
  components: { datePanel, singleChoiceList },
  props: {
    column: {
      type: Object
    },
    columnIndex: {
      type: Number
    },
    condition: {
      type: [String, Array, Number, Boolean]
    },
    monitorOptions: {
      type: Array
    },
    searchCycleValue: {
      type: Number
    },
    metertypeOptions: {
      type: Array,
      default: () => []
    },
    simpleList: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    condition: {
      handler: function (val) {
        this.initValue = _.cloneDeep(val);
        this.value = val;
      },
      immediate: true
    },
    monitorOptions: {
      handler: function (val) {
        this.options = val;
      },
      immediate: true
    }
  },
  computed: {
    isSearch() {
      return (
        this.column.name !== "monitorname" &&
        ["string", "int4", "int8", "float"].includes(this.column.datatype)
      );
    }
  },
  data() {
    return {
      props: {
        label: "text",
        value: "id",
        children: "children",
        multiple: true
      },
      simpleProps: {
        label: "name",
        value: "id",
        children: "children",
        multiple: true
      },
      options: null,
      value: undefined,
      initValue: undefined,
      ElInput_string: {
        value: "",
        placeholder: $T("请输入"),
        style: {
          width: "164px"
        },
        event: {}
      },
      ElInputNumber_num1: {
        ...common.check_numberInt,
        value: "",
        controls: false,
        placeholder: $T("请输入"),
        style: {
          width: "164px"
        },
        event: {}
      },
      ElInputNumber_num2: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        placeholder: $T("请输入"),
        style: {
          width: "164px"
        },
        event: {}
      },
      cycleList: [
        {
          id: 12,
          text: $T("天")
        },
        {
          id: 14,
          text: $T("月")
        },
        {
          id: 17,
          text: $T("年")
        }
      ],
      verificationcycle: undefined,
      metertypeProps: {
        expandTrigger: "hover",
        children: "child",
        multiple: true,
        label: "text",
        value: "id"
      }
    };
  },
  methods: {
    getIconId() {
      return this.initValue || this.initValue === false
        ? "grid-filter-lin"
        : this.isSearch
        ? "search-lin"
        : "arrow-right-lin";
    },
    //显示弹出框时重置数据
    handlerShow() {
      this.value = this.condition;
      if (this.searchCycleValue) {
        this.verificationcycle = this.searchCycleValue;
      }
    },
    handlerExpandChange() {
      //级联面板展开节点变化,重新计算位置
      this.$nextTick(() => {
        const name = `popover-${this.columnIndex}`;
        this.$refs[name].updatePopper();
      });
    },
    handlerReset() {
      this.value = undefined;
      //级联面板展开节点变化,重新计算位置
      if (this.column.name == "monitorname") {
        this.$nextTick(() => {
          const name = `popover-${this.columnIndex}`;
          this.$refs[name].updatePopper();
        });
      }
      if (this.column.name == "plannedverificationcycle") {
        this.verificationcycle = undefined;
      }
    },
    handlerQuery() {
      if (this.column.name == "plannedverificationcycle") {
        const params = {
          verificationcycle: this.verificationcycle,
          plannedverificationcycle: this.value
        };
        this.$emit("queryPlannedverificationcycle", params);
        if (this.verificationcycle) {
          this.$emit("searchCycle", this.verificationcycle);
        }
        return;
      }
      if (this.column.name == "monitorname") {
        const formattedNodeList =
          this.findMatchedObjects(
            this._.cloneDeep(this.value),
            this.simpleList
          ) || [];
        this.$emit("query", "monitorNodeList", formattedNodeList);
      }
      this.$emit("query", this.column.name, this.value);
      const name = `popover-${this.columnIndex}`;
      this.$refs[name].doClose();
    },
    findMatchedObjects(array, responseData) {
      if (!array?.length || !responseData?.length) return null;
      const matchedObjects = [];
      const idsToMatch = array.map(item => item[item.length - 1]);
      for (const id of idsToMatch) {
        let found = false;
        function traverse(nodes) {
          for (const node of nodes) {
            if (node.id === id) {
              matchedObjects.push(node);
              found = true;
              return;
            }
            if (node.children && Array.isArray(node.children)) {
              traverse(node.children);
              if (found) return;
            }
          }
        }
        traverse(responseData);
        found = false;
      }
      return matchedObjects;
    }
  }
};
</script>
<style lang="scss" scoped>
.filter {
  margin: 8px 8px 0;
}
.btns {
  margin: 10px 8px 6px;
}
.cascader-btns {
  margin: 6px 8px;
}
.grid-filter-lin {
  @include font_color(ZS);
}
.arrow-right-lin {
  transform: rotate(90deg);
}
.cycleSelect {
  position: absolute;
  right: 9px;
  top: 9px;
  :deep(.el-input__inner) {
    border: 0;
    height: 30px;
    line-height: 30px;
    @include background_color(BG);
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
</style>
<style lang="scss">
.type-popover {
  padding: 0;
  .el-button + .el-button {
    margin-left: 40px;
  }
  .el-cascader-panel {
    border-bottom: 1px solid;
    @include border_color(B2);
  }
  .el-cascader-node__label {
    max-width: 180px;
  }
}
</style>
