<template>
  <div class="notifyContent">
    <div style="font-size: 16px; font-weight: bold" v-if="!finalProgressData">
      <i
        class="el-icon-upload"
        style="font-size: 24px; color: var(--Sta4); margin-right: 8px"
      ></i>
      {{ deleteFlag ? $T("删除中") : $T("生成中") }}
    </div>
    <div class="importResult" v-if="finalProgressData">
      <i
        class="el-icon-circle-check"
        style="font-size: 24px; color: var(--Sta1)"
        v-if="processStatus === 3"
      ></i>
      <i
        class="el-icon-circle-close"
        style="font-size: 24px; color: var(--Sta3)"
        v-else
      ></i>
      <span style="font-size: 16px; font-weight: bold; margin-left: 8px">
        {{ importResultText }}
      </span>
    </div>
    <el-tooltip effect="light" :content="fileName" placement="bottom">
      <div
        style="
          width: 289px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        "
      >
        {{ fileName || "--" }}
      </div>
    </el-tooltip>
    <el-progress
      :percentage="percentage"
      :status="finalProgressData && processStatus !== 3 ? 'exception' : null"
    ></el-progress>
    <div class="text-right" v-if="!hideShowLog">
      <el-button type="text" @click="goRouter" :disabled="!finalProgressData">
        {{ $T("查看日志") }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { noticeGoRouter } from "eem-base/utils/notice/noticeRouter.js";
import { store } from "@omega/app";
import { api } from "@altair/knight";

export default {
  props: {
    type: Number,
    vm: Object,
    deleteFlag: Boolean,
    hideShowLog: Boolean,
    cb: Function
  },
  computed: {
    processData() {
      const processList = store.state?.importProgress?.processList ?? [];
      const data = processList.find(i => i.type === this.type);
      return data;
    },
    showProgressData() {
      return this.finalProgressData || this.processData;
    },
    processStatus() {
      return this.showProgressData?.processInfo?.status;
    },
    importResultText() {
      if (this.finalProgressData.processInfo.status === 3)
        return this.deleteFlag ? $T("删除成功") : $T("生成成功");
      return this.deleteFlag ? $T("删除失败") : $T("生成失败");
    },
    fileName() {
      return this.showProgressData?.processInfo?.name;
    },
    percentage() {
      const ratio = this.showProgressData?.processInfo?.ratio ?? 0;
      return Math.floor(ratio * 100);
    }
  },
  data() {
    return {
      finalProgressData: null
    };
  },
  watch: {
    processStatus: {
      handler: function (newVal, oldVal) {
        if (newVal !== 2 && oldVal === 2 && !this.finalProgressData) {
          // 导入状态由导入中变化为其他时，将现在的进度和导入结果另存起来，不再读store里面的数据
          this.finalProgressData = this._.cloneDeep(this.processData);
          this.cb?.(this._.cloneDeep(this.processData));
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    goRouter() {
      // 处于平台页面则不进行跳转
      if (api.projectMode() === "PlatInPlat") {
        this.$message($T("请先进入项目之后再跳转"));
        return;
      }
      noticeGoRouter(this.vm, this.finalProgressData);
    }
  }
};
</script>

<style lang="scss" scoped>
.notifyContent {
  width: 270px;
}
</style>
<style lang="scss">
.custom-import-progress {
  z-index: 9999 !important;
}
</style>
