<template>
  <div class="h-full w-full flex flex-col">
    <div class="flex flex-row mb-J3 justify-between">
      <div class="flex flex-row items-center">
        <el-button
          size="small"
          icon="el-icon-arrow-left"
          @click="yearQueryPrv"
          class="mr-J0"
        ></el-button>
        <CustomElDatePicker
          class="yearPicker"
          :prefix_in="$T('时间')"
          v-model="yearTime"
          type="year"
          size="small"
          value-format="timestamp"
          :placeholder="$T('选择日期')"
          :clearable="false"
          :format="$T('yyyy 年')"
          @change="yearTimeChange"
        ></CustomElDatePicker>
        <el-button
          size="small"
          icon="el-icon-arrow-right"
          @click="yearQueryNext"
          class="ml-J0"
        ></el-button>
      </div>
      <div class="flex flex-row justify-end">
        <el-button
          size="small"
          icon="el-icon-arrow-left"
          @click="monthQueryPrv"
          class="mr-J0"
        ></el-button>
        <CustomElDatePicker
          :prefix_in="$T('时间')"
          v-model="monthTime"
          type="month"
          size="small"
          value-format="timestamp"
          :placeholder="$T('选择日期')"
          :clearable="false"
          :format="$T('yyyy 年 MM 月')"
          @change="monthTimeChange"
          popperClass="energyAlarmConfig_limitationCurrentYear"
        ></CustomElDatePicker>
        <el-button
          size="small"
          icon="el-icon-arrow-right"
          @click="monthQueryNext"
          class="ml-J0"
        ></el-button>
      </div>
    </div>
    <div class="flex flex-row flex-auto">
      <div class="w-[250px] mr-J3 flex flex-col">
        <div
          class="flex flex-row items-center border rounded-Ra border-B1 border-solid"
        >
          <span class="w-[70px] text-center p-J0">
            {{ $T("年度") }}
          </span>
          <span
            class="p-J0 box-border text-center flex-auto border-l border-B1 border-solid h-full border-t-0 border-r-0 border-b-0"
          >
            {{ yearConfigValue }}
          </span>
        </div>
        <el-table
          class="flex-auto mt-J3 month-table"
          :data="monthData"
          highlight-current-row
          border
          height="true"
        >
          <el-table-column
            prop="key1"
            :label="$T('时间')"
            align="left"
            width="80"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="key2"
            align="left"
            :label="$T('能效阈值')"
            show-overflow-tooltip
            :formatter="val => (val.key2 != null ? val.key2.toFixed(2) : '--')"
          ></el-table-column>
        </el-table>
      </div>
      <div class="flex-auto flex flex-col">
        <div class="flex-auto flex flex-row">
          <div class="w-[210px] mr-J3">
            <el-table
              class="h-full week-table"
              :data="weekTableData"
              border
              height="true"
            >
              <el-table-column
                prop="key1"
                :label="$T('周数')"
                align="left"
                width="70"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="key2"
                align="left"
                :label="$T('周能效阈值')"
                show-overflow-tooltip
                :formatter="
                  val => (val.key2 != null ? val.key2.toFixed(2) : '--')
                "
              ></el-table-column>
            </el-table>
          </div>
          <div class="flex-auto overflow-auto">
            <EditMonth
              v-bind="editMonth"
              :data_in="benchmarksetConfig"
              :disabled="true"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import EditMonth from "./EditMonth.vue";
export default {
  name: "aiAlarmConfig",
  props: {
    data_in: Object,
    selectYearTime: Number,
    initDate_in: Number
  },
  components: {
    EditMonth
  },
  computed: {
    benchmarksetConfig() {
      return this.data_in?.benchmarkset_model ?? [];
    }
  },
  data() {
    const initMonthData = [
      {
        key1: $T("1月"),
        key2: null
      },
      {
        key1: $T("2月"),
        key2: null
      },
      {
        key1: $T("3月"),
        key2: null
      },
      {
        key1: $T("4月"),
        key2: null
      },
      {
        key1: $T("5月"),
        key2: null
      },
      {
        key1: $T("6月"),
        key2: null
      },
      {
        key1: $T("7月"),
        key2: null
      },
      {
        key1: $T("8月"),
        key2: null
      },
      {
        key1: $T("9月"),
        key2: null
      },
      {
        key1: $T("10月"),
        key2: null
      },
      {
        key1: $T("11月"),
        key2: null
      },
      {
        key1: $T("12月"),
        key2: null
      }
    ];
    return {
      yearTime: +this.$moment(),
      monthTime: +this.$moment(),
      yearConfigValue: "",
      initMonthData: initMonthData,
      monthData: this._.cloneDeep(initMonthData),
      weekTableData: [],
      calendarValue: Date.now(),
      editMonth: {
        data: [],
        date: Date.now(),
        update_in: Date.now()
      }
    };
  },
  watch: {
    data_in: {
      handler(val) {
        this.initData(val);
      },
      deep: true,
      immediate: true
    },
    initDate_in: {
      handler() {
        this.initDate();
      }
    }
  },
  methods: {
    initDate() {
      this.yearTime = +this.$moment();
      this.monthTime = +this.$moment();
      this.yearTimeChange();
    },
    initData() {
      this.yearDataHandle();
      this.monthTimeChange();

      this.editMonth.update_in = Date.now();
    },
    yearQueryPrv() {
      const value = this.$moment(this.yearTime);
      this.yearTime = +value.subtract(1, "years");
      this.yearTimeChange();
    },
    yearQueryNext() {
      const value = this.$moment(this.yearTime);
      this.yearTime = +value.add(1, "years");
      this.yearTimeChange();
    },
    monthQueryPrv() {
      const value = this.$moment(this.monthTime);

      const year = this.$moment(this.yearTime).year();
      this.monthTime = +value.subtract(1, "months").year(year);
      this.monthTimeChange();
    },
    monthQueryNext() {
      const value = this.$moment(this.monthTime);

      const year = this.$moment(this.yearTime).year();
      this.monthTime = +value.add(1, "months").year(year);
      this.monthTimeChange();
    },
    yearTimeChange() {
      this.$emit("update:selectYearTime", this.yearTime);
      this.$emit("yearTimeChange", this.yearTime);

      const value = this.$moment(this.monthTime);
      const year = this.$moment(this.yearTime).year();
      this.monthTime = +value.year(year);
    },
    yearDataHandle() {
      const year = +this.$moment(this.yearTime).startOf("year");

      // 处理年数据
      const yearConfig = this.benchmarksetConfig.find(
        item => item.validtime === year
      );
      this.yearConfigValue =
        yearConfig?.limitvalue != null ? yearConfig.limitvalue : "--";

      // 处理月数据
      this.monthData = this._.cloneDeep(this.initMonthData);
      this.benchmarksetConfig.forEach(
        ({ aggregationcycle, validtime, limitvalue }) => {
          if (
            +this.$moment(validtime).startOf("year") === year &&
            aggregationcycle === 14
          ) {
            this.monthData[this.$moment(validtime).month()].key2 = limitvalue;
          }
        }
      );
    },
    monthTimeChange() {
      this.$emit("monthTimeChange", this.monthTime);
      // 处理周数据
      const startDay = this.$moment(this.monthTime)
        .startOf("month")
        .day(1)
        .startOf("date");
      const month = this.$moment(this.monthTime).month();
      const startTime = +this.$moment(startDay);
      const startWeekConfig = this.benchmarksetConfig.find(
        ({ validtime, aggregationcycle }) => {
          return validtime === startTime && aggregationcycle === 13;
        }
      );
      const weekTableData = [
        {
          key1: startDay.week(),
          key2: startWeekConfig?.limitvalue ?? null,
          time: startTime
        }
      ];
      while (startDay.add("days", 7).month() == month) {
        const time = +this.$moment(startDay);
        const weekConfig = this.benchmarksetConfig.find(
          ({ validtime, aggregationcycle }) => {
            return validtime === time && aggregationcycle === 13;
          }
        );
        weekTableData.push({
          key1: this.$moment(startDay).week(),
          key2: weekConfig?.limitvalue ?? null,
          time
        });
      }
      this.weekTableData = weekTableData;

      this.editMonth.date = this.monthTime;
    },
    getSaveData() {
      return { benchmarkset_model: this.benchmarksetConfig };
    },
    defaultBenchmarkset() {
      const benchmarkset = [];
      // 自动默认传当前时间，周期传年，默认上限
      let date = this.$moment(this.yearTime).startOf("year");

      benchmarkset.push({
        validtime: +this.$moment(this.yearTime).startOf("year"),
        aggregationcycle: 17,
        limittype: 1
      });

      // 月
      for (var i = 0; i < 12; i++) {
        benchmarkset.push({
          validtime: +this.$moment(this.yearTime).month(i).startOf("month"),
          aggregationcycle: 14,
          limittype: 1
        });
      }

      // 日
      while (this.$moment(date).year() <= this.$moment(this.yearTime).year()) {
        benchmarkset.push({
          validtime: +this.$moment(date),
          aggregationcycle: 12,
          limittype: 1
        });
        date = this.$moment(date).add(1, "day");
      }

      return benchmarkset;
    }
  }
};
</script>

<style lang="scss" scoped>
.yearPicker :deep(.el-date-editor.el-input) {
  width: 112px;
}
.calendar :deep(.el-calendar__header) {
  display: none;
}
.month-table :deep(.el-table__body-wrapper .el-table__cell) {
  height: 30px;
}
.week-table :deep(.el-table__body-wrapper .el-table__cell) {
  height: 80px;
}
</style>

<style>
.energyAlarmConfig_limitationCurrentYear .el-date-picker__header {
  display: none;
}
</style>
