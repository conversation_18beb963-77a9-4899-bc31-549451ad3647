span
<template>
  <div>
    <CetDialog class="CetDialog" v-bind="CetDialog_1" v-on="CetDialog_1.event">
      <div class="eem-cont-c1">
        <CetChart v-bind="CetChart_1" style="height: 450px"></CetChart>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom";
import { themeMap } from "cet-chart";
import omegaTheme from "@omega/theme";

export default {
  name: "dataPreview",
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Array
    }
  },
  data(vm) {
    return {
      // 设置组件唯一识别字段弹窗组件
      CetDialog_1: {
        title: $T("数据预览"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        width: "960px",
        showClose: true,
        // 遮盖层操作,将自身置于最外层展示
        "append-to-body": true
      },
      // confirm组件
      CetButton_confirm: {
        visible_in: false,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      // cancel组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // echart图表配置
      // 1组件
      CetChart_1: {
        //组件输入项
        inputData_in: null,
        options: {
          //图表配置
          tooltip: {
            trigger: "axis",
            formatter: function (params) {
              let dataStr = "";
              if (params && params.length) {
                dataStr = params[0].axisValue + "<br/>";
                params.forEach((item, index) => {
                  const serieType = item.data.serieType;
                  if (serieType === 1) {
                    dataStr +=
                      "<span style='display:inline-block;vertical-align:middle;margin-right:10px;width:15px;height:2px;background-color:" +
                      item.color +
                      ";'></span>" +
                      item.seriesName +
                      ":" +
                      "<span style='margin-left: 8px'>" +
                      item.value +
                      "</span><br/>";
                  } else {
                    dataStr +=
                      "<span style='display:inline-block;width:15px;height:1px;margin-right:10px;border-bottom:2px dashed;border-color:" +
                      item.color +
                      ";'></span>" +
                      item.seriesName +
                      ":" +
                      "<span style='margin-left: 8px'>" +
                      item.value +
                      "</span><br/>";
                  }
                });
              }
              return dataStr;
            }
          },
          legend: {
            type: "scroll",
            itemStyle: {
              // 去除图例上的圆圈
              opacity: 0
            }
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "10%",
            containLabel: true
          },
          xAxis: {
            type: "category",
            // 刻度与标签文本对齐
            axisTick: {
              alignWithLabel: true
            },
            data: []
          },
          yAxis: {
            name: $T("数据"),
            type: "value",
            scale: true,
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed" // y轴分割线类型
              }
            }
          },
          // 下面区间滑块
          dataZoom: [
            {
              xAxisIndex: 0,
              type: "slider",
              bottom: "10",
              height: "20",
              start: 0,
              end: 100
            }
          ],
          series: []
        }
      }
    };
  },
  watch: {
    openTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = this._.cloneDeep(val);
      this.initChart();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    inputData_in(val) {}
  },
  methods: {
    CetButton_confirm_statusTrigger_out() {},
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    },
    // 图表初始化展示
    initChart() {
      const currentTheme = omegaTheme.theme;
      const themeMapConfig = themeMap.get(currentTheme);
      let colorList = themeMapConfig.color;
      let data = this._.cloneDeep(this.inputData_in);
      let chartData = this.updateData(data);
      let series = [];
      if (chartData.length) {
        let num = 0;
        chartData.forEach((item, index) => {
          const serieType = item[0]?.serieType;
          series.push({
            name: item[0].name,
            type: "line",
            data: item,
            color: colorList[num],
            lineStyle:
              serieType === 1
                ? {}
                : {
                    normal: {
                      width: 2,
                      type: "dashed"
                    }
                  }
          });
          if (index % 2 !== 0) {
            num++;
          }
        });
        this.CetChart_1.options.xAxis.data = this.getTimeValue(chartData[0]);
      }
      this.CetChart_1.options.series = this._.cloneDeep(series);
    },
    // 对数据进行处理
    updateData(data) {
      let result = [];
      if (data.length) {
        if (data[0].logicalValues.length) {
          for (let j = 0; j < data[0].logicalValues.length; j++) {
            result.push([]);
            result.push([]);
          }
        }
        data.forEach(item => {
          let index = 0;
          item.logicalValues.forEach(i => {
            result[index++].push({
              name: $T("回路{0}实际数据", i.logicalid),
              logTime: item.logtime,
              serieType: 1,
              value: this.formatNumber(i.datalogValue)
            });
            result[index++].push({
              name: $T("回路{0}录入数据", i.logicalid),
              logTime: item.logtime,
              serieType: 2,
              value: this.formatNumber(i.value)
            });
          });
        });
      }
      return result;
    },
    // 对x轴进行处理
    getTimeValue(data) {
      let result = [];
      data.forEach(item => {
        let time = this.$moment(item.logTime).format("YYYY-MM-DD HH:mm");
        result.push(time);
      });
      return result;
    },
    // 数字格式化
    formatNumber(value) {
      return value !== null ? Number(value).toFixed(2) : "--";
    }
  }
};
</script>

<style lang="scss" scoped></style>
