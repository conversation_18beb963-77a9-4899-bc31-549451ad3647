<template>
  <!-- 1弹窗组件 -->
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <div class="fullheight eem-cont-c1">
      <el-checkbox v-model="checked" @change="checkedChange" class="mb-J1">
        {{ $T("默认选中子节点") }}
      </el-checkbox>
      <!-- 父子关联 -->
      <CetGiantTree
        ref="giantTree1"
        class="switch-tree"
        v-show="!checked"
        v-bind="CetGiantTree_1"
        v-on="CetGiantTree_1.event"
      ></CetGiantTree>
      <CetGiantTree
        ref="giantTree2"
        class="switch-tree"
        v-show="checked"
        v-bind="CetGiantTree_2"
        v-on="CetGiantTree_2.event"
      ></CetGiantTree>
    </div>
    <span slot="footer">
      <span class="fl ml-J1">
        {{ `${$T("最多选中{0}个节点", this.maxExportNodeNumber)}` }}
      </span>
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import common from "eem-base/utils/common.js";
import customApi from "@/api/custom";
export default {
  name: "shareRate",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    // 已关联管网设备列表
    deviceList: {
      type: Array,
      default() {
        return [];
      }
    },
    // 导出接口地址
    urlStr: {
      type: String
    },
    treeData: Array
  },
  data() {
    return {
      // 选择框是否点击
      checked: false,
      CetDialog_1: {
        title: $T("选择节点"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        width: "600px",
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      checkedNodes: [],
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {}
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out //勾选节点输出
        }
      },
      // 2组件
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "ps", N: "ps" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_2_checkedNodes_out //勾选节点输出
        }
      },
      maxExportNodeNumber: 100
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    async visibleTrigger_in(val) {
      var vm = this;
      this.checked = false;
      vm.CetDialog_1.openTrigger_in = val;
      this.getConfig();
      await this.getTreeData();
      this.CetGiantTree_1.checkedNodes = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_2.checkedNodes = [];
      this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
      this.checkedNodes = [];
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    inputData_in(val) {
      this.CetDialog_1.inputData_in = val;
    }
  },

  methods: {
    //选中节点树节点
    CetGiantTree_1_checkedNodes_out(val) {
      if (!this.checked) {
        // val = val || [];
        // if (val.length > this.maxExportNodeNumber) {
        //   this.$message.warning(
        //     `${$T("最多选中{0}个节点", this.maxExportNodeNumber)}`
        //   );
        //   this.CetGiantTree_1.checkedNodes = this._.cloneDeep(
        //     this.checkedNodes
        //   );
        //   return;
        // }
        this.checkedNodes = val;
      }
    },
    CetGiantTree_2_checkedNodes_out(val) {
      if (this.checked) {
        // val = val || [];
        // if (val.length > this.maxExportNodeNumber) {
        //   this.$message.warning(
        //     `${$T("最多选中{0}个节点", this.maxExportNodeNumber)}`
        //   );
        //   this.CetGiantTree_2.checkedNodes = this._.cloneDeep(
        //     this.checkedNodes
        //   );
        //   return;
        // }
        this.checkedNodes = val;
      }
    },
    //获取导出节点数量配置
    getConfig() {
      let queryData = {};
      customApi.getDataEntryConfig(queryData).then(response => {
        if (response.code === 0) {
          this.maxExportNodeNumber =
            this._.get(response, "data.maxExportNodeNumber", 100) || 100;
        }
      });
    },
    //获取节点树数据
    getTreeData() {
      const treeData = this._.cloneDeep(this.treeData);
      this.CetGiantTree_1.inputData_in = treeData;
      this.CetGiantTree_2.inputData_in = treeData;
    },
    // 保存
    addSupplyRelation_out() {
      var me = this;
      if (me.checkedNodes.length === 0) {
        this.$message({
          message: $T("请选择导出节点"),
          type: "warning"
        });
        return;
      }
      const nodes = me.checkedNodes.map(item => {
        return {
          id: item.id,
          modelLabel: item.modelLabel,
          tree_id: item.tree_id
        };
      });
      if (this.checkedNodes.length > this.maxExportNodeNumber) {
        this.$message.warning(
          `${$T("最多选中{0}个节点", this.maxExportNodeNumber)}`
        );
        return;
      }
      const params = {
        ...this.inputData_in,
        nodes: nodes
      };
      const urlStr = this.urlStr;
      common.downExcel(urlStr, params, function () {
        me.$emit("updata_out");
        me.CetDialog_1.closeTrigger_in = new Date().getTime();
      });
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      this.addSupplyRelation_out();
    },
    // 选择框的改变
    checkedChange() {
      const vm = this;
      let checkNodes = vm._.cloneDeep(this.checkedNodes);
      setTimeout(() => {
        $(this.$refs.giantTree1.$el).find(".ztree").scrollTop(0);
        $(this.$refs.giantTree2.$el).find(".ztree").scrollTop(0);
        if (vm.checked) {
          vm.CetGiantTree_2.checkedNodes = checkNodes;
          vm.expandNode(checkNodes, "tree_id", vm.$refs.giantTree2.ztreeObj);
          if (!checkNodes.length) {
            vm.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
          }
        } else {
          vm.CetGiantTree_1.checkedNodes = checkNodes;
          vm.expandNode(checkNodes, "tree_id", vm.$refs.giantTree1.ztreeObj);
          if (!checkNodes.length) {
            vm.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
          }
        }
      }, 0);
    },
    // 展开节点
    expandNode(nodes, key, ztreeObj) {
      setTimeout(() => {
        nodes.forEach(item => {
          let node = ztreeObj.getNodeByParam(key, item[key]);
          let parentNodes = [],
            parentNode = node && node.getParentNode();
          while (parentNode) {
            parentNodes.push(parentNode);
            parentNode = parentNode.getParentNode();
          }
          parentNodes.forEach(i => {
            ztreeObj.expandNode(i, true);
          });
        });
        $(this.$refs.giantTree1.$el).find(".ztree").scrollTop(0);
        $(this.$refs.giantTree2.$el).find(".ztree").scrollTop(0);
      }, 0);
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.switch-tree {
  height: 400px;
}
</style>
