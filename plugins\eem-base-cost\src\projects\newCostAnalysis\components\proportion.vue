<template>
  <div class="flex flex-col fullfilled">
    <div class="font-bold title text-H3 mb-J3">{{ $T("分类成本占比") }}</div>
    <div class="flex-auto mt-J3" v-if="!showEmpty">
      <CetChart v-bind="CetChart_1"></CetChart>
    </div>
    <el-empty
      class="flex-auto"
      v-else-if="isLight"
      :image-size="216"
      image="static/assets/empty_min_light.png"
    ></el-empty>
    <el-empty
      v-else
      class="flex-auto"
      :image-size="216"
      image="static/assets/empty_min.png"
    ></el-empty>
  </div>
</template>

<script>
import * as echarts from "echarts";
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
import omegaTheme from "@omega/theme";
export default {
  name: "newCostAnalysisProportion",
  props: {
    params: Object
  },
  computed: {
    isLight() {
      return omegaTheme.theme === "light";
    }
  },
  data() {
    return {
      showEmpty: true,
      unit: "--",
      CetChart_1: {
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "item",
            formatter: ({ name, data }) => {
              const { radio, realValue } = data;
              return `${name}<br/>
                ${$T("成本值")}：${this.valueFormat(realValue)} ${
                this.unit
              }<br/>
                ${$T("占比")}：${this.valueFormat(radio)}%`;
            }
          },
          legend: {
            type: "scroll",
            top: "center",
            right: 20,
            orient: "vertical",
            icon: "circle",
            textStyle: {
              rich: {
                name: {
                  width: 80,
                  fontSize: 12
                },
                radio: {
                  width: 40,
                  fontSize: 12
                }
              }
            },
            formatter: name => {
              const seriesData = this.CetChart_1.options.series[0].data;
              const data = seriesData.find(i => i.name === name);
              const nameStr = `{name|${echarts.format.truncateText(
                name,
                80,
                "12px Microsoft Yahei",
                "…"
              )}}`;
              const radio = `{radio|${echarts.format.truncateText(
                `${this.valueFormat(data.radio)}%`,
                50,
                "12px Microsoft Yahei",
                "…"
              )}}`;
              return nameStr + radio;
            },
            tooltip: {
              show: true,
              formatter: ({ name }) => {
                const seriesData = this.CetChart_1.options.series[0].data;
                const data = seriesData.find(i => i.name === name);
                return `${name}<br/>
                ${$T("成本值")}：${this.valueFormat(data.realValue)} ${
                  this.unit
                }<br/>
                ${$T("占比")}：${this.valueFormat(data.radio)}%`;
              }
            }
          },
          series: [
            {
              type: "pie",
              radius: ["20%", "50%"],
              center: ["25%", "50%"],
              avoidLabelOverlap: false,
              roseType: "radius",
              label: {
                show: false
              },
              emphasis: {
                label: {
                  show: false
                }
              },
              labelLine: {
                show: false
              },
              data: []
            }
          ]
        }
      }
    };
  },
  watch: {
    params: {
      handler() {
        this.getData();
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async getData() {
      if (!this.params) {
        this.showEmpty = true;
        return;
      }

      const res = await customApi.realtimeCostGroupByEnergyType(this.params);
      const dataList = res?.data ?? [];
      this.showEmpty = !dataList?.length;
      if (!dataList?.length) {
        return;
      }

      let total = null;
      dataList.forEach(item => {
        if (item.value != null) {
          total += item.value;
        }
      });
      this.unit = res?.data?.[0]?.unitName ?? "--";

      const seriesData = [];
      dataList.forEach(item => {
        let radio = null;
        if (total != null && item.value != null) {
          radio = (item.value / total) * 100;
        }
        seriesData.push({
          value: item.value > 0 ? item.value : 0,
          realValue: item.value,
          radio: radio,
          name: item.energyTypeName
        });
      });
      this.total = this.valueFormat(total);
      this.CetChart_1.options.series[0].data = seriesData;
    },
    valueFormat(val) {
      if (val == null) return "--";
      return common.formatNum(val.toFixed(2));
    }
  }
};
</script>

<style lang="scss" scoped></style>
