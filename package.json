{"name": "fusion-template", "version": "1.0.0", "description": "TODO: 简要介绍你的项目。通过此节说明此项目的目标或动机。", "main": "index.js", "workspaces": ["main", "plugins/*"], "scripts": {"dev:main": "pnpm --filter main dev", "dev:sub": "pnpm --filter sub dev", "dev:eem-base-config": "pnpm --filter eem-base-config dev", "dev:eem-base-loss": "pnpm --filter eem-base-loss dev", "dev:eem-base-cost": "pnpm --filter eem-base-cost dev", "dev:eem-base-ef": "pnpm --filter eem-base-ef dev", "dev:eem-base-energy": "pnpm --filter eem-base-energy dev", "dev:virtual-power-plant": "pnpm --filter virtual-power-plant dev", "dev:eem-base-demand": "pnpm --filter eem-base-demand dev", "dev:eem-base-datamaintain": "pnpm --filter eem-base-datamaintain dev", "i18n": "auto-i18n-cli", "i18n-wrap": "auto-i18n-zh-wrap-cli", "i18n-check": "auto-i18n-zh-check-cli"}, "repository": {"type": "git", "url": "ssh://cetsoft-svr1:22/Platforms/PLT-Omega/_git/Fusion-template"}, "keywords": [], "author": "chengkai", "license": "ISC", "engines": {}, "dependencies": {"i18n-autoscan-cli": "^1.0.0"}}