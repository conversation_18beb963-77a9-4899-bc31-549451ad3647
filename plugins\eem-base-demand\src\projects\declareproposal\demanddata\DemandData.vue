<template>
  <div class="demand-page h-full flex flex-col">
    <div class="mb-J3 p-0">
      <el-row>
        <el-col :span="8">
          <div class="leading-8 text-ellipsis">
            <el-tooltip
              :content="`${$T('需量数据维护')}-${$moment(
                CetDatePicker_time.val
              ).format($T('YYYY年MM月'))}${$T('各进线需量数据')}`"
              effect="light"
              placement="top"
            >
              <span class="text-H2 font-bold inline">
                {{ $T("需量数据维护") }}-{{
                  $moment(CetDatePicker_time.val).format($T("YYYY年MM月"))
                }}{{ $T("各进线需量数据") }}
              </span>
            </el-tooltip>
          </div>
        </el-col>
        <el-col :span="16">
          <template v-if="$checkPermission('demandmaintain_update')">
            <CetButton
              class="fr"
              v-bind="{ ...CetButton_4, title: $T('编辑') }"
              v-on="CetButton_4.event"
            ></CetButton>
            <CetButton
              class="fr mr-J1"
              v-bind="{ ...CetButton_5, title: $T('保存') }"
              v-on="CetButton_5.event"
            ></CetButton>
            <CetButton
              class="fr mr-J1"
              v-bind="{ ...CetButton_6, title: $T('取消') }"
              v-on="CetButton_6.event"
            ></CetButton>
            <CetButton
              class="fr mr-J1"
              v-bind="{ ...CetButton_3, title: $T('生成模板') }"
              v-on="CetButton_3.event"
            ></CetButton>
            <CetButton
              class="fr mr-J1"
              v-bind="{ ...CetButton_2, title: $T('导入数据') }"
              v-on="CetButton_2.event"
            ></CetButton>
          </template>
          <div class="fr mr-J1">
            <!-- 向前查询按钮 -->
            <CetButton
              class="fl mr-J1 custom—square"
              v-bind="CetButton_prv"
              v-on="CetButton_prv.event"
            ></CetButton>
            <CustomElDatePicker
              class="fl mr-J1"
              :prefix_in="$T('选择月份')"
              v-bind="CetDatePicker_time.config"
              v-model="CetDatePicker_time.val"
            />
            <CetButton
              class="fl custom—square"
              v-bind="CetButton_next"
              v-on="CetButton_next.event"
            ></CetButton>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="min-w-0 min-h-0 flex flex-col flex-auto">
      <div class="min-w-0 min-h-0 flex-auto p-0">
        <el-table
          ref="demandDataTable"
          :data="tableData"
          highlight-current-row
          border
          tooltip-effect="light"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            :label="$T('序号')"
            type="index"
            header-align="left"
            align="left"
            width="65"
          ></el-table-column>
          <template v-for="(item, index) in tableColumns">
            <el-table-column
              :key="item.key"
              :show-overflow-tooltip="true"
              :label="item.title"
              :min-width="item.width"
              :width="item.fixedwidth"
              :prop="item.key"
              :sortable="item.sortable"
              header-align="left"
              :align="item.align"
            >
              <template slot-scope="scope">
                <template v-if="index < 2 || !CetButton_5.visible_in">
                  {{ scope.row[item.key] || "--" }}
                </template>

                <el-select
                  v-else-if="index == 2"
                  v-model="scope.row[item.key]"
                  :placeholder="$T('请选择')"
                  size="small"
                  @change="scope.row.modify = true"
                  :disabled="
                    !(
                      $moment(CetDatePicker_time.val).month() ==
                        unnaturalmonthbegin ||
                      $moment(CetDatePicker_time.val).month() ==
                        (unnaturalmonthbegin + 3 >= 12
                          ? unnaturalmonthbegin + 3 - 12
                          : unnaturalmonthbegin + 3) ||
                      $moment(CetDatePicker_time.val).month() ==
                        (unnaturalmonthbegin + 6 >= 12
                          ? unnaturalmonthbegin + 6 - 12
                          : unnaturalmonthbegin + 6) ||
                      $moment(CetDatePicker_time.val).month() ==
                        (unnaturalmonthbegin + 9 >= 12
                          ? unnaturalmonthbegin + 9 - 12
                          : unnaturalmonthbegin + 9)
                    )
                  "
                >
                  <el-option
                    :label="$T('容量计费')"
                    :value="$T('容量计费')"
                  ></el-option>
                  <el-option
                    :label="$T('需量计费')"
                    :value="$T('需量计费')"
                  ></el-option>
                </el-select>
                <el-input
                  v-else-if="index > 2 && index < 6"
                  v-model="scope.row[item.key]"
                  @keyup.native="handleNum(scope.row, item.key, 2)"
                  @blur="handleNum(scope.row, item.key, 2)"
                  :placeholder="$T('请输入内容')"
                  size="small"
                  class="mt5 mb5"
                ></el-input>
              </template>
            </el-table-column>
          </template>

          <el-table-column
            :label="$T('操作')"
            width="75"
            header-align="left"
            align="left"
          >
            <template slot-scope="scope">
              <!-- <i
                  class="row-detail"
                  @click="modifyTableDataCurrentChange_out(scope.row)"
                ></i> -->
              <span
                @click="modifyTableDataCurrentChange_out(scope.row)"
                class="handel"
              >
                {{ $T("详情") }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="p-0 leading-8 mt-J3 flex">
        <div class="min-w-0 min-h-0 flex-auto text-right">
          <el-pagination
            class="inline-block"
            @size-change="handleSizeChange"
            @current-change="handleCurrentPageChange"
            :current-page.sync="currentPage"
            :page-size.sync="pageSize"
            :total="totalCount"
            layout="total,sizes, prev, pager, next, jumper"
            :pageSizes="[10, 20, 50, 100]"
          ></el-pagination>
        </div>
      </div>
    </div>
    <DemandDetail
      :visibleTrigger_in="DemandDetail.visibleTrigger_in"
      :closeTrigger_in="DemandDetail.closeTrigger_in"
      :queryId_in="DemandDetail.queryId_in"
      :inputData_in="DemandDetail.inputData_in"
    />
    <el-dialog
      :title="$T('选择模板时段')"
      :visible.sync="dialogVisible"
      width="520px"
      class="CetDialog small"
    >
      <div class="clearfix flex">
        <CustomElDatePicker
          :prefix_in="$T('选择时段')"
          style="width: 320px"
          v-bind="CetDatePicker_1.config"
          v-model="CetDatePicker_1.val"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" plain @click="dialogVisible = false">
          {{ $T("取 消") }}
        </el-button>
        <el-button
          size="small"
          type="primary"
          @click="exportDemandMaintainData"
        >
          {{ $T("确 定") }}
        </el-button>
      </span>
    </el-dialog>
    <UploadDialog
      v-bind="uploadDialog"
      v-on="uploadDialog.event"
    ></UploadDialog>
  </div>
</template>
<script>
import common from "eem-base/utils/common";
import DemandDetail from "./DemandDetail";
import UploadDialog from "eem-base/components/uploadDialog";
import customApi from "@/api/custom";
import omegaI18n from "@omega/i18n";

export default {
  name: "DemandData",
  components: {
    DemandDetail,
    UploadDialog
  },
  computed: {
    language() {
      return omegaI18n.locale === "en";
    }
  },
  props: {
    currentNode: {
      type: Object
    }
  },
  data(vm) {
    return {
      unnaturalmonthbegin: 0, //年起始月
      dialogVisible: false,
      copyTableData: [],
      // 分页
      currentPage: 1,
      pageSize: 20,
      totalCount: 0,
      // 编辑表格
      tableData: [],
      tableColumns: [
        {
          title: $T("进线名称"),
          key: "name",
          width: 100,
          fixedwidth: 200
        },
        {
          title: $T("户号"),
          key: "accountno",
          width: vm.language ? 190 : 100
        },
        {
          title: $T("计费方式"),
          key: "chargingwayText",
          width: vm.language ? 190 : 150
        },
        {
          title: $T("容量（kVA）"),
          key: "volume",
          type: "number",
          width: vm.language ? 190 : 150,
          precision: 2
        },
        {
          title: $T("计划需量值（kW）") + $T("（非合同需量可作为预警值）"),
          key: "declarevalue",
          type: "number",
          width: vm.language ? 770 : 335,
          precision: 2
        },
        {
          title: $T("供电局账单值（kW）"),
          key: "billingvalue",
          type: "number",
          width: vm.language ? 320 : 170,
          precision: 2
        }
      ],

      CetDatePicker_1: {
        disable_in: false,
        val: [
          this.$moment().subtract(1, "month").startOf("month").valueOf(),
          this.$moment().add(0, "month").startOf("month").valueOf()
        ],
        config: {
          valueFormat: "timestamp",
          type: "monthrange",
          rangeSeparator: $T("至"),
          clearable: false,
          size: "small"
        }
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      CetDatePicker_time: {
        disable_in: false,
        val: this.$moment().add(0, "M").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "month",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          clearable: false,
          pickerOptions: {
            // 添加回到今天的快捷键
            shortcuts: [
              {
                text: this.$T("当月"),
                onClick(picker) {
                  picker.$emit("pick", new Date());
                }
              }
            ]
          },
          size: "small",
          style: {
            display: "inline-block"
          }
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetButton_3: {
        visible_in: true,
        disable_in: true,
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      CetButton_4: {
        visible_in: true,
        disable_in: true,
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_4_statusTrigger_out
        }
      },
      CetButton_5: {
        visible_in: false,
        disable_in: false,
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_5_statusTrigger_out
        }
      },
      CetButton_6: {
        visible_in: false,
        disable_in: false,
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_6_statusTrigger_out
        }
      },
      CetButton_7: {
        visible_in: true,
        disable_in: false,
        title: "",
        plain: true,
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_7_statusTrigger_out
        }
      },
      CetButton_8: {
        visible_in: true,
        disable_in: false,
        title: "",
        plain: true,
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_8_statusTrigger_out
        }
      },
      DemandDetail: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      isEdit: false,
      uploadDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        extensionNameList_in: [".xlsx", ".xls"],
        hideDownload: true,
        dialogTitle: $T("导入数据"),
        event: {
          uploadFile: this.uploadDialog_uploadFile
        }
      }
    };
  },
  watch: {
    "CetDatePicker_time.val": function (val) {
      this.getTableData();
    },
    currentNode: function (val) {
      this.CetButton_2.disable_in = false;
      this.getTableData();
    }
  },

  methods: {
    // 获取列表数据
    async getTableData(currentPage) {
      this.tableData = [];
      this.totalCount = 0;
      if (!this.currentNode) {
        this.CetButton_2.disable_in = true;
        return;
      }
      var data = {
        starttime: this.$moment(this.CetDatePicker_time.val)
          .startOf("M")
          .valueOf(),
        endtime:
          this.$moment(this.CetDatePicker_time.val).endOf("M").valueOf() + 1,
        ids: [],
        index: 0,
        limit: *********
      };
      var obj = {};
      this.getChildrenDevice(this.currentNode, obj);
      data.ids = obj.demandaccount;
      if (!data.ids || data.ids.length === 0) {
        this.CetButton_3.disable_in = true;
        this.CetButton_4.disable_in = true;
        return;
      }
      const response = await customApi.getDemandMaintainData(data);
      if (response.code === 0 && response.data?.length > 0) {
        this.CetButton_3.disable_in = false;
        this.CetButton_4.disable_in = false;
        response.data.forEach(item => {
          item.chargingwayText =
            item.chargingway == 1
              ? $T("容量计费")
              : item.chargingway == 2
              ? $T("需量计费")
              : "--";
          item.volume = item.volume && item.volume.toFixed2(2);
          item.declarevalue =
            item.declarevalue && item.declarevalue.toFixed2(2);
          item.billingvalue =
            item.billingvalue && item.billingvalue.toFixed2(2);
        });

        // this.tableData = this._.cloneDeep(response.data);
        this.totalCount = response.total;

        // 保存数据，用于分页
        this.copyTableData = this._.cloneDeep(response.data);
        this.handleCurrentPageChange(currentPage || 1);
        this.totalCount = this.copyTableData.length;
      } else {
        this.CetButton_3.disable_in = true;
        this.CetButton_4.disable_in = true;
      }
      this.$nextTick(() => {
        this.$refs.demandDataTable.doLayout();
      });
    },
    // 获取节点下的所有进线id
    getChildrenDevice(node, obj) {
      if (node.modelLabel == "demandaccount") {
        // 找到设备
        if (obj[node.modelLabel]) {
          if (obj[node.modelLabel].indexOf(node.id) == -1) {
            obj[node.modelLabel].push(node.id);
          }
        } else {
          obj[node.modelLabel] = [node.id];
        }
      } else {
        if (node.children && node.children.length > 0) {
          node.children.forEach(item => {
            this.getChildrenDevice(item, obj);
          });
        }
      }
    },
    //分页大小变化
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.handleCurrentPageChange(1);
    },
    //分页当前页变化
    handleCurrentPageChange(val) {
      this.currentPage = val;
      this.tableData = this._.cloneDeep(
        this.copyTableData.slice(
          (this.currentPage - 1) * this.pageSize,
          this.currentPage * this.pageSize
        )
      );
    },
    // 生成模板
    exportDemandMaintainData() {
      if (!this.currentNode) {
        return;
      }
      var data = {
        starttime: this.$moment(this.CetDatePicker_1.val[0])
          .startOf("M")
          .valueOf(),
        endtime:
          this.$moment(this.CetDatePicker_1.val[1]).endOf("M").valueOf() + 1,
        ids: [],
        page: {
          index: 0,
          limit: *********
        }
      };
      var obj = {};
      this.getChildrenDevice(this.currentNode, obj);
      data.ids = obj.demandaccount;
      common.downExcel("/eembasedemand/v1/maintain/export", data);
      this.dialogVisible = false;
    },
    modifyTableDataCurrentChange_out(val) {
      this.DemandDetail.queryId_in = val.id;
      this.DemandDetail.visibleTrigger_in = new Date().getTime();
    },
    CetButton_prv_statusTrigger_out(val) {
      var time = this.CetDatePicker_time.val;
      this.CetDatePicker_time.val = this.$moment(time)
        .subtract(1, "M")
        .valueOf();
    },
    CetButton_next_statusTrigger_out(val) {
      var time = this.CetDatePicker_time.val;
      this.CetDatePicker_time.val = this.$moment(time).add(1, "M").valueOf();
    },
    CetButton_2_statusTrigger_out(val) {
      this.uploadDialog.openTrigger_in = new Date().getTime();
    },
    CetButton_3_statusTrigger_out(val) {
      this.CetDatePicker_1.val = [
        this.$moment().subtract(1, "month").startOf("month").valueOf(),
        this.$moment().add(0, "month").startOf("month").valueOf()
      ];
      this.dialogVisible = true;
    },
    CetButton_4_statusTrigger_out(val) {
      this.CetButton_4.visible_in = false;
      this.CetButton_5.visible_in = true;
      this.CetButton_6.visible_in = true;
    },
    // 保存编辑
    async CetButton_5_statusTrigger_out(val) {
      var data = [];
      this.tableData.forEach(item => {
        if (item.modify) {
          data.push({
            accountno: item.accountno,
            billingvalue:
              item.billingvalue !== "" ? Number(item.billingvalue) : null,
            declarevalue:
              item.declarevalue !== "" ? Number(item.declarevalue) : null,
            volume: item.volume !== "" ? Number(item.volume) : null,
            chargingway:
              item.chargingwayText == $T("容量计费")
                ? 1
                : item.chargingwayText == $T("需量计费")
                ? 2
                : 0,
            id: item.id,
            name: item.name
          });
        }
      });
      const response = await customApi.saveDemandMaintainData(data, {
        startTime: this.$moment(this.CetDatePicker_time.val)
          .startOf("M")
          .valueOf(),
        endTime:
          this.$moment(this.CetDatePicker_time.val).endOf("M").valueOf() + 1
      });
      if (response.code === 0) {
        this.$message({
          message: $T("保存成功"),
          type: "success"
        });
        this.getTableData(this.currentPage);
        this.CetButton_4.visible_in = true;
        this.CetButton_5.visible_in = false;
        this.CetButton_6.visible_in = false;
      }
    },
    CetButton_6_statusTrigger_out(val) {
      this.tableData = this._.cloneDeep(
        this.copyTableData.slice(
          (this.currentPage - 1) * this.pageSize,
          this.currentPage * this.pageSize
        )
      );
      this.CetButton_4.visible_in = true;
      this.CetButton_5.visible_in = false;
      this.CetButton_6.visible_in = false;
    },
    CetButton_7_statusTrigger_out(val) {
      const date1 = this.$moment(this.CetDatePicker_1.val[0]);
      const date2 = this.$moment(this.CetDatePicker_1.val[1]);
      this.CetDatePicker_1.val = [
        date1.subtract(1, "month").startOf("month").valueOf(),
        date2.subtract(1, "month").startOf("month").valueOf()
      ];
    },
    CetButton_8_statusTrigger_out(val) {
      const date1 = this.$moment(this.CetDatePicker_1.val[0]);
      const date2 = this.$moment(this.CetDatePicker_1.val[1]);
      this.CetDatePicker_1.val = [
        date1.add(1, "month").startOf("month").valueOf(),
        date2.add(1, "month").startOf("month").valueOf()
      ];
    },
    // 输入数字控制
    handleNum(row, key, num) {
      row.modify = true;
      var value;
      if (typeof row[key] === "object") {
        if (row[key]) {
          value = row[key].val;
        } else {
          return;
        }
      } else {
        value = row[key];
      }
      var reg = new RegExp("^(\\-)*(\\d+)\\.(\\d{0," + num + "}).*$");
      var reg2 = new RegExp("^(\\d{0,12})(\\d+)\\.(\\d{0," + num + "}).*$");
      var reg3 = new RegExp("^(\\d{0,12})(\\d+)$");
      var val = String(value);
      if (val) {
        val = val.replace(/[^\d.]/g, ""); //清除数字和'.'以外的字符
        val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", "."); //只保留顺位第一的'.'
        val = val.replace(reg, "$1$2.$3"); //只能输入两位位小数
      }
      if (val.length >= 2 && val.indexOf(".") != 1 && val[0] == 0) {
        //在非小数的时候清除前导0
        val = val.replace(/0/, "");
      }
      if (val.length >= 2 && val.indexOf(".") == 0) {
        // 在先输入小数点时补0
        val = Number(val);
      }
      if (typeof row[key] === "object") {
        if (String(val).indexOf(".") != -1 && Number(val) > *********999.99) {
          val = val.replace(reg2, "$1.$3");
        } else if (String(val).indexOf(".") == -1 && String(val).length > 12) {
          val = val.replace(reg3, "$1");
        }
        row[key].val = val;
      } else {
        if (String(val).indexOf(".") != -1 && Number(val) > *********999.99) {
          val = val.replace(reg2, "$1.$3");
        } else if (String(val).indexOf(".") == -1 && String(val).length > 12) {
          val = val.replace(reg3, "$1");
        }
        row[key] = val;
      }
    },
    async uploadDialog_uploadFile(val) {
      const formData = new FormData();
      formData.append("file", val.file);
      const response = await customApi.importDemandMaintainData(formData, {
        startTime: this.$moment(this.CetDatePicker_time.val)
          .startOf("M")
          .valueOf(),
        endTime:
          this.$moment(this.CetDatePicker_time.val).endOf("M").valueOf() + 1
      });
      if (response.code !== 0) {
        this.getTableData();
        return;
      }

      this.$message({
        message: $T("导入成功"),
        type: "success"
      });
      this.getTableData();
      this.uploadDialog.closeTrigger_in = Date.now();
    }
  },

  created: function () {},
  activated: function () {
    this.CetButton_5.visible_in = true;
    this.CetButton_4.visible_in = true;
    this.CetButton_6.visible_in = false;
    this.$nextTick(() => {
      this.CetButton_5.visible_in = false;
    });
    this.getTableData();
  }
};
</script>
<style lang="scss" scoped>
.demand-page {
  width: 100%;
  height: calc(100% - 52px);
  position: relative;
}

.basic-box-label {
  line-height: 30px;
  height: 30px;
}

.row-detail {
  vertical-align: middle;
  display: inline-block;
  width: 24px;
  height: 24px;
  cursor: pointer;
  background: url("./assets/details.png") no-repeat center center;
  border-radius: 50%;
}
.handel {
  cursor: pointer;
  @include font_color(ZS);
  &.delete {
    @include font_color(Sta3);
  }
}
.CetDialog {
  :deep(.el-dialog__body) {
    @include padding(J1);
    @include background_color(BG);
  }
}
:deep(.el-range-separator) {
  width: 30px;
}
</style>
