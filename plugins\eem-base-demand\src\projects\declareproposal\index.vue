<template>
  <div class="page">
    <div class="h-full">
      <CetAside class="cet-aside">
        <template #aside>
          <leftTree
            ref="leftTree"
            :transferCurrentNode="$route.query"
            @setCurrentNode="CetTree_1_currentNode_out"
          />
        </template>
        <template #container>
          <div class="cet-aside-main">
            <div class="cet-aside-main-header text-over">
              {{ currentNode && currentNode.name }}
            </div>
            <DemandData :currentNode="currentNode"></DemandData>
          </div>
        </template>
      </CetAside>
    </div>
  </div>
</template>

<script>
import DemandData from "./demanddata/DemandData.vue";
import leftTree from "../components/leftTree.vue";

export default {
  name: "declareproposal",
  components: {
    DemandData,
    leftTree
  },
  data() {
    return {
      sideWidth: "315px",
      currentNode: null,
      selectedMenu: "",
      copyTableData: []
    };
  },
  methods: {
    CetTree_1_currentNode_out(val) {
      this.currentNode = val;
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
}
.cet-aside {
  :deep(.cet-content-aside-container) {
    padding: 0;
    background-color: var(--BG1);
    border-radius: var(--Ra);
    padding: var(--J3) var(--J4);
  }
  &-main {
    overflow: auto;
    padding: 0;
    height: 100%;
    &-header {
      @include margin_bottom(J3);
      padding: 0;
      @include font_size(H1);
      font-weight: 700;
      max-width: 100%;
      text-overflow: ellipsis;
    }
  }
}
.text-over {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
</style>
