<template>
  <div class="login-normal">
    <el-form :model="login" :rules="rules" ref="form">
      <el-form-item prop="userName">
        <el-input :placeholder="$T('请输入账号')" v-model="login.userName">
          <omega-icon
            slot="prefix"
            class="form-line-item-label-icon icon-size-I1 icon-p3"
            symbolId="user-one-lin"
          ></omega-icon>
        </el-input>
      </el-form-item>

      <el-form-item prop="passWord">
        <el-input
          :placeholder="$T('请输入密码')"
          v-model="login.passWord"
          show-password
        >
          <omega-icon
            slot="prefix"
            class="form-line-item-label-icon icon-size-I1 icon-p3"
            symbolId="password-lin"
          ></omega-icon>
        </el-input>
      </el-form-item>
      <el-button
        class="login-btn"
        type="primary"
        size="medium"
        @click="evLoginBtnClick"
      >
        {{ $T("登录") }}
      </el-button>
    </el-form>
  </div>
</template>

<script>
import omegaAuth from "@omega/auth";
export default {
  name: "LoginNormal",
  data() {
    return {
      login: {
        userName: "",
        passWord: ""
      },
      rules: {
        userName: [
          {
            required: true,
            message: $T("账号不能为空"),
            trigger: "change"
          }
        ],
        passWord: [
          {
            required: true,
            message: $T("密码不能为空"),
            trigger: "change"
          }
        ]
      }
    };
  },
  methods: {
    async evLoginBtnClick() {
      await this.$refs.form.validate();

      const param = {
        userName: this.login.userName,
        password: this.login.passWord
      };

      await omegaAuth.login(param, { type: "security" });
    }
  }
};
</script>

<style lang="scss" scoped>
.form-line-item-label-icon {
  height: 32px;
}
::v-deep .el-input__prefix {
  display: flex;
  line-height: unset;
  align-items: center;
  margin: 0 var(--J3);
}

::v-deep .el-input--prefix .el-input__inner {
  padding-left: calc(2 * var(--J3) + 30px);
}

.login-btn {
  @include margin_top(J4);
  @include font_color(T5);
  width: 100%;
}
</style>
