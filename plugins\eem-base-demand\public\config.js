__registeAppConfig.setAppconfig({
  version: "1.0.0",
  i18n: {
    en: {
      需量管理: "Demand Management",
      用电需量分析: "Electricity demand analysis",
      进线管理: "Inlet Wire Management",
      需量计划: "Demand Declaration"
    }
  },
  navmenu: [
    {
      label: "需量管理",
      icon: "permission-management-lin",
      type: "subMenu",
      subMenuList: [
        {
          label: "用电需量分析",
          category: "project",
          type: "menuItem",
          location: "/demandAnalysis",
          permission: "demandAnalysis"
        },
        {
          label: "进线管理",
          category: "project",
          type: "menuItem",
          location: "/inletwireManage",
          permission: "inletwireManage"
        },
        {
          label: "需量计划",
          category: "project",
          type: "menuItem",
          location: "/declareproposal",
          permission: "declareproposal"
        }
      ]
    }
  ],
  newGuideSteps: []
});
