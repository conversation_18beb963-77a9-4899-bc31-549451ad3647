<template>
  <el-dialog
    :visible="visible"
    :title="$T('选择关联站点')"
    width="640px"
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    :append-to-body="true"
    @close="handleClose"
    class="room-selector-dialog"
  >
    <div class="room-selector-content">
      <div class="room-selection-area">
        <div class="room-type-section mb-J3">
          <div class="section-header mb-J2">
            <h4 class="text-T1 font-medium text-base mb-0 flex items-center">
              <i class="el-icon-office-building mr-J1 text-primary"></i>
              {{ $T("选择站点类型") }}
            </h4>
            <p class="text-T3 text-sm mt-J1 mb-0">
              {{ $T("请先选择站点类型，然后从对应的站点列表中选择具体站点") }}
            </p>
          </div>
          <div class="room-type-selector">
            <el-select
              v-model="selectedRoomType"
              :placeholder="
                roomTypesLoading ? $T('加载中...') : $T('请选择站点类型')
              "
              :loading="roomTypesLoading"
              :disabled="roomTypesLoading || availableRoomTypes.length === 0"
              @change="handleRoomTypeChange"
              clearable
              filterable
              size="medium"
              style="width: 100%; max-width: 400px"
              class="room-type-select"
            >
              <el-option
                v-for="type in availableRoomTypes"
                :key="type.value"
                :label="`${type.label} (${type.count}个站点)`"
                :value="type.value"
                :disabled="!!type.disabled"
                class="room-type-option"
              >
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                  "
                >
                  <span style="font-weight: 500">{{ type.label }}</span>
                  <span style="color: #999; font-size: 12px">
                    {{ type.count }}{{ $T("个站点") }}
                  </span>
                </div>
              </el-option>

              <div
                slot="empty"
                class="no-data-tip"
                style="padding: 12px; text-align: center; color: #999"
              >
                <i class="el-icon-info" style="margin-right: 4px"></i>
                {{ $T("暂无可用的站点类型") }}
              </div>
            </el-select>
          </div>
        </div>
      </div>
      <div class="room-list" v-loading="roomListLoading">
        <el-table
          :data="filteredRooms"
          @selection-change="handleRoomSelectionChange"
          height="300px"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column
            prop="name"
            :label="$T('站点名称')"
            show-overflow-tooltip
            min-width="120"
          />
          <el-table-column
            :label="$T('站点类型')"
            show-overflow-tooltip
            min-width="100"
          >
            <template slot-scope="scope">
              {{ getRoomTypeName(scope.row.roomtype) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="id"
            :label="$T('站点ID')"
            show-overflow-tooltip
            min-width="80"
          />
        </el-table>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">
        {{ $T("取消") }}
      </el-button>
      <el-button type="primary" @click="confirmRoomSelection">
        {{ $T("确定") }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getAvailableRooms } from "@/api/base-config";

export default {
  name: "RoomSelectorDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      roomListLoading: false,
      roomTypesLoading: false,
      selectedRoomType: null,
      allRooms: {},
      selectedRooms: [],
      roomTypeMap: {
        1: "配电室",
        2: "IT机房",
        3: "空调机房",
        4: "空压机房",
        5: "锅炉房",
        6: "管道房",
        7: "蓄电池房",
        8: "储能电站",
        9: "充电站",
        10: "风电场站",
        11: "网络机房",
        12: "光伏电站",
        15: "调光器室",
        16: "发电机室"
      }
    };
  },
  computed: {
    availableRoomTypes() {
      if (!this.allRooms || typeof this.allRooms !== "object") {
        return [];
      }

      const keys = Object.keys(this.allRooms);
      const filteredKeys = keys.filter(key => {
        const rooms = this.allRooms[key];
        return Array.isArray(rooms) && rooms.length > 0;
      });

      return filteredKeys.map(key => {
        const rooms = this.allRooms[key];
        const roomCount = rooms.length;
        const numericKey = Number(key);
        const label =
          this.roomTypeMap[numericKey] ||
          this.roomTypeMap[key] ||
          `站点类型${key}`;

        return {
          value: key,
          label: label,
          count: roomCount,
          disabled: roomCount === 0,
          rooms: rooms
        };
      });
    },
    filteredRooms() {
      if (!this.selectedRoomType || !this.allRooms[this.selectedRoomType]) {
        return [];
      }
      return this.allRooms[this.selectedRoomType] || [];
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.loadAllRooms();
      }
    }
  },
  methods: {
    async loadAllRooms() {
      try {
        const response = await getAvailableRooms(true);

        if (response && response.code === 0) {
          this.allRooms = response.data || {};

          // 等待Vue响应式更新
          await this.$nextTick();

          // 强制更新组件以确保选择框正确渲染
          this.$forceUpdate();
          await this.$nextTick();
        }
      } catch (error) {
        // 错误处理已由httping统一处理
        this.allRooms = {};
      }
    },
    handleRoomTypeChange() {
      this.selectedRooms = [];
    },
    handleRoomSelectionChange(selection) {
      this.selectedRooms = selection;
    },
    confirmRoomSelection() {
      if (this.selectedRooms.length === 0) {
        this.$message.warning(this.$T("请选择至少一个站点"));
        return;
      }
      const selectedRoom = this.selectedRooms[0];
      // 选中的站点

      // 根据实际API返回的字段名进行映射
      const roomData = {
        id: selectedRoom.id,
        name: selectedRoom.name, // 实际字段名是 name
        roomName: selectedRoom.name, // 为了兼容性，同时提供 roomName
        roomType: selectedRoom.roomtype, // 实际字段名是 roomtype
        roomtype: selectedRoom.roomtype // 保持原始字段名
        // 其他字段根据实际需要添加
      };

      // 返回的站点数据
      this.$emit("confirm", roomData);
      this.handleClose();
    },
    getRoomTypeName(roomType) {
      return this.roomTypeMap[roomType] || `站点类型${roomType}`;
    },
    handleClose() {
      // 关闭对话框
      this.$emit("update:visible", false);
    }
  }
};
</script>

<style scoped>
.room-selector-dialog .el-dialog__body {
  padding: 20px;
}
.room-list {
  border: 1px solid var(--BG3);
  border-radius: 4px;
}
.no-data-tip {
  padding: 12px 20px;
  text-align: center;
  color: var(--T3);
  font-size: 14px;
}
</style>

<style>
/* 修复 Element UI 选择框下拉列表显示问题 */
.room-selector-dialog .el-select-dropdown {
  z-index: 9999 !important;
  position: absolute !important;
}

.room-selector-dialog .el-popper {
  z-index: 9999 !important;
  position: absolute !important;
}

/* 确保下拉选项可见 */
.el-select-dropdown.el-popper[x-placement] {
  z-index: 9999 !important;
  position: absolute !important;
}

.el-select-dropdown .el-select-dropdown__item {
  display: block !important;
  padding: 0 20px !important;
  line-height: 34px !important;
  font-size: 14px !important;
  color: #606266 !important;
  cursor: pointer !important;
}

.el-select-dropdown .el-select-dropdown__item:hover {
  background-color: #f5f7fa !important;
}
</style>
