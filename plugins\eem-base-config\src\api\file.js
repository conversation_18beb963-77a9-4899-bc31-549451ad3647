import fetch from "eem-base/utils/fetch";
/**
 * 删除文件
 */
export function commonDeleteFile(params) {
  return fetch({
    url: `/eembaseconfig/v1/common/deleteFile`,
    method: "DELETE",
    params
  });
}

/**
 * 上传文件
 */
export function commonUploadFile(data) {
  return fetch({
    url: `/eembaseconfig/v1/common/uploadFile`,
    method: "POST",
    data
  });
}

/**
 * 下载文件
 */
export function commonDownloadFile(params) {
  return fetch({
    url: `/eembaseconfig/v1/common/downloadFile`,
    method: "GET",
    responseType: "blob",
    params
  });
}
