<template>
  <CetAside class="cet-aside">
    <template #aside>
      <div class="h-full flex flex-col">
        <customElSelect
          v-model="ElSelect_1.value"
          v-bind="ElSelect_1"
          v-on="ElSelect_1.event"
          class="mb-J3"
          :prefix_in="$T('能源类型')"
        >
          <ElOption
            v-for="item in ElOption_1.options_in"
            :key="item[ElOption_1.key]"
            :label="item[ElOption_1.label]"
            :value="item[ElOption_1.value]"
            :disabled="item[ElOption_1.disabled]"
          ></ElOption>
        </customElSelect>
        <CetGiantTree
          class="flex-auto"
          v-show="!showCheckbox"
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
        ></CetGiantTree>
        <CetGiantTree
          v-show="showCheckbox"
          class="flex-auto giant-tree"
          ref="cetGiantTree"
          v-bind="CetGiantTree_2"
          v-on="CetGiantTree_2.event"
        ></CetGiantTree>
      </div>
    </template>
    <template #container>
      <div class="h-full flex flex-col">
        <el-tooltip effect="light" :content="nodeName" placement="top-start">
          <div class="text-H2 font-bold mb-J3">
            {{ nodeName || "--" }}
          </div>
        </el-tooltip>
        <EnergyQuery
          v-bind="energyQuery"
          v-on="energyQuery.event"
          :energy="energy"
          class="flex-auto"
          ref="energyQuery"
        />
      </div>
    </template>
  </CetAside>
</template>
<script>
import customApi from "@/api/custom";
import EnergyQuery from "./energyQuery/energyQuery.vue";

export default {
  name: "networkEnergy",
  components: {
    EnergyQuery
  },

  computed: {
    energy() {
      return this.ElOption_1.options_in.find(
        item => this.ElSelect_1.value == item.energytype
      );
    },
    compareNodeLimit() {
      return this.$store.state.compareNodeLimit || 4;
    },
    standardEnergyType() {
      return this.$store.state.standardEnergyType;
    }
  },

  data() {
    const setNodeClasses = (treeId, treeNode) => {
      return treeNode.childSelectState == 2
        ? { add: ["halfSelectedNode"] }
        : { remove: ["halfSelectedNode"] };
    };
    return {
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          // 按照要求进行zTree的节点权限置灰操作
          view: {
            nodeClasses: setNodeClasses
          }
        },
        event: {
          currentNode_out: this.nodeClick_out //选中单行输出
        }
      },
      clickNode: null,
      checkedNodes: [],
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          callback: {
            beforeCheck: this.CetGiantTree_2_beforeCheck
          },
          // 按照要求进行zTree的节点权限置灰操作
          view: {
            nodeClasses: setNodeClasses
          }
        },
        event: {
          currentNode_out: this.nodeClick2_out, //选中单行输出
          checkedNodes_out: this.checkboxClick //勾选节点输出
        }
      },
      nodeName: "",
      showCheckbox: false,
      ElSelect_1: {
        value: null,
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      energyQuery: {
        clickNode: null,
        checkedNodes: [],
        event: {
          setTree1Node: this.setTree1Node,
          setTree2Node: this.setTree2Node,
          changeCheckbox: this.changeCheckbox,
          updateTree: this.updateTree
        }
      }
    };
  },
  watch: {
    showCheckbox: {
      handler: function (val) {
        if (val) {
          this.CetGiantTree_2.selectNode = this._.cloneDeep(
            this.CetGiantTree_1.selectNode
          );
        } else {
          this.CetGiantTree_1.selectNode = this._.cloneDeep(
            this.CetGiantTree_2.selectNode
          );
        }
      }
    }
  },

  methods: {
    updateTree(type, name) {
      if (type === "expandNode") {
        // 展开节点
        this.$refs.cetGiantTree.ztreeObj.expandAll(false);
        const node = this.$refs.cetGiantTree.ztreeObj.getNodeByParam(
          "tree_id",
          name
        );
        this.$refs.cetGiantTree.ztreeObj.selectNode(node, false);
        this.$refs.cetGiantTree.ztreeObj.expandNode(
          node.getParentNode(),
          true,
          false
        );
      } else if (type === "cancelSelect") {
        // 取消节点选中
        const node = this.$refs.cetGiantTree.ztreeObj.getNodeByParam(
          "tree_id",
          name
        );
        this.$refs.cetGiantTree.ztreeObj.checkNode(node, false, false);

        // 高亮取消的节点
        this.$refs.cetGiantTree.ztreeObj.expandAll(false);
        this.$refs.cetGiantTree.ztreeObj.selectNode(node, false);
        this.$refs.cetGiantTree.ztreeObj.expandNode(
          node.getParentNode(),
          true,
          false
        );
      }
    },
    CetGiantTree_2_beforeCheck(treeId, treeNode) {
      if (["project", "room"].includes(treeNode.modelLabel)) {
        return false;
      }
    },
    setTree1Node(obj) {
      this.CetGiantTree_1.selectNode = obj;
    },
    setTree2Node(obj) {
      this.CetGiantTree_2.selectNode = obj;
      this.CetGiantTree_2.checkedNodes = [obj];
    },
    changeCheckbox(val) {
      this.showCheckbox = val;
    },
    async grtCetNoEnumSelect_1() {
      const res = await customApi.getProjectEnergy();
      if (res.code !== 0) {
        this.ElOption_1.options_in = [];
        this.ElSelect_1.value = null;
        return;
      }
      let list = res.data || [];
      const selectData = list.filter(
        item => this.standardEnergyType.indexOf(item.energytype) === -1
      );
      this.ElOption_1.options_in = selectData;
      if (selectData.find(item => item.energytype == 2)) {
        this.ElSelect_1.value = 2;
      } else {
        this.ElSelect_1.value = selectData[0].energytype;
      }
    },
    nodeClick_out(val) {
      // 按照张壮要求，在通用3.5迭代中，重新添加成点击弹窗处理
      if (val.childSelectState == 2) {
        return this.$message.warning($T("没有该节点权限"));
      }
      if (["project", "room"].includes(val.modelLabel)) {
        this.$message.warning($T("请选择设备节点！"));
        return;
      }
      if (this.showCheckbox) {
        return;
      }
      if (val) {
        this.nodeName = val.name;
        this.clickNode = val;
        this.energyQuery.clickNode = val;
        this.$nextTick(() => {
          this.$refs.energyQuery.nodeClick_out(val);
        });
      } else {
        this.energyQuery.clickNode = {};
        this.clickNode = {};
        this.$message.warning($T("请选择节点！"));
      }
    },
    nodeClick2_out(val) {
      // 按照张壮要求，在通用3.5迭代中，重新添加成点击弹窗处理
      if (val.childSelectState == 2) {
        return this.$message.warning($T("没有该节点权限"));
      }
      if (["project", "room"].includes(val.modelLabel)) {
        this.$message.warning($T("请选择设备节点！"));
        return;
      }
      if (!this.showCheckbox) {
        return;
      }
      if (val) {
        this.nodeName = val.name;
        this.clickNode = val;
        this.energyQuery.clickNode = val;
        this.CetGiantTree_2.checkedNodes = [this._.cloneDeep(val)];
      } else {
        this.clickNode = {};
        this.energyQuery.clickNode = {};
        this.$message.warning($T("请选择节点！"));
      }
    },
    checkboxClick(val) {
      if (val.length > this.compareNodeLimit) {
        this.$message.warning($T("最多对比{0}个节点", this.compareNodeLimit));
        this.CetGiantTree_2.checkedNodes = this._.cloneDeep(this.checkedNodes);
        return;
      }
      this.energyQuery.checkedNodes = val;
      this.checkedNodes = val;
      this.$nextTick(() => {
        this.$refs.energyQuery.getChartData3();
      });
    },
    ElSelect_1_change_out() {
      this.clickNode = null;
      this.energyQuery.clickNode = null;
      this.CetGiantTree_1.selectNode = null;
      this.checkedNodes = [];
      this.energyQuery.checkedNodes = [];
      this.CetGiantTree_2.selectNode = null;
      this.CetGiantTree_2.checkedNodes = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
      this.getTreeData(() => {
        this.CetGiantTree_1.selectNode = this._.get(
          this.CetGiantTree_1.inputData_in,
          "[0].children[0].children[0]"
        );
        this.CetGiantTree_2.selectNode = this._.get(
          this.CetGiantTree_2.inputData_in,
          "[0].children[0].children[0]"
        );
      });
    },
    async getTreeData(cb) {
      const data = {
        nodeTreeGroupId: 1,
        energyType: this.ElSelect_1.value
      };
      const res = await customApi.getNodeTree(data);
      if (res.code !== 0) {
        return;
      }
      const treeData = this.setTreeDisabled(res.data);
      this.getDisabledNode(treeData);
      this.CetGiantTree_1.inputData_in = treeData;
      this.CetGiantTree_2.inputData_in = treeData;
      this.CetGiantTree_1.selectNode = this._.get(
        res,
        "data[0].children[0].children[0]"
      );
      cb && cb();
    },
    setTreeDisabled(treeData) {
      treeData.forEach(item => {
        item.chkDisabled = true;
        if (item.children) {
          item.children.forEach(i => {
            i.chkDisabled = true;
          });
        }
      });
      return treeData;
    },
    // 3.5迭代中对勾选框中的没有权限的节点进行禁用处理
    getDisabledNode(tree) {
      tree.forEach(item => {
        if (item.childSelectState === 2) {
          item.chkDisabled = true;
        }
        if (item.children && item.children.length) {
          this.getDisabledNode(item.children);
        }
      });
    },
    async init() {
      this.showCheckbox = false;
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
      await this.grtCetNoEnumSelect_1();
      this.getTreeData();
    }
  },

  mounted() {
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  // 进行zTree中的节点文本权限置灰
  :deep(.halfSelectedNode .node_name) {
    @include font_color(T6);
  }
  :deep(.checkbox_false_disable) {
    @include background_color(T6);
    @include border_color(T6);
  }
}

.head-label {
  @include font_size(H2);
  font-weight: bold;
  line-height: 32px;
  position: relative;
  .radiogroup {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    z-index: 1;
  }
}
.giant-tree {
  :deep(li:has(.checkbox_true_full) > .chk) {
    @include border_color(ZS);
    @include background_color(ZS);
  }
  :deep(li:has(.checkbox_true_full_focus) > .chk) {
    @include border_color(ZS);
    @include background_color(ZS);
  }
  :deep(li:has(.checkbox_true_part) > .chk) {
    @include border_color(ZS);
    @include background_color(ZS);
  }
  :deep(li:has(.checkbox_true_part_focus) > .chk) {
    @include border_color(ZS);
    @include background_color(ZS);
  }
}
.cet-aside :deep(.cet-content-aside-container) {
  background-color: var(--BG1);
  border-radius: var(--Ra);
}
</style>
