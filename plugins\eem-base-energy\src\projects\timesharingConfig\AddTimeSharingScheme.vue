<template>
  <div>
    <CetDialog
      class="CetDialog max"
      ref="CetDialog"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
    >
      <div>
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="label mb-J1">
              {{ $T("分时方案名称") }}
              <span style="color: red">*</span>
            </div>
            <div class="value">
              <ElInput
                v-model="ElInput_1.value"
                v-bind="ElInput_1"
                v-on="ElInput_1.event"
              ></ElInput>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label mb-J1">
              {{ $T("能源类型") }}
              <span style="color: red">*</span>
            </div>
            <div class="value">
              <ElSelect
                v-model="ElSelect_1.value"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in ElOption_1.options_in"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </ElSelect>
            </div>
          </el-col>
          <el-col :span="8" v-show="ElOption_2.options_in?.length > 1">
            <div class="label mb-J1">
              {{ $T("所属项目") }}
              <span style="color: red">*</span>
            </div>
            <div class="value">
              <ElSelect
                v-model="ElSelect_2.value"
                v-bind="ElSelect_2"
                v-on="ElSelect_2.event"
                :disabled="!!editId_in"
              >
                <ElOption
                  v-for="item in ElOption_2.options_in"
                  :key="item[ElOption_2.key]"
                  :label="item[ElOption_2.label]"
                  :value="item[ElOption_2.value]"
                  :disabled="item[ElOption_2.disabled]"
                ></ElOption>
              </ElSelect>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-tabs v-model="activeTabsName" class="eem-tabs-custom">
        <el-tab-pane :label="$T('时段方案')" name="1" style="height: 100%">
          <div class="flex-row flex">
            <div class="flex-col flex" style="width: 350px">
              <div class="daySchemeArr mb-J3 flex-auto">
                <div
                  v-for="(item, index) in daySchemeArr"
                  :key="index"
                  :class="{
                    action: actionDayScheme === index ? true : false,
                    ' flex-row flex': true
                  }"
                  @click="daySchemeClick(index)"
                >
                  <span
                    :style="{
                      background: getTimeSchemeColor(index)
                    }"
                  ></span>
                  <el-tooltip effect="light" :content="item.name">
                    <span class="flex-auto ml-J1 text-ellipsis">
                      {{ item.name }}
                    </span>
                  </el-tooltip>
                </div>
              </div>
              <div class="clearfix">
                <CetButton
                  class="fr ml-J1"
                  v-bind="CetButton_1"
                  v-on="CetButton_1.event"
                ></CetButton>
                <CetButton
                  class="fr ml-J1"
                  v-bind="CetButton_copy"
                  v-on="CetButton_copy.event"
                ></CetButton>
                <CetButton
                  class="fr ml-J1"
                  v-bind="CetButton_2"
                  v-on="CetButton_2.event"
                ></CetButton>
                <CetButton
                  class="fr ml-J1"
                  v-bind="CetButton_3"
                  v-on="CetButton_3.event"
                ></CetButton>
              </div>
            </div>
            <div class="flex-auto ml-J3">
              <CetTable
                style="height: 300px"
                :data.sync="CetTable_1.data"
                :dynamicInput.sync="CetTable_1.dynamicInput"
                v-bind="CetTable_1"
                v-on="CetTable_1.event"
              >
                <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
                <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
                <ElTableColumn
                  v-bind="ElTableColumn_timeInterval"
                ></ElTableColumn>
              </CetTable>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane name="2" style="height: 100%">
          <span slot="label">
            {{ $T("关联年历") }}
            <el-tooltip
              effect="light"
              :content="$T('已关联年历时段方案出现点标记')"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <div class="flex-row flex">
            <div class="flex-col flex" style="width: 350px">
              <div class="daySchemeArr mb-J1 flex-auto">
                <div
                  v-for="(item, index) in daySchemeArr"
                  :key="index"
                  :class="{
                    action: actionDayScheme === index ? true : false,
                    ' flex-row flex': true
                  }"
                  @click="daySchemeClick(index)"
                >
                  <span
                    :style="{
                      background: getTimeSchemeColor(index)
                    }"
                  ></span>
                  <el-tooltip effect="light" :content="item.name">
                    <span class="flex-auto ml-J1 text-ellipsis">
                      {{ item.name }}
                    </span>
                  </el-tooltip>
                  <span
                    class="ml-J1 drop"
                    v-if="alreadyConfigIndexs.includes(index)"
                  ></span>
                </div>
              </div>
            </div>
            <div class="flex-auto ml-J3">
              <div style="height: 100%">
                <yearPicker
                  style="height: 100%"
                  :id_in="'AddTimeSharingSchemeYearPicker'"
                  :color_in="yearPicker.color_in"
                  :disable_in="yearPicker.disable_in"
                  :dateChangeCheck="true"
                  ref="yearPicker"
                  :saveFn="yearPicker_saveFn"
                  :timeSchemeColors="timeSchemeColors"
                  @year_out="yearPicker_year_out"
                  @selectHandle="yearPicker_selectHandle"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer">
        <CetButton
          :visible_in="activeTabsName == '2'"
          v-bind="CetButton_4"
          v-on="CetButton_4.event"
        ></CetButton>
        <CetButton
          class="ml-J1"
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="ml-J1"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
    <AddDayScheme
      :visibleTrigger_in="AddDayScheme.visibleTrigger_in"
      :closeTrigger_in="AddDayScheme.closeTrigger_in"
      :inputData_in="AddDayScheme.inputData_in"
      @confirm_out="AddDayScheme_confirm_out"
    />
  </div>
</template>
<script>
import AddDayScheme from "./AddDayScheme.vue";
import yearPicker from "./yearPicker/yearPicker.vue";
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
export default {
  name: "AddTimeSharingScheme",
  components: {
    AddDayScheme,
    yearPicker
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    editId_in: Number,
    timeSchemeColors: Array,
    rootNodes: Array
  },

  computed: {
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data() {
    return {
      activeTabsName: "1",
      // 日时段方案列表
      daySchemeArr: [],
      actionDayScheme: 0,
      yearPicker: {
        disable_in: true,
        color_in: "color1",
        date: null
      },
      CetDialog_1: {
        title: $T("分时方案"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          closeTrigger_out: this.CetDialog_1_closeTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      AddDayScheme: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      ElSelect_1: {
        value: 2,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: 0,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_2: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {
          width: "100%"
        },
        event: {}
      },
      CetTable_1: {
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT$T("  小于等于 ")LE$T(" 大于 ")GT$T(" 大于等于")GE$T(" 相等  ")EQ$T("  不等于 ")NE$T(" 像")LIKE$T(" 间于")BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        label: $T("序号"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "70" //绝对宽度
      },
      ElTableColumn_name: {
        prop: "name", // 支持path a[0].b
        label: $T("时段名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "200", //绝对宽度
        formatter: function (val) {
          if (val.name) {
            return val.name;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_timeInterval: {
        prop: "timeInterval", // 支持path a[0].b
        label: $T("时段"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.timeInterval) {
            return val.timeInterval;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: $T("新增"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: $T("编辑"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetButton_3: {
        visible_in: true,
        disable_in: true,
        title: $T("删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      CetButton_copy: {
        visible_in: true,
        disable_in: true,
        title: $T("复制"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_copy_statusTrigger_out
        }
      },
      CetButton_4: {
        visible_in: true,
        disable_in: false,
        title: $T("一键清空"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_4_statusTrigger_out
        }
      },
      daySchemeDetail: null,
      oneQuery: false,
      alreadyConfigIndexs: []
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    async visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.alreadyConfigIndexs = [];
      this.yearPicker.date = this.$moment().valueOf();
      this.oneQuery = false;
      this.ElOption_2.options_in = this.rootNodes;
      this.ElSelect_2.value = this.rootNodes?.[0]?.id;
      if (this.editId_in) {
        this.oneQuery = 1;
      }
      await this.getEnergytype();
      this.actionDayScheme = 0;
      this.activeTabsName = "1";
      this.$nextTick(() => {
        this.$refs.yearPicker.disable(this.yearPicker.disable_in);
        this.$refs.yearPicker.setYear(this.$moment());
      });
      if (!this.editId_in && !this.inputData_in) {
        this.CetDialog_1.title = $T("新增分时方案");
        if (this.ElOption_1.options_in.find(i => i.id === 2)) {
          this.ElSelect_1.value = 2;
        } else {
          this.ElSelect_1.value = this._.get(
            this.ElOption_1,
            "options_in[0].id"
          );
        }
        this.ElInput_1.value = "";
        this.daySchemeArr = [];
      } else {
        if (this.editId_in) {
          this.CetDialog_1.title = $T("编辑分时方案");
          this.oneQuery = 1;
          await this.getTimeShareSchemeDetail();
          setTimeout(() => {
            this.oneQuery = false;
          }, 500);
          this.initEditAndCopy(this.daySchemeDetail);
        } else {
          this.CetDialog_1.title = $T("复制分时方案");
          this.initEditAndCopy(this.inputData_in);
        }
      }
      this.daySchemeClick(this.actionDayScheme);
      this.$nextTick(() => {
        this.renderYear(this.$moment().year());
      });
    },
    daySchemeArr: {
      handler: function (val) {
        if (val.length > 0) {
          this.CetButton_2.disable_in = false;
          this.CetButton_3.disable_in = false;
          this.CetButton_copy.disable_in = false;
          this.yearPicker.disable_in = false;
        } else {
          this.CetButton_2.disable_in = true;
          this.CetButton_3.disable_in = true;
          this.CetButton_copy.disable_in = true;
          this.yearPicker.disable_in = true;
        }
      },
      deep: true
    }
  },

  methods: {
    // 日历输出时间
    async yearPicker_year_out(val) {
      if (this.editId_in) {
        if (typeof this.oneQuery === "number") return;
        await this.getTimeShareSchemeDetail({
          startTime: this.$moment(val).startOf("year").valueOf(),
          endTime: this.$moment(val).endOf("year").valueOf() + 1
        });
        this.upDateYearPicker(this.daySchemeDetail);
      }
      this.renderYear(this.$moment(val).year());
      this.yearPicker.date = val;
    },
    // 渲染年历
    renderYear(year) {
      // 清空
      this.$refs.yearPicker.drap_select(
        this.$moment().startOf("year").format("MM-DD"),
        this.$moment().endOf("year").format("MM-DD"),
        "",
        true
      );

      if (this.actionDayScheme != -1) {
        if (this.daySchemeArr && this.daySchemeArr.length > 0) {
          this.daySchemeArr.forEach((item, index) => {
            if (item.timeInterval && item.timeInterval.length > 0) {
              if (
                item.timeInterval[0].daysetObj &&
                item.timeInterval[0].daysetObj[year]
              ) {
                item.timeInterval[0].daysetObj[year].forEach(ite => {
                  this.$refs.yearPicker.drap_select(
                    this.$moment(ite.day).format("MM-DD"),
                    this.$moment(ite.day).format("MM-DD"),
                    "color" + (index + 1)
                  );
                });
              }
            }
          });
        }
      }
      this.setAlreadyConfigIndexs();
    },
    //获取能源类型
    async getEnergytype() {
      this.ElOption_1.options_in = [];
      const response = await customApi.getProjectEnergyNoStandardized();
      if (response.code !== 0) {
        return;
      }
      const data = response.data || [];
      let selectData = data.map(item => ({
        id: item.energytype,
        text: item.name
      }));
      this.ElOption_1.options_in = selectData;
    },
    // 日方案点击
    daySchemeClick(index) {
      this.actionDayScheme = index;
      if (this.daySchemeArr.length > 0) {
        this.CetTable_1.data = this._.cloneDeep(
          this.daySchemeArr[this.actionDayScheme].timeInterval
        );
      } else {
        this.CetTable_1.data = [];
      }
      this.$nextTick(() => {
        this.$refs.yearPicker.setColor("color" + (index + 1));
      });
    },
    // 日时段方案的输出
    AddDayScheme_confirm_out(val) {
      // console.log("日时段方案输出", val);
      if (val.old) {
        // 编辑输出
        var flag = true;
        this.daySchemeArr.forEach((item, index) => {
          if (index != this.actionDayScheme && item.name == val.name && flag) {
            this.$message({
              message: $T("已存在相同日时段方案"),
              type: "warning"
            });
            flag = false;
            return;
          }
        });
        if (flag) {
          this.daySchemeArr[this.actionDayScheme] = val;
        }
      } else {
        // 新增输出
        if (
          this.daySchemeArr.filter(item => item.name == val.name).length > 0
        ) {
          this.$message({
            message: $T("已存在相同日时段方案"),
            type: "warning"
          });
          return;
        } else {
          this.daySchemeArr.push(val);
        }
      }
      this.daySchemeClick(this.actionDayScheme);
    },
    CetButton_1_statusTrigger_out(val) {
      this.AddDayScheme.inputData_in = {};
      this.AddDayScheme.visibleTrigger_in = this._.cloneDeep(val);
    },
    CetButton_2_statusTrigger_out(val) {
      this.AddDayScheme.inputData_in = this._.cloneDeep(
        this.daySchemeArr[this.actionDayScheme]
      );
      this.AddDayScheme.visibleTrigger_in = this._.cloneDeep(val);
    },
    CetButton_3_statusTrigger_out() {
      this.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        cancelButtonClass: "btn-custom-cancel",
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: (action, instance, done) => {
          if (action == "confirm") {
            this.daySchemeArr.splice(this.actionDayScheme, 1);
            this.daySchemeClick(0);
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action != "confirm") {
            this.$message({
              type: "info",
              message: $T("取消删除")
            });
          }
        }
      });
    },
    CetButton_copy_statusTrigger_out() {
      const actionDayScheme = this.daySchemeArr[this.actionDayScheme];
      this.$prompt($T("请输入方案名称"), $T("复制"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        closeOnClickModal: false,
        inputValue: $T("{0}-副本", actionDayScheme.name),
        inputValidator: val => {
          if (val.length > 20) {
            return $T("方案名称长度在 1 到 20 个字符");
          } else if (!common.check_pattern_name.pattern.test(val)) {
            return $T("方案名称不能输入特殊字符");
          } else if (this.daySchemeArr.find(i => i.name === val)) {
            return $T("已存在相同日时段方案");
          }
          return true;
        }
      })
        .then(({ value }) => {
          const scheme = {
            name: value,
            timeInterval: actionDayScheme.timeInterval.map((item, index) => {
              return {
                id: -(index + 5),
                name: item.name,
                timeInterval: item.timeInterval
              };
            })
          };
          scheme.name = value;
          this.daySchemeArr.push(scheme);
        })
        .catch(() => {});
    },
    CetButton_4_statusTrigger_out() {
      this.$refs.yearPicker.drap_select(
        this.$moment().startOf("year").format("MM-DD"),
        this.$moment().endOf("year").format("MM-DD"),
        "",
        true
      );
      this.$refs.yearPicker.clickFlag = true;
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetDialog_1_closeTrigger_out() {
      this.$emit("finishData_out", new Date().getTime());
    },
    CetButton_confirm_statusTrigger_out() {
      this.addScheme();
    },
    // 传给年历组件的保存方法
    async yearPicker_saveFn() {
      await this.addScheme(true);
    },
    async addScheme(yearPickerConfirm) {
      if (!this.ElSelect_2.value) {
        return this.$message({
          message: $T("请选择所属项目"),
          type: "warning"
        });
      }
      if (!this.ElInput_1.value) {
        this.$message({
          message: $T("方案名称不能为空"),
          type: "warning"
        });
        return false;
      } else if (this.ElInput_1.value.length > 20) {
        this.$message({
          message: $T("方案名称长度在 1 到 20 个字符"),
          type: "warning"
        });
        return false;
      } else if (
        !common.check_pattern_name.pattern.test(this.ElInput_1.value)
      ) {
        this.$message({
          message: $T("方案名称不能输入特殊字符"),
          type: "warning"
        });
        return false;
      }
      var yearPickerAll = this.$refs.yearPicker.getAll();
      for (let index = 1; index <= this.daySchemeArr.length; index++) {
        if (this.daySchemeArr[index - 1]) {
          this.daySchemeArr[index - 1].dayset = yearPickerAll["color" + index];
        }
      }
      const rootNode = this.ElOption_2.options_in.find(
        item => item.id == this.ElSelect_2.value
      );
      var data = {
        energytype: this.ElSelect_1.value,
        id: 0,
        // modelLabel: "string",
        name: this.ElInput_1.value,
        projectId: this.projectId,
        timeshareperiod_model: [
          {
            dayset_model: [
              // {
              //   day: 0,
              //   id: 0,
              //   modelLabel: "string"
              // }
            ],
            dayshareset_model: [
              // {
              //   beginhour: 0,
              //   beginminute: 0,
              //   endhour: 0,
              //   endminute: 0,
              //   id: 0,
              //   modelLabel: "string"
              // }
            ],
            id: 0,
            identification: "string",
            modelLabel: "string",
            name: "string"
          }
        ],
        rootnodeid: rootNode.id,
        rootnodelabel: rootNode.modelLabel
      };
      if (this.editId_in) {
        data.id = this.editId_in;
      }
      var timeshareperiod_model = [];
      this.daySchemeArr.forEach(item => {
        var obj = {
          id: 0,
          identification: "",
          // modelLabel: "string",
          name: item.name,
          dayset_model: [],
          dayshareset_model: []
        };
        // 编辑才传id
        if (this.editId_in) {
          obj.id = item.id;
        }
        // 取关联日设置
        if (item.dayset && item.dayset.length > 0) {
          item.dayset.forEach(daysetItem => {
            obj.dayset_model.push({
              day: this.$moment(daysetItem).startOf("d").valueOf(),
              id: 0
            });
          });
        }
        // 取具体时段设置
        if (item.timeInterval && item.timeInterval.length > 0) {
          item.timeInterval.forEach(ite => {
            var newObj = this._.cloneDeep(obj);
            if (this.editId_in) {
              newObj.daysetId = ite.id > 0 ? ite.id : null;
              newObj.id = ite.id > 0 ? ite.id : null;
            }
            newObj.identification = ite.name;
            if (ite.timeInterval) {
              ite.timeInterval.split(";").forEach(timeIntervalItem => {
                if (timeIntervalItem) {
                  newObj.dayshareset_model.push({
                    beginhour: Number(
                      timeIntervalItem.split("~")[0].split(":")[0]
                    ),
                    beginminute: Number(
                      timeIntervalItem.split("~")[0].split(":")[1]
                    ),
                    endhour: Number(
                      timeIntervalItem.split("~")[1].split(":")[0]
                    ),
                    endminute: Number(
                      timeIntervalItem.split("~")[1].split(":")[1]
                    ),
                    id: 0
                    // modelLabel: "string"
                  });
                }
              });
            }
            timeshareperiod_model.push(newObj);
          });
        } else {
          // 无具体时段设置
          timeshareperiod_model.push(this._.cloneDeep(obj));
        }
      });
      data.timeshareperiod_model = timeshareperiod_model;
      const params = {
        startTime: this.$moment(this.yearPicker.date).startOf("year").valueOf(),
        endTime: this.$moment(this.yearPicker.date).endOf("year").valueOf() + 1
      };
      const res = await customApi.saveTimeShareScheme(data, params);

      if (res.code !== 0) {
        return;
      }
      this.$message({
        message: $T("保存成功"),
        type: "success"
      });
      this.$refs.yearPicker.clickFlag = false;
      if (!yearPickerConfirm) {
        // 按编辑重新走弹框逻辑
        this.imitateEdit(res);
      }
    },
    imitateEdit(res) {
      // 通知父组件更新传参
      this.$emit("updateParams", {
        inputData_in: null,
        editId_in: this._.get(res, "data[0].id")
      });
      const yearPickerDate = this.yearPicker.date;
      this.$nextTick(async () => {
        this.oneQuery = 1;
        await this.getTimeShareSchemeDetail({
          startTime: this.$moment(yearPickerDate).startOf("year").valueOf(),
          endTime: this.$moment(yearPickerDate).endOf("year").valueOf() + 1
        });
        setTimeout(() => {
          this.oneQuery = false;
        }, 500);
        this.initEditAndCopy(this.daySchemeDetail);
        this.daySchemeClick(this.actionDayScheme);
      });
    },
    async getTimeShareSchemeDetail(params_in) {
      if (!this.editId_in) return;
      if (typeof this.oneQuery === "number") {
        if (this.oneQuery < 1) return;
        this.oneQuery -= 1;
      }
      let params = {
        startTime: this.$moment().startOf("year").valueOf(),
        endTime: this.$moment().endOf("year").valueOf() + 1
      };
      const res = await customApi.timeShareSchemeById(
        this.editId_in,
        params_in || params
      );
      if (res.code !== 0) {
        return;
      }
      let timeshareperiod_model =
        this._.get(res, "data[0].timeshareperiod_model", []) || [];
      timeshareperiod_model.forEach(item => {
        var daysharesetText = [];
        if (item.dayshareset_model && item.dayshareset_model.length > 0) {
          item.dayshareset_model.forEach(ite => {
            if (Number(ite.beginhour) < 10) {
              ite.beginhour = "0" + Number(ite.beginhour);
            }
            if (Number(ite.beginminute) < 10) {
              ite.beginminute = "0" + Number(ite.beginminute);
            }
            if (Number(ite.endhour) < 10) {
              ite.endhour = "0" + Number(ite.endhour);
            }
            if (Number(ite.endminute) < 10) {
              ite.endminute = "0" + Number(ite.endminute);
            }
            daysharesetText.push(
              `${ite.beginhour}:${ite.beginminute}~${ite.endhour}:${ite.endminute}`
            );
          });
        }
        item.daysharesetText = daysharesetText.join(";");
      });
      if (
        res.data[0].timeshareperiod_model &&
        res.data[0].timeshareperiod_model.length > 0
      ) {
        res.data[0].timeshareperiod_model.forEach(item => {
          if (item.dayset_model && item.dayset_model.length > 0) {
            item.dayset_model.forEach(ite => {
              if (!item.daysetObj) {
                item.daysetObj = {};
              }
              if (!item.daysetObj[this.$moment(ite.day).year()]) {
                item.daysetObj[this.$moment(ite.day).year()] = [ite];
              } else {
                item.daysetObj[this.$moment(ite.day).year()].push(ite);
              }
            });
          }
        });
      }
      // 过滤出日时段方案
      let dayArr = [];
      if (
        res.data[0].timeshareperiod_model &&
        res.data[0].timeshareperiod_model.length > 0
      ) {
        res.data[0].timeshareperiod_model.forEach(item => {
          const dataSource = dayArr.find(i => i.name === item.name);
          if (!dataSource) {
            dayArr.push({
              name: item.name,
              data: [item]
            });
          } else {
            dataSource.data.push(item);
          }
        });
      }
      res.data[0].timeshareperiodArr = dayArr;
      this.daySchemeDetail = res.data[0];
    },
    initEditAndCopy(val) {
      // 编辑或复制新增
      this.ElSelect_1.value = val.energytype;
      this.ElInput_1.value = val.name;
      this.daySchemeArr = val.timeshareperiodArr;
      this.ElSelect_2.value = val.rootnodeid;
      this.daySchemeArr.forEach(item => {
        item.data.forEach(ite => {
          ite.copyName = ite.name;
          ite.name = ite.identification;
          ite.timeInterval = ite.daysharesetText;
        });
        this.$set(item, "timeInterval", item.data);
      });
    },
    upDateYearPicker(val) {
      if (!val) return;
      // 接口数据扁平化
      let dayAll = [];
      if (val.timeshareperiodArr) {
        val.timeshareperiodArr.forEach(item => {
          if (item.data) {
            item.data.forEach(i => {
              dayAll.push(i);
            });
          }
        });
      }

      // 将查询年份的关联的日期复制过来
      this.daySchemeArr.forEach(item => {
        if (item.timeInterval && item.timeInterval.length) {
          // 在原数据中找到编辑的日时段id
          const timeId = item.timeInterval.find(i => i.id > 0)?.id;
          // 在历史方案中找到日关联最长的那个
          const daySchemeName = dayAll.find(day => day.id === timeId)?.name;
          let maxDayset,
            maxLengthIndex = 0;
          dayAll.forEach(day => {
            if (day.name === daySchemeName) {
              if (day.dayset_model) {
                if (!maxLengthIndex) {
                  maxDayset = day;
                  maxLengthIndex = day.dayset_model.length;
                } else if (day.dayset_model.length > maxLengthIndex) {
                  maxDayset = day;
                  maxLengthIndex = day.dayset_model.length;
                }
              }
            }
          });
          // 重新给日时段进行关联日期赋值
          if (maxDayset) {
            item.timeInterval.forEach(i => {
              i.daysetObj = this._.cloneDeep(maxDayset.daysetObj);
              i.dayset_model = this._.cloneDeep(maxDayset.dayset_model);
            });
          }
        }
      });
    },
    getTimeSchemeColor(index) {
      return this.timeSchemeColors[index % 12];
    },
    setAlreadyConfigIndexs() {
      let alreadyConfigIndexs = [];
      const yearPickerAll = this.$refs.yearPicker.getAll() || {};
      for (const key in yearPickerAll) {
        if (Object.hasOwnProperty.call(yearPickerAll, key)) {
          alreadyConfigIndexs.push(key.split("color")[1] - 1);
        }
      }
      this.alreadyConfigIndexs = alreadyConfigIndexs;
    },
    yearPicker_selectHandle() {
      this.setAlreadyConfigIndexs();
    }
  }
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep() {
    .el-tabs__nav-wrap::after {
      display: none;
    }
    .el-tabs__header {
      @include margin_bottom(J1);
      @include margin_left(J1);
    }
  }
}
.addTimeSharingScheme_title {
  text-align: center;
  line-height: 25px;
  height: 25px;
  box-shadow: none;
  border-radius: 11px;
  margin-bottom: 5px;
}
.daySchemeArr {
  text-align: left;
  overflow: auto;
  max-height: 670px;
  & > div {
    height: 16px;
    cursor: pointer;
    @include border_color(B1);
    @include padding_left(J3);
    @include padding_right(J3);
    padding-top: 8px;
    padding-bottom: 8px;
    & > span {
      display: inline-block;
      height: 16px;
      width: 16px;
      line-height: 16px;
    }
    .drop {
      display: inline-block;
      margin-top: 5px;
      height: 6px;
      width: 6px;
      border-radius: 2px;
      @include background_color(ZS);
      line-height: 16px;
    }
  }
  & > div.action {
    @include background_color(BG4);
  }
  & > div:hover {
    @include background_color(BG2);
  }
}
</style>
