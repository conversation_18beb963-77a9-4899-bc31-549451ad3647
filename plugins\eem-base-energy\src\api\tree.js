import fetch from "eem-base/utils/fetch";

// 查询维度节点树列表（包含id为-1的固定管理层级）
export function getAttributeDimensionTreeNodeConfig(data, params) {
  return fetch({
    url: `/eembaseenergy/v1/attribute-dimension/tree/node-config`,
    method: "POST",
    data,
    params
  });
}

/**
 * 获取多维度节点树 可支持传入末端需要保留的节点
 * @param {*keepNodeTypes} array 传入末端保留的节点的modelLabel，默认不过滤
 * @returns
 */
export function dimensionTreeFilterByEnergytype(data) {
  return fetch({
    url: `/eembaseenergy/v1/attribute-dimension/tree/filter-by-energytype`,
    method: "POST",
    data
  });
}

/**
 * 获取节点树
 */
export function getNodeTree(data) {
  return fetch({
    url: `/eembaseenergy/v1/node/tree`,
    method: "POST",
    data
  });
}

/**
 * 根据指定的业务查询指定租户或者节点下的节点树，在节点未指定的情况下查询租户的
 */
export function getBusinessTree(data) {
  return fetch({
    url: `/eembaseenergy/v1/node/tree/business`,
    method: "POST",
    data
  });
}

/**
 * 查询根节点
 */
export function rootNode() {
  return fetch({
    url: `/eembaseenergy/v1/node/root-node`,
    method: "GET"
  });
}

/**
 * 查询指定节点类型以及子级
 */
export function findTreeConfigNode(data) {
  return fetch({
    url: `/eembaseenergy/v1/tree-config/findNode`,
    method: "POST",
    data
  });
}

//获取可以关联管理层级节点的节点树
export function relatedTagLabel() {
  return fetch({
    url: `/eembaseenergy/v1/attributedimension/nodeconfig/related-tag-label`,
    method: "GET"
  });
}

//查询管理层级节点树，关联tag的时候用
export function nodeTreeRelateTag(data) {
  return fetch({
    url: `/eembaseenergy/v1/attribute-dimension/tree/node-tree/relate-tag`,
    method: "POST",
    data
  });
}

export function getNodeTreeSimpleSupplyTo(data, params) {
  return fetch({
    url: `/eembaseenergy/v1/node/tree/no-share`,
    method: "POST",
    data,
    params
  });
}

export function getBusinessTreeSimpleSupplyTo(data, params) {
  return fetch({
    url: `/eembaseenergy/v1/node/tree/business/no-share`,
    method: "POST",
    data,
    params
  });
}
