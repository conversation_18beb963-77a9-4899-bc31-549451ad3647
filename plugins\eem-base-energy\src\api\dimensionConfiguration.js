import fetch from "eem-base/utils/fetch";
const version = "v1";

// 查询维度配置的内容
export function queryAttributedimensionConfig(data) {
  return fetch({
    url: `/eembaseenergy/v1/attributedimension/config/query`,
    method: "POST",
    data
  });
}

// 新增维度属性
export function insertAttributedimensionConfig(data) {
  return fetch({
    url: `/eembaseenergy/v1/attributedimension/config/insert`,
    method: "POST",
    data
  });
}

// 编辑配置列表
export function editAttributedimensionConfig(data) {
  return fetch({
    url: `/eembaseenergy/v1/attributedimension/config/edit`,
    method: "POST",
    data
  });
}

// 删除配置列表
export function deleteAttributedimensionConfig(data) {
  return fetch({
    url: `/eembaseenergy/v1/attributedimension/config/delete`,
    method: "DELETE",
    data
  });
}

// 查询维度标签和赋值的列表
export function attributedimensionNodeConfigTag(data) {
  return fetch({
    url: `/eembaseenergy/v1/attributedimension/nodeconfig/tag`,
    method: "POST",
    data
  });
}

// 根据节点查询维度标签赋值
export function attributedimensionNodeConfigQuery(data) {
  return fetch({
    url: `/eembaseenergy/v1/attributedimension/nodeconfig/query`,
    method: "POST",
    data
  });
}

// 节点维度标签赋值
export function attributedimensionNodeConfigEdit(data) {
  return fetch({
    url: `/eembaseenergy/v1/attributedimension/nodeconfig/edit`,
    method: "POST",
    data
  });
}

// 节点维度标签导入
export function attributedimensionNodeConfigImport(data) {
  return fetch({
    url: `/eembaseenergy/v1/attributedimension/nodeconfig/import`,
    method: "POST",
    data
  });
}

// 查询编辑记录
export function attributedimensionNodeConfigRecord(data) {
  return fetch({
    url: `/eembaseenergy/v1/attributedimension/nodeconfig/record`,
    method: "POST",
    data
  });
}

// 查询编辑记录
export function attributedimensionNodeConfigColumnName(data) {
  return fetch({
    url: `/eembaseenergy/v1/attributedimension/nodeconfig/column-name`,
    method: "POST",
    data
  });
}

// 标签排序
export function attributedimensionConfigSort(data) {
  return fetch({
    url: `/eembaseenergy/v1/attributedimension/config/sort`,
    method: "POST",
    data
  });
}

// 查询多维度节点树的列表
export function queryAttributedimensionTreeList(data) {
  return fetch({
    url: `/eembaseenergy/v1/attribute-dimension/tree/list`,
    method: "POST",
    data
  });
}

// 选择节点树列表中的某一颗，查询具体的多维度节点树
export function queryAttributedimensionTreeNodeinfo(data) {
  return fetch({
    url: `/eembaseenergy/v1/attribute-dimension/tree/nodeinfo`,
    method: "POST",
    data
  });
}

// 启动or停用节点树
export function setAttributedimensionTreeStatus(data) {
  return fetch({
    url: `/eembaseenergy/v1/attribute-dimension/tree/status`,
    method: "POST",
    data
  });
}

// 删除节点树
export function deleteAttributedimensionTreeStatus(data) {
  return fetch({
    url: `/eembaseenergy/v1/attribute-dimension/tree/delete`,
    method: "DELETE",
    data
  });
}

// 新增一颗多维度节点树
export function addAttributedimensionTree(data) {
  return fetch({
    url: `/eembaseenergy/v1/attribute-dimension/tree/add`,
    method: "POST",
    data
  });
}

// 修改多维度树名称
export function editAttributedimensionTreeName(data) {
  return fetch({
    url: `/eembaseenergy/v1/attribute-dimension/tree/edit/name`,
    method: "POST",
    data
  });
}

// 获取所有分项维度
export function dimensionAll(data) {
  return fetch({
    url: `/eembaseenergy/v1/dimension/all`,
    method: "POST",
    data
  });
}

// 获取所有分项维度
export function unrelatedLevels(data) {
  return fetch({
    url: `/eembaseenergy/v1/dimension/unrelatedLevels`,
    method: "POST",
    data
  });
}

// 新增分项维度
export function addDimension(data) {
  return fetch({
    url: `/eembaseenergy/v1/dimension`,
    method: "POST",
    data
  });
}

// 编辑分项维度
export function editDimension(data) {
  return fetch({
    url: `/eembaseenergy/v1/dimension`,
    method: "PUT",
    data
  });
}

// 根据维度id查询节点树
export function dimensionTreeById(params) {
  return fetch({
    url: `/eembaseenergy/v1/dimension/dimension-by-id`,
    method: "GET",
    params
  });
}
// 删除分项维度
export function deleteDimension(data) {
  return fetch({
    url: `/eembaseenergy/v1/dimension`,
    method: "DELETE",
    data
  });
}
