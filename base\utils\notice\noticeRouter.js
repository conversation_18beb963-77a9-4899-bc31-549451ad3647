import { api } from "@altair/knight";

export async function noticeGoRouter(vm, msg) {
  const currentPath = api.getCurrentRoutePath();
  let isOk = true;

  const router = msg.redirectPageUrl;

  if (!router) {
    vm.$message.warning($T("未配置跳转路径！"));
    return;
  }

  if (currentPath.includes(router)) {
    isOk = false;
  }

  if (!api.checkNavmenuExit(router)) {
    // 菜单不存在
    vm.$message.warning($T("{0}不存在！", router));
    return;
  }

  const params = {
    ...msg,
    source: "notice"
  };
  api.routerPushWithQuery({ path: router, query: params });

  if (!isOk) {
    vm.$message.info($T("已在当前页面，可点击刷新按钮查看最新事件!"));
  }
}
