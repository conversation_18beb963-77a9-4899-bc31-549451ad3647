<template>
  <div class="page flex-col flex">
    <header style="height: 32px">
      <span class="title">{{ title_in }}</span>
      <CetButton
        v-permission="'dimattributeconfig_update'"
        class="fr ml-J3"
        v-bind="CetButton_addTree"
        v-on="CetButton_addTree.event"
        :disable_in="!rootNode"
      ></CetButton>
      <customElSelect
        class="fr ml-J3"
        v-model="ElSelect_1.value"
        v-bind="ElSelect_1"
        v-on="ElSelect_1.event"
        :prefix_in="$T('标签类型')"
      >
        <ElOption
          v-for="item in ElOption_1.options_in"
          :key="item[ElOption_1.key]"
          :label="item[ElOption_1.label]"
          :value="item[ElOption_1.value]"
          :disabled="item[ElOption_1.disabled]"
        ></ElOption>
      </customElSelect>
      <customElSelect
        :prefix_in="$T('所属项目')"
        v-model="ElSelect_project.value"
        v-bind="ElSelect_project"
        v-on="ElSelect_project.event"
        class="fr"
        v-show="ElOption_project.options_in?.length > 1"
      >
        <ElOption
          v-for="item in ElOption_project.options_in"
          :key="item[ElOption_project.key]"
          :label="item[ElOption_project.label]"
          :value="item[ElOption_project.value]"
          :disabled="item[ElOption_project.disabled]"
        ></ElOption>
      </customElSelect>
    </header>
    <div class="mt-J3 flex-auto">
      <CetTable
        :data.sync="CetTable_1.data"
        :dynamicInput.sync="CetTable_1.dynamicInput"
        v-bind="CetTable_1"
        v-on="CetTable_1.event"
        ref="table"
      >
        <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
        <el-table-column
          v-if="ElSelect_1.value === 0"
          :label="$T('排序')"
          align="left"
          :width="70"
        >
          <template>
            <omega-icon
              class="sortBtn"
              style="cursor: s-resize"
              symbolId="organizational-level-metadata"
            ></omega-icon>
          </template>
        </el-table-column>
        <template v-for="item in Columns_1">
          <ElTableColumn
            :key="item.label"
            v-bind="item"
            v-if="item.prop === 'tagType'"
          >
            <template slot="header">
              <span>{{ item.label }}</span>
              <el-tooltip
                class="ml-J0"
                :content="
                  $T('枚举是一组相关的标签赋值的定义；文本是可以自由定义的内容')
                "
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
          </ElTableColumn>
          <ElTableColumn :key="item.label" v-bind="item" v-else></ElTableColumn>
        </template>
        <ElTableColumn :label="$T('标签赋值')">
          <template slot-scope="scope">
            <el-tag
              class="ml-J0"
              type="info"
              v-for="item in scope.row.tagVoList || []"
              :key="item.id"
            >
              {{ item.name }}
            </el-tag>
          </template>
        </ElTableColumn>
        <ElTableColumn
          :label="$T('操作')"
          width="120"
          headerAlign="center"
          align="center"
        >
          <template slot-scope="scope">
            <span
              v-permission="'dimattributeconfig_update'"
              @click="handleEdit(scope.row)"
              class="handleBtn editBtn"
            >
              {{ $T("编辑") }}
            </span>
            <span
              v-permission="'dimattributeconfig_update'"
              @click="handleDelete(scope.row)"
              class="handleBtn deleteBtn ml-J1"
            >
              {{ $T("删除") }}
            </span>
          </template>
        </ElTableColumn>
      </CetTable>
    </div>
    <addEdit
      v-bind="addEdit"
      :isPublic="isPublic"
      v-on="addEdit.event"
      :rootNode_in="rootNode"
    />
  </div>
</template>

<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
import addEdit from "./addEdit.vue";
import Sortable from "sortablejs";
export default {
  name: "dimensionConfigurationPlatform",
  components: { addEdit },
  props: {
    // 是否为公有属性
    isPublic: {
      type: Boolean,
      default: true
    },
    title_in: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        doLayoutTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        highlightCurrentRow: false,
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        label: $T("序号"), //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        width: "70" //绝对宽度
      },
      Columns_1: [
        {
          prop: "name", // 支持path a[0].b
          label: $T("标签名称"), //列名
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          width: 260,
          formatter: common.formatTextCol() //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
        },
        {
          prop: "tagType", // 支持path a[0].b
          label: $T("标签类型"), //列名
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          width: 160,
          formatter: (row, column, cellValue) => {
            return (
              this.ElOption_1.options_in.find(i => i.value === cellValue)
                ?.label ?? "--"
            );
          }
        }
      ],
      // 1组件
      ElSelect_1: {
        value: 0,
        style: {
          width: "240px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      // 1组件
      ElOption_1: {
        options_in: [
          { value: 0, label: $T("全部") },
          { value: 1, label: $T("文本") },
          { value: 2, label: $T("枚举") }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      CetButton_addTree: {
        visible_in: true,
        disable_in: false,
        title: $T("新建标签"),
        type: "primary",
        plain: false,
        icon: "el-icon-plus",
        event: {
          statusTrigger_out: this.CetButton_addTree_statusTrigger_out
        }
      },
      addEdit: {
        openTrigger_in: Date.now(),
        inputData_in: {},
        event: { finishTrigger_out: this.getTableData }
      },
      ElSelect_project: {
        value: 0,
        style: {},
        event: {
          change: this.getTableData
        }
      },
      ElOption_project: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  computed: {
    rootNode() {
      const id = this.ElSelect_project.value;
      const list = this.ElOption_project.options_in;
      return list.find(item => item.id === id);
    }
  },
  methods: {
    async getTableData() {
      const queryData = {
        isPublic: this.isPublic,
        tagType: this.ElSelect_1.value,
        rootNode: {
          id: this.rootNode.id,
          modelLabel: this.rootNode.modelLabel
        }
      };
      const res = await customApi.queryAttributedimensionConfig(queryData);
      this.CetTable_1.data = res?.data ?? [];
    },
    CetButton_addTree_statusTrigger_out() {
      this.addEdit.inputData_in = {};
      this.addEdit.openTrigger_in = Date.now();
    },
    handleEdit(row) {
      this.addEdit.inputData_in = this._.cloneDeep(row);
      this.addEdit.openTrigger_in = Date.now();
    },
    handleDelete(row) {
      this.$confirm($T("确定要删除该便签吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(async () => {
          const res = await customApi.deleteAttributedimensionConfig([row.id]);
          if (res?.code) return;
          this.$message.success($T("删除成功"));
          this.getTableData();
        })
        .catch(() => {
          this.$message.info($T("已取消删除"));
        });
    },
    async ElSelect_1_change_out() {
      await this.getTableData();
      this.CetTable_1.doLayoutTrigger_in = Date.now();
    },
    // 表格拖拽
    setSort() {
      const el = this.$refs.table.$refs.cetTable.$el.querySelectorAll(
        ".el-table__body-wrapper > table > tbody"
      )[0];
      Sortable.create(el, {
        ghostClass: "sortable-ghost",
        handle: ".sortBtn",
        onEnd: async evt => {
          const newTableData = this._.cloneDeep(this.CetTable_1.data);
          const temp = { ...newTableData[evt.oldIndex], newDataFlag: true };
          let index = evt.newIndex + 1;
          if (evt.oldIndex > evt.newIndex) {
            index = evt.newIndex;
          }
          newTableData.splice(index, 0, temp);
          const data = newTableData
            .filter(i => i.id !== temp.id || i.newDataFlag)
            .map(i => {
              if (i.newDataFlag) delete i.newDataFlag;
              return i;
            });
          const params = {
            indexParamlist: data.map((item, index) => ({
              id: item.id,
              index: index
            }))
          };
          const res = await customApi.attributedimensionConfigSort(params);
          if (res.code !== 0) return;
          this.CetTable_1.data = [];
          this.$nextTick(() => {
            this.CetTable_1.data = data;
          });
        }
      });
    },
    async getRootNode() {
      this.ElOption_project.options_in = [];
      const res = await customApi.rootNode();
      const data = res.data || [];
      this.ElOption_project.options_in = data;
      this.ElSelect_project.value = data?.[0]?.id;
    }
  },
  async mounted() {
    await this.getRootNode();
    await this.getTableData();
    this.setSort();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;
  @include background_color(BG1);
  border-radius: var(--Ra);
  @include padding(J4);
  .handleBtn {
    cursor: pointer;
  }
  .editBtn {
    @include font_color(ZS);
  }
  .deleteBtn {
    @include font_color(Sta3);
  }
  .el-tag.el-tag--info {
    @include background_color(BG12);
  }
  .title {
    @include font_size(H3);
    font-weight: bold;
  }
}
</style>
