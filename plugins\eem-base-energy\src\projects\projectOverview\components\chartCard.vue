<template>
  <div class="flex-row flex fullheight">
    <div class="left rounded-Ra bg-BG1 p-J4">
      <div class="head">
        <div class="title">{{ $T("用能趋势") }}</div>
        <chartTypeSwitch
          v-model="trendChartType"
          @change="handlerTrendChartTypeChange"
        />
      </div>
      <CetChart v-bind="trendChart"></CetChart>
    </div>
    <div class="right rounded-Ra bg-BG1 p-J4">
      <div class="head">
        <div class="title">{{ $T("用能排名") }}TOP10</div>
        <chartTypeSwitch
          v-model="rankChartType"
          @change="handlerRankChartTypeChange"
        />
      </div>
      <CetChart v-bind="rankChart" ref="cetChart"></CetChart>
    </div>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import * as echarts from "echarts";
import omegaTheme from "@omega/theme";
import chartTypeSwitch from "eem-base/components/chartTypeSwitch.vue";

const BARSERIES = [
  {
    z: 1,
    type: "bar",
    barGap: "0%",
    barWidth: 8,
    itemStyle: {
      borderRadius: [0, 0, 0, 0],
      color:
        omegaTheme.theme === "dark"
          ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "#73FFBC" },
              { offset: 1, color: "rgba(74,253,167,0.2)" }
            ])
          : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "#80C0FF" },
              { offset: 1, color: "rgba(76,166,255,0.2)" }
            ])
    },
    data: [] //Y轴上的高度
  },
  {
    z: 1,
    type: "bar",
    barWidth: 8,
    itemStyle: {
      borderRadius: [0, 0, 0, 0],
      color:
        omegaTheme.theme === "dark"
          ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "#30DB89" },
              { offset: 1, color: "rgba(74,253,167,0.2)" }
            ])
          : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "#4CA6FF" },
              { offset: 1, color: "rgba(76,166,255,0.2)" }
            ])
    },
    data: []
  },
  {
    z: 2,
    type: "pictorialBar",
    data: [], //此数据对应底部组件
    symbol: "diamond", //底部组件形状，不写默认为椭圆
    symbolOffset: ["0", "50%"], //与柱子的偏移角度
    symbolSize: [15, 7.5], //底面[宽，高]
    itemStyle: {
      normal: {
        color:
          omegaTheme.theme === "dark"
            ? "rgba(74, 253, 167, 0.20)"
            : "rgba(76, 166, 255, 0.20)"
      }
    },
    tooltip: {
      show: false
    }
  },
  {
    z: 3,
    type: "pictorialBar",
    symbolPosition: "end",
    data: [], //此数据对应顶部组件
    symbol: "diamond",
    symbolOffset: ["0", "-50%"],
    symbolSize: [15, 7.5],
    itemStyle: {
      normal: {
        color:
          omegaTheme.theme === "dark"
            ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#3ABF8C" },
                { offset: 1, color: "#4AFDA7" }
              ])
            : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#4CA6FF" },
                { offset: 1, color: "#BADDFF" }
              ])
      }
    },
    tooltip: {
      show: false
    }
  }
];

export default {
  components: { chartTypeSwitch },
  props: {
    node: {
      type: Object
    },
    dateObj: {
      type: Object
    },
    eneryType: {
      type: Number
    },
    rootNode_in: Object
  },
  data() {
    return {
      trendChartType: 2,
      rankChartType: 1,
      trendChart: {
        options: {
          toolbox: {
            show: true,
            top: 8,
            feature: {
              saveAsImage: {
                name: $T("用能趋势")
              } // 导出图片
            }
          },
          tooltip: {
            trigger: "axis"
          },
          grid: {
            top: 43,
            bottom: 0,
            left: 0,
            right: "0%",
            containLabel: true
          },
          xAxis: {
            type: "category",
            axisTick: {
              show: false
            },
            data: []
          },
          yAxis: {
            name: "",
            type: "value",
            nameTextStyle: {
              align: "left"
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed"
              }
            }
          },
          series: [
            {
              data: [],
              type: "line",
              symbolSize: 1,
              smooth: true,
              areaStyle: {
                color:
                  omegaTheme.theme === "dark"
                    ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: "#0D86FF" },
                        { offset: 1, color: "rgba(13,134,255,0)" }
                      ])
                    : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: "#70E09E" },
                        { offset: 1, color: "rgba(112,224,158,0)" }
                      ])
              }
            }
          ]
        }
      },
      rankChart: {
        options: {
          toolbox: {
            show: true,
            top: 8,
            feature: {
              saveAsImage: {
                name: $T("用能排名")
              } // 导出图片
            }
          },
          tooltip: {
            trigger: "axis",
            transitionDuration: 0 //鼠标移入时抖动问题处理
          },
          grid: {
            top: 43,
            bottom: 4,
            left: 0,
            right: "0%",
            containLabel: true
          },
          xAxis: {
            type: "category",
            axisTick: {
              show: false
            },
            axisLabel: {
              show: true,
              interval: 0,
              width: 40,
              overflow: "truncate"
            },
            data: []
          },
          yAxis: {
            name: "",
            type: "value",
            nameTextStyle: {
              align: "left"
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed"
              }
            }
          },
          series: BARSERIES
        }
      }
    };
  },
  methods: {
    handlerTrendChartTypeChange(val) {
      this.trendChart.options.series[0].type = val === 1 ? "bar" : "line";
    },
    handlerRankChartTypeChange(val) {
      const data = this.rankChart.options.series[0].data;
      if (val === 1) {
        this.rankChart.options.series = _.cloneDeep(BARSERIES);
        this.setData(data);
      } else if (val === 2) {
        this.rankChart.options.series = [
          {
            type: "line",
            symbolSize: 1,
            smooth: true,
            data: data,
            itemStyle: {
              color: omegaTheme.theme === "dark" ? "#73FFBC" : "#80C0FF"
            }
          }
        ];
        this.rankChart.options = _.cloneDeep(this.rankChart.options);
      }
    },
    setData(data) {
      let conData = [];
      let showData = [];
      data.forEach(item => {
        if (item) {
          conData.push(1);
          showData.push(item);
        } else {
          conData.push(0);
          showData.push({
            value: 1,
            itemStyle: {
              normal: {
                borderColor: "rgba(0,0,0,0)",
                borderWidth: 2,
                color: "rgba(0,0,0,0)"
              }
            }
          });
        }
      });
      this.rankChart.options.series[0].data = data;
      this.rankChart.options.series[1].data = data;
      this.rankChart.options.series[2].data = conData;
      this.rankChart.options.series[3].data = showData;
    },
    async getTrendData() {
      this.trendChartType = 2;
      if (!this.node || !this.eneryType) return;
      let params = {
        aggregationCycle: this.dateObj.type,
        energyType: this.eneryType,
        node: {
          modelLabel: this.node.modelLabel,
          id: this.node.id
        },
        queryType: 0,
        startTime: this.dateObj.time[0],
        endTime: this.dateObj.time[1],
        energyDataType: 4,
        rootNode: {
          id: this.rootNode_in?.id,
          modelLabel: this.rootNode_in?.modelLabel
        }
      };
      const { data = [] } = await customApi.getV2EmergyConsumptionTbhb(params);
      let xList = [];
      let yList = [];

      if (data?.currentData && data?.currentData.length > 0) {
        data.currentData.forEach(item => {
          xList.push(this.getAxixs(item.logTime, this.dateObj.type));
          yList.push(
            item.value || item.value === 0 ? item.value.toFixed2(2) : null
          );
        });
        this.trendChart.options.xAxis.data = xList;
        this.trendChart.options.yAxis.name = data.symbol;
        this.trendChart.options.series[0].type = "line";
        this.trendChart.options.series[0].data = yList;
        this.trendChart.options.tooltip.formatter = val => {
          let list = val || [];
          let str = list[0]?.name + "<br/>";
          list.forEach(item => {
            str += `${item.marker}${this.getTime()}: ${
              item.value == null ? "--" : item.value
            }(${data.symbol || "--"})`;
          });
          return str;
        };
      }
    },
    getTime() {
      let format = "";
      if (this.dateObj.type === 12) {
        //日
        format = "YYYY-MM-DD";
      } else if (this.dateObj.type === 14) {
        //月
        format = "YYYY-MM";
      } else if (this.dateObj.type === 17) {
        //年
        format = "YYYY";
      }
      return this.$moment(this.dateObj.time[0]).format(format);
    },
    async getRankData() {
      this.rankChartType = 1;
      if (!this.node || !this.node?.children || !this.eneryType) {
        this.rankChart.options.xAxis.data = [];
        this.rankChart.options.yAxis.name = "";
        this.rankChart.options.series[0].data = [];
        this.rankChart.options.series[1].data = [];
        this.rankChart.options.series[2].data = [];
        this.rankChart.options.series[3].data = [];
        return;
      }
      let nodes = [];
      const groupNodes = _.groupBy(this.node.children, "modelLabel");
      for (let key in groupNodes) {
        const list = groupNodes[key].map(item => {
          return {
            id: item.id,
            name: item.name,
            modelLabel: item.modelLabel
          };
        });
        nodes.push(...list);
      }

      let params = {
        aggregationCycle: this.dateObj.type, //年月日时间周期
        energyTypes: [this.eneryType], //能源类型
        nodes: nodes,
        startTime: this.dateObj.time[0],
        endTime: this.dateObj.time[1],
        limit: 10,
        energyDataType: 4,
        dimConfigId: -1,
        rootNode: {
          id: this.rootNode_in?.id,
          modelLabel: this.rootNode_in?.modelLabel
        }
      };
      const { data = [] } = await customApi.getV2EmergyConsumptionTop(params);
      let list = data || [];
      let xList = [];
      let yList = [];
      list[0]?.energyDataList.forEach(item => {
        xList.push(item.objectName);
        yList.push(
          item.total || item.total === 0 ? item.total.toFixed2(2) : null
        );
      });
      this.rankChart.options.xAxis.data = xList;
      this.rankChart.options.yAxis.name = list[0]?.energyDataList[0]?.symbol;
      this.rankChart.options.series = _.cloneDeep(BARSERIES);
      this.setData(yList);
      this.rankChart.options.tooltip.formatter = val => {
        let list2 = val?.length === 2 ? val.slice(0, 1) : [];
        let str = list2[0]?.name + "<br/>";
        list2.forEach(item => {
          str += `${item.marker}${this.getTime()}: ${
            item.value == null ? "--" : item.value
          }(${list[0]?.energyDataList[0]?.symbol || "--"})`;
        });
        return str;
      };
    },
    getAxixs(time, type) {
      let formatStr = "";
      if (type === 12) {
        formatStr = "hh:mm";
      } else if (type === 14) {
        formatStr = "DD";
      } else if (type === 17) {
        formatStr = "MM";
      }
      return this.$moment(time).format(formatStr);
    }
  },
  watch: {
    node() {
      this.getTrendData();
      this.getRankData();
    },
    eneryType() {
      this.getTrendData();
      this.getRankData();
    },
    dateObj() {
      this.getTrendData();
      this.getRankData();
    }
  },
  mounted() {
    this.getTrendData();
    this.getRankData();
  }
};
</script>
<style lang="scss" scoped>
.left {
  width: calc(48.5% - 8px);
  .echarts {
    height: calc(100% - 35px);
  }
}
.right {
  width: calc(51.5% - 8px);
  margin-left: 16px;
  .echarts {
    height: calc(100% - 35px);
  }
}
.head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    font-weight: bold;
    font-size: 16px;
  }
  .icon {
    width: 20px;
    height: 20px;
  }
}
</style>
