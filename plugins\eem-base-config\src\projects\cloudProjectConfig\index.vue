<template>
  <div class="fullfilled relative flex flex-col">
    <div class="projectInfo p-J4 bg-BG1 rounded-Ra flex flex-row box-border">
      <UploadImg
        class="loadImg mr-J3"
        :static_in="true"
        :imgUrl.sync="projectInfo.pic"
      />
      <div class="flex-auto flex flex-col">
        <div class="text-H2 font-bold leading-[1.5]">
          {{ projectInfo.name || "--" }}
        </div>
        <div
          class="flex-auto flex flex-row mt-J1 projectInfoList"
          height="auto"
        >
          <div class="flex-auto flex flex-row">
            <div class="projectInfoItem">
              <div class="label">{{ $T("项目编号") }}</div>
              <el-tooltip
                effect="light"
                :content="projectInfo.code"
                placement="bottom"
              >
                <div class="value text-ellipsis fl">
                  {{ projectInfo.code || "--" }}
                </div>
              </el-tooltip>
            </div>
            <div class="projectInfoItem">
              <div class="label">{{ $T("所属公司") }}</div>
              <el-tooltip
                effect="light"
                :content="projectInfo.enterprisename"
                placement="bottom"
              >
                <div class="value text-ellipsis fl">
                  {{ projectInfo.enterprisename || "--" }}
                </div>
              </el-tooltip>
            </div>
            <div class="projectInfoItem">
              <div class="label">{{ $T("所属区域") }}</div>
              <el-tooltip
                effect="light"
                :content="projectInfo.hierarchy"
                placement="bottom"
              >
                <div class="value text-ellipsis fl">
                  {{ projectInfo.hierarchy || "--" }}
                </div>
              </el-tooltip>
            </div>
          </div>
          <div class="rightBtn">
            <span
              class="btn mr-J3"
              id="cloud_project_config_project_config"
              @click="CetButton_projectCfg_statusTrigger_out"
            >
              {{ $T("项目设置") }}
            </span>
            <span
              class="btn mr-J3"
              @click="editProject"
              v-permission="'nodeconfig_update'"
            >
              {{ $T("编辑") }}
            </span>
            <span class="btn" @click="detailProject">
              {{ $T("详情") }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="flex-auto flex flex-row mt-J3">
      <div
        class="bg-BG1 rounded-Ra p-J4 flex flex-col box-border h-full"
        style="width: 315px"
      >
        <CetGiantTree
          ref="cetGiantTree"
          class="flex-auto"
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
        ></CetGiantTree>
        <div
          class="justify-end flex flex-row mt-J3"
          v-permission="'nodeconfig_update'"
        >
          <el-tooltip
            effect="light"
            :disabled="!importing"
            :content="importingStr"
            placement="top"
          >
            <div>
              <CetButton
                class="ml-J1"
                v-bind="CetButton_1"
                v-if="!currentSelectProject"
                :disable_in="importing"
                v-on="CetButton_1.event"
              ></CetButton>
            </div>
          </el-tooltip>
          <el-dropdown
            class="ml-J1"
            trigger="click"
            @command="treeFootClick"
            v-if="netWork && showAddChildBtn"
          >
            <el-button type="primary">
              {{ $T("更多") }}
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <!-- 添加子级 -->
              <el-tooltip
                effect="light"
                :disabled="!importing"
                :content="importingStr"
                placement="top"
              >
                <div>
                  <el-dropdown-item
                    command="addChild"
                    v-if="showAddChildBtn"
                    :disabled="importing"
                  >
                    {{ $T("添加子级") }}
                  </el-dropdown-item>
                </div>
              </el-tooltip>
              <!-- 批量添加子级 -->
              <el-tooltip
                effect="light"
                :disabled="!importing"
                :content="importingStr"
                placement="top"
              >
                <div>
                  <el-dropdown-item
                    command="batchAddChild"
                    v-if="showAddChildBtn"
                    :disabled="importing"
                  >
                    {{ $T("批量添加子级") }}
                  </el-dropdown-item>
                </div>
              </el-tooltip>
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown
            class="ml-J1"
            trigger="click"
            @command="treeFootClick"
            v-if="!netWork && showAddChildBtn"
            ref="moreDropdown"
          >
            <el-button type="primary">
              {{ $T("更多") }}
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>

            <el-dropdown-menu slot="dropdown">
              <!-- 导入子级--->
              <el-tooltip
                effect="light"
                :disabled="!importing"
                :content="importingStr"
                placement="top"
              >
                <!-- 未联调，屏蔽导入子级 -->
                <div v-if="false">
                  <el-dropdown-item
                    command="importChild"
                    v-if="!netWork && showAddChildBtn"
                    :disabled="importing"
                    id="cloud_project_config_more"
                  >
                    {{ $T("导入子级") }}
                  </el-dropdown-item>
                </div>
              </el-tooltip>
              <!-- 添加子级 -->
              <el-tooltip
                effect="light"
                :disabled="!importing"
                :content="importingStr"
                placement="top"
              >
                <div>
                  <el-dropdown-item
                    command="addChild"
                    v-if="showAddChildBtn"
                    :disabled="importing"
                  >
                    {{ $T("添加子级") }}
                  </el-dropdown-item>
                </div>
              </el-tooltip>
              <!-- 批量添加子级 -->
              <el-tooltip
                effect="light"
                :disabled="!importing"
                :content="importingStr"
                placement="top"
              >
                <div>
                  <el-dropdown-item
                    command="batchAddChild"
                    v-if="showAddChildBtn"
                    :disabled="importing"
                  >
                    {{ $T("批量添加子级") }}
                  </el-dropdown-item>
                </div>
              </el-tooltip>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      <div
        class="ml-J3 flex flex-col bg-BG1 rounded-Ra p-J4 flex-auto box-border"
      >
        <div class="flex flex-row mb-J3">
          <div class="flex-auto text-ellipsis">
            <el-tooltip
              effect="light"
              :content="currentNode && currentNode.name"
              placement="bottom-start"
            >
              <span class="text-H2 font-bold leading-[1.5] nodeName">
                {{ currentNode?.name || "--" }}
              </span>
            </el-tooltip>
          </div>
          <div class="clearfix">
            <div v-permission="'nodeconfig_update'" class="fr flex flex-row">
              <el-tooltip
                effect="light"
                :disabled="!importing"
                :content="importingStr"
                placement="top"
              >
                <div class="inline-block">
                  <CetButton
                    class="mr-J1"
                    v-bind="CetButton_3"
                    :disable_in="disableBatchDelete || importing"
                    v-on="CetButton_3.event"
                  ></CetButton>
                </div>
              </el-tooltip>
              <el-tooltip
                effect="light"
                :disabled="!importing"
                :content="importingStr"
                placement="top"
              >
                <div class="inline-block">
                  <!-- 批量导入未联调，先屏蔽 -->
                  <CetButton
                    v-if="netWork && false"
                    class="mr-J1"
                    v-bind="CetButton_batchNode"
                    v-on="CetButton_batchNode.event"
                    :disable_in="importing"
                  ></CetButton>
                </div>
              </el-tooltip>
              <el-tooltip
                effect="light"
                :disabled="!importing"
                :content="importingStr"
                placement="top"
              >
                <div class="inline-block">
                  <CetButton
                    v-bind="CetButton_4"
                    v-on="CetButton_4.event"
                    :disable_in="disabledBatchMove || importing"
                  ></CetButton>
                </div>
              </el-tooltip>
              <!-- 
                平台的设备关联页面未知，暂时屏蔽按钮
                复制粘贴功能暂无
              -->
              <!-- <el-dropdown trigger="click" @command="dropdownClick">
                <el-button type="primary">
                  {{ $T("更多") }}
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="copy"
                    :disabled="currentSelectProject"
                  >
                    {{ $T("复制") }}
                  </el-dropdown-item>
                  <el-tooltip
                    effect="light"
                    :disabled="!importing"
                    :content="importingStr"
                    placement="top"
                  >
                    <div>
                      <el-dropdown-item
                        command="paste"
                        :disabled="disabledPaste || importing"
                      >
                        {{ $T("粘贴") }}
                      </el-dropdown-item>
                    </div>
                  </el-tooltip>
                  <el-tooltip
                    effect="light"
                    :disabled="!importing"
                    :content="importingStr"
                    placement="top"
                  >
                    <div>
                      <el-dropdown-item
                        command="batchMove"
                        v-show="netWork"
                        :disabled="disabledBatchMove || importing"
                      >
                        {{ $T("批量移动") }}
                      </el-dropdown-item>
                    </div>
                  </el-tooltip>
                  <el-dropdown-item
                    command="association"
                    v-show="showAssociation"
                  >
                    {{ $T("关联设备") }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown> -->
            </div>
            <CustomElSelect
              class="fr mr-J1"
              v-show="showTab"
              :prefix_in="$T('节点类型')"
              v-model="selectedMenu"
              v-bind="ElSelect_1"
              @change="handleTabClick_out"
            >
              <ElOption
                v-for="item in menus"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              ></ElOption>
            </CustomElSelect>
            <ElInput
              class="fl mr-J1"
              suffix-icon="el-icon-search"
              v-model="ElInput_1.value"
              v-bind="ElInput_1"
              v-on="ElInput_1.event"
            ></ElInput>
          </div>
        </div>
        <div class="flex-auto flex flex-col">
          <CetTable
            class="flex-auto mb-J3"
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
            @selection-change="handleSelectionChange"
          >
            <ElTableColumn
              type="selection"
              width="50"
              headerAlign="left"
              align="left"
            ></ElTableColumn>
            <ElTableColumn
              :label="$T('序号')"
              width="70"
              type="index"
              headerAlign="left"
              align="left"
            ></ElTableColumn>
            <ElTableColumn
              v-for="(item, index) in ElTableColumnArr"
              headerAlign="left"
              align="left"
              showOverflowTooltip
              :key="index"
              v-bind="item"
            ></ElTableColumn>
            <ElTableColumn
              :label="$T('操作')"
              width="110"
              headerAlign="left"
              align="left"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-tooltip
                  effect="light"
                  :disabled="!importing"
                  :content="importingStr"
                  placement="top"
                >
                  <el-button
                    class="fl p-0 text-Ab"
                    type="text"
                    size="mini"
                    :disabled="importing"
                    @click.stop="editNode(scope.row)"
                    v-permission="'nodeconfig_update'"
                  >
                    {{ $T("编辑") }}
                  </el-button>
                </el-tooltip>
                <el-button
                  class="fl p-0 text-Ab"
                  type="text"
                  size="mini"
                  @click.stop="nodeDetail(scope.row)"
                >
                  {{ $T("详情") }}
                </el-button>
              </template>
            </ElTableColumn>
          </CetTable>
          <el-pagination
            class="text-right p-0"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-sizes="pageSizes"
            :page-size.sync="pageSize"
            layout="total,sizes, prev, pager, next, jumper"
            :total="totalCount"
            background
          ></el-pagination>
        </div>
      </div>
    </div>

    <AddAndEditNode
      :netWork="netWork"
      :visibleTrigger_in="addAndEditNode.visibleTrigger_in"
      :closeTrigger_in="addAndEditNode.closeTrigger_in"
      :inputData_in="addAndEditNode.inputData_in"
      :fatherNode_in="fatherNode"
      :treeNameList_in="treeNameList"
      @saveData_out="addAndEditNode_saveData_out"
      :projectId_in="projectId"
    />
    <AddProjectConfig1
      :visibleTrigger_in="addProjectConfig1.visibleTrigger_in"
      :closeTrigger_in="addProjectConfig1.closeTrigger_in"
      :queryId_in="addProjectConfig1.queryId_in"
      :inputData_in="addProjectConfig1.inputData_in"
      :treeData_in="fatherNode"
      @saveData_out="addProjectConfig1_saveData_out"
    />
    <ProjectConfigDetail
      :visibleTrigger_in="projectConfigDetail.visibleTrigger_in"
      :closeTrigger_in="projectConfigDetail.closeTrigger_in"
      :queryId_in="projectConfigDetail.queryId_in"
      :inputData_in="projectConfigDetail.inputData_in"
    />
    <Detail
      :visibleTrigger_in="detail.visibleTrigger_in"
      :closeTrigger_in="detail.closeTrigger_in"
      :inputData_in="detail.inputData_in"
      :projectId_in="projectId"
    />
    <UploadDialog v-bind="uploadDialog" v-on="uploadDialog.event">
      <div slot="search" class="flex flex-row">
        <CustomElSelect
          class="flex-auto mb-J1"
          :prefix_in="$T('能源类型')"
          v-model="ElSelect_energyType.value"
          v-bind="ElSelect_energyType"
          v-on="ElSelect_energyType.event"
        >
          <ElOption
            v-for="item in ElOption_energyType.options_in"
            :key="item[ElOption_energyType.key]"
            :label="item[ElOption_energyType.label]"
            :value="item[ElOption_energyType.value]"
            :disabled="item[ElOption_energyType.disabled]"
          ></ElOption>
        </CustomElSelect>
        <CustomElSelect
          class="flex-auto ml-J1 mb-J1 multipleSelect"
          :prefix_in="$T('节点类型')"
          :popover_in="$T('变压器节点信息橙色字段为变压器分析功能需要的配置')"
          v-model="ElSelect_nodeTypes.value"
          v-bind="ElSelect_nodeTypes"
          v-on="ElSelect_nodeTypes.event"
        >
          <ElOption
            v-for="item in ElOption_nodeTypes.options_in"
            :key="item[ElOption_nodeTypes.key]"
            :label="item[ElOption_nodeTypes.label]"
            :value="item[ElOption_nodeTypes.value]"
            :disabled="item[ElOption_nodeTypes.disabled]"
          ></ElOption>
        </CustomElSelect>
      </div>
    </UploadDialog>
    <BatchChangeNode
      :netWork="netWork"
      :visibleTrigger_in="batchChangeNode.visibleTrigger_in"
      :closeTrigger_in="batchChangeNode.closeTrigger_in"
      :fatherNode_in="fatherNode"
      :selectedData_in="selectedData"
      @updata_out="updataOut"
    />
    <BatchImport
      ref="batchImport"
      v-bind="batchImport"
      v-on="batchImport.event"
    />
    <BatchAdd
      v-bind="batchAdd"
      v-on="batchAdd.event"
      :projectId_in="projectId"
    />

    <ProjectCfg
      :visibleTrigger_in="projectCfg.visibleTrigger_in"
      :closeTrigger_in="projectCfg.closeTrigger_in"
      :inputData_in="projectCfg.inputData_in"
    ></ProjectCfg>
  </div>
</template>
<script>
import UploadImg from "@/components/uploadImg.vue";
import { CustomElSelect } from "eem-base/components";
import omegaI18n from "@omega/i18n";
import customApi from "@/api/custom.js";
import { createColumnArr } from "./initColumn.js";
import AddAndEditNode from "./addProjectConfig/addAndEditNode.vue";
import AddProjectConfig1 from "./addProjectConfig/addProjectConfig1.vue";
import ProjectConfigDetail from "./ProjectConfigDetail.vue";
import Detail from "./detail.vue";
import { UploadDialog } from "eem-base/components";
import common from "eem-base/utils/common";
import BatchChangeNode from "./subcomponents/batchChangeNode";
import BatchImport from "./batchImport/index.vue";
import BatchAdd from "./batchAdd/index.vue";
import ProjectCfg from "./ProjectCfg/ProjectCfg";
import { api } from "@altair/knight";
import useCounter from "@/hooks/useDriver.js";

export default {
  name: "cloudProjectConfig",
  components: {
    UploadImg,
    CustomElSelect,
    AddAndEditNode,
    AddProjectConfig1,
    ProjectConfigDetail,
    Detail,
    UploadDialog,
    BatchChangeNode,
    BatchImport,
    BatchAdd,
    ProjectCfg
  },
  props: {
    netWork: Boolean
  },
  computed: {
    importing() {
      return (
        this.$store.getters["importProgress/importing"](5) ||
        this.$store.getters["importProgress/importing"](15)
      );
    },
    importingStr() {
      return (
        this.$store.getters["importProgress/importingStr"](5) ||
        this.$store.getters["importProgress/importingStr"](15)
      );
    },
    showTab() {
      const modelLabel = this.currentNode?.modelLabel;
      return ["building", "floor"].includes(modelLabel);
    },
    currentSelectProject() {
      return this.currentNode?.modelLabel === "project";
    },
    /**
     * 是否禁用粘贴
     */
    disabledPaste() {
      if (!this.copyNode) {
        return true;
      }

      return (
        this.currentNode?.modelLabel !== this.copyNode.fatherNodeModelLabel
      );
    },
    /**
     * 是否禁用批量移动
     */
    disabledBatchMove() {
      return !this.selectedData?.length;
    },
    /**
     * 是否显示关联设备
     */
    showAssociation() {
      const modelLabel = this.currentNode?.modelLabel;
      return ![
        "project",
        "sectionarea",
        "building",
        "floor",
        "district"
      ].includes(modelLabel);
    },
    /**
     * 是否禁用批量删除
     */
    disableBatchDelete() {
      return !this.selectedData?.length;
    }
  },
  data() {
    return {
      projectId: 0,
      showAddChildBtn: false,
      totalCount: 0,
      currentPage: 1,
      pageSizes: [10, 20, 50, 100],
      pageSize: 20,
      pageTotal: 0,
      projectInfo: {},
      fatherNode: null,
      currentNode: null,
      copyNode: null,
      fatherCurrentNode: null,
      isRefreshTable: false,
      menus: [
        {
          label: $T("车间/楼栋"),
          id: "building"
        },
        {
          label: $T("用能设备"),
          id: "manuequipment"
        }
      ],
      selectedMenu: "building",
      treeNameList: [],
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        unCheckTrigger_in: new Date().getTime(),
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out //选中单行输出
        }
      },
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: $T("添加同级"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: false,
        disable_in: false,
        title: $T("添加子级"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetButton_batchAdd: {
        visible_in: true,
        disable_in: false,
        title: $T("批量添加子级"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_batchAdd_statusTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        style: {
          width: "200px"
        },
        placeholder: $T("请输入内容"),
        event: {
          input: this.ElInput_1_input_out
        }
      },
      ElSelect_1: {
        value: "",
        style: {
          width: omegaI18n.locale === "en" ? "260px" : "200px"
        }
      },
      CetButton_3: {
        visible_in: true,
        disable_in: true,
        title: $T("批量删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      CetButton_4: {
        visible_in: true,
        disable_in: true,
        title: $T("批量移动"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_4_statusTrigger_out
        }
      },
      CetButton_batchNode: {
        visible_in: true,
        disable_in: false,
        title: $T("批量导入节点"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_batchNode_statusTrigger_out
        }
      },
      selectedData: [],
      ElTableColumnArr: [],
      CetTable_1: {
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        style: {
          textAlign: "right"
        },
        exportFileName: "",
        tableKey: "tree_id",
        event: {}
      },
      ElSelect_energyType: {
        value: "",
        style: {},
        event: {
          change: this.ElSelect_energyType_change_out
        }
      },
      ElOption_energyType: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      addAndEditNode: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      addProjectConfig1: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      projectConfigDetail: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      detail: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      uploadDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        extensionNameList_in: [".xlsx", ".xls"],
        hideDownload: false,
        dialogTitle: $T("导入节点"),
        event: {
          download: this.uploadDialog_download,
          uploadFile: this.uploadDialog_uploadFile
        }
      },
      ElSelect_nodeTypes: {
        value: [],
        filterable: true,
        clearable: true,
        multiple: true,
        "collapse-tags": true,
        style: {},
        event: {}
      },
      ElOption_nodeTypes: {
        options_in: [],
        key: "modelLabel",
        value: "modelLabel",
        label: "name",
        disabled: "disabled"
      },
      //批量转移节点弹框
      batchChangeNode: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      batchImport: {
        visibleTrigger_in: Date.now(),
        closeTrigger_in: Date.now(),
        currentNode_in: null,
        netWork: false,
        event: {
          batchImportSave_out: this.batchImportSave_out
        }
      },
      batchAdd: {
        visibleTrigger_in: Date.now(),
        currentNode_in: null,
        netWork: false,
        event: {
          save_out: this.batchAdd_out
        }
      },
      projectCfg: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      }
    };
  },
  watch: {
    "$route.query": {
      handler() {
        this.initDriver();
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    /**
     * 获取树数据
     */
    async getTreeData() {
      const query = {
        nodeTreeGroupId: this.netWork ? 1 : 2
      };
      const res = await customApi.getNodeTree(query);
      const resData = res?.data ?? [];
      this.CetGiantTree_1.inputData_in = resData;
      const oldNode = this._.cloneDeep(this.currentNode);

      if (!oldNode) {
        this.CetGiantTree_1.selectNode = resData?.[0];
        return;
      }

      await this.$nextTick();

      this.CetGiantTree_1.selectNode = oldNode;

      setTimeout(() => {
        let node = this.$refs.cetGiantTree.ztreeObj.getNodeByParam(
          "tree_id",
          oldNode.tree_id
        );
        this.CetGiantTree_1_currentNode_out(node);
      }, 0);
    },
    //获取项目信息
    async getProject() {
      const treeData = this.CetGiantTree_1.inputData_in || [];
      const projectNode = treeData.find(i => i.modelLabel === "project");
      this.projectId = projectNode.id;
      if (!projectNode) {
        return;
      }

      const queryData = [
        {
          modelLabel: "project",
          id: projectNode.id
        }
      ];

      const res = await customApi.getNodeDetail(queryData);
      this.projectInfo = res.data?.[0] ?? {};
    },
    /**
     * 节点选中事件
     */
    CetGiantTree_1_currentNode_out: _.debounce(async function (val) {
      this.currentNode = val;
      await this.setShowAddChildBtn();
      const { modelLabel } = val;

      // 存当前节点的父级节点，方便后续粘贴的位置控制
      if (modelLabel === "project") {
        this.fatherCurrentNode = null;
      } else {
        this.fatherCurrentNode = val.getParentNode() || null;
      }

      if (this.isRefreshTable) {
        this.isRefreshTable = false;
        this.getTableData();
        return;
      }

      // 重置筛选条件
      this.ElInput_1.value = "";

      this.initTableTabs(val);

      this.initColumnArr(val);
      const noChildrenFlag = !this.showAddChildBtn;
      this.getTableData(noChildrenFlag);
    }, 300),
    /**
     * 设置是否展示添加子节点按钮
     */
    async setShowAddChildBtn() {
      this.showAddChildBtn = false;
      if (!this.currentNode) {
        return;
      }
      const data = await this.findCurrentNodeChildrenConfig();
      this.showAddChildBtn = data.length > 0;
    },
    /**
     * 查询当前节点的子节点类型
     */
    async findCurrentNodeChildrenConfig() {
      if (!this.currentNode) {
        return [];
      }
      const queryData = {
        nodeTreeGroupId: this.netWork ? 1 : 2,
        parentLabel: this.currentNode?.modelLabel,
        parentSubType: this.currentNode?.roomtype ?? null
      };
      const res = await customApi.findTreeConfigNode(queryData);
      return res.data?.[0]?.nodeRelationList ?? [];
    },
    /**
     * 控制是否展示表格的tabs，用于切换是否展示用能设备
     * @param val 当前选中节点
     */
    initTableTabs(val) {
      const { modelLabel } = val;

      //重置tab 现在只有保留building和floor有两个tab了
      this.selectedMenu = null;
      if (modelLabel === "building") {
        this.menus = [
          {
            label: $T("车间/楼栋"),
            id: "building"
          },
          {
            label: $T("用能设备"),
            id: "manuequipment"
          }
        ];
        this.selectedMenu = "building";
      } else if (modelLabel === "floor") {
        this.menus = [
          {
            label: $T("产线/房间"),
            id: "floor"
          },
          {
            label: $T("用能设备"),
            id: "manuequipment"
          }
        ];
        this.selectedMenu = "floor";
      }
    },
    /**
     * 初始化表格列配置
     * @param val 当前选中节点
     */
    async initColumnArr(val) {
      this.ElTableColumnArr = await createColumnArr(val);
    },
    /**
     * 分页大小变化
     * @param val 当前页码
     */
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.getTableData();
    },
    /**
     * 当前页码变化
     * @param val 当前页码
     */
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getTableData();
    },
    /**
     * 获取表格数据
     */
    async getTableData(noChildrenFlag) {
      if (!this.currentNode) {
        return;
      }
      let children = [];
      if (!noChildrenFlag) {
        children = await this.findCurrentNodeChildrenConfig();
      }
      if (!children?.length) {
        // 如果点了末端节点，则列表展示自身
        const queryData = [
          {
            modelLabel: this.currentNode.modelLabel,
            id: this.currentNode.id
          }
        ];
        const res = await customApi.getNodeDetail(queryData);
        const data = res.data || [];
        this.CetTable_1.data = data;
        this.totalCount = data.length;
        return;
      }

      const queryData = {
        parentNode: {
          modelLabel: this.currentNode.modelLabel,
          id: this.currentNode.id
        },
        name: this.ElInput_1.value,
        index: (this.currentPage - 1) * this.pageSize,
        limit: this.pageSize,
        nodeTreeGroupId: this.netWork ? 1 : 2
      };

      const res = await customApi.getChildNodes(queryData);
      this.CetTable_1.data = res?.data ?? [];
      this.totalCount = res.total || 0;
    },
    /**
     * 添加同级节点
     */
    CetButton_1_statusTrigger_out() {
      if (!this.currentNode) {
        return;
      }

      // 油气田项目中，《油气井》下只能新建一个机采设备
      if (this.oilCheckData(this.currentNode, "sibling")) return;

      this.fatherNode = this._.cloneDeep(this.fatherCurrentNode);
      let treeData = this.fatherNode.children || [],
        treeNameList = [];
      treeData.forEach(item => {
        treeNameList.push(item.name);
      });
      this.treeNameList = treeNameList;
      this.addAndEditNode.inputData_in = null;
      this.addAndEditNode.visibleTrigger_in = new Date().getTime();
    },
    /**
     * 添加子级节点
     */
    CetButton_2_statusTrigger_out() {
      if (!this.currentNode) {
        return;
      }

      // 油气田项目中，《油气井》下只能新建一个机采设备
      if (this.oilCheckData(this.currentNode)) return;

      this.fatherNode = this._.cloneDeep(this.currentNode);
      const treeData = this.fatherNode.children || [];
      const treeNameList = [];
      treeData.forEach(item => {
        treeNameList.push(item.name);
      });
      this.treeNameList = treeNameList;
      this.addAndEditNode.inputData_in = null;
      this.addAndEditNode.visibleTrigger_in = new Date().getTime();
    },
    /**
     * 批量添加子级
     */
    CetButton_batchAdd_statusTrigger_out() {
      this.batchAdd.currentNode_in = this._.cloneDeep(this.currentNode);
      this.batchAdd.netWork = this.netWork;
      this.batchAdd.visibleTrigger_in = Date.now();
    },
    /**
     * 管理层级和用能设备的切换，只存在于楼栋、房间层级
     */
    async handleTabClick_out(tab) {
      const data = {
        modelLabel: tab
      };
      this.ElTableColumnArr = await createColumnArr(data);
      this.getTableData();
    },
    // 暂无复制粘贴功能
    // /**
    //  * 表格操作区更多按钮点击事件
    //  */
    // dropdownClick(val) {
    //   switch (val) {
    //     case "copy":
    //       this.copyNodeHandle();
    //       break;
    //     case "paste":
    //       this.pasteNodeHandle();
    //       break;
    //     case "batchMove":
    //       this.batchMoveHandle();
    //       break;
    //     case "association":
    //       this.gotoAssociatRelation();
    //       break;
    //     default:
    //       break;
    //   }
    // },
    // /**
    //  * 复制节点
    //  */
    // copyNodeHandle() {
    //   if (!this.fatherCurrentNode) {
    //     return;
    //   }
    //   this.copyNode = {
    //     fatherNodeModelLabel: this.fatherCurrentNode.modelLabel,
    //     node: this.currentNode
    //   };
    // },
    // /**
    //  * 粘贴节点
    //  */
    // pasteNodeHandle() {
    //   // 油气田项目中，《油气井》下只能新建一个机采设备
    //   if (this.oilCheckData(this.currentNode)) return;

    //   const confirmMsg = $T(
    //     "是否将【{0}】复制到【{1}】",
    //     this.copyNode.node.name,
    //     this.currentNode.name
    //   );
    //   this.$confirm(confirmMsg, $T("提示"), {
    //     confirmButtonText: $T("确定"),
    //     cancelButtonText: $T("取消"),
    //     cancelButtonClass: "btn-custom-cancel",
    //     type: "info",
    //     closeOnClickModal: false,
    //     showClose: false,
    //     beforeClose: async (action, instance, done) => {
    //       let data = {
    //         cmodelLabel: this.copyNode.node.modelLabel,
    //         cmoldelId: this.copyNode.node.id,
    //         pmodelId: this.currentNode.id,
    //         pmodelLabel: this.currentNode.modelLabel,
    //         system: "cloudsp"
    //       };

    //       if (action === "confirm") {
    //         const response = await customApi.copyNodes(data);
    //         if (response.code !== 0) {
    //           return;
    //         }
    //         this.$message.success($T("操作成功"));
    //         this.getTreeData();
    //       }
    //       instance.confirmButtonLoading = false;
    //       done();
    //     },
    //     callback: action => {
    //       if (action !== "confirm") {
    //         this.$message.info($T("已取消！"));
    //       }
    //     }
    //   });
    // },
    /**
     * 批量移动节点
     */
    async batchMoveHandle() {
      let nodes = this.selectedData || [];
      if (nodes.length === 0) {
        return;
      }

      const data = await this.findCurrentNodeChildrenConfig();
      const leafNode = !data?.length;

      if (leafNode) {
        // 属于叶子节点
        this.fatherNode = this._.cloneDeep(this.fatherCurrentNode);
      } else {
        this.fatherNode = this._.cloneDeep(this.currentNode);
      }
      this.batchChangeNode.visibleTrigger_in = new Date().getTime();
    },
    /**
     * 前往设备关联
     */
    gotoAssociatRelation() {},
    /**
     * 节点树操作区更多按钮点击事件
     */
    treeFootClick(val) {
      switch (val) {
        case "importChild":
          this.batchImport.currentNode_in = this._.cloneDeep(this.currentNode);
          this.batchImport.netWork = this.netWork;
          this.batchImport.visibleTrigger_in = Date.now();
          break;
        case "addChild":
          this.CetButton_2_statusTrigger_out();
          break;
        case "batchAddChild":
          this.CetButton_batchAdd_statusTrigger_out();
          break;
        default:
          break;
      }
    },
    /**
     * 批量删除节点
     */
    CetButton_3_statusTrigger_out() {
      if (!this.selectedData.length) {
        return;
      }

      this.$confirm($T("是否删除此节点?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          this.toDeleteNode();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已取消删除")
          });
        });
    },
    /**
     * 删除节点
     */
    async toDeleteNode() {
      const deleteData = this.selectedData.map(item => {
        return {
          id: item.id,
          modelLabel: item.modelLabel
        };
      });
      const res = await customApi.nodeBatchDelete(deleteData);
      if (res.code !== 0) {
        return;
      }
      if (
        this.selectedData.length === 1 &&
        this.currentNode.modelLabel === this.selectedData[0].modelLabel &&
        this.currentNode.id === this.selectedData[0].id
      ) {
        this.currentNode = null;
        this.CetGiantTree_1.selectNode = null;
      }
      this.$message.success($T("删除成功"));
      this.isRefreshTable = true;
      this.getTreeData();
      this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
    },
    /**
     * 批量移动节点
     */
    CetButton_4_statusTrigger_out() {
      this.batchMoveHandle();
    },
    /**
     * 批量导入节点
     */
    async CetButton_batchNode_statusTrigger_out() {
      await this.getProjectEnergy();
      this.uploadDialog.openTrigger_in = new Date().getTime();
    },
    /**
     * 获取项目能源类型
     */
    async getProjectEnergy() {
      const res = await customApi.queryProjectEnergyList();
      if (res.code !== 0) return;

      const data = this._.get(res, "data", []).filter(
        item =>
          [
            this.totalEnergyType,
            this.totalEnergyTypeCO2,
            this.totalEnergyTypeC
          ].indexOf(item.energytype) === -1
      );
      if (data.length) {
        this.ElOption_energyType.options_in = data;
        this.ElSelect_energyType.value =
          this.ElOption_energyType.options_in[0].energytype;
        this.ElSelect_energyType_change_out();
      } else {
        this.ElOption_energyType.options_in = [];
        this.ElSelect_energyType.value = null;
      }
    },
    /**
     * 导入时能源类型切换
     */
    async ElSelect_energyType_change_out() {
      const templateName = this.getTemplateName();
      const res = await customApi.topologyNodeTypes({
        projectId: this.projectId,
        templateName
      });
      const data = res?.data ?? [];
      if (
        this.ElSelect_energyType === 2 &&
        data.find(i => i.modelLabel === "linesegmentwithswitch")
      ) {
        this.ElSelect_nodeTypes.value = ["linesegmentwithswitch"];
      } else if (
        this.ElSelect_energyType !== 2 &&
        data.find(i => i.modelLabel === "pipeline")
      ) {
        this.ElSelect_nodeTypes.value = ["pipeline"];
      }
      this.ElSelect_nodeTypes.value = [data?.[0]?.modelLabel];
      this.ElOption_nodeTypes.options_in = this._.get(res, "data");
    },
    /**
     * 获取模板名称
     */
    getTemplateName() {
      let templateName = "other-device";
      const energyType = this.ElSelect_energyType.value;
      if (energyType === 2) {
        templateName = "power-device";
      } else if (energyType === 26) {
        templateName = "communication-device";
      } else if (energyType === 16) {
        // 压缩空气
        templateName = "aircompressor-device";
      } else if (energyType === 24) {
        // 冷量
        templateName = "refrigeration-device";
      } else if (energyType === 7 || energyType === 46) {
        // 热水/热量
        templateName = "boiler-device";
      }
      return templateName;
    },
    // 打开项目配置
    CetButton_projectCfg_statusTrigger_out() {
      this.projectCfg.visibleTrigger_in = new Date().getTime();
    },
    //点击弹出项目详情弹框
    detailProject() {
      this.projectConfigDetail.inputData_in = this._.cloneDeep(
        this.projectInfo
      );
      this.projectConfigDetail.visibleTrigger_in = new Date().getTime();
    },
    /**
     * 编辑项目
     */
    editProject() {
      if (!this.projectInfo?.id) return;
      this.addProjectConfig1.inputData_in = this._.cloneDeep(this.projectInfo);
      this.addProjectConfig1.visibleTrigger_in = Date.now();
    },
    //表格点击多选框
    handleSelectionChange(val) {
      this.selectedData = val;
    },
    /**
     * 编辑节点
     */
    async editNode(val) {
      // 找父级节点
      if (this.currentNode.modelLabel === val.modelLabel) {
        this.fatherNode = this._.cloneDeep(this.fatherCurrentNode);
      } else {
        this.fatherNode = this._.cloneDeep(this.currentNode);
      }

      // 找同级别所有name
      const data = await this.findCurrentNodeChildrenConfig();
      const leafNode = !data?.length;

      if (!leafNode) {
        this.treeNameList = this.currentNode.children.map(i => i.name);
      } else {
        this.treeNameList = this.fatherCurrentNode.children.map(i => i.name);
      }

      this.addAndEditNode.inputData_in = this._.cloneDeep(val);
      this.addAndEditNode.visibleTrigger_in = Date.now();
    },
    /**
     * 节点详情
     */
    nodeDetail(val) {
      this.detail.inputData_in = this._.cloneDeep(val);
      this.detail.visibleTrigger_in = Date.now();
    },
    /**
     * 油气田项目中，《油气井》下只能新建一个机采设备
     * @param currentNode 当前选中节点
     * @param type 添加的类型。child：添加子层级；sibling：添加同级
     */
    oilCheckData(currentNode, type = "child") {
      const mes = $T("一个井只能有一个机采设备，该井目前已有机采设备！");
      // 机采设备不允许创建同级节点
      if (
        currentNode?.modelLabel === "mechanicalminingmachine" &&
        type === "sibling"
      ) {
        this.$message.warning(mes);
        return true;
      }
      // 油气井下有机采设备时不允许创建子层级
      const hasDevice = !!currentNode?.children?.find(
        item => item.modelLabel === "mechanicalminingmachine"
      );
      const hasChildrenFlag =
        currentNode?.modelLabel === "oilwell" && type === "child" && hasDevice;

      hasChildrenFlag && this.$message.warning(mes);
      return hasChildrenFlag;
    },
    /**
     * 完成节点编辑或者新增的回调
     * @param val 节点数据
     */
    addAndEditNode_saveData_out() {
      if (this.showTab) {
        this.isRefreshTable = true;
      }

      this.getTreeData();
    },
    /**
     * 编辑项目的输出
     */
    async addProjectConfig1_saveData_out() {
      await this.getTreeData();
      await this.getProject();
    },
    /**
     * 批量导入节点的导出模板
     */
    uploadDialog_download() {
      var queryData = {
        templateName: this.getTemplateName(),
        energyType: this.ElSelect_energyType.value
      };
      if (
        this.ElSelect_nodeTypes.value &&
        this.ElSelect_nodeTypes.value.length
      ) {
        queryData.modelLables = this.ElSelect_nodeTypes.value;
      }
      common.downExcel(
        "/eembaseconfig/eem-base/v1/topology/exportNodeAndConnection?projectId=" +
          this.projectId,
        queryData,
        this.token,
        this.projectId
      );
    },
    /**
     * 批量导入节点
     * @param val 文件
     */
    uploadDialog_uploadFile(val) {
      const formData = new FormData();
      formData.append("file", val.file);
      var queryData = {
        projectId: this.projectId,
        templateName: this.getTemplateName(),
        energyType: this.ElSelect_energyType.value
      };
      customApi.topologyImportNodeAsync(formData, queryData).then(response => {
        if (response.code === 0) {
          this.$message({
            type: "success",
            message: response.msg
          });
          this.uploadDialog.closeTrigger_in = Date.now();
          this.getTreeData();
        }
      });
    },
    /**
     * 导入子级保存
     */
    batchImportSave_out() {
      this.isRefreshTable = true;
      this.getTreeData();
    },
    /**
     * 批量添加子级保存
     */
    batchAdd_out() {
      this.isRefreshTable = true;
      this.getTreeData();
    },

    /**
     * 批量转移节点之后，刷新页面
     */
    updataOut() {
      this.getTreeData();
    },
    /**
     * 初始化driver
     * step: 1-表计全量创建全流程，2-单步创建能源类型，3-批量导入子层级全流程，4-单步批量导入
     */
    async initDriver() {
      const { step } = api.getRouterQuery();
      if (!step) return;
      await this.$nextTick();
      switch (step) {
        case "1":
          this.initDriverToCreateEnergyType($T("批量配置"), () => {
            const config = {
              path: "/fusion/eembaseconfig/eem-base-config/projectBatchConfig",
              query: {
                t: Date.now(),
                step: 1
              }
            };
            api.subRouterQuery(config);
          });
          break;
        case "2":
          this.initDriverToCreateEnergyType();
          break;
        case "3":
          this.initDriverToCreateEnergyType($T("批量配置"), () => {
            this.projectCfg.closeTrigger_in = Date.now();
            this.initDriverToImportStep();
          });
          break;
        case "4":
          this.initDriverToImportStep();
          break;
      }
    },
    sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    },
    /**
     * 创建能源类型引导
     * @param doneBtnText 确定按钮的文案
     * @param callback 完成之后的回调
     */
    initDriverToCreateEnergyType(doneBtnText, callback) {
      const [Driver] = useCounter(
        doneBtnText && {
          doneBtnText
        }
      );
      Driver.setSteps([
        {
          element: "#cloud_project_config_project_config",
          popover: {
            title: $T("设置能源类型"),
            description: $T("点击项目设置，打开能源类型设置弹框"),
            onNextClick: async () => {
              this.projectCfg.visibleTrigger_in = Date.now();
              await this.sleep(500);
              Driver.moveNext();
            },
            onPrevClick: () => {
              Driver.movePrevious();
            }
          }
        },
        {
          element: "#cloudProjectConfig_projectCfg_energy",
          popover: {
            title: $T("新增能源类型"),
            description: $T("点击新增按钮，添加能源类型并保存"),
            onNextClick: () => {
              callback && callback();
              Driver.moveNext();
            },
            onPrevClick: () => {
              this.projectCfg.closeTrigger_in = Date.now();
              Driver.movePrevious();
            }
          }
        }
      ]);
      Driver.drive();
    },
    /**
     * 批量导入子层级流程
     */
    async initDriverToImportStep() {
      // 初次刷新时，dom可能没构建出来
      await this.sleep(500);
      this.$refs.moreDropdown.show();
      // 展开选项之后等dom渲染完才能获取dom
      await this.sleep(500);
      const [Driver] = useCounter();
      Driver.setSteps([
        {
          element: "#cloud_project_config_more",
          popover: {
            title: $T("导入子级"),
            description: $T("点击更多按钮选择导入子级菜单，进入导入子级弹框"),
            onNextClick: async () => {
              this.treeFootClick("importChild");
              await this.sleep(500);
              Driver.moveNext();
            },
            onPrevClick: () => {
              Driver.movePrevious();
            }
          }
        },
        {
          element: "#cloudProjectConfig_batchImport_nodeType",
          popover: {
            title: $T("选择节点类型"),
            description: $T("选择想要创建的子级节点类型"),
            onNextClick: () => {
              Driver.moveNext();
            },
            onPrevClick: async () => {
              this.$refs.moreDropdown.show();
              await this.sleep(500);
              this.batchImport.closeTrigger_in = new Date().getTime();
              Driver.movePrevious();
            }
          }
        },
        {
          element: "#cloudProjectConfig_batchImport_energyType",
          popover: {
            title: $T("选择能源类型"),
            description: $T("选择想要创建的子级所属能源类型"),
            onNextClick: async () => {
              // 切换tab
              this.$refs.batchImport.stepActive = 1;
              await this.sleep(500);
              Driver.moveNext();
            },
            onPrevClick: () => {
              Driver.movePrevious();
            }
          }
        },
        {
          element: "#cloudProjectConfig_batchImport_treeBox",
          popover: {
            title: $T("选择节点"),
            description: $T("在左侧将节点拖拽至右侧表格中新增节点"),
            onNextClick: () => {
              Driver.moveNext();
            },
            onPrevClick: async () => {
              this.$refs.batchImport.stepActive = 0;
              await this.sleep(500);
              Driver.movePrevious();
            }
          }
        },
        {
          element: "#cloudProjectConfig_batchImport_tableBox",
          popover: {
            title: $T("编辑表格"),
            description: $T(
              "支持类似excel表格操作进行编辑表格，支持单个、多个单元格复制，支持外部excel复制内容并粘贴进来，支持拖拽序号列进行顺序微调，单个单元格中支持键盘按delete进行删除数据，单元格支持选择已存在层级节点名称或者全新填写名称"
            ),
            onNextClick: () => {
              Driver.moveNext();
            },
            onPrevClick: () => {
              Driver.movePrevious();
            }
          }
        },
        {
          element: "#cloudProjectConfig_batchImport_delete",
          popover: {
            title: $T("删除数据"),
            description: $T("勾选后可支持删除"),
            onNextClick: () => {
              Driver.moveNext();
            },
            onPrevClick: () => {
              Driver.movePrevious();
            }
          }
        },
        {
          element: "#cloudProjectConfig_batchImport_confirm",
          popover: {
            title: $T("批量导入子级"),
            description: $T("点击可将表格中新增条目、草稿条目自动创建配置"),
            onNextClick: () => {
              Driver.moveNext();
            },
            onPrevClick: () => {
              Driver.movePrevious();
            }
          }
        }
      ]);
      Driver.drive();
    }
  },
  async mounted() {
    this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
    await this.getTreeData();
    await this.getProject();
  }
};
</script>
<style lang="scss" scoped>
.projectInfo {
  .loadImg {
    width: 100px;
    height: 100px;
  }
  .projectInfoList {
    overflow: auto;
    position: relative;
    .projectInfoItem {
      width: 160px;
      padding: var(--J3);
      margin-right: var(--J3);
      background-color: var(--BG);
      border-radius: var(--Ra);
      .label {
        color: var(--T3);
        font-size: var(--Aa);
      }
      .value {
        max-width: 100%;
        font-size: var(--Aa);
        font-weight: bold;
      }
    }
  }
  .rightBtn {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    .btn {
      cursor: pointer;
      color: var(--ZS);
    }
  }
}
// el-select多选以tag展示时，超过显示长度以...省略号显示
.multipleSelect {
  :deep(.el-select__tags-text) {
    display: inline-block;
    max-width: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
