<template>
  <div>
    <CetDialog v-bind="CetDialog_edit" v-on="CetDialog_edit.event">
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="ml-J1"
          v-bind="CetButton_preserve"
          v-on="CetButton_preserve.event"
        ></CetButton>
      </span>
      <div>
        <CetForm
          :data.sync="CetForm_edit.data"
          v-bind="CetForm_edit"
          v-on="CetForm_edit.event"
        >
          <el-form-item :label="$T('名称')" prop="name">
            <el-input
              v-model.trim="CetForm_edit.data.name"
              :placeholder="$T('请输入')"
            />
          </el-form-item>
        </CetForm>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";

export default {
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    node: {
      type: Object
    }
  },
  data() {
    return {
      CetDialog_edit: {
        title: $T("编辑名称"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        width: "600px",
        event: {}
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      CetForm_edit: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ]
        },
        event: {
          saveData_out: this.CetForm_edit_saveData_out
        }
      }
    };
  },
  watch: {
    openTrigger_in(val) {
      this.CetDialog_edit.openTrigger_in = val;
      this.CetForm_edit.data = {
        name: this.node.name
      };
      this.CetForm_edit.resetTrigger_in = Date.now();
    }
  },
  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_edit.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      this.CetForm_edit.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetForm_edit_saveData_out(val) {
      this.saveData(val);
    },
    async saveData(val) {
      const res = await customApi.updateFavorite(
        Object.assign({}, this.node, {
          name: this.CetForm_edit.data.name
        })
      );
      if (res.code !== 0) return;
      this.$message.success($T("保存成功"));
      this.CetDialog_edit.closeTrigger_in = Date.now();
      this.$emit("saveData_out", this._.cloneDeep(val));
    }
  }
};
</script>
<style lang="scss" scoped></style>
