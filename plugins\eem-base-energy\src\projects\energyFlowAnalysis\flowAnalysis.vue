<template>
  <div class="page">
    <div id="graphID" ref="container"></div>
    <el-tooltip placement="bottom" :content="$T('保存为图片')">
      <span
        class="downloadIcon el-icon-download"
        @click="handleDownloadIcon"
      ></span>
    </el-tooltip>
    <nodeDetail v-bind="nodeDetail" />
  </div>
</template>

<script>
import G6 from "@antv/g6";
import common from "eem-base/utils/common";
import addDark from "./assets/addDark.png";
import addLight from "./assets/addLight.png";
import reduceDark from "./assets/reduceDark.png";
import reduceLight from "./assets/reduceLight.png";
import customApi from "@/api/custom";
import nodeDetail from "./nodeDetail.vue";
import omegaTheme from "@omega/theme";
import {
  lightLoopColor,
  loopColor,
  loopHighLightColor,
  ligthLoopHighLightColor
} from "./flowColor.js";

export default {
  name: "TopologyChart",
  props: {
    inputData_in: {
      type: [Object, Array]
    },
    imageName: {
      type: String
    },
    params: {
      type: Object
    }
  },
  components: { nodeDetail },
  computed: {
    lightTheme() {
      return omegaTheme.theme === "light";
    }
  },
  data() {
    return {
      graph: null,
      graphData: {},
      isExpand: [], //存储节点展开状态
      initInputData: {}, //初始化数据存放
      handInputData: {}, //过滤使用数据存放
      projectValue: 0, //储存第一层能源流向值
      childData: {}, //子节点数据
      maxIindex: 0,
      initIsExpand: [], //存储初始时节点展开状态
      nodeDetail: {
        openTrigger_in: Date.now(),
        inputData_in: null,
        params: null
      }
    };
  },
  watch: {
    inputData_in: {
      handler(val) {
        this.$nextTick(() => {
          this.paintChart();
        });
      },
      deep: true
    }
  },
  methods: {
    handlerFocusNode(val) {
      //将所有当前有choose状态的节点的choose状态置为false
      let chooseNodes = this.graph.findAllByState("node", "choose");
      chooseNodes.forEach(node => {
        this.graph.setItemState(node, "choose", false);
      });
      //搜索值为空
      if (!val) {
        return;
      }

      const findNodes = this.graph.findAll("node", node => {
        return node._cfg.id === val;
      });
      if (findNodes.length > 0) {
        const item = findNodes[0];
        //设置当前节点的choose状态为true
        this.graph.setItemState(item, "choose", true);
        //使用 focusItem 自动居中
        this.graph.focusItem(item, true, {
          duration: 500, // 动画时长
          easing: "easeCubic", // 缓动效果
          zoom: 1 // 不改变当前缩放级别
        });
      }
    },
    handleExpandNode(node) {
      //将所有当前有expand状态的节点的choose状态置为false
      let expandNodes = this.graph.findAllByState("node", "expand");
      expandNodes.forEach(node => {
        this.graph.setItemState(node, "expand", false);
      });

      //设置当前节点的expand状态为true
      this.graph.setItemState(node, "expand", true);
    },
    // 初始化图形配置
    initGraphConf() {
      let _this = this;
      if (this.graph) return;
      var width = this.$refs.container.scrollWidth;
      var height = this.$refs.container.scrollHeight || 500;
      this.graph = new G6.Graph({
        container: "graphID",
        width: width,
        height: height,
        fitView: true,
        minZoom: 0.5,
        maxZoom: 3,
        modes: {
          default: [
            "drag-canvas",
            "drag-node",
            "zoom-canvas",
            {
              type: "tooltip",
              formatText: this.formatLabel
            }
          ]
        },
        layout: {
          type: "dagre",
          rankdir: "LR",
          ranksep: 100,
          nodesep: 20,
          controlPoints: true
        },
        defaultNode: {
          type: "customCircle",
          labelCfg: {
            style: {
              fill: "#fff",
              fontSize: 14
            }
          },
          style: {
            fill: "#C6E5FF",
            cursor: "pointer"
          }
        },
        defaultEdge: {
          size: 1,
          color: "#A3B1BF",
          type: "cubic-horizontal",
          style: {
            lineWidth: 5,
            stroke: this.lightTheme
              ? "l(0) 0:#EDF8F1 1:#A4DDBC"
              : "l(0) 0:#0E1B47 0.5:#123778 1:#123778",
            endArrow: {
              path: "M 0,0 L 20,10 L 20,-10 Z",
              d: 0,
              fill: this.lightTheme ? "#A4DDBC" : "#123778"
            }
          }
        }
      });
      this.graph.on("node:click", function (evt) {
        const shapeName = evt.target.get("name"); // 获取被点击的图形元素的名称
        const val = _this._.get(evt, "item._cfg.model", {}) || {};
        if (
          [
            "rect-item",
            "text-nodeName",
            "text-value",
            "text-percentage"
          ].includes(shapeName)
        ) {
          _this.openDrawer(val);
        } else if (shapeName === "image-shape") {
          _this.clickChart(val);
        }
      });
    },
    //显示抽屉
    openDrawer(val) {
      this.nodeDetail.params = this.params;
      this.nodeDetail.inputData_in = _.cloneDeep(val);
      this.nodeDetail.openTrigger_in = Date.now();
    },
    formatLabel(params) {
      return `${params.label}: ${
        params.energyValue || params.energyValue === 0
          ? common.formatNum(params.energyValue.toFixed(2))
          : "--"
      }${common.formatSymbol(params.unit)} ${
        params.percentage ? params.percentage : ""
      }`;
    },
    // 获取图表数据
    getChartData() {
      this.isExpand = [];
      let data = this.handInputData;
      if (!data || !data.energyFlowNodeDataList || !data.linkNodeList) return;
      // 数据源
      const energyFlowNodeDataList = data.energyFlowNodeDataList || [];
      // 数据连线
      const linkNodeList = data.linkNodeList || [];
      let depth = 0;
      // 先默认所有节点都可展开
      energyFlowNodeDataList.forEach(item => {
        item.expend = true;
        if (item.depth > depth) {
          depth = item.depth;
        }
      });
      //如果是最后一层子节点，不需要显示/可展示
      energyFlowNodeDataList.forEach(item => {
        let expanded = false;
        let isChild = false;
        let getIsChild = this.initInputData.linkNodeList.filter(ii => {
          return item.treeId === ii.source;
        });
        isChild = getIsChild.length === 0;
        if (item.depth < depth) {
          expanded = true;
        } else {
          expanded = this._.cloneDeep(isChild);
        }
        //这里默认层级小于最后一层的均展开，但搜索定位时搜索节点各级的兄弟节点均需为收缩状态
        if (
          this.inputData_in.selectId &&
          item.depth <= depth - 1 &&
          item.depth > 0 &&
          item.treeId !== this.inputData_in.selectId
        ) {
          expanded = isChild ? false : this.getIsExpand(getIsChild[0].target);
        }
        this.isExpand.push({
          id: item.treeId,
          expanded: expanded,
          isChild: isChild
        });
      });
      this.initIsExpand = _.cloneDeep(this.isExpand);
      // 点集
      let nodes = [];
      energyFlowNodeDataList.forEach(item => {
        let obj = {
          id: item.treeId,
          label: item.nodeName,
          energyValue:
            item.energyValues &&
            item.energyValues[0] &&
            item.energyValues[0].value,
          unit: data.unit,
          percentage: item.percentage,
          depth: item.depth,
          expend: item.expend,
          iindex: item.iindex,
          nodeLabel: item.nodeLabel
        };
        nodes.push(obj);
      });
      // 边集
      // 项目节点的能源值，用于节点百分比计算宽度
      const target = energyFlowNodeDataList.find(flow => flow.depth === 0);
      const projectValue = (target && target.totalValue) || 0;
      this.projectValue = projectValue;
      const edges = linkNodeList.map(item => {
        // 节点值占根节点的百分比
        const percentage = projectValue ? Number(item.value / projectValue) : 0;
        // 设置连线的最大最小宽度
        const minWidth = 10;
        const maxWidth = 30;
        const width = maxWidth * percentage;
        const lineWidth =
          width < minWidth ? minWidth : width > maxWidth ? maxWidth : width;
        return {
          source: item.source,
          target: item.target,
          style: {
            lineWidth: projectValue ? lineWidth : minWidth,
            stroke: this.lightTheme
              ? "l(0) 0:#EDF8F1 1:#A4DDBC"
              : "l(0) 0:#0E1B47 0.5:#123778 1:#123778"
          }
        };
      });
      return { nodes, edges };
    },
    getIsExpand(target) {
      const nodeObj = this.initInputData.energyFlowNodeDataList.find(
        item => item.treeId === target
      );
      return !_.isEmpty(nodeObj);
    },
    // 绘制图形
    paintChart() {
      const inputData = this.inputData_in;
      if (
        !inputData ||
        !inputData.energyFlowNodeDataList ||
        !inputData.linkNodeList
      )
        return;
      if (Array.isArray(inputData.energyFlowNodeDataList)) {
        inputData.energyFlowNodeDataList.forEach((item, index) => {
          item.iindex = index;
        });
        this.maxIindex = inputData.energyFlowNodeDataList.length;
      }
      this.initInputData = this._.cloneDeep(inputData);
      //搜索定位节点时，不需仅展示三层节点
      if (_.isEmpty(this.inputData_in.selectId)) {
        this.handInputData = this.filterInitData(inputData);
      } else {
        this.handInputData = _.cloneDeep(inputData);
      }
      const data = this.getChartData();
      this.$nextTick(() => {
        if (this._.isEmpty(this.graph)) this.initGraphConf();
        this.graphData = this._.cloneDeep(data);
        this.graph.data(data);
        this.graph.render();
        //搜索定位
        this.handlerFocusNode(this.inputData_in.selectId);
      });
    },
    // 超长文本显示
    fittingString(str, maxWidth, fontSize) {
      const ellipsis = "...";
      const ellipsisLength = G6.Util.getTextSize(ellipsis, fontSize)[0];
      let currentWidth = 0;
      let res = str;
      const pattern = new RegExp("[\u4E00-\u9FA5]+"); // distinguish the Chinese charactors and letters
      str.split("").forEach((letter, i) => {
        if (currentWidth > maxWidth - ellipsisLength) return;
        if (pattern.test(letter)) {
          // Chinese charactors
          currentWidth += fontSize;
        } else {
          // get the width of single letter according to the fontSize
          currentWidth += G6.Util.getLetterWidth(letter, fontSize);
        }
        if (currentWidth > maxWidth - ellipsisLength) {
          res = `${str.substr(0, i)}${ellipsis}`;
        }
      });
      return res;
    },
    //过滤初始化返回数据，只需要显示三层数据结构;depth ===0和depth === 1归类为父节点
    filterInitData(val) {
      let energyFlowNodeDataList = val.energyFlowNodeDataList || [],
        linkNodeList = val.linkNodeList || [],
        toDelete = [];
      let filEnergyFlowNodeDataList = energyFlowNodeDataList.filter(
        item => item.depth <= 2
      );
      toDelete = energyFlowNodeDataList.filter(item => item.depth > 2);
      let filLinkNodeList = linkNodeList.filter(item => {
        let isOk = true;
        for (let i = 0, len = toDelete.length; i < len; i++) {
          if (item.target === toDelete[i].treeId) {
            isOk = false;
            break;
          }
        }
        return isOk;
      });
      return {
        energyFlowNodeDataList: filEnergyFlowNodeDataList,
        linkNodeList: filLinkNodeList,
        unit: val.unit
      };
    },
    // 点击节点触发方法
    async clickChart(val) {
      if (!val) {
        return;
      }
      //查找对应节点信息
      const target = this.initInputData.linkNodeList.find(
        item => item.source === val.id
      );
      //如果是点击最后一层子节点，直接return退出处理
      if (!target) return;
      const energyType = target.energyType;
      const otherTypes = [16, 24, 46]; // 压缩空气、冷量、热量处理
      if (
        val.depth === 0 ||
        (val.depth === 1 && otherTypes.includes(energyType))
      ) {
        return;
      }
      //找到节点现在是收缩还是展开状态
      let index = 0;
      const result = this.isExpand.find((item, idx) => {
        index = idx;
        return item.id === val.id;
      });
      if (!result) {
        return;
      }
      const obj = this.initIsExpand.find(item => item.id === val.id);
      const mark = obj ? obj.expanded : false;
      // 收缩，根节点不能收缩
      if (result.expanded) {
        this.isExpand[index].expanded = false;
        const nodes = this._.cloneDeep(this.graphData.nodes);
        const edges = this._.cloneDeep(this.graphData.edges);
        const copyEdges = this._.cloneDeep(edges);
        this.handleNode(edges, val, nodes, copyEdges);
        let data = { nodes, edges };
        this.graphData = this._.cloneDeep(data);
        this.graph.data(data);
        this.graph.render();
        if (this.inputData_in.selectId) {
          this.holdState();
        }
        this.focusCurrentNode(val.id);
        // 展开
      } else if (!result.expanded) {
        this.isExpand[index].expanded = true;
        let childNodeData = [];
        if (this.inputData_in.selectId && mark === false) {
          await this.getChildNodeDataNew(val);
          childNodeData = this.childrenData;
          this.initInputData.linkNodeList =
            this.initInputData.linkNodeList.concat(childNodeData.linkNodeList);
          this.initInputData.energyFlowNodeDataList =
            this.initInputData.energyFlowNodeDataList.concat(
              childNodeData.energyFlowNodeDataList
            );
        } else {
          childNodeData = this.getChildNodeData(val);
        }

        const chartData = [];
        // 桑基图data
        const energyFlowNodeDataList =
          childNodeData.energyFlowNodeDataList.filter(item => {
            return item.treeId !== val.id;
          });
        // 先默认所有节点都可展开
        energyFlowNodeDataList.forEach(item => {
          item.expend = true;
          //如果是最后一层子节点，不需要显示-可展示
          let expanded = false;
          let isChild = false;
          let getIsChild = this.initInputData.linkNodeList.filter(ii => {
            return item.treeId === ii.source;
          });
          isChild = getIsChild.length === 0;
          expanded = this._.cloneDeep(isChild);
          this.isExpand.push({
            id: item.treeId,
            expanded: expanded,
            isChild: isChild
          });
          chartData.push({
            id: item.treeId,
            label: item.nodeName,
            energyValue:
              item.energyValues &&
              item.energyValues[0] &&
              item.energyValues[0].value,
            unit: this.handInputData.unit,
            percentage: item.percentage,
            depth: item.depth,
            expend: item.expend,
            iindex:
              this.inputData_in.selectId && mark === false
                ? this.maxIindex++
                : item.iindex,
            nodeLabel: item.nodeLabel
          });
        });
        let linkNodeList = this._.cloneDeep(childNodeData.linkNodeList);
        // 边集
        // 项目节点的能源值，用于节点百分比计算宽度
        const linkNode = linkNodeList.map(item => {
          // 节点值占根节点的百分比
          const percentage = this.projectValue
            ? Number(item.value / this.projectValue)
            : 0;
          // 设置连线的最大最小宽度
          const minWidth = 10;
          const maxWidth = 30;
          const width = maxWidth * percentage;
          const lineWidth =
            width < minWidth ? minWidth : width > maxWidth ? maxWidth : width;
          return {
            source: item.source,
            target: item.target,
            style: {
              lineWidth: this.projectValue ? lineWidth : minWidth,
              stroke: this.lightTheme
                ? "l(0) 0:#EDF8F1 1:#A4DDBC"
                : "l(0) 0:#0E1B47 0.5:#123778 1:#123778"
            }
          };
        });
        let nodes = this.graphData.nodes.concat(chartData);
        const edges = this.graphData.edges.concat(linkNode);

        nodes = this.sortDatafilter(nodes);
        let data = { nodes, edges };
        this.graphData = this._.cloneDeep(data);
        this.graph.data(data);
        this.graph.render();
        if (this.inputData_in.selectId) {
          this.holdState();
        }
        this.focusCurrentNode(val.id);
      }
    },
    //保存搜索节点选中状态
    holdState() {
      const findNodes = this.graph.findAll("node", node => {
        return node._cfg.id === this.inputData_in.selectId;
      });
      //设置搜索节点的choose状态为true
      if (findNodes.length > 0) {
        this.graph.setItemState(findNodes[0], "choose", true);
      }
    },
    //将当前点击展开或收缩的节点移动到中心，且高亮，方便查看
    focusCurrentNode(val) {
      const currentNodes = this.graph.findAll("node", node => {
        return node._cfg.id === val;
      });
      if (currentNodes.length > 0) {
        this.graph.focusItem(currentNodes[0], true, {});
        this.handleExpandNode(currentNodes[0]);
      }
    },
    //点击缩收删除选中节点下子节点
    handleNode(linkData, data, nodeData, copyLinkData) {
      const toDelete = [];
      // linkData中找到所有source为当前点击的节点
      const deleteArr = linkData.filter(item => {
        return data && item.source === data.id;
      });
      if (deleteArr.length) {
        // linkData中删除deleteArr数据
        deleteArr.forEach(item => {
          this.deleteFun(linkData, item, toDelete);
        });
      }

      if (toDelete.length) {
        // linkData中被删除的数据继续调用handleNode
        toDelete.forEach(item => {
          // 判断子节点存在两个父节点情况
          const filFatherNodes = copyLinkData.filter(ii => {
            return item.target === ii.target;
          });
          if (filFatherNodes.length < 2) {
            const deleteData = nodeData.find(it => {
              return it.id === item.target;
            });
            this.handleNode(linkData, deleteData, nodeData, copyLinkData);
          }
        });
      }
      // nodeData中删除deleteArr中target节点，可展开节点中同步删除
      deleteArr.forEach(item => {
        // 判断子节点存在两个父节点情况
        const filFatherNodes = copyLinkData.filter(ii => {
          return item.target === ii.target;
        });
        if (filFatherNodes.length < 2) {
          const idx = nodeData.findIndex(it => {
            return item.target === it.id;
          });
          const it = this.isExpand.findIndex(i => {
            return item.target === i.id;
          });
          if (idx >= 0) {
            nodeData.splice(idx, 1);
          }
          if (it >= 0) {
            this.isExpand.splice(it, 1);
          }
        }
      });
    },
    deleteFun(linkData, item, toDelete) {
      const obj = this.initIsExpand.find(node => node.id === item.source);
      const mark = obj ? obj.expanded : false;
      const idx = linkData.findIndex(it => {
        return this._.isEqual(it, item);
      });
      if (idx >= 0 && this.inputData_in.selectId && mark === false) {
        toDelete.push(linkData[idx]);
      } else if (idx >= 0) {
        toDelete.push(linkData.splice(idx, 1)[0]);
      }
    },
    //获取点击选择节点下一子层级
    getChildNodeData(data) {
      let energyFlowNodeDataList =
          this.initInputData.energyFlowNodeDataList || [],
        linkNodeList = this.initInputData.linkNodeList || [];
      // linkData中找到所有source为当前点击的节点
      let childLinkData = linkNodeList.filter(item => {
        return data && item.source === data.id;
      });
      // nodeData中删除deleteArr中target节点，可展开节点中同步删除
      let childData = energyFlowNodeDataList.filter(item => {
        let isOk = false;
        for (let i = 0, len = childLinkData.length; i < len; i++) {
          if (item.treeId === childLinkData[i].target) {
            isOk = true;
            break;
          }
        }
        return isOk;
      });
      return {
        energyFlowNodeDataList: childData,
        linkNodeList: childLinkData,
        unit: this.initInputData.unit
      };
    },
    //调用接口获取节点子级数据
    async getChildNodeDataNew(data) {
      const { searchNode, ...params } = this.params;
      const list = data.id.split("_") || [];
      params.node = list
        ? {
            id: Number(list[0]),
            modelLabel: list[1]
          }
        : null;
      params.depth = 1;
      params.startDepth = data.depth;
      // 调用接口获取数据，暂时手动赋值
      let nodeList = [];
      let linkList = [];
      const res = await customApi.queryEnergyFlowNoLoading(params);
      if (res.code === 0) {
        nodeList = res.data.energyFlowNodeDataList || [];
        linkList = res.data.linkNodeList || [];
      }
      nodeList = nodeList.filter(node => node.treeId !== data.id);
      linkList = linkList.filter(link => link.source !== data.id);
      this.childrenData = {
        energyFlowNodeDataList: nodeList,
        linkNodeList: linkList,
        unit: this.initInputData.unit
      };
    },
    //对桑基图节点列表重新排序顺序
    sortDatafilter(data) {
      data = data || [];
      let obj = {},
        newArr = [];
      data.forEach(item => {
        obj[item.iindex] = item;
      });
      for (let i in obj) {
        newArr.push(obj[i]);
      }
      return newArr;
    },
    //点击下载图片
    handleDownloadIcon() {
      if (!this.graph) return;
      let bgColor = "#0e1b47";
      if (this.lightTheme) {
        bgColor = "#ffffff";
      }
      this.graph.downloadFullImage(this.imageName, null, {
        backgroundColor: bgColor,
        padding: [16, 16, 16, 16]
      });
    },
    // 进行颜色循环赋值
    getColors(depth) {
      return this.lightTheme
        ? lightLoopColor[depth % 10]
        : loopColor[depth % 10];
    },
    // 高亮颜色赋值
    getHighlightColors(depth) {
      return this.lightTheme
        ? ligthLoopHighLightColor[depth % 10]
        : loopHighLightColor[depth % 10];
    },
    getProjectColor() {
      return this.lightTheme
        ? "l(0) 0:#4FD8A6 1:#00B45E"
        : "l(0) 0:#53A2D4 1:#0D86FF";
    }
  },
  mounted() {
    const vm = this;
    G6.registerNode("customCircle", {
      drawShape(cfg, group) {
        const { depth, nodeLabel } = cfg;
        let width, height;
        // 定义每一层级的颜色
        const gColors = [
          "l(0) 0:#3684FA 1:#53A2D4",
          "l(0) 0:#418FEB 1:#5EAEC5"
        ];
        if (depth === 0) {
          width = 200;
          height = 100;
        } else {
          width = 150;
          height = 60;
        }
        group.addShape("rect", {
          attrs: {
            width,
            height,
            x: -width / 2,
            y: -height / 2,
            fill:
              nodeLabel !== "project"
                ? vm.getColors(depth)
                : vm.getProjectColor(),
            radius: [6, 6, 6, 6],
            cursor: "pointer"
          },
          name: "rect-item"
        });
        group.addShape("text", {
          attrs: {
            textAlign: "center",
            textBaseline: "top",
            x: 0,
            y: height * (depth === 0 ? 0.3 : 0.13) - height / 2,
            fill: "#fff",
            text: vm.fittingString(cfg.label, width - 20, 14),
            fontSize: 14,
            cursor: "pointer"
          },
          name: "text-nodeName"
        });
        group.addShape("text", {
          attrs: {
            textAlign: "center",
            textBaseline: "top",
            x: 0,
            y: height * (depth === 0 ? 0.6 : 0.46) - height / 2,
            fill: "#fff",
            text: `${
              cfg.energyValue || cfg.energyValue === 0
                ? common.formatNum(cfg.energyValue.toFixed(2))
                : "--"
            }(${common.formatSymbol(cfg.unit)})`,
            fontSize: 14,
            fontWeight: 900,
            cursor: "pointer"
          },
          name: "text-value"
        });
        if (cfg.percentage) {
          group.addShape("text", {
            attrs: {
              textAlign: "center",
              textBaseline: "top",
              x: 0,
              y: height * 0.25,
              fill: "#fff",
              text: `${cfg.percentage}`,
              cursor: "pointer"
            },
            name: "text-percentage"
          });
        }
        const result = vm.isExpand.find(item => item.id === cfg.id);
        let isExpend = false;
        if (cfg.expend && result && !result.isChild) {
          isExpend = true;
        }
        if (result && result.expanded) {
          isExpend = false;
        }
        if (isExpend) {
          group.addShape("image", {
            attrs: {
              x: width / 2 + 4,
              y: -10,
              width: 20,
              height: 20,
              img: vm.lightTheme ? addLight : addDark
            },
            name: "image-shape"
          });
        } else if (!result.isChild && depth !== 0) {
          group.addShape("image", {
            attrs: {
              x: width / 2 + 4,
              y: -10,
              width: 20,
              height: 20,
              img: vm.lightTheme ? reduceLight : reduceDark
            },
            name: "image-shape"
          });
        }

        return group;
      },
      setState(name, value, item) {
        const group = item.getContainer();
        const shape = group.find(e => e.get("name") === "rect-item");
        // 搜索选中节点、点击展开收缩节点时样式变化
        if (["choose", "expand"].includes(name) && value) {
          //项目节点不高亮
          shape.attr({
            fill:
              item._cfg.model.nodeLabel !== "project"
                ? vm.getHighlightColors(item._cfg.model.depth)
                : vm.getProjectColor()
          });
        } else if (["choose", "expand"].includes(name) && !value) {
          shape.attr({
            fill:
              item._cfg.model.nodeLabel !== "project"
                ? vm.getColors(item._cfg.model.depth)
                : vm.getProjectColor()
          });
        }
      },
      getAnchorPoints: function getAnchorPoints() {
        return [
          [1, 0.5],
          [0, 0.5]
        ];
      }
    });
    // this.$nextTick(() => {
    //   this.paintChart();
    // });
  },
  activated() {}
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  .downloadIcon {
    position: absolute;
    top: 30px;
    right: 30px;
    font-size: 20px;
    cursor: pointer;
    z-index: 9999;
  }
}
#graphID {
  width: 100%;
  height: 100%;
}
</style>
<style lang="scss">
.g6-tooltip {
  white-space: nowrap;
  transition: left 0.4s cubic-bezier(0.23, 1, 0.32, 1) 0s,
    top 0.4s cubic-bezier(0.23, 1, 0.32, 1) 0s;
  background-color: rgba(50, 50, 50, 0.7);
  font-size: 14px;
  border-radius: 4px;
  color: rgb(255, 255, 255);
  padding: 10px;
  // min-width: 300px;
  // min-height: 200px;
  pointer-events: none;
}
</style>
