import { modules, state, mutations, actions } from "eem-base/store/index.js";
import customApi from "@/api/custom.js";

export default {
  modules: { ...modules },
  state: {
    ...state,
    multidimensional: false,
    systemCfg: {
      // 数据录入，tab切换列表,1：实际产量；2：账单能耗；3：台账；4：计划产量；5：能耗
      dataEntryList: [
        {
          label: "实际产量",
          name: "1"
        },
        {
          label: "账单能耗",
          name: "2"
        },
        {
          label: "台账",
          name: "3"
        },
        {
          label: "能耗",
          name: "5"
        },
        {
          label: "计划产量",
          name: "4"
        }
      ],
      // 计量仪表台账同步节点限制上限
      synchronousNodeLimit: 200
    }
  },
  mutations: {
    ...mutations,
    setMultidimensional(state, val) {
      state.multidimensional = val;
    },
    setSystemCfg(state, val) {
      state.systemCfg = val;
    }
  },
  actions: {
    ...actions,
    async getConfig({ commit }) {
      const res = await customApi.getConfigInfo();
      const dataEntryList = res?.data?.dataEntryList || [];
      const multi = res?.data?.supportMultiDimension || false;
      commit("setSystemCfg", { dataEntryList });
      commit("setMultidimensional", multi);
    }
  }
};
