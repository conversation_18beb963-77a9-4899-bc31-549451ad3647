import omegaApp from "@omega/app";
import "./omega/index";
import "./resources/index.css";
import "./resources/index.scss";
import "./icons/index.js";

import Vue from "vue";

import lodash from "lodash";
import moment from "moment";
import ElementUI from "element-ui";

import routerOption from "./router";
import storeOption from "./store";

import enums from "./config/enums";

import customApi from "./api/custom";
import CetCommon from "cet-common";
import CetChart, { registerTheme } from "cet-chart";

Vue.use(CetCommon, {
  api: customApi,
  CetDialog: {
    isDraggable: false
  }
});
Vue.use(CetChart, {
  // themeConf: {
  // backgroundColor: "#000"
  // }
});

Vue.config.productionTip = false;

Vue.prototype.$enums = enums;

// 工具库
Vue.prototype._ = lodash;
Vue.prototype.$moment = moment;

// ElementUI
Vue.use(ElementUI, { size: "small" });

//引入趋势曲线组件
import OmegaTend from "@omega/trend";
Vue.use(OmegaTend);

Vue.component("slot", {
  render(h) {
    return h("span", this.$slots.default);
  }
});
// 启动
omegaApp.createApp({
  el: "#app",
  config: {},
  routerOption,
  storeOption
});
