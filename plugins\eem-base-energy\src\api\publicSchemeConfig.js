import fetch from "eem-base/utils/fetch";

const version = "v1";

// 获取分摊方案列表详细数据
export function getApportDetailData(data) {
  return fetch({
    url: `/eembaseenergy/v1/energy-share-config/shareConfig/${data}`,
    method: "GET"
  });
}

// 分摊方案保存
export function saveEnergyShareConfigScheme(data) {
  return fetch({
    url: `/eembaseenergy/v1/energy-share-config/saveEnergyShareConfigScheme`,
    method: "PUT",
    data
  });
}

//  分摊方案批量删除
export function delEnergyShareConfigSchemeBatch(data) {
  return fetch({
    url: `/eembaseenergy/v1/energy-share-config/delEnergyShareConfigSchemeBatch`,
    method: "DELETE",
    data
  });
}

//  查询分摊方案 -- 新的接口
export function getEnergyShareConfigSchemeGroup(data) {
  return fetch({
    url: `/eembaseenergy/v1/energy-share-config/energyShareConfigScheme/group`,
    method: "POST",
    data
  });
}

// 新增或编辑分摊方案前调用的校验功能
export function checkSchemeConfig(data) {
  return fetch({
    url: `/eembaseenergy/v1/energy-share-config/saveEnergyShareConfigScheme/check`,
    method: "POST",
    data
  });
}

//  获取当前项目重算状态
export function getEnergyReCalcState(projectId) {
  return fetch({
    url: `/eem-service/${version}/system/data/energy/re-calc/state/${projectId}`,
    method: "POST"
  });
}

//  flink执行能耗重算
export function getEnergyReCalc(data) {
  return fetch({
    url: `/eem-service/${version}/system/data/energy/re-calc`,
    method: "POST",
    timeout: 600000,
    data
  });
}
