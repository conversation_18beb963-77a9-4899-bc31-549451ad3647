<template>
  <div>
    <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="min">
      <el-main class="eem-cont-c1 fullheight">
        <CetForm
          :data.sync="CetForm_1.data"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
        >
          <el-form-item :label="$T('属性标识')" prop="name">
            <ElInput
              v-model.trim="CetForm_1.data.name"
              v-bind="ElInput_name"
              v-on="ElInput_name.event"
              :disabled="editData_in ? true : false"
            ></ElInput>
          </el-form-item>
          <el-form-item :label="$T('属性名称')" prop="alias">
            <ElInput
              v-model.trim="CetForm_1.data.alias"
              v-bind="ElInput_alias"
              v-on="ElInput_alias.event"
            ></ElInput>
          </el-form-item>
          <el-form-item :label="$T('属性类型')" prop="datatype">
            <ElSelect
              v-model="CetForm_1.data.datatype"
              v-bind="ElSelect_type"
              v-on="ElSelect_type.event"
              :disabled="editData_in ? true : false"
            >
              <ElOption
                v-for="item in ElOption_type.options_in"
                :key="item[ElOption_type.key]"
                :label="item[ElOption_type.label]"
                :value="item[ElOption_type.value]"
                :disabled="item[ElOption_type.disabled]"
              ></ElOption>
            </ElSelect>
          </el-form-item>
          <!-- <el-form-item label="是否必填：" prop="allownull">
            <ElSelect v-model="CetForm_1.data.allownull" v-bind="ElSelect_allownull" v-on="ElSelect_allownull.event">
              <ElOption
                v-for="item in ElOption_allownull.options_in"
                :key="item[ElOption_allownull.key]"
                :label="item[ElOption_allownull.label]"
                :value="item[ElOption_allownull.value]"
                :disabled="item[ElOption_allownull.disabled]"
              ></ElOption>
            </ElSelect>
          </el-form-item> -->
          <el-form-item
            v-if="
              CetForm_1.data.datatype === 'enum' ||
              (CetForm_1.data.enumerationvalue &&
                CetForm_1.data.enumerationvalue.length)
            "
            :label="$T('取值范围')"
            prop="enumerationvalue"
          >
            <div
              v-for="(item, index) in CetForm_1.data.enumerationvalue"
              :key="index"
              class="flex flex-row mb10"
            >
              <ElInput
                class="flex-auto"
                v-model.trim="CetForm_1.data.enumerationvalue[index].text"
                v-bind="ElInput_value"
                v-on="ElInput_value.event"
                :disabled="editData_in ? true : false"
              ></ElInput>
              <i
                v-if="!editData_in"
                class="el-icon-delete delete ml-J1 mt-J0"
                @click="deleteValue(index)"
              ></i>
            </div>
            <div v-if="!editData_in" class="add" @click="addValue">+</div>
          </el-form-item>
        </CetForm>
      </el-main>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
export default {
  name: "add",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    editData_in: {
      type: Object
    }
  },
  data(vm) {
    var checkValue = (rule, value, callback) => {
      if (!value || !value.length) {
        return callback(new Error($T("请输入取值范围")));
      }
      value.forEach(item => {
        if (!item.text) {
          return callback(new Error($T("取值范围不能为空")));
        }
        if (value.filter(i => i.text === item.text).length > 1) {
          return callback(new Error($T("取值重复")));
        }
      });

      callback();
    };
    return {
      // 1弹窗组件
      CetDialog_1: {
        title: $T("新增属性"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // 1表单组件
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {
          datatype: "string"
          // allownull: true
        },
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "100px",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入属性标识"),
              trigger: ["blur", "change"]
            },
            {
              pattern: /^[a-zA-Z]+$/,
              message: $T("只能输入英文"),
              trigger: ["blur", "change"]
            }
          ],
          alias: [
            {
              required: true,
              message: $T("请输入属性名称"),
              trigger: ["blur", "change"]
            }
          ],
          datatype: [
            {
              required: true,
              message: $T("请选择属性类型"),
              trigger: ["blur", "change"]
            }
          ],
          // allownull: [
          //   {
          //     required: true,
          //     message: "请选择是否必填属性",
          //     trigger: ["blur", "change"]
          //   }
          // ],
          enumerationvalue: [
            {
              required: true,
              validator: checkValue,
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      // type组件
      ElSelect_type: {
        value: "string",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_type_change_out
        }
      },
      // type组件
      ElOption_type: {
        options_in: [
          {
            value: "string",
            label: $T("文本类型")
          },
          {
            value: "int8",
            label: $T("整数类型")
          },
          {
            value: "float",
            label: $T("小数类型")
          },
          {
            value: "date",
            label: $T("日期类型")
          },
          {
            value: "enum",
            label: $T("枚举类型")
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      // ElSelect_allownull: {
      //   value: true,
      //   style: {
      //     width: "200px"
      //   },
      //   event: {
      //     change: this.ElSelect_allownull_change_out
      //   }
      // },
      // ElOption_allownull: {
      //   options_in: [
      //     {
      //       value: true,
      //       label: "是"
      //     },
      //     {
      //       value: false,
      //       label: "否"
      //     }
      //   ],
      //   key: "value",
      //   value: "value",
      //   label: "label",
      //   disabled: "disabled"
      // },
      ElInput_name: {
        value: "",
        style: {
          width: "100%"
        },
        placeholder: $T("请输入内容"),
        event: {}
      },
      ElInput_value: {
        value: "",
        style: {},
        placeholder: $T("请输入内容"),
        event: {}
      },
      ElInput_alias: {
        value: "",
        style: {
          width: "100%"
        },
        placeholder: $T("请输入内容"),
        event: {}
      }
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      if (vm.editData_in) {
        vm.CetForm_1.data = vm.editData_in;
        vm.CetForm_1.data.enumerationvalue = JSON.parse(
          vm.CetForm_1.data.enumerationvalue
        );
        vm.CetDialog_1.title = $T("编辑属性");
      } else {
        vm.CetDialog_1.title = $T("新增属性");
        vm.reset();
      }
      vm.CetForm_1.resetTrigger_in = new Date().getTime();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },
  methods: {
    reset() {
      this.CetForm_1.data = {
        datatype: "string",
        // allownull: true,
        value: [],
        id: 1
      };
    },
    addValue() {
      if (
        this.CetForm_1.data.enumerationvalue &&
        this.CetForm_1.data.enumerationvalue.length > 0
      ) {
        this.CetForm_1.data.enumerationvalue.push({
          text: ""
        });
      } else {
        this.$set(this.CetForm_1.data, "enumerationvalue", [
          {
            text: ""
          }
        ]);
      }
    },
    deleteValue(index) {
      this.CetForm_1.data.enumerationvalue.splice(index, 1);
    },
    addTemplate(val) {
      var data = [];
      if (val.enumerationvalue && val.enumerationvalue.length > 0) {
        val.enumerationvalue.forEach((item, index) => {
          item.id = index + 1;
        });
      }
      if (this.editData_in) {
        val.modelLabel = "meterpropertyextend";
        data = [val];
      } else {
        data = [
          {
            id: 0,
            modelLabel: "meterpropertyextend",
            name: val.name,
            alias: val.alias,
            datatype: val.datatype,
            enumerationvalue: val.enumerationvalue,
            display: true,
            allowdeactivation: true,
            active: true,
            allowduplication: false,
            allownull: false,
            nullable: true,
            projectid: this.projectId,
            repeatable: true
          }
        ];
      }
      customApi.editDeviceTemplate(data, this.projectId).then(response => {
        if (response.code === 0) {
          this.$message({
            type: "success",
            message: $T("保存成功")
          });
          this.CetDialog_1.closeTrigger_in = new Date().getTime();
          // 刷新
          this.$emit("updata_out");
        }
      });
    },
    CetButton_confirm_statusTrigger_out(val) {
      if (!this.editData_in) {
        this.$confirm(
          $T("保存之后将不再允许修改属性标识、属性类型、属性值?"),
          $T("提示"),
          {
            confirmButtonText: $T("确定"),
            cancelButtonText: $T("取消"),
            type: "warning"
          }
        )
          .then(() => {
            this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: $T("已取消")
            });
          });
      } else {
        this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
      }
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = val;
    },
    CetForm_1_saveData_out(val) {
      this.addTemplate(val);
    },
    ElSelect_type_change_out(val) {
      if (val === "enum") {
        this.$set(this.CetForm_1.data, "enumerationvalue", []);
      } else {
        this.$set(this.CetForm_1.data, "enumerationvalue", null);
      }
    }
  },
  activated: function () {}
};
</script>
<style lang="scss" scoped>
.line {
  margin: 0 5px;
}
.handel {
  cursor: pointer;
}
.handel.display {
  color: #72809b;
  cursor: context-menu;
}
.add {
  border: 2px solid;
  @include border_color(B1);
  border-radius: 3px;
  cursor: pointer;
  height: 50px;
  width: 50px;
  text-align: center;
  line-height: 50px;
  font-size: 30px;
}
.delete {
  font-size: 20px;
  cursor: pointer;
  @include font_color(Sta3);
}
</style>
