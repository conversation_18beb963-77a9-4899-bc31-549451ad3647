__registeAppConfig.setAppconfig({
  version: "1.0.0",
  i18n: {
    en: {
      告警中心: "Alarm Center",
      邮件通知配置: "Email notification configuration",
      消息推送: "Message push"
    }
  },
  navmenu: [
    {
      label: "告警中心",
      icon: "permission-management-lin",
      type: "subMenu",
      subMenuList: [
        {
          label: "邮件通知配置",
          category: "project",
          type: "menuItem",
          location: "/emailserverset",
          permission: "emailserverset"
        },
        {
          label: "消息推送",
          category: "project",
          type: "menuItem",
          location: "/messagePush",
          permission: "messagePush"
        }
      ]
    }
  ],
  newGuideSteps: []
});
