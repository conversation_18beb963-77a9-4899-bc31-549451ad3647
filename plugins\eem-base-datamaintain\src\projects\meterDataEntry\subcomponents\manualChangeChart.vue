<template>
  <div class="fullheight">
    <CetChart
      :inputData_in="CetChart_1.inputData_in"
      v-bind="CetChart_1.config"
      style="height: 100%"
      class="chart"
    ></CetChart>
  </div>
</template>

<script>
import { themeMap } from "cet-chart";
import omegaTheme from "@omega/theme";
import customApi from "@/api/custom";

export default {
  name: "manualChangeChart",
  props: {
    paramData: {
      type: Object
    }
  },
  data(vm) {
    return {
      // echart图表配置
      // 1组件
      CetChart_1: {
        //组件输入项
        inputData_in: null,
        config: {
          options: {
            //图表配置
            tooltip: {
              trigger: "axis",
              triggerOn: "click",
              enterable: true,
              formatter: function (params) {
                console.log(params);
                let dataStr = "";
                // let showButtom = false;
                let time;
                if (params && params.length) {
                  dataStr = params[0].axisValue + "<br/>";
                  params.forEach((item, index) => {
                    dataStr +=
                      "<span style='display:inline-block;vertical-align:middle;margin-right:10px;width:15px;height:2px;background-color:" +
                      item.color +
                      ";'></span>" +
                      item.seriesName +
                      ":" +
                      "<span style='margin-left: 8px'>" +
                      item.value +
                      "</span><br/>";
                    time = item.data.logTime;
                    if (
                      item.data &&
                      item.data.enterValue &&
                      item.data.enterValue !== "--"
                    ) {
                      dataStr +=
                        "<span style='display:inline-block;vertical-align:middle;margin-right:10px;width:15px;height:2px;background-color:" +
                        item.color +
                        ";'></span>" +
                        item.data.enterName +
                        ":" +
                        "<span style='margin-left: 8px'>" +
                        item.data.enterValue +
                        "</span><br/>";
                    }
                  });
                  const auth = vm.$checkPermission(
                    "devicerepairdatalog_update"
                  );
                  if (auth) {
                    // dataStr +=
                    //   "<div style='margin-top:8px'><buttom id='buttomStyle' class='tooltip-btn' onclick='buttomClick(" +
                    //   time +
                    //   `)'>${$T("换表")}</buttom></div>`;
                    dataStr += `<div style='margin-top:8px'><buttom id='buttomStyle' class='tooltip-btn' data-time='${time}'>${$T(
                      "换表"
                    )}</buttom></div>`;
                  }
                }
                return dataStr;
              }
            },
            legend: {
              type: "scroll",
              itemStyle: {
                // 去除图例上的圆圈
                opacity: 0
              }
            },
            grid: {
              left: "3%",
              right: "4%",
              bottom: "10%",
              containLabel: true
            },
            xAxis: {
              type: "category",
              // 刻度与标签文本对齐
              axisTick: {
                alignWithLabel: true
              },
              data: []
            },
            yAxis: {
              name: $T("数据"),
              type: "value",
              scale: true,
              splitLine: {
                show: true,
                lineStyle: {
                  type: "dashed" // y轴分割线类型
                }
              }
            },
            // 下面区间滑块
            dataZoom: [
              {
                xAxisIndex: 0,
                type: "slider",
                bottom: "10",
                height: "30",
                start: 0,
                end: 100
              }
            ],
            series: []
          }
        }
      },
      testSeries: [],
      templateData: []
    };
  },
  watch: {
    paramData(newVal, oldVal) {
      this.initChart();
    }
  },
  methods: {
    // 图表初始化展示
    initChart() {
      const currentTheme = omegaTheme.theme;
      const themeMapConfig = themeMap.get(currentTheme);
      let colorList = themeMapConfig.color;
      // 初始化图表之前进行清除数据处理，防止出现上次的残留数据
      this.clearChart();
      customApi.getFlinkDatalogTrend(this.paramData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.templateData = this._.cloneDeep(data);
          let chartData = this.updateData(data);
          let markLineData = this.getMarkLineData(chartData);
          let pointData = this.getChangeData(chartData);
          let series = this._.cloneDeep(this.testSeries);
          if (chartData.length) {
            chartData.forEach((item, index) => {
              series.push({
                name: item[0].name,
                type: "line",
                data: item,
                color: colorList[index],
                markPoint: {
                  data: pointData[index],
                  symbolSize: 30,
                  itemStyle: {
                    color: "#FF3F3F"
                  }
                },
                markLine: {
                  symbol: ["none", "none"],
                  label: {
                    show: false
                  },
                  lineStyle: {
                    type: "dashed",
                    width: 2,
                    color: colorList[index]
                  },
                  data: markLineData[index]
                }
              });
            });
            this.CetChart_1.config.options.xAxis.data = this.getTimeValue(
              chartData[0]
            );
          }
          this.CetChart_1.config.options.series = this._.cloneDeep(series);
        }
      });
    },
    clearChart() {
      this.CetChart_1.config.options.series = [];
      this.CetChart_1.config.options.xAxis.data = [];
      this.CetChart_1.config.options = this._.cloneDeep(
        this.CetChart_1.config.options
      );
    },
    // 对数据进行处理
    updateData(data) {
      const currentTheme = omegaTheme.theme;
      const themeMapConfig = themeMap.get(currentTheme);
      let colorList = themeMapConfig.color;
      let result = [];
      if (data.length) {
        if (data[0].logicalValues.length) {
          for (let j = 0; j < data[0].logicalValues.length; j++) {
            result.push([]);
          }
        }
        data.forEach(item => {
          let index = 0;
          item.logicalValues.forEach(i => {
            result[index].push({
              name: $T("回路{0}实际数据", i.logicalid),
              logTime: item.logtime,
              value: this.formatNumber(i.datalogValue),
              enterValue: this.formatNumber(i.value),
              enterName: $T("回路{0}旧表表码值", i.logicalid),
              symbolSize: i.value !== null ? 15 : 6,
              itemStyle: {
                color: i.value !== null ? colorList[index] : null
              }
            });
            index++;
          });
        });
      }
      return result;
    },
    // 对数据进行处理录入值存在数据进行打线
    getMarkLineData(data) {
      let result = [];
      data.forEach(item => {
        let num = [];
        item.forEach(i => {
          if (i.enterValue && i.enterValue !== "--") {
            num.push({
              name: $T("录入数据"),
              xAxis: this.$moment(i.logTime).format("YYYY-MM-DD HH:mm")
            });
          }
        });
        result.push(num);
      });
      return result;
    },
    // 获取需要进行换表的数据
    getChangeData(data) {
      let result = [];
      let startValue;
      data.forEach((item, index) => {
        let num = [];
        startValue = Number(item[0].value);
        for (let i = 1; i < item.length - 1; i++) {
          let currentValue = Number(item[i].value);
          let nextValue = Number(item[i + 1].value);
          if (currentValue < startValue && currentValue < nextValue) {
            num.push({
              name: $T("换表值"),
              xAxis: this.$moment(item[i].logTime).format("YYYY-MM-DD HH:mm"),
              yAxis: item[i].value
            });
            item[i].showButtom = true;
          }
          if (item[i].value || item[i].value == 0) {
            startValue = item[i].value;
          }
        }
        result.push(num);
      });
      return result;
    },
    // 对x轴进行处理
    getTimeValue(data) {
      let result = [];
      data.forEach(item => {
        let time = this.$moment(item.logTime).format("YYYY-MM-DD HH:mm");
        result.push(time);
      });
      return result;
    },
    // 数字格式化
    formatNumber(value) {
      return value !== null ? Number(value).toFixed(2) : "--";
    },
    // 通过换表点的时间获取实际数据和录入数据
    getAllData(time) {
      let result = [];
      this.templateData.forEach(item => {
        if (item.logtime === time) {
          item.logicalValues.forEach(i => {
            item["value_" + i.logicalid] =
              i.value === null ? undefined : i.value.toFixed(2);
            item["datalogValue_" + i.logicalid] = i.datalogValue;
          });
          item.time = this.$moment(item.logtime).format("YYYY-MM-DD HH:mm");
          result.push(item);
        }
      });
      return result;
    },
    // 处理点击换表
    setupTooltipHandler() {
      const win = window.__WUJIE_RAW_WINDOW__ || window.top || window;
      const chartDom = this.$el.querySelector(".chart");

      if (chartDom) {
        chartDom.addEventListener("click", e => {
          if (e.target.classList.contains("tooltip-btn")) {
            const time = e.target.getAttribute("data-time");
            win.buttomClick(Number(time));
          }
        });
      }
    }
  },
  mounted() {
    this.initChart();
    let vm = this;

    const win = window.__WUJIE_RAW_WINDOW__ || window.top || window;
    win.buttomClick = function (time) {
      let data = vm.getAllData(time);
      vm.$emit("buttomClick", data);
    };

    // win.document.addEventListener("click", e => {
    //   if (e.target.matches(".tooltip-btn")) {
    //     const fnStr = e.target.onclick.toString();
    //     const match = fnStr.match(/\bbuttomClick\(([^)]+)\)/);

    //     let data = this.getAllData(Number(match[1]));
    //     this.$emit("buttomClick", data);
    //   }
    // });

    this.setupTooltipHandler();
  }
};
</script>

<style lang="scss">
#buttomStyle {
  margin-top: 16px;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 3px;
  color: #fff;
  @include border_color(ZS);
  @include background_color(ZS);
  cursor: pointer;
}
</style>
