<template>
  <div>
    <CetDialog v-bind="CetDialog_add" v-on="CetDialog_add.event">
      <span slot="footer">
        <CetButton
          v-bind="{ ...CetButton_cancel, title: $T('取消') }"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="ml-J1"
          v-bind="CetButton_preserve"
          v-on="CetButton_preserve.event"
        ></CetButton>
      </span>
      <CetForm
        :data.sync="CetForm_add.data"
        v-bind="CetForm_add"
        v-on="CetForm_add.event"
      >
        <el-form-item :label="$T('文件夹名称')" prop="name">
          <el-input
            v-model.trim="CetForm_add.data.name"
            :placeholder="$T('请输入')"
          />
        </el-form-item>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";

export default {
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    node: {
      type: Object
    }
  },
  data() {
    return {
      CetDialog_add: {
        title: $T("新建文件夹"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        width: "600px",
        event: {}
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      CetForm_add: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: []
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入文件夹名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ]
        },
        event: {
          saveData_out: this.CetForm_add_saveData_out
        }
      }
    };
  },
  watch: {
    openTrigger_in(val) {
      this.CetDialog_add.openTrigger_in = val;
      this.CetForm_add.data = {};
      this.CetForm_add.resetTrigger_in = new Date().getTime();
    }
  },
  methods: {
    async saveData(val) {
      const res = await customApi.saveNewFavorite({
        name: this.CetForm_add.data.name,
        dataType: 1,
        nodeType: 1,
        parentId: 0
      });
      if (res.code !== 0) return;
      this.$message.success($T("保存成功"));
      this.CetDialog_add.closeTrigger_in = Date.now();
      this.$emit("saveData_out", this._.cloneDeep(val));
    },
    CetForm_add_saveData_out(val) {
      this.saveData(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      this.CetForm_add.localSaveTrigger_in = this._.cloneDeep(val);
    }
  }
};
</script>
<style lang="scss" scoped>
.formContent {
  :deep(.el-form-item__content) {
    display: flex;
  }
}
</style>
