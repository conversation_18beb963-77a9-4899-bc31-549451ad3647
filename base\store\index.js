import customApi from "@/api/custom";
import { api } from "@altair/knight";
import importProgress from "./modules/importProgress/index.js";

const modules = {
  importProgress
};

const state = {
  // 存储枚举类
  enumerations: {},
  token: null,
  userInfo: null
};

const mutations = {
  setEnumerations(state, val) {
    state.enumerations = val;
  },
  setUserInfo(state, val) {
    state.userInfo = val;
  },
  setToken(state, token) {
    state.token = token;
  }
};

const actions = {
  async setEnumerations({ commit }) {
    const res = await customApi.getEnumerations();
    const enumerations = res.data || [];

    // 所有枚举类型
    let allEnumerations = {};
    enumerations.forEach(item => {
      allEnumerations[item.modelLabel] = item.enums;
    });
    commit("setEnumerations", allEnumerations);
  },
  setUserInfo({ commit }) {
    const user = api.getUser()?._user;
    commit("setUserInfo", user);
  },
  setToken({ commit }, token) {
    commit("setToken", token);
  }
};

export { modules, state, mutations, actions };
