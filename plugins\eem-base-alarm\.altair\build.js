module.exports = {
  pluginInfos: {
    // frontVersion: "1.0.0",//可自定义前端部分的版本号，如果使用package.json中的版本号，则可以不填
    version: "5.0.1", //整体插件的版本号，必填
    name: "eem-base-alarm" //整体插件的唯一值，必填
  },
  market: {
    host: "http://************:8190",
    user: {
      name: "eem-base",
      password: "sA123456@"
    }
  },
  build: {
    // command: "vite build --outDir {{outputDir}}"
    // command: "vue-cli-service build --dest {{outputDir}}"//默认的命令
  },
  ding: {
    //是否发送钉钉消息，默认不发送
    send: true,
    // proxy: "http://*************:9898",
    proxy: "",
    robots: [
      {
        secret:
          "SECd2fe0b081e6e1be1805adae7f25aa585db56d40dd724aee3d182f38b9e6fbc4f",
        webhook:
          "https://oapi.dingtalk.com/robot/send?access_token=abdc828b9c9838192d867731af569932f04cef03b8202b7948e097d54d6ef948"
      }
    ],
    isAtAll: false,
    // 通过手机号@相关人
    // atMobiles: ["18062123947"]
    atMobiles: []
  }
};
