<template>
  <div class="h-full flex flex-col">
    <div class="top-chart chart">
      <div class="filter-row">
        <div class="title">
          {{ $T("需量趋势") }}
        </div>
        <div>
          <CetDateSelect
            v-bind="CetDateSelect_time"
            v-on="CetDateSelect_time.event"
          ></CetDateSelect>
          <el-button type="primary" class="ml-J1" @click="onExportClick">
            {{ $T("导出") }}
          </el-button>
        </div>
      </div>
      <CetChart v-bind="dayChart"></CetChart>
    </div>
    <div class="bottom-chart">
      <div class="week-chart chart">
        <div class="flex flex-row title-line">
          <div class="title">{{ $T("本周最大需量") }}</div>
          <chartTypeSwitch
            v-model="weekChartType"
            @change="handlerWeekChartTypeChange"
          />
        </div>
        <CetChart v-bind="weekChart"></CetChart>
      </div>
      <div class="month-chart chart">
        <div class="flex flex-row title-line">
          <div class="title">{{ $T("当月最大需量") }}</div>
          <chartTypeSwitch
            v-model="monthChartType"
            @change="handlerMonthChartTypeChange"
          />
        </div>
        <CetChart v-bind="monthChart"></CetChart>
      </div>
    </div>
  </div>
</template>
<script>
import { CetDateSelect } from "cet-common";
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
import * as echarts from "echarts";
import chartTypeSwitch from "eem-base/components/chartTypeSwitch.vue";

export default {
  components: {
    CetDateSelect,
    chartTypeSwitch
  },
  props: {
    id: {
      type: Number
    }
  },
  watch: {
    id: {
      handler(val) {
        if (val) {
          this.getDayChartData();
          this.getWeekMonthChartData();
        }
      },
      immediate: true
    }
  },
  computed: {},
  data() {
    return {
      weekChartType: 1,
      monthChartType: 1,
      CetDateSelect_time: {
        showOption: false,
        nextDisabledNum: 1,
        value: {
          dateType: "1",
          value: this.getTimeRangeOfTheDay(new Date(), -1).startTime
        },
        dayDateOption: {
          firstDayOfWeek: 1,
          disabledDate(time) {
            return time.getTime() >= new Date().setHours(0, 0, 0, 0);
          }
        },
        event: {
          date_out: this.onDateSelect
        }
      },
      selectedDate: this.getTimeRangeOfTheDay(new Date(), -1),
      dayChart: {
        options: {
          legend: {
            show: true
          },
          tooltip: {
            trigger: "axis",
            formatter: params => {
              let str = "";
              let list0 = params[0].axisValueLabel.split(" ");
              str += list0[1].slice(0, 5) + "<br>";
              params.forEach(item => {
                str += `${item.marker} ${item.seriesName}${$T(
                  "（{0}）",
                  "kW"
                )}:  ${item.value[1] ?? "--"}<br>`;
              });
              return str;
            }
          },
          grid: {
            top: "20%",
            bottom: 20,
            left: 20,
            right: "1.5%",
            containLabel: true
          },
          color: ["#0D86FF", "#05F974"],
          xAxis: {
            type: "time",
            // min: this.getTimeRangeOfTheDay(new Date()).startTime,
            // max: this.getTimeRangeOfTheDay(new Date()).endTime,
            axisLabel: {
              formatter: "{HH}:{mm}"
            },
            axisTick: {
              show: false
            },
            splitNumber: 8
          },
          yAxis: {
            name: "kW",
            type: "value",
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed"
              }
            }
          },
          series: [
            {
              name: this.$T("今日需量"),
              data: [],
              type: "line",
              smooth: true,
              symbolSize: 1,
              markPoint: {
                data: [
                  {
                    name: this.$T("最大值"),
                    type: "max"
                  }
                ]
              }
            },
            {
              name: this.$T("查询需量"),
              data: [],
              type: "line",
              smooth: true,
              symbolSize: 1,
              markPoint: {
                data: [
                  {
                    name: this.$T("最大值"),
                    type: "max"
                  }
                ]
              }
            }
          ]
        }
      },
      weekChart: {
        options: {
          tooltip: {
            trigger: "axis",
            formatter: params => {
              let str = "";
              let week = this.$moment().isoWeekday();
              let time = this.$moment().format("YYYY-MM-DD");
              str +=
                this.getNextDate(time, params[0].dataIndex - week + 1, 1) +
                "<br>";
              params.forEach(item => {
                str += `${item.marker} ${item.seriesName}${$T(
                  "（{0}）",
                  "kW"
                )}: ${item.value ?? "--"}`;
              });
              return str;
            }
          },
          grid: {
            top: "15%",
            bottom: 20,
            left: 12,
            right: "0%",
            containLabel: true
          },
          xAxis: {
            type: "category",
            axisTick: {
              show: false
            },
            data: [
              this.$T("一"),
              this.$T("二"),
              this.$T("三"),
              this.$T("四"),
              this.$T("五"),
              this.$T("六"),
              this.$T("日")
            ]
          },
          yAxis: {
            name: "kW",
            type: "value",
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed"
              }
            }
          },
          series: [
            {
              name: $T("最大需量"),
              data: [],
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(94, 119, 255, 1)" },
                  { offset: 1, color: "rgba(94, 119, 255, 0.1)" }
                ])
              },
              type: "bar",
              barWidth: "16px"
            }
          ]
        }
      },
      monthChart: {
        options: {
          tooltip: {
            trigger: "axis",
            formatter: params => {
              let str = "";
              str +=
                this.$moment().format("MM") +
                "-" +
                params[0].axisValue +
                "<br>";
              params.forEach(item => {
                str += `${item.marker} ${item.seriesName}${$T(
                  "（{0}）",
                  "kW"
                )}: ${item.value ?? "--"}`;
              });
              return str;
            }
          },
          grid: {
            top: "15%",
            bottom: 20,
            left: 12,
            right: "0%",
            containLabel: true
          },
          xAxis: {
            type: "category",
            axisTick: {
              show: false
            },
            data: this.getMonthCategory()
          },
          yAxis: {
            name: "kW",
            type: "value",
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed"
              }
            }
          },
          series: [
            {
              name: $T("最大需量"),
              data: [],
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(97, 236, 255, 1)" },
                  { offset: 1, color: "rgba(97, 236, 255, 0.1)" }
                ])
              },
              type: "bar",
              barWidth: "16px"
            }
          ]
        }
      }
    };
  },
  methods: {
    handlerWeekChartTypeChange(val) {
      this.weekChart.options.series[0].type = val === 1 ? "bar" : "line";
    },
    handlerMonthChartTypeChange(val) {
      this.monthChart.options.series[0].type = val === 1 ? "bar" : "line";
    },
    getNextDate(val, day, type) {
      const date = this.$moment(val).add(day, "d");
      const format = type === 2 ? "YYYY-MM-DD" : "MM-DD";
      return date.format(format);
    },
    //获取日需量数据
    async getDayChartData() {
      const { data = [] } = await customApi.queryDayDemand({
        deviceId: this.id,
        logicalId: 1,
        queryTimeRange: this.selectedDate,
        todayTimeRange: this.getTimeRangeOfTheDay(new Date())
      });
      if (!data || !data.length) {
        return;
      }
      this.dayChart.options.xAxis.min = data[0].time;
      this.dayChart.options.xAxis.max = data[data.length - 1].time;
      const dayDemand = data.map(({ time, dayDemand }) => {
        return [
          time,
          Number.isFinite(dayDemand) ? Number(dayDemand.toFixed(2)) : null
        ];
      });
      Object.assign(this.dayChart.options.series[0], { data: dayDemand });
      const consultDemand = data?.map(({ time, consultDemand }) => {
        return [
          time,
          Number.isFinite(consultDemand)
            ? Number(consultDemand.toFixed(2))
            : null
        ];
      });
      Object.assign(this.dayChart.options.series[1], { data: consultDemand });
    },
    //获取周、月最大需量
    async getWeekMonthChartData() {
      this.weekChartType = 1;
      this.monthChartType = 1;
      const now = new Date();
      const { data = [] } = await customApi.queryMonthDemand({
        deviceId: this.id,
        logicalId: 1,
        startTime: +this.$moment(now).startOf("month"),
        endTime: +this.$moment(now).add(1, "month").startOf("month")
      });
      if (!data) {
        return;
      }
      // 处理月度数据
      const monthData = data.map(item => {
        return Number.isFinite(item.value)
          ? Number(item.value.toFixed(2))
          : null;
      });
      this.monthChart.options.series[0].type = "bar";
      Object.assign(this.monthChart.options.series[0], {
        data: monthData
      });
      // 处理周数据
      const weekData = [];
      let week = this.$moment().isoWeekday();
      let time = this.$moment().format("YYYY-MM-DD");
      for (let i = 0; i < 7; i++) {
        let str = this.getNextDate(time, i - week + 1, 2);
        let list = str.split("-");
        let targetDate = new Date(list[0], list[1] - 1, list[2]).valueOf();
        const target = data.find(({ logTime }) => {
          return logTime === targetDate;
        });
        weekData.push(
          target?.value || target?.value === 0
            ? Number(target.value.toFixed(2))
            : null
        );
      }
      this.weekChart.options.series[0].type = "bar";
      Object.assign(this.weekChart.options.series[0], { data: weekData });
    },
    //获取某天0-24点的时间范围
    getTimeRangeOfTheDay(theDay, bias = 0) {
      const startTime = +this.$moment(theDay).add(bias, "day").startOf("day");
      const endTime = startTime + 24 * 60 * 60 * 1000;
      return { startTime, endTime };
    },
    // 这个月有多少天
    dayCountOfMonth() {
      return this.$moment().daysInMonth();
    },
    getMonthCategory() {
      const daysOfMonth = this.dayCountOfMonth();
      const monthCategory = new Array(daysOfMonth)
        .fill()
        .map((item, idx) => (idx + 1 + "").padStart(2, "0"));
      return monthCategory;
    },
    onDateSelect(date) {
      this.selectedDate = {
        startTime: date[0],
        endTime: date[1] + 1
      };
      this.getDayChartData();
    },
    onExportClick() {
      common.downExcel("/eembasedemand/v1/device-demand/export", {
        deviceId: this.id,
        logicalId: 1,
        queryTimeRange: this.selectedDate,
        todayTimeRange: this.getTimeRangeOfTheDay(new Date())
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.title {
  font-weight: bold;
  font-size: 16px;
}
.chart {
  border-radius: 4px;
  padding: 24px;
  box-sizing: border-box;
}
.top-chart {
  height: 52%;
  @include background_color(BG1);
  .filter-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.title-line {
  justify-content: space-between;
  align-items: center;
}
.bottom-chart {
  height: calc(48% - 16px);
  display: flex;
  margin-top: 16px;

  .week-chart {
    width: 31%;
    @include background_color(BG1);
  }
  .month-chart {
    width: calc(69% - 16px);
    margin-left: 16px;
    @include background_color(BG1);
  }
  .echarts {
    height: calc(100% - 35px);
  }
}
</style>
