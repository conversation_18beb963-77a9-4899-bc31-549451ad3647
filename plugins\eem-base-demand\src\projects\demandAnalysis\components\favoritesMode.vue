<template>
  <div class="h-full flex flex-col">
    <CetTree
      ref="cetTree"
      class="flex-auto flex-1"
      :selectNode.sync="CetTree_f.selectNode"
      :checkedNodes.sync="CetTree_f.checkedNodes"
      :searchText_in.sync="CetTree_f.searchText_in"
      v-bind="CetTree_f"
      v-on="CetTree_f.event"
    ></CetTree>
    <div class="footer">
      <template v-if="!deleteHandle">
        <el-dropdown trigger="click" class="fr ml-J1" @command="handleCommand">
          <el-button type="primary" class="more-btn">
            {{ $T("更多") }}
            <i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              :class="{ fcSta3: !!currentNode }"
              command="batch"
              :disabled="!currentNode"
            >
              {{ $T("批量删除") }}
            </el-dropdown-item>
            <el-dropdown-item command="name" :disabled="!currentNode">
              {{ $T("编辑名称") }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <CetButton
          class="fr"
          v-bind="CetButton_addFavorites"
          v-on="CetButton_addFavorites.event"
        ></CetButton>
      </template>
      <template v-else>
        <CetButton
          class="fr ml-J1"
          v-bind="CetButton_delete"
          v-on="CetButton_delete.event"
          :disable_in="!checkedNodes?.length"
        ></CetButton>
        <CetButton
          class="fr"
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </template>
    </div>
    <addFolder v-bind="addFolder" v-on="addFolder.event" />
    <editName v-bind="editName" v-on="editName.event" />
  </div>
</template>
<script>
import customApi from "@/api/custom";
import addFolder from "./addFolder.vue";
import editName from "./editName.vue";

export default {
  components: { addFolder, editName },
  props: {
    tree: {
      type: Array
    }
  },
  data() {
    return {
      CetTree_f: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        "empty-text": $T("暂无数据，请建立收藏夹"),
        event: {
          currentNode_out: this.CetTree_f_currentNode_out,
          checkedNodes_out: this.CetTree_f_checkedNodes_out
        }
      },
      CetButton_addFavorites: {
        visible_in: true,
        disable_in: false,
        title: $T("新建文件夹"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_addFavorites_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_delete: {
        visible_in: true,
        title: $T("删除"),
        type: "danger",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_delete_statusTrigger_out
        }
      },
      deleteHandle: false,
      currentNode: null,
      checkedNodes: [],
      addFolder: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          saveData_out: this.addFolder_saveData_out
        }
      },
      editName: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        node: null,
        event: {
          saveData_out: this.editName_saveData_out
        }
      }
    };
  },
  watch: {
    tree: {
      handler(val) {
        this.CetTree_f.inputData_in = this._.cloneDeep(val);
        if (val.length > 0) {
          this.CetTree_f.selectNode = val[0];
        }
      },
      immediate: true
    }
  },
  methods: {
    handleCommand(val) {
      switch (val) {
        case "batch":
          this.CetTree_f.showCheckbox = true;
          this.deleteHandle = true;
          break;
        case "name":
          this.editName.openTrigger_in = Date.now();
          this.editName.node = this._.cloneDeep(this.currentNode);
          break;
        default:
          break;
      }
    },
    clear() {
      this.CetTree_f.checkedNodes = [];
      this.CetTree_f.showCheckbox = false;
      this.deleteHandle = false;
    },
    CetTree_f_currentNode_out(val) {
      this.currentNode = val;
      this.$emit("currentNode_out", val);
    },
    CetTree_f_checkedNodes_out(val) {
      this.checkedNodes = val.map(item => item.id);
    },
    CetButton_addFavorites_statusTrigger_out(val) {
      this.addFolder.openTrigger_in = val;
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.clear();
    },
    deleteData() {
      let text = $T("是否删除所选项？");
      let obj = this.CetTree_f.checkedNodes.find(item => item.children?.length);
      if (obj) {
        text = $T("是否删除文件夹以及所有节点？");
      }
      this.$confirm(text, $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(async () => {
          const res = await customApi.deleteFavorite(this.checkedNodes);
          if (res.code !== 0) return;
          this.$message.success($T("删除成功"));
          let check = this.checkedNodes.find(i => i === this.currentNode.id);
          if (check) {
            this.CetTree_f.selectNode = {};
            this.CetTree_f_currentNode_out({});
            this.currentNode = null;
          }
          this.checkedNodes = [];
          this.clear();
          this.$emit("updateFavorites");
        })
        .catch(() => {
          this.$message.info($T("已取消"));
        });
    },
    CetButton_delete_statusTrigger_out(val) {
      this.deleteData();
    },
    addFolder_saveData_out(val) {
      this.$emit("updateFavorites");
    },
    editName_saveData_out(val) {
      this.$emit("updateFavorites");
    }
  }
};
</script>
<style lang="scss" scoped>
.fcSta3 {
  @include font_color(Sta3);
}
.more-btn {
  width: 84px;
}
.footer {
  height: 32px;
}
</style>
