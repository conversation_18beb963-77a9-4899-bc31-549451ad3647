<template>
  <div class="flex flex-col treeBox">
    <el-radio-group
      v-model="tabValue"
      @change="treeRadioChange"
      class="fullwidth mb-J0 treeRadio"
    >
      <el-radio-button label="virtual" class="w50">
        {{ $T("虚拟表录入") }}
      </el-radio-button>
      <el-radio-button label="physical" class="w50">
        {{ $T("物理表补录") }}
      </el-radio-button>
    </el-radio-group>
    <CetGiantTree
      class="flex-auto CetGiantTree mt-J2"
      v-bind="CetGiantTree_1"
      v-on="CetGiantTree_1.event"
    ></CetGiantTree>
  </div>
</template>

<script>
//项目级逻辑组件
import customApi from "@/api/custom";
export default {
  props: {
    refreshTrigger: {
      type: Number
    }
  },

  computed: {
    projectTenantId() {
      return this.$store.state.userInfo.tenantId;
    }
  },

  watch: {
    refreshTrigger() {
      this.CetGiantTree_1.inputData_in = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.getTreeData();
    },
    tabValue: {
      handler: function (val) {
        this.$emit("communicate_out", val);
      },
      immediate: true
    }
  },
  data() {
    return {
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "id"
            },
            key: {
              name: "text"
            }
          }
        },
        event: {
          currentNode_out: this.CetTree_1_currentNode_out //选中单行输出
        }
      },
      tabValue: "virtual"
    };
  },
  methods: {
    getTreeData() {
      let queryData = {
        loadDevice: true,
        nodeId: 0,
        nodeType: 0,
        async: false,
        // tenantId: this.projectTenantId,
        tenantId: 7,
        connected: this.tabValue === "virtual" ? false : true
      };
      customApi.pecCoreMeterTreeWithConnection(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_1.inputData_in = this._.cloneDeep(data);
          this.$emit("project_node", data);
        }
      });
    },
    treeRadioChange() {
      this.$emit("currentNode_out", {});
      this.CetGiantTree_1.inputData_in = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.getTreeData();
    },
    CetTree_1_currentNode_out(val) {
      this.$emit("currentNode_out", val);
    },
    getSelectNode(val) {
      this.CetGiantTree_1.selectNode = this._.cloneDeep(val);
    },
    getTabValue(val) {
      this.tabValue = this._.cloneDeep(val);
      this.getTreeData();
    }
  },
  activated() {
    this.getTreeData();
  }
};
</script>

<style lang="scss" scoped>
.treeBox {
  height: calc(100% - 40px);
  .treeRadio {
    :deep(.el-radio-button__inner) {
      width: 100%;
    }
  }
  .w50 {
    width: 50%;
  }
}
</style>
