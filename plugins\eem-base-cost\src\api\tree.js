import fetch from "eem-base/utils/fetch";
import { http, httping } from "@omega/http";

// 查询维度节点树列表（包含id为-1的固定管理层级）
export function getAttributeDimensionTreeNodeConfig(data, params) {
  return fetch({
    url: `/eembasecost/v1/attribute-dimension/tree/node-config`,
    method: "POST",
    data,
    params
  });
}

// 成本配置-多维度成本分析节点树
export function queryCostcheckplanMultitree(data) {
  return fetch({
    url: `/eembasecost/v1/cost-check-plan/multi-tree`,
    method: "POST",
    data
  });
}

/**
 * 查询根节点
 */
export function rootNode() {
  return httping({
    url: `/eembasecost/v1/common/node/root-node`,
    method: "GET"
  });
}
