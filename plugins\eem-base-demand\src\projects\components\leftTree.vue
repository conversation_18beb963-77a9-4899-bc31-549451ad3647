<template>
  <div class="h-full flex flex-col">
    <div class="flex mb-J1">
      <customElSelect
        class="device-Select"
        v-model="ElSelect_2.value"
        v-bind="ElSelect_2"
        @change="handleChangeProject"
        :prefix_in="$T('所属项目')"
      >
        <ElOption
          v-for="item in ElOption_2.options_in"
          :key="item[ElOption_2.key]"
          :label="item[ElOption_2.label]"
          :value="item[ElOption_2.value]"
          :disabled="item[ElOption_2.disabled]"
        ></ElOption>
      </customElSelect>
    </div>
    <div class="flex mb-J1">
      <customElSelect
        class="device-Select"
        :value="ElSelect_1.value"
        v-bind="ElSelect_1"
        @change="handleChangeCalculationType"
        :prefix_in="$T('计费方式')"
      >
        <ElOption
          v-for="item in ElOption_1.options_in"
          :key="item[ElOption_1.key]"
          :label="item[ElOption_1.label]"
          :value="item[ElOption_1.value]"
          :disabled="item[ElOption_1.disabled]"
        ></ElOption>
      </customElSelect>
    </div>
    <div class="flex-auto">
      <CetTree
        :selectNode.sync="CetTree_1.selectNode"
        :checkedNodes.sync="CetTree_1.checkedNodes"
        v-bind="CetTree_1"
        v-on="CetTree_1.event"
        @currentNode_out="CetTree_1_currentNode_out"
      ></CetTree>
    </div>
    <slot name="footerBtn"></slot>
  </div>
</template>

<script>
import customApi from "@/api/custom";
import { mapActions } from "vuex";
export default {
  name: "leftTree",
  components: {},
  props: {
    //路由跳转传递的选中节点
    transferCurrentNode: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      currentNode: null,
      ElSelect_2: {
        value: "",
        multiple: false,
        collapseTags: true
      },
      ElOption_2: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_1: {
        value: [1, 2],
        style: {},
        multiple: true,
        collapseTags: true
      },
      ElOption_1: {
        options_in: [
          {
            id: 1,
            text: $T("容量计费")
          },
          {
            id: 2,
            text: $T("需量计费")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ treeId: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ treeId: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        defaultExpandAll: true,
        ShowRootNode: false,
        nodeKey: "treeId",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false
      },
      projectSelect: {}
    };
  },
  created: function () {
    this.init();
  },
  activated: function () {
    this.init();
  },
  methods: {
    ...mapActions(["getDemandNodes"]),
    handleChangeProject(id) {
      let modelLabel = "";
      if (id) {
        const findItem = this.ElOption_2.options_in.find(
          item => item.id === id
        );
        modelLabel = findItem?.modelLabel;
      }
      this.projectSelect = { id, modelLabel };
      this.getTree(true);
      this.$emit("changeProject", this.projectSelect);
    },
    handleChangeCalculationType(val) {
      if (val.length === 0) {
        // 控制最少选择一个计费方式
        this.$message({
          message: $T("至少选择一种计费方式"),
          type: "warning"
        });
      } else {
        this.ElSelect_1.value = val;
        this.sliceTree(this.copyTreeData, val);
      }
    },
    getTree: _.debounce(async function (selectFirstNodeFlag) {
      this.CetTree_1.inputData_in = [];
      this.copyTreeData = [];
      const { id, modelLabel } = this.projectSelect;
      if (!id || !modelLabel) {
        return;
      }
      const data = await this.getDemandNodes({ id, modelLabel });
      if (data) {
        this.copyTreeData = this._.cloneDeep(data);
        this.sliceTree(this.copyTreeData, this.ElSelect_1.value);
        if (selectFirstNodeFlag && this.copyTreeData.length > 0) {
          this.CetTree_1.selectNode = {
            id: this.copyTreeData[0].id,
            modelLabel: this.copyTreeData[0].modelLabel,
            treeId:
              this.copyTreeData[0].modelLabel + "_" + this.copyTreeData[0].id
          };
        }
      }
    }, 300),
    //筛选计费方式
    sliceTree(treeData, filterChargingWay) {
      if (filterChargingWay.length === 2) {
        //如果是全选（即容量、需量计费），null也要给筛选出来
        filterChargingWay = filterChargingWay.concat(null);
      }
      treeData = this._.cloneDeep(treeData);
      if (!treeData || treeData?.length == 0 || !this.ElSelect_1.value) {
        return;
      }
      if (this.ElSelect_1.value.length === 0) {
        this.CetTree_1.inputData_in = [];
      } else {
        const nodes = treeData.map(projectItem => {
          return Object.assign(projectItem, {
            children:
              projectItem?.children?.map(groupsItem => {
                return Object.assign(groupsItem, {
                  children:
                    groupsItem?.children?.filter(accoundItem => {
                      return filterChargingWay.includes(
                        accoundItem.chargingWay
                      );
                    }) || []
                });
              }) || []
          });
        });
        this.CetTree_1.inputData_in = this._.cloneDeep(nodes);
      }
    },
    async init() {
      await this.getProject();
      //从进线管理【需量数据维护】跳转过来
      const { id, modelLabel } = this.transferCurrentNode || {};
      if (id && modelLabel) {
        await this.getTree(false);
        this.CetTree_1.selectNode = {
          id,
          modelLabel,
          treeId: `${modelLabel}_${id}`
        };
        return;
      }
      this.getTree(true);
    },
    async getProject() {
      this.CetTree_1.inputData_in = [];
      this.copyTreeData = [];
      const response = await customApi.getCommonRootNode();
      if (response.code === 0 && response.data) {
        this.ElOption_2.options_in = response.data;
        this.ElSelect_2.value = response.data[0].id;
        this.handleChangeProject(this.ElSelect_2.value);
      }
    },

    CetTree_1_currentNode_out(val) {
      if (!val) {
        return;
      }
      this.currentNode = val;
      this.$emit("setCurrentNode", val);
    }
  }
};
</script>

<style lang="scss" scoped>
.device-Select {
  overflow: hidden;
}
</style>
