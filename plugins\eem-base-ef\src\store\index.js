import { modules, state, mutations, actions } from "eem-base/store/index.js";
import customApi from "@/api/custom";

export default {
  modules: { ...modules },
  state: {
    ...state,
    multidimensional: true,
    isSimpleAnalysis: true,
    numberOfNodesCompared: 4,
    relateNodeSize: 200,
    alarmConfigTypes: [1, 2]
  },
  mutations: {
    ...mutations,
    setMultidimensional(state, val) {
      state.multidimensional = val;
    },
    setIsSimpleAnalysis(state, val) {
      state.isSimpleAnalysis = val;
    },
    setNumberOfNodesCompared(state, val) {
      state.numberOfNodesCompared = val;
    },
    setRelateNodeSize(state, val) {
      state.relateNodeSize = val;
    },
    setAlarmConfigTypes(state, val) {
      state.alarmConfigTypes = val;
    }
  },
  actions: {
    ...actions,
    async getConfig({ commit }) {
      const res = await customApi.configProperties();
      const config = res.data || {};
      commit("setMultidimensional", !!config.isSupportMultiDimension);
      commit("setIsSimpleAnalysis", !!config.isSimpleAnalysis);
      commit("setNumberOfNodesCompared", config.nodeComparisonSize);
      commit("setRelateNodeSize", config.relateNodeSize);
      commit("setAlarmConfigTypes", config.eventcallpoliceConfigType);
    }
  }
};
