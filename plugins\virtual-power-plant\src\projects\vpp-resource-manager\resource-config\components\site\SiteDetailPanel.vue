﻿<template>
  <!-- 右侧滑出面板 -->
  <el-drawer
    :visible="localVisible"
    @close="handleClose"
    :title="$T('站点详情')"
    direction="rtl"
    size="960px"
    :show-close="false"
    :modal="false"
  >
    <!-- 内容区域 -->
    <div class="drawer-content">
      <!-- 加载-->
      <div v-if="!siteData" class="loading-container">
        <div class="loading-text text-T2">{{ $T("加载中..") }}</div>
      </div>

      <!-- 站点详情内容 -->
      <div v-if="siteData">
        <!-- 储能类详情信�?-->
        <div v-if="currentGroup === 'STORAGE'" class="info-section">
          <!-- 第一行：站点名称、站点类型、设备数�?-->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("站点名称") }}</span>
              <span class="value text-T1">
                {{ siteData.site_name || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("站点类型") }}</span>
              <span class="value text-T1">
                {{ getSiteTypeName(siteData.site_type) }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("设备数量") }}</span>
              <span class="value text-T1">
                {{ siteData.device_count || "10" }}
              </span>
            </div>
          </div>

          <!-- 第二行：经度、纬度、电压等�?-->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("经度") }}</span>
              <span class="value text-T1">
                {{ siteData.longitude || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("纬度") }}</span>
              <span class="value text-T1">
                {{ siteData.latitude || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("电压等级") }}</span>
              <span class="value text-T1">
                {{ getVoltageLevelName(siteData.voltage_level) }}
              </span>
            </div>
          </div>

          <!-- 第三行：并网电压、总装机容量、总储电量 -->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("并网电压") }} (kV)</span>
              <span class="value text-T1">
                {{ siteData.grid_voltage || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("总装机容量") }} (kWh)</span>
              <span class="value text-T1">
                {{ siteData.total_capacity || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("总储电量") }} (kWh)</span>
              <span class="value text-T1">
                {{ siteData.total_storage || "-" }}
              </span>
            </div>
          </div>

          <!-- 第四行：投运时间、联系人、联系电�?-->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("投运时间") }}</span>
              <span class="value text-T1">
                {{ formatDate(siteData.operation_date) }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("联系人") }}</span>
              <span class="value text-T1">
                {{ siteData.contact_person || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("联系电话") }}</span>
              <span class="value text-T1">
                {{ siteData.phone_number || "-" }}
              </span>
            </div>
          </div>

          <!-- 第五行：关联站点 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("关联站点") }}</span>
              <span class="value text-T1">
                {{ siteData.related_room || "-" }}
              </span>
            </div>
          </div>

          <!-- 第六行：地址 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("地址") }}</span>
              <span class="value text-T1">
                {{ siteData.site_address || "-" }}
              </span>
            </div>
          </div>

          <!-- 第七行：图片 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("图片") }}</span>
              <div class="image-container">
                <fileImage
                  v-if="siteData.picturePath"
                  fit="contain"
                  style="height: 200px; width: 300px"
                  :source="siteData.picturePath"
                  :placeholderImg="
                    require('../../../../../assets/site-default.svg')
                  "
                  :onHandleImgSrc="
                    fileName =>
                      `/vpp/api/v1/resource-manager/base-config/images/download/${fileName}`
                  "
                  hideNotice
                />
                <div v-else class="no-image-placeholder">
                  <img
                    :src="require('../../../../../assets/site-default.svg')"
                    alt="默认站点图片"
                    style="height: 200px; width: 300px; object-fit: contain"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 新能源类详情信息 -->
        <div v-if="currentGroup === 'RENEWABLE'" class="info-section">
          <!-- 第一行：站点名称、站点类型、设备数�?-->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("站点名称") }}</span>
              <span class="value text-T1">
                {{ siteData.site_name || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("站点类型") }}</span>
              <span class="value text-T1">
                {{ getSiteTypeName(siteData.site_type) }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("设备数量") }}</span>
              <span class="value text-T1">
                {{ siteData.device_count || "10" }}
              </span>
            </div>
          </div>

          <!-- 第二行：经度、纬度、电压等�?-->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("经度") }}</span>
              <span class="value text-T1">
                {{ siteData.longitude || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("纬度") }}</span>
              <span class="value text-T1">
                {{ siteData.latitude || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("电压等级") }}</span>
              <span class="value text-T1">
                {{ getVoltageLevelName(siteData.voltage_level) }}
              </span>
            </div>
          </div>

          <!-- 第三行：发电模式、并网电压、总装机容�?-->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("发电模式") }}</span>
              <span class="value text-T1">
                {{ getGenerationModeName(siteData.generation_mode) }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("并网电压") }} (kV)</span>
              <span class="value text-T1">
                {{ siteData.grid_voltage || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("总装机容量") }} (kWh)</span>
              <span class="value text-T1">
                {{ siteData.total_capacity || "-" }}
              </span>
            </div>
          </div>

          <!-- 第四行：投运时间、联系人、联系电�?-->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("投运时间") }}</span>
              <span class="value text-T1">
                {{ formatDate(siteData.operation_date) }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("联系人") }}</span>
              <span class="value text-T1">
                {{ siteData.contact_person || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("联系人") }}</span>
              <span class="value text-T1">
                {{ siteData.phone_number || "-" }}
              </span>
            </div>
          </div>

          <!-- 第五行：关联站点 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("关联站点") }}</span>
              <span class="value text-T1">
                {{ siteData.related_room || "-" }}
              </span>
            </div>
          </div>

          <!-- 第六行：地址 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("地址") }}</span>
              <span class="value text-T1">
                {{ siteData.site_address || "-" }}
              </span>
            </div>
          </div>

          <!-- 第七行：图片 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("图片") }}</span>
              <div class="image-container">
                <fileImage
                  v-if="siteData.picturePath"
                  fit="contain"
                  style="height: 200px; width: 300px"
                  :source="siteData.picturePath"
                  :placeholderImg="
                    require('../../../../../assets/site-default.svg')
                  "
                  :onHandleImgSrc="
                    fileName =>
                      `/vpp/api/v1/resource-manager/base-config/images/download/${fileName}`
                  "
                  hideNotice
                />
                <div v-else class="no-image-placeholder">
                  <img
                    :src="require('../../../../../assets/site-default.svg')"
                    alt="默认站点图片"
                    style="height: 200px; width: 300px; object-fit: contain"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 其他类详情信�?-->
        <div v-if="currentGroup === 'OTHER'" class="info-section">
          <!-- 第一行：站点名称、站点类型、设备数�?-->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("站点名称") }}</span>
              <span class="value text-T1">
                {{ siteData.site_name || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("站点类型") }}</span>
              <span class="value text-T1">
                {{ getSiteTypeName(siteData.site_type) }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("设备数量") }}</span>
              <span class="value text-T1">
                {{ siteData.device_count || "10" }}
              </span>
            </div>
          </div>

          <!-- 第二行：经度、纬度、电压等�?-->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("经度") }}</span>
              <span class="value text-T1">
                {{ siteData.longitude || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("纬度") }}</span>
              <span class="value text-T1">
                {{ siteData.latitude || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("电压等级") }}</span>
              <span class="value text-T1">
                {{ getVoltageLevelName(siteData.voltage_level) }}
              </span>
            </div>
          </div>

          <!-- 第三行：投运时间、联系人、联系电�?-->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("投运时间") }}</span>
              <span class="value text-T1">
                {{ formatDate(siteData.operation_date) }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("联系人") }}</span>
              <span class="value text-T1">
                {{ siteData.contact_person || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("联系人") }}</span>
              <span class="value text-T1">
                {{ siteData.phone_number || "-" }}
              </span>
            </div>
          </div>

          <!-- 第四行：关联站点 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("关联站点") }}</span>
              <span class="value text-T1">
                {{ siteData.related_room || "-" }}
              </span>
            </div>
          </div>

          <!-- 第五行：地址 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("地址") }}</span>
              <span class="value text-T1">
                {{ siteData.site_address || "-" }}
              </span>
            </div>
          </div>

          <!-- 第六行：图片 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("图片") }}</span>
              <div class="image-container">
                <fileImage
                  v-if="siteData.picturePath"
                  fit="contain"
                  style="height: 200px; width: 300px"
                  :source="siteData.picturePath"
                  :placeholderImg="
                    require('../../../../../assets/site-default.svg')
                  "
                  :onHandleImgSrc="
                    fileName =>
                      `/vpp/api/v1/resource-manager/base-config/images/download/${fileName}`
                  "
                  hideNotice
                />
                <div v-else class="no-image-placeholder">
                  <img
                    :src="require('../../../../../assets/site-default.svg')"
                    alt="默认站点图片"
                    style="height: 200px; width: 300px; object-fit: contain"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { getEnumLabel } from "@/utils/enumManager";
import fileImage from "../common/FileImage.vue";

export default {
  name: "SiteDetailPanel",
  components: {
    fileImage
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    siteData: {
      type: Object,
      default: null
    }
  },
  computed: {
    // 本地可见状态，避免直接修改prop
    localVisible() {
      return this.visible;
    },

    // 当前站点类型分组
    currentGroup() {
      return this.siteData && this.siteData.site_type
        ? this.getSiteTypeGroup(this.siteData.site_type)
        : null;
    }
  },
  methods: {
    // 关闭面板
    handleClose() {
      this.$emit("close");
    },

    // 获取站点类型名称
    getSiteTypeName(siteType) {
      if (!siteType) return "-";
      return getEnumLabel("SITE_TYPE_CODES", siteType) || "-";
    },

    // 获取站点类型分组
    getSiteTypeGroup(siteType) {
      // 根据站点类型返回对应的分�?
      // 这里需要根据实际的站点类型分组逻辑来实�?
      const storageTypes = [2, 10]; // 储能类型：用户侧储能、分布式独立储能
      const renewableTypes = [8, 9]; // 可再生能源类型：分布式光伏、分布式风电

      if (storageTypes.includes(siteType)) {
        return "STORAGE";
      } else if (renewableTypes.includes(siteType)) {
        return "RENEWABLE";
      } else {
        return "OTHER";
      }
    },

    // 获取发电模式名称
    getGenerationModeName(mode) {
      const modeMap = {
        self_use: this.$T("自发自用"),
        full_grid: this.$T("全额上网"),
        surplus_grid: this.$T("余电上网")
      };
      return modeMap[mode] || "-";
    },

    // 格式化日�?
    formatDate(date) {
      if (!date) return "-";

      // 处理时间戳格�?
      if (typeof date === "number") {
        const dateObj = new Date(date);
        return dateObj.toLocaleDateString();
      }

      if (typeof date === "string") {
        return date.split("T")[0]; // 简单处理ISO日期格式
      }

      if (date instanceof Date) {
        return date.toLocaleDateString();
      }

      return "-";
    },

    // 获取电压等级名称
    getVoltageLevelName(voltageLevel) {
      if (!voltageLevel) return "-";
      return getEnumLabel("VOLTAGE_LEVEL", voltageLevel) || voltageLevel || "-";
    }
  }
};
</script>

<style scoped>
.site-detail-panel {
  width: 100%;
}

.drawer-header {
  padding: 0 var(--J3);
  height: 56px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--B2);
}

.title {
  font-size: var(--Aa);
  font-weight: 500;
}

.drawer-content {
  height: 100%;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-text {
  font-size: 14px;
}

.info-section {
  margin-bottom: 24px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  border-left: 4px solid var(--ZS);
  padding-left: 12px;
}

/* 基础网格 - 两列 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px 24px;
  margin-bottom: 16px;
}

/* 三列网格 */
.info-grid-3 {
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px 24px;
}

/* 单列网格 */
.info-grid-1 {
  grid-template-columns: 1fr;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label {
  font-size: 14px;
  font-weight: 500;
  color: var(--T2);
}

.value {
  font-size: 14px;
  word-break: break-all;
  color: var(--T1);
}

.image-container {
  display: flex;
  justify-content: flex-start;
  margin-top: 8px;
}

.site-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 6px;
  cursor: pointer;
  transition: transform 0.3s;
  border: 1px solid var(--BG3);
}

.site-image:hover {
  transform: scale(1.05);
}

.image-placeholder {
  width: 120px;
  height: 120px;
  border: 2px dashed var(--BG3);
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  background-color: var(--BG2);
}

.image-placeholder:hover {
  border-color: var(--ZS);
  background-color: var(--BG3);
}

.image-placeholder i {
  font-size: 32px;
  color: var(--T3);
  margin-bottom: 8px;
}

.placeholder-text {
  font-size: 12px;
  color: var(--T3);
}

/* 响应式设�?*/
@media (max-width: 992px) {
  .info-grid-3 {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .info-grid,
  .info-grid-3 {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
