import fetch from "eem-base/utils/fetch";
import { httping } from "@omega/http";
import { getCustomParameter } from "./common.js";
import { hideNoticeFetch } from "eem-base/utils/fetch";
const version = "v1";

// 获取拓扑图
export function getMeasureTopology(data) {
  return fetch({
    url: `/eem-service/${version}/topology/network`,
    method: "POST",
    data: data
  });
}
// 获取表格
export function getMeasureTable(data) {
  return fetch({
    url: `/eem-service/${version}/topology/network/form`,
    method: "POST",
    data
  });
}
// 导入
export function networkImportNodeAndConnection(formData, queryData) {
  return fetch({
    url: `/eem-service/${version}/topology/network/importNodeAndConnection/${queryData.projectId}`,
    method: "POST",
    data: formData
  });
}
//获取计量网络节点树
export function getNetWorkTree(data) {
  return fetch({
    url: `/eem-service/${version}/topology/network/tree`,
    method: "POST",
    data
  });
}
//获取计量网络图节点信息
export function getNetWorkStatistics(data) {
  return httping({
    url: `/eem-service/${version}/topology/node/status/statistics`,
    method: "POST",
    data
  });
}

// 获取项目能源类型
export function getProjectEnergy() {
  return fetch({
    url: `/eembasedatamaintain/project/projectEnergy`,
    method: "GET"
  });
}

// 查询导入进度
export function importobjectRatio(params, data) {
  return hideNoticeFetch({
    url: `/eembasedatamaintain/importobject/ratio`,
    method: "POST",
    params,
    data
  });
}

// 查询维度节点树列表（包含id为-1的固定管理层级）
export function getAttributeDimensionTreeNodeConfig(data, params) {
  return fetch({
    url: `/eembasedatamaintain/attribute-dimension/tree/node-config`,
    method: "POST",
    data,
    params
  });
}

export function getNodeTreeSimple(data, hideNotice, params) {
  return fetch({
    url: `/eembasedatamaintain/node/tree`,
    method: "POST",
    headers: { hideNotice: hideNotice },
    data,
    params: params
  });
}

// 查询维度节点树数据
export function getAttributeDimensionTreeNodetree(data) {
  return fetch({
    url: `eembasedatamaintain/attribute-dimension/tree/node-tree`,
    method: "POST",
    data
  });
}
