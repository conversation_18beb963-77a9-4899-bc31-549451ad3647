import { store, conf } from "@omega/app";
const beforeAppBoot = async function () {};
const afterAppLogin = async function () {};

const completeAppBoot = async function () {
  await store.dispatch("setEnumerations");
  store.dispatch("setUserInfo");
  const token = sessionStorage.getItem("omega_token");
  store.dispatch("setToken", token);
};

export { beforeAppBoot, afterAppLogin, completeAppBoot };
