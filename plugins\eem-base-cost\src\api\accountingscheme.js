import fetch from "eem-base/utils/fetch";
import { http, httping } from "@omega/http";

/**
 * 获取核算方案列表
 */
export function costCheckPlanList(data) {
  return fetch({
    url: `/eembasecost/v1/cost-check-plan/${data.rootNodeId}/${data.rootNodeLabel}`,
    method: "GET"
  });
}

/**
 * 删除核算方案
 */
export function costCheckPlanDelete(id) {
  return fetch({
    url: `/eembasecost/v1/fee/scheme/config/costCheckPlan/${id}`,
    method: "GET"
  });
}

/**
 * 保存核算方案
 */
export function costCheckPlanSave(data) {
  return fetch({
    url: `/eembasecost/v1/fee/scheme/config/costCheckPlan`,
    method: "POST",
    data
  });
}

/**
 * 获取核算方案的成本构成项
 */
export function getCostCheckPlanDetail(id) {
  return fetch({
    url: `/eembasecost/v1/fee/scheme/config/costCheckPlan/plan/${id}`,
    method: "GET"
  });
}

/**
 * 删除成本构成项
 */
export function costCheckPlanComponentDelete(data) {
  return fetch({
    url: `/eembasecost/v1/fee/scheme/config/costCheckPlan/component`,
    method: "POST",
    data
  });
}

/**
 * 获取费率列表
 */
export function getSchemeConfigFeeScheme(data) {
  return httping({
    url: `/eembasecost/v1/fee/scheme/config/feeScheme?energyType=${data.energyType}`,
    method: "POST",
    data
  });
}

// 成本配置-核算方案关联节点
export function queryCostcheckplanMultitreeConfig(data) {
  return fetch({
    url: `/eembasecost/v1/cost-check-plan/multi-tree/config`,
    method: "POST",
    data
  });
}

// 成本配置-核算方案保存关联节点
export function saveCostcheckplanCostCheckNodeConfig(data) {
  return fetch({
    url: `/eembasecost/v1/cost-check-plan/cost-check-node-config/save`,
    method: "POST",
    data
  });
}

// 查询维度节点树数据
export function getAttributeDimensionTreeNodetree(data) {
  return fetch({
    url: `/eembasecost/v1/attribute-dimension/tree/node-tree`,
    method: "POST",
    data
  });
}

// 获取管理层级树
export function getNodeTree(data) {
  return fetch({
    url: `/eembasecost/v1/common/node/tree`,
    method: "POST",
    data
  });
}
