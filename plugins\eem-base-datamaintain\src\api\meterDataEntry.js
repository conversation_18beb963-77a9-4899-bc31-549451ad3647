import fetch from "eem-base/utils/fetch";
const version = "v1";

// 根据表计查询表计测点数据
export function getPoints(deviceId) {
  return fetch({
    url: `/eembasedatamaintain/flink/datalog/points/${deviceId}`,
    method: "GET"
  });
}
// flink数据录入数据查询
export function getFlinkDatalogData(data) {
  return fetch({
    url: `/eembasedatamaintain/flink/datalog/query`,
    method: "POST",
    data
  });
}

// flink数据录入新增和修改接口
export function getFlinkEdit(data) {
  return fetch({
    url: `/eembasedatamaintain/flink/datalog/edit`,
    method: "POST",
    data
  });
}

// flink数据导出中查询可以导出的设备节点
export function getFlinkMaxNodes() {
  return fetch({
    url: `/eembasedatamaintain/flink/datalog/getMaxNodes`,
    method: "GET"
  });
}

// flink数据导出中查询最大可以导出的数据数量
export function getFlinkMaxExportCount() {
  return fetch({
    url: `/eembasedatamaintain/flink/datalog/getMaxExportCount`,
    method: "GET"
  });
}

//  获取设备的回路
export function getDeviceLogic(data) {
  return fetch({
    url: `/eembasedatamaintain/flink/datalog/getDeviceLogic`,
    method: "POST",
    data: data
  });
}

//  flink执行能耗重算
export function getEnergyReCalc(data) {
  return fetch({
    url: `/eem-service/${version}/system/data/energy/re-calc`,
    method: "POST",
    timeout: 600000,
    data: data
  });
}

//  获取当前项目重算状态
export function getEnergyReCalcState(projectId) {
  return fetch({
    url: `/eem-service/${version}/system/data/energy/re-calc/state/${projectId}`,
    method: "POST"
  });
}
//  过滤通讯或者未通讯的pecCore设备的节点树
export function pecCoreMeterTreeWithConnection(data) {
  return fetch({
    url: `/eembasedatamaintain/peccore/pecCoreMeterTreeWithConnection`,
    method: "POST",
    data
  });
}

// flink数据换表曲线数据查询
export function getFlinkDatalogTrend(data) {
  return fetch({
    url: `/eembasedatamaintain/flink/datalog/query/trend`,
    method: "POST",
    data
  });
}

// flink数据换表曲线数据删除处理
export function getFlinkDatalogDelete(data) {
  return fetch({
    url: `/eembasedatamaintain/flink/datalog/delete`,
    method: "POST",
    data
  });
}
