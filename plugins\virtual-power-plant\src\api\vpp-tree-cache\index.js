import { httping } from "@omega/http";

/**
 * 获取所有VPP树
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: Array<TreeNodeDTO>, // VPP树节点列表
 *   msg: string,
 *   total: number
 * }
 *
 * TreeNodeDTO结构：
 * {
 *   id: number,              // 节点ID
 *   parentId: number,        // 父节点ID
 *   name: string,            // 节点名称
 *   label: string,           // 节点类型标签(vpp/user/resource/site/device)
 *   isLeaf: boolean,         // 是否为叶子节点
 *   childrenCount: number,   // 子节点数量
 *   nodePath: string,        // 节点路径
 *   roomId: number,          // 站点对应房间id（站点节点特有）
 *   siteType: number,        // 站点类型（站点节点特有）
 *   resourceType: number,    // 资源类型（资源节点特有）
 *   province: number,        // 电厂所属省份（VPP节点特有）
 *   children: Array<TreeNodeDTO> // 子节点列表
 * }
 */
export function getAllVppTrees() {
  const request = httping({
    url: `/vpp/api/v1/resource-manager/tree/cache/all`,
    method: "GET"
  });
  return request;
}

/**
 * 清空所有VPP树缓存
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: boolean, // true表示清空成功
 *   msg: string,
 *   total: number
 * }
 */
export function clearAllVppTreeCache() {
  return httping({
    url: `/vpp/api/v1/resource-manager/tree/cache/all`,
    method: "DELETE"
  });
}

/**
 * 数据转换：将API返回的TreeNodeDTO转换为VppTree组件需要的格式
 * @param {Array<TreeNodeDTO>} apiTreeData - API返回的树节点数据
 * @returns {Array} 转换后的树形数据
 */
export function transformTreeData(apiTreeData) {
  if (!apiTreeData || !Array.isArray(apiTreeData)) {
    return [];
  }

  return apiTreeData.map(node => transformTreeNode(node));
}

/**
 * 转换单个树节点
 * @param {TreeNodeDTO} apiNode - API返回的树节点
 * @returns {Object} 转换后的树节点
 */
function transformTreeNode(apiNode) {
  const transformed = {
    tree_id: `${apiNode.label}_${apiNode.id}`, // 组合类型和ID作为tree_id
    pId: apiNode.parentId
      ? `${getParentLabel(apiNode)}_${apiNode.parentId}`
      : null,
    name: apiNode.name,
    type: apiNode.label, // vpp/user/resource/site/device
    open: false, // 默认不展开
    isParent: !apiNode.isLeaf,
    iconSkin: `${apiNode.label}-icon`, // 根据类型设置图标

    // 保留原始API数据
    originalId: apiNode.id,
    originalParentId: apiNode.parentId,
    childrenCount: apiNode.childrenCount,
    nodePath: apiNode.nodePath,

    // 新增字段支持
    roomId: apiNode.roomId || null, // 站点对应房间id
    siteType: apiNode.siteType || null, // 站点类型
    resourceType: apiNode.resourceType || null, // 资源类型
    province: apiNode.province || null // 电厂所属省份
  };

  // 如果有子节点，递归转换
  if (apiNode.children && apiNode.children.length > 0) {
    transformed.children = apiNode.children.map(child =>
      transformTreeNode(child)
    );
  }

  return transformed;
}

/**
 * 根据节点路径推断父节点类型
 * @param {TreeNodeDTO} node - 当前节点
 * @returns {string} 父节点类型
 */
function getParentLabel(node) {
  // 根据层级关系推断父节点类型
  switch (node.label) {
    case "user":
      return "vpp";
    case "resource":
      return "user";
    case "site":
      return "resource";
    case "device":
      return "site";
    default:
      return "vpp";
  }
}

/**
 * 扁平化树形数据为zTree格式
 * @param {Array} treeData - 层级树形数据
 * @returns {Array} 扁平化的树形数据
 */
export function flattenTreeData(treeData) {
  const result = [];

  function flatten(nodes, parentId = null) {
    if (!nodes || !Array.isArray(nodes)) return;

    nodes.forEach(node => {
      const flatNode = {
        tree_id: node.tree_id,
        pId: parentId,
        name: node.name,
        type: node.type,
        open: node.open || false,
        isParent: node.isParent || false,
        iconSkin: node.iconSkin,
        originalId: node.originalId,
        originalParentId: node.originalParentId,
        childrenCount: node.childrenCount,
        nodePath: node.nodePath,
        // 新增字段
        roomId: node.roomId,
        siteType: node.siteType,
        resourceType: node.resourceType,
        province: node.province
      };

      result.push(flatNode);

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        flatten(node.children, node.tree_id);
      }
    });
  }

  flatten(treeData);
  return result;
}

/**
 * 获取指定VPP的树结构
 * @param {number} vppId - VPP ID
 * @returns {Promise} 返回Promise对象
 */
export function getVppTree(vppId) {
  return httping({
    url: `/vpp/api/v1/resource-manager/tree/cache/${vppId}`,
    method: "GET"
  });
}

/**
 * 构建VPP树缓存
 * @param {number} vppId - VPP ID
 * @returns {Promise} 返回Promise对象
 */
export function buildVppTreeCache(vppId) {
  return httping({
    url: `/vpp/api/v1/resource-manager/tree/cache/build/${vppId}`,
    method: "POST"
  });
}

/**
 * 刷新VPP树缓存
 * @param {number} vppId - VPP ID
 * @returns {Promise} 返回Promise对象
 */
export function refreshVppTreeCache(vppId) {
  return httping({
    url: `/vpp/api/v1/resource-manager/tree/cache/refresh/${vppId}`,
    method: "POST"
  });
}

/**
 * 删除VPP树缓存
 * @param {number} vppId - VPP ID
 * @returns {Promise} 返回Promise对象
 */
export function deleteVppTreeCache(vppId) {
  return httping({
    url: `/vpp/api/v1/resource-manager/tree/cache/${vppId}`,
    method: "DELETE"
  });
}

/**
 * 检查VPP树缓存是否存在
 * @param {number} vppId - VPP ID
 * @returns {Promise} 返回Promise对象
 */
export function checkVppTreeCacheExists(vppId) {
  return httping({
    url: `/vpp/api/v1/resource-manager/tree/cache/${vppId}/exists`,
    method: "GET"
  });
}
