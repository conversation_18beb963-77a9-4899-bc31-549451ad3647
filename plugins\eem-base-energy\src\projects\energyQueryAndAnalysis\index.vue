<template>
  <div class="relative page">
    <CetAside class="cet-aside">
      <template #aside>
        <div class="h-full flex-col flex">
          <customElSelect
            :prefix_in="$T('所属项目')"
            v-model="ElSelect_project.value"
            v-bind="ElSelect_project"
            v-on="ElSelect_project.event"
            class="mb-J3"
            v-show="ElOption_project.options_in?.length > 1"
          >
            <ElOption
              v-for="item in ElOption_project.options_in"
              :key="item[ElOption_project.key]"
              :label="item[ElOption_project.label]"
              :value="item[ElOption_project.value]"
              :disabled="item[ElOption_project.disabled]"
            ></ElOption>
          </customElSelect>
          <customElSelect
            class="mb-J3"
            v-model="ElSelect_energyType.value"
            v-bind="ElSelect_energyType"
            v-on="ElSelect_energyType.event"
            :prefix_in="$T('能源类型')"
          >
            <ElOption
              v-for="item in ElOption_energyType.options_in"
              :key="item[ElOption_energyType.key]"
              :label="item[ElOption_energyType.label]"
              :value="item[ElOption_energyType.value]"
              :disabled="item[ElOption_energyType.disabled]"
            ></ElOption>
          </customElSelect>
          <customElSelect
            v-model="ElSelect_treeType.value"
            v-bind="ElSelect_treeType"
            v-on="ElSelect_treeType.event"
            :prefix_in="$T('节点树类型')"
            class="mb-J3"
            v-if="multidimensional"
            v-show="ElOption_treeType.options_in?.length > 1"
          >
            <ElOption
              v-for="item in ElOption_treeType.options_in"
              :key="item[ElOption_treeType.key]"
              :label="item[ElOption_treeType.label]"
              :value="item[ElOption_treeType.value]"
              :disabled="item[ElOption_treeType.disabled]"
            ></ElOption>
          </customElSelect>
          <customElSelect
            v-show="showEnergyDetailTab"
            v-model="ElSelect_viewMethod.value"
            v-bind="ElSelect_viewMethod"
            v-on="ElSelect_viewMethod.event"
            :prefix_in="$T('分析方式')"
            class="mb-J3"
            :tooltipContent="
              $T('表计数据：原始表计数据；能耗分析：原始表计数据+分摊数据')
            "
          >
            <el-option
              v-for="item in viewMethodOptions"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            />
          </customElSelect>
          <CetTree
            class="flex-auto cetTree"
            ref="cetTree"
            :selectNode.sync="CetTree_1.selectNode"
            :checkedNodes.sync="CetTree_1.checkedNodes"
            v-bind="CetTree_1"
            v-on="CetTree_1.event"
          >
            <div
              class="custom-tree-node el-tree-node__label"
              slot-scope="{ node }"
              :level="node.level"
              :class="{ 'focus-node': foucsNode?.id === node.id }"
            >
              <span
                :style="{
                  color: filNodeColor(node)
                }"
              >
                {{ node.label }}
              </span>
              <div class="icon" v-if="node?.data?.changeStatus">
                <el-tooltip effect="light" :content="effTimeFormat(node.data)">
                  <omega-icon symbolId="collect-lin" />
                </el-tooltip>
              </div>
            </div>
          </CetTree>
        </div>
      </template>
      <template #container>
        <div class="h-full overflow-auto flex-col flex">
          <el-tabs class="tabs mb-J3" v-model="tabsValue">
            <template v-for="(item, index) in ElOption_analyseType.options_in">
              <el-tab-pane
                :label="item.label"
                :name="item.value.toString()"
                :key="index"
                v-if="item.show()"
              ></el-tab-pane>
            </template>
          </el-tabs>
          <el-container class="flex-auto fullheight flex-col flex">
            <div class="flex-row flex justify-between mb-J3">
              <div class="flex flex-row items-center">
                <customElSelect
                  v-show="
                    ElSelect_analyseType.value == 2 &&
                    ![13, 18].includes(ElSelect_energyType.value)
                  "
                  v-model="ElSelect_timeScheme.value"
                  v-bind="ElSelect_timeScheme"
                  v-on="ElSelect_timeScheme.event"
                  class="mr-J3"
                  :prefix_in="$T('分时方案')"
                >
                  <ElOption
                    v-for="item in ElOption_timeScheme.options_in"
                    :key="item[ElOption_timeScheme.key]"
                    :label="item[ElOption_timeScheme.label]"
                    :value="item[ElOption_timeScheme.value]"
                    :disabled="item[ElOption_timeScheme.disabled]"
                  ></ElOption>
                </customElSelect>
                <customElSelect
                  v-model="ElSelect_dateType.value"
                  v-bind="ElSelect_dateType"
                  v-on="ElSelect_dateType.event"
                  class="mr-J3"
                  :prefix_in="$T('分析周期')"
                >
                  <ElOption
                    v-for="item in ElOption_dateType.options_in"
                    :key="item[ElOption_dateType.key]"
                    :label="item[ElOption_dateType.label]"
                    :value="item[ElOption_dateType.value]"
                    :disabled="item[ElOption_dateType.disabled]"
                  ></ElOption>
                </customElSelect>
                <template v-if="ElSelect_dateType.value === -1">
                  <customElSelect
                    v-model="ElSelect_statisticsType.value"
                    v-bind="ElSelect_statisticsType"
                    v-on="ElSelect_statisticsType.event"
                    :prefix_in="$T('统计周期')"
                    class="mr-J3"
                  >
                    <ElOption
                      v-for="item in ElOption_statisticsType.options_in"
                      :key="item[ElOption_statisticsType.key]"
                      :label="item[ElOption_statisticsType.label]"
                      :value="item[ElOption_statisticsType.value]"
                      :disabled="item[ElOption_statisticsType.disabled]"
                    ></ElOption>
                  </customElSelect>
                  <DatePointPicker
                    style="width: 300px"
                    v-bind="datePointPicker"
                    v-on="datePointPicker.event"
                    :prefix_in="$T('批量筛选时间')"
                    :value.sync="datePointPicker.value"
                  />
                </template>
                <template v-else>
                  <CetButton
                    v-bind="CetButton_prv"
                    v-on="CetButton_prv.event"
                  ></CetButton>
                  <div class="datePicker ml-J0 mr-J0">
                    <el-date-picker
                      v-model="elDate.value"
                      v-bind="elDate"
                      v-on="elDate.event"
                    ></el-date-picker>
                  </div>
                  <CetButton
                    v-bind="CetButton_next"
                    v-on="CetButton_next.event"
                  ></CetButton>
                </template>
              </div>
              <div class="flex flex-row item-center">
                <CetButton
                  v-if="false"
                  class="mr-J3"
                  v-bind="CetButton_trendcurve"
                  v-on="CetButton_trendcurve.event"
                ></CetButton>
                <el-tooltip
                  effect="light"
                  :disabled="!importing"
                  :content="importingStr"
                  placement="top"
                >
                  <CetButton
                    :disable_in="exportDisable"
                    v-bind="CetButton_1"
                    v-on="CetButton_1.event"
                    :title="exportTitle"
                  ></CetButton>
                </el-tooltip>
              </div>
            </div>
            <div
              class="flex-auto flex-col flex border-solid border-[1px] border-B1 p-J3"
            >
              <div class="mb-J3 flex flex-row items-center justify-between">
                <div class="text-H3 font-bold">
                  {{ Title }}
                </div>
                <div class="chartHandle">
                  <template
                    v-if="
                      ![2, 3, 4].includes(ElSelect_analyseType.value) &&
                      ![13, 18].includes(ElSelect_energyType.value) &&
                      !showCustomCycleEnergy
                    "
                  >
                    <ElCheckbox
                      class="mr0"
                      v-model="ElCheckbox_tb.value"
                      v-bind="ElCheckbox_tb"
                      v-on="ElCheckbox_tb.event"
                    >
                      {{ ElCheckbox_tb.text }}
                    </ElCheckbox>
                    <ElCheckbox
                      class="ml-J3 mr0"
                      v-model="ElCheckbox_hb.value"
                      v-bind="ElCheckbox_hb"
                      v-on="ElCheckbox_hb.event"
                    >
                      {{ ElCheckbox_hb.text }}
                    </ElCheckbox>
                  </template>
                  <el-checkbox
                    class="ml-J3 mr0"
                    v-model="maximum"
                    @change="extremValueHandler"
                    v-show="showChartHandle"
                  >
                    {{ $T("最值") }}
                  </el-checkbox>
                  <el-checkbox
                    class="ml-J3 mr0"
                    v-model="average"
                    @change="averageHandler"
                    v-show="showChartHandle"
                  >
                    {{ $T("平均值") }}
                  </el-checkbox>

                  <el-radio-group
                    v-model="chartType"
                    class="ml-J3"
                    v-if="
                      [1, 3].includes(ElSelect_analyseType.value) ||
                      showCustomCycleEnergy
                    "
                    @change="chartTypeChange"
                  >
                    <el-radio-button :label="1">
                      <omega-icon class="cetIcon" symbolId="bar" />
                    </el-radio-button>
                    <el-radio-button :label="2">
                      <omega-icon class="cetIcon" symbolId="line" />
                    </el-radio-button>
                  </el-radio-group>
                  <el-radio-group
                    v-model="timeShareChartType"
                    class="ml-J3"
                    v-if="ElSelect_analyseType.value === 2"
                    @change="timeSharechartTypeChange"
                  >
                    <el-radio-button :label="1">
                      <omega-icon class="cetIcon" symbolId="bar" />
                    </el-radio-button>
                    <el-radio-button :label="2">
                      <omega-icon class="cetIcon" symbolId="line" />
                    </el-radio-button>
                  </el-radio-group>
                </div>
              </div>
              <div class="relative flex-auto">
                <legendInteraction
                  v-if="ElSelect_analyseType.value === 3"
                  class="legend-model"
                  @changeModel="handlerChangeModel"
                />
                <CustomCycleEnergy
                  ref="customCycleEnergy"
                  v-if="showCustomCycleEnergy"
                  :aggregationCycle="ElSelect_statisticsType.value"
                  :energyTypeObj="energyTypeObj"
                  :logTimes="logTimes"
                  :currentNode="currentNode"
                  :maximum="maximum"
                  :averageValue="average"
                  :chartType="chartType"
                  :energyDataType="ElSelect_viewMethod.value"
                />
                <CetChart
                  ref="CetChart"
                  v-bind="CetChart_1"
                  @legendselectchanged="handlerLegendClick"
                  v-else
                ></CetChart>
              </div>
            </div>
            <div style="height: 250px" class="proportion" v-show="showFooter">
              <div class="flex-row flex fullfilled">
                <div
                  class="flex-auto fullheight flex-col flex box-border border-solid border-[1px] border-B1 p-J3"
                >
                  <div class="mb-J3 text-H3 font-bold">
                    {{ comparison1.title }}
                  </div>
                  <div
                    :class="{
                      'flex-auto': true,
                      'proportion-item': true,
                      comparison1: comparison1.type == 1,
                      'comparison1-bg-light':
                        comparison2.type == 1 && themeLight,
                      comparison2: comparison1.type == 2,
                      comparison2_1: comparison1.type == 2
                    }"
                  >
                    <el-tooltip :content="comparison1.ratio">
                      <div
                        :class="{
                          ratio: true,
                          'text-ellipsis': true,
                          up: comparison1.ratioType === true,
                          down: comparison1.ratioType === false
                        }"
                      >
                        <span class="arrow"></span>
                        {{ comparison1.ratio }}
                      </div>
                    </el-tooltip>
                    <div class="valueBox">
                      <el-tooltip
                        :content="comparison1.value"
                        effect="light"
                        placement="top"
                      >
                        <div>
                          <span class="value text-ellipsis">
                            {{ cudate }}：
                          </span>
                          <span class="value text-ellipsis">
                            {{ comparison1.value }}
                          </span>
                        </div>
                      </el-tooltip>
                      <el-tooltip
                        :content="comparison1.oldValue"
                        effect="light"
                        placement="top"
                      >
                        <div>
                          <span class="value text-ellipsis">
                            {{ tbdate }}：
                          </span>
                          <span class="value text-ellipsis">
                            {{ comparison1.oldValue }}
                          </span>
                        </div>
                      </el-tooltip>
                      <div class="text">{{ $T("同期对比") }}</div>
                    </div>
                  </div>
                </div>
                <div
                  class="flex-auto fullheight flex-col flex ml-J3 mr-J3 box-border border-solid border-[1px] border-B1 p-J3"
                >
                  <div class="mb-J3 text-H3 font-bold">
                    {{ comparison2.title }}
                  </div>
                  <div
                    class="flex-auto proportion-item"
                    v-if="
                      ElSelect_analyseType.value === 1 &&
                      ElSelect_dateType.value === 17
                    "
                  >
                    <div class="no-number">
                      <span>{{ $T("暂无环比数据") }}</span>
                    </div>
                  </div>
                  <div
                    :class="{
                      'flex-auto': true,
                      'proportion-item': true,
                      comparison1: comparison2.type == 1,
                      'comparison1-bg-light':
                        comparison2.type == 1 && themeLight,
                      comparison2: comparison2.type == 2,
                      comparison2_2: comparison2.type == 2
                    }"
                    v-else
                  >
                    <el-tooltip
                      :content="comparison2.ratio"
                      effect="light"
                      placement="top"
                    >
                      <div
                        :class="{
                          ratio: true,
                          'text-ellipsis': true,
                          up: comparison2.ratioType === true,
                          down: comparison2.ratioType === false
                        }"
                      >
                        <span class="arrow"></span>
                        {{ comparison2.ratio }}
                      </div>
                    </el-tooltip>
                    <div class="valueBox">
                      <el-tooltip
                        :content="comparison2.value"
                        effect="light"
                        placement="top"
                      >
                        <div>
                          <span class="value text-ellipsis">
                            {{ cudate }}：
                          </span>
                          <span class="value text-ellipsis">
                            {{ comparison2.value }}
                          </span>
                        </div>
                      </el-tooltip>
                      <el-tooltip
                        :content="comparison2.oldValue"
                        effect="light"
                        placement="top"
                      >
                        <div>
                          <span class="value text-ellipsis">
                            {{ hbdate }}：
                          </span>
                          <span class="value text-ellipsis">
                            {{ comparison2.oldValue }}
                          </span>
                        </div>
                      </el-tooltip>
                      <div class="text">{{ $T("与上期对比") }}</div>
                    </div>
                  </div>
                </div>
                <div
                  class="flex-auto fullheight flex-col flex box-border border-solid border-[1px] border-B1 p-J3"
                >
                  <div class="mb-J3 text-H3 font-bold">
                    {{ CetChartTitle }}
                  </div>
                  <div
                    class="flex-auto proportion-item"
                    style="overflow: hidden"
                  >
                    <!-- 同环比最后一层级节点没有排名 -->
                    <div
                      class="itemBox-empty"
                      v-if="
                        ElSelect_analyseType.value === 1 &&
                        currentNode &&
                        (!currentNode.children ||
                          currentNode.children.length === 0)
                      "
                    >
                      {{ $T("无TOP排名") }}
                    </div>
                    <div
                      class="itemBox-empty"
                      v-else-if="
                        CetChart_2_legend.length === 0 &&
                        currentNode &&
                        currentNode.children &&
                        currentNode.children.length > 0
                      "
                    >
                      {{ $T("暂无数据") }}
                    </div>
                    <template v-else>
                      <CetChart
                        v-bind="CetChart_2"
                        @click="CetChartClick"
                        @legendselectchanged="CetChartClick"
                      ></CetChart>
                      <div
                        :class="{
                          CetChart_2_legend1: comparison2.type == 1,
                          CetChart_2_legend2: comparison2.type == 2
                        }"
                      >
                        <div
                          v-for="(item, index) in CetChart_2_legend"
                          :key="index"
                          @click="CetChartClick({ name: item.name })"
                          class="text-ellipsis"
                        >
                          <el-tooltip
                            :content="item.value"
                            effect="light"
                            placement="top"
                          >
                            <div class="text-ellipsis">
                              {{ item.value }}
                            </div>
                          </el-tooltip>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </el-container>
        </div>
      </template>
    </CetAside>
    <BatchExport v-bind="batchExport" />
  </div>
</template>

<script>
import commonApi from "@/api/custom.js";
import common from "eem-base/utils/common.js";
import * as echarts from "echarts";
import DatePointPicker from "./customCycleEnergy/datePointPicker.vue";
import CustomCycleEnergy from "./customCycleEnergy/index.vue";
import BatchExport from "./batchExport";
import legendInteraction from "eem-base/components/legendInteraction.vue";
import omegaI18n from "@omega/i18n";
import omegaTheme from "@omega/theme";

export default {
  name: "energyQueryAndAnalysis",
  components: {
    DatePointPicker,
    CustomCycleEnergy,
    BatchExport,
    legendInteraction
  },
  data(vm) {
    const language = omegaI18n.locale === "en";
    return {
      logTimes: [],
      chartType: 1,
      timeShareChartType: 2,
      tabsValue: "1",
      maximum: true,
      average: false,
      ajaxFlag: false,
      // 对比图
      comparison1: {
        title: $T("同比"),
        type: 1,
        ratio: "--",
        ratioType: "--",
        value: "--",
        oldValue: "--"
      },
      comparison2: {
        title: $T("环比"),
        type: 1,
        ratio: "--",
        ratioType: "--",
        value: "--",
        oldValue: "--"
      },
      cudate: "",
      tbdate: "",
      hbdate: "",
      elDate: {
        value: vm.$moment().startOf("month").valueOf(),
        "value-format": "timestamp",
        type: "month",
        placeholder: $T("请选择"),
        size: "small",
        clearable: false,
        style: {
          width: "150px"
        },
        pickerOptions: {
          disabledDate(time) {
            if (vm.ElSelect_dateType.value) {
              if (vm.ElSelect_dateType.value === 12) {
                return (
                  time.getTime() >
                  vm.$moment(vm.startDayTime).add(1, "d").valueOf()
                );
              } else if (vm.ElSelect_dateType.value === 14) {
                return (
                  time.getTime() >
                  vm.$moment(vm.startMonthTime).add(1, "M").valueOf()
                );
              } else if (vm.ElSelect_dateType.value === 17) {
                return (
                  time.getTime() >
                  vm.$moment(vm.startYearTime).add(1, "y").valueOf()
                );
              }
            }
            return time.getTime() > vm.$moment().valueOf();
          },
          // 添加回到今天的快捷键
          shortcuts: [
            {
              text: this.$T("当月"),
              onClick(picker) {
                picker.$emit("pick", new Date());
              }
            }
          ]
        },
        event: {
          change: this.elDate_change
        }
      },
      currentNode: null,
      top5Data: [],
      // treeType组件
      ElSelect_treeType: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_treeType_change_out
        }
      },
      ElSelect_viewMethod: {
        value: 4,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_viewMethod_change_out
        }
      },
      viewMethodOptions: [
        { label: $T("能耗分析"), id: 4 },
        { label: $T("表计数据"), id: 1 }
      ],
      // treeType组件
      ElOption_treeType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        defaultExpandedKeys: [],
        showCheckbox: false,
        checkStrictly: true,
        expandWhenChecked: false,
        event: {
          currentNode_out: this._.debounce(this.CetTree_1_currentNode_out, 300),
          check: this._.debounce(this.CetTree_1_checkedNodes_out, 300)
        }
      },
      CetChart_1: {
        //组件输入项
        inputData_in: null,
        options: {}
      },
      CetChart_2: {
        //组件输入项
        inputData_in: null,
        options: {}
      },
      CetChart_2_legend: [],
      CetButton_1: {
        visible_in: true,
        // disable_in: false,
        title: $T("导出"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_trendcurve: {
        visible_in: true,
        disable_in: false,
        title: $T("趋势分析"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_trendcurve_statusTrigger_out
        }
      },
      ElSelect_dateType: {
        value: 14,
        size: "small",
        style: {
          width: language ? "220px" : "180px"
        },
        event: {
          change: this.ElSelect_dateType_change_out
        }
      },
      dateTypeOptions: [
        {
          id: 17,
          text: $T("年")
        },
        {
          id: 14,
          text: $T("月")
        },
        {
          id: 12,
          text: $T("日")
        }
      ],
      ElOption_dateType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      ElCheckbox_hb: {
        value: false,
        text: $T("环比"),
        disabled: false,
        event: {
          change: this.ElCheckbox_hb_change_out
        }
      },
      // tb组件
      ElCheckbox_tb: {
        value: false,
        text: $T("同比"),
        disabled: false,
        event: {
          change: this.ElCheckbox_tb_change_out
        }
      },
      // timeScheme组件
      ElSelect_timeScheme: {
        value: "",
        style: {
          width: language ? "220px" : "200px"
        },
        event: {
          change: this.ElSelect_timeScheme_change_out
        }
      },
      // timeScheme组件
      ElOption_timeScheme: {
        options_in: [],
        key: "timesharefeerecord_id",
        value: "timesharefeerecord_id",
        label: "tsRecordName",
        disabled: "disabled"
      },
      // analyseType组件
      ElSelect_analyseType: {
        value: 1,
        style: {
          width: language ? "280px" : "240px"
        },
        event: {
          change: this.ElSelect_analyseType_change_out
        }
      },
      // analyseType组件
      ElOption_analyseType: {
        options_in: [
          {
            value: 1,
            label: $T("同环比"),
            show: () => {
              return true;
            }
          },
          {
            value: 2,
            label: $T("分时分析"),
            show: () => {
              return ![13, 18].includes(this.ElSelect_energyType.value);
            }
          },
          {
            value: 3,
            label: $T("节点对比"),
            show: () => {
              return ![13, 18].includes(this.ElSelect_energyType.value);
            }
          },
          {
            value: 4,
            label: $T("能耗详情"),
            show: () => {
              return (
                vm.showEnergyDetailTab && this.ElSelect_viewMethod.value !== 1
              );
            }
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      // energyType组件
      ElSelect_energyType: {
        value: "",
        style: {},
        event: {
          change: this.ElSelect_energyType_change_out
        }
      },
      // energyType组件
      ElOption_energyType: {
        options_in: [],
        key: "id",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      copySelectNodes: [], //拷贝节点选中列表，按照顺序排序
      ElSelect_statisticsType: {
        value: 12,
        size: "small",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_statisticsType_change_out
        }
      },
      ElOption_statisticsType: {
        options_in: [
          {
            id: 17,
            text: $T("年")
          },
          {
            id: 14,
            text: $T("月")
          },
          {
            id: 13,
            text: $T("周")
          },
          {
            id: 12,
            text: $T("日")
          },
          {
            id: 7,
            text: $T("小时")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      datePointPicker: {
        type: "dates",
        value: [],
        selectNumMax: 20,
        event: {
          change: this.datePointPicker_change
        }
      },
      batchExport: {
        openTrigger_in: Date.now(),
        closeTrigger_in: Date.now(),
        treeData_in: null,
        modelName: $T("能耗查询与分析"),
        defaultCheckedNodes: [],
        confirmFn: null
      },
      currentModel: 1,
      foucsNode: null,

      ElSelect_project: {
        value: 0,
        style: {},
        event: {
          change: this.ElSelect_project_change_out
        }
      },
      ElOption_project: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  computed: {
    energyQueryAndAnalysisTimeShareChartType() {
      return (
        this.$store.state.systemCfg.energyQueryAndAnalysisTimeShareChartType ||
        "line"
      );
    },
    isEN() {
      return omegaI18n.locale === "en";
    },
    Title() {
      const isEN = omegaI18n.locale === "en";
      if (isEN) {
        return (
          `${$T("用")}${this.energyTypeObj ? this.energyTypeObj.name : "--"} ` +
          $T("分析")
        );
      }
      return `用${this.energyTypeObj ? this.energyTypeObj.name : "--"}分析`;
    },
    CetChartTitle() {
      if (
        this.ElSelect_energyType.value === 13 ||
        this.ElSelect_energyType.value === 18 ||
        this.ElSelect_analyseType.value !== 2
      ) {
        const energyObj = this.ElOption_energyType.options_in.find(
          i => i.energytype === this.ElSelect_energyType.value
        );
        const isEN = omegaI18n.locale === "en";
        if (isEN) {
          return (
            `${$T("用")}${energyObj ? energyObj.name : "--"} ` +
            $T("排名") +
            " top5"
          );
        } else {
          return `用${energyObj ? energyObj.name : "--"}排名top5`;
        }
      } else {
        return $T("分时占比");
      }
    },
    customTimeComparisonRule() {
      return this.$store.state.systemCfg.customTimeComparisonRule.map(item => {
        Object.keys(item).forEach(key => {
          item[key] = $T(item[key]);
        });
        return item;
      });
    },
    startDayTime() {
      return this.$moment().startOf("day").valueOf();
    },
    startMonthTime() {
      return this.$moment().startOf("month").valueOf();
    },
    startYearTime() {
      return this.$moment().startOf("year").valueOf();
    },
    energyTypeObj() {
      return this.ElOption_energyType.options_in.find(
        item => item.energytype === this.ElSelect_energyType.value
      );
    },
    themeLight() {
      return omegaTheme.theme === "light";
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    showChartHandle() {
      return (
        ![13, 18].includes(this.ElSelect_energyType.value) &&
        this.ElSelect_analyseType.value === 1
      );
    },
    showFooter() {
      if (this.showCustomCycleEnergy) return false;
      if (this.ElSelect_analyseType.value === 3) return false;
      if (this.ElSelect_analyseType.value === 4) return false;
      return true;
    },
    showCustomCycleEnergy() {
      return (
        this.ElSelect_analyseType.value === 1 &&
        this.ElSelect_dateType.value === -1
      );
    },
    numberOfNodesCompared() {
      return this.$store.state.systemCfg.numberOfNodesCompared || 4;
    },
    // 用能分析页面中能耗切换以及总能耗详情tab页面是否进行展示
    showEnergyDetailTab() {
      return (
        this.$store.state.systemCfg.totalEnergyConsumptionDetail &&
        this.$checkPermission("energy_detail_query")
      );
    },
    importingType() {
      let type;
      switch (this.ElSelect_analyseType.value) {
        case 1:
          type = 7;
          break;
        case 2:
          type = 8;
          break;
        case 4:
          type = 9;
          break;
        default:
          break;
      }
      return type;
    },
    importing() {
      return this.$store.getters["importProgress/importing"](
        this.importingType
      );
    },
    exportDisable() {
      return (
        (this.ElSelect_analyseType.value === 2 &&
          !this.ElSelect_timeScheme.value) ||
        (this.ElSelect_dateType.value === -1 &&
          !this.datePointPicker.value.length) ||
        (this.ElSelect_analyseType.value === 3 &&
          !this.copySelectNodes.length) ||
        this.importing
      );
    },
    importingStr() {
      return this.$store.getters["importProgress/importingStr"](
        this.importingType
      );
    },
    exportTitle() {
      if (this.ElSelect_analyseType.value === 3) return $T("导出");
      return $T("批量导出");
    },
    multidimensional() {
      return this.$store.state.multidimensional;
    },
    rootNode() {
      const id = this.ElSelect_project.value;
      const list = this.ElOption_project.options_in;
      return list.find(item => item.id === id);
    }
  },
  watch: {
    "elDate.value": {
      handler: function () {
        this.handlerNextBtn();
      },
      deep: true
    },
    "ElSelect_analyseType.value": {
      handler: function (val) {
        this.initDateTypeOptions();
        this.ElSelect_analyseType_change_out();
      },
      immediate: true
    },
    tabsValue(val) {
      this.chartType = 1;
      this.ElSelect_analyseType.value = Number(val);
      if (Number(val) == 4) {
        this.ElSelect_viewMethod.value = 4;
      }
    }
  },
  methods: {
    //通过节点childSelectState属性，调整字体颜色
    filNodeColor(node) {
      const state = this._.get(node, "data.childSelectState", null);
      if (state !== 1) {
        return "#989898";
      }
      return;
    },
    handlerNextBtn() {
      let time, dateStr, maxTime;
      switch (this.ElSelect_dateType.value) {
        case 17:
          time = this.startYearTime;
          dateStr = "year";
          maxTime = this.$moment(time).add(1, "y").startOf(dateStr).valueOf();
          break;
        case 14:
          time = this.startMonthTime;
          dateStr = "month";
          maxTime = this.$moment(time).add(1, "M").startOf(dateStr).valueOf();
          break;
        case 12:
          time = this.startDayTime;
          dateStr = "date";
          maxTime = this.$moment(time).add(1, "d").startOf(dateStr).valueOf();
          break;
        default:
          break;
      }
      if (
        this.$moment(this.elDate.value).startOf(dateStr).valueOf() >= maxTime
      ) {
        this.CetButton_next.disable_in = true;
      } else {
        this.CetButton_next.disable_in = false;
      }
    },
    ElSelect_timeScheme_change_out(val) {
      this.getData();
    },
    ElSelect_analyseType_change_out(val) {
      this.CetTree_1.showCheckbox = this.ElSelect_analyseType.value === 3;
      // 节点对比单点处理
      if (
        ![13, 18].includes(this.ElSelect_energyType.value) &&
        this.ElSelect_analyseType.value === 3
      ) {
        this.currentModel = 1;
        this.$refs.cetTree.$refs.tree.setCheckedKeys([
          this.currentNode?.tree_id
        ]);
        this.copySelectNodes = [this.currentNode];
        this.getChartData3();
      } else if (
        ![13, 18].includes(this.ElSelect_energyType.value) &&
        this.ElSelect_analyseType.value === 2
      ) {
        this.getTimeShareScheme();
      } else {
        this.getData();
      }
    },
    // 获取节点树类型下拉框
    async queryTreeType() {
      if (!this.rootNode) {
        return;
      }

      const queryData = {
        status: true,
        rootNode: {
          id: this.rootNode.id,
          modelLabel: this.rootNode.modelLabel
        }
      };
      const res = await commonApi.getAttributeDimensionTreeNodeConfig(
        queryData
      );
      this.ElOption_treeType.options_in = (res?.data || []).map(i => ({
        id: i.id,
        name: i.name
      }));
      const flag = this.ElOption_treeType.options_in.find(i => i.id === -1);
      this.ElSelect_treeType.value = flag
        ? -1
        : this.ElOption_treeType.options_in?.[0]?.id ?? null;
      this.ElSelect_treeType_change_out(this.ElSelect_treeType.value);
    },
    ElSelect_viewMethod_change_out(val) {
      this.ElSelect_treeType_change_out(this.ElSelect_treeType.value);
      if (this.tabsValue === "4" && val === 1) {
        this.tabsValue = "1";
      }
    },
    async ElSelect_project_change_out() {
      this.queryTreeType();
    },
    async ElSelect_treeType_change_out(val) {
      if (this._.isNil(val)) {
        this.CetTree_1.inputData_in = [];
        return;
      }
      val === -1 ? await this.getTreeData1() : await this.getTreeData2();
    },
    async ElSelect_energyType_change_out(val) {
      this.CetTree_1.checkedNodes = [];
      this.currentNode = null;
      this.reset();
      this.ajaxFlag = false;
      await this.ElSelect_treeType_change_out(this.ElSelect_treeType.value);
      this.nextToDo2();
    },
    nextToDo2() {
      this.$nextTick(() => {
        this.ajaxFlag = true;
      });
      if ([13, 18].includes(this.ElSelect_energyType.value)) {
        this.CetTree_1.showCheckbox = false;
        this.tabsValue = "1";
      } else if (this.ElSelect_analyseType.value === 3) {
        this.CetTree_1.showCheckbox = true;
        this.CetTree_1.checkedNodes = [this.CetTree_1.selectNode];
      }
    },
    ElCheckbox_hb_change_out(val) {
      this.getTbhbData();
    },
    ElCheckbox_tb_change_out(val) {
      this.getTbhbData();
    },
    CetButton_1_statusTrigger_out(val) {
      if (this.showCustomCycleEnergy) {
        this.customCycleEnergyExport();
        return;
      }
      let queryData = {
        cycle: this.ElSelect_dateType.value,
        endTime: this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).endTime,
        startTime: this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).startTime,
        dimConfigId: this.ElSelect_treeType.value,
        rootNode: {
          id: this.rootNode.id,
          modelLabel: this.rootNode.modelLabel
        }
      };

      const timeStr = `${this.$moment(queryData.startTime).format(
        "YYYY-MM-DD"
      )}-${this.$moment(queryData.endTime).format("YYYY-MM-DD")}`;

      switch (this.ElSelect_analyseType.value) {
        case 1:
          this.batchExport.modelName = `${$T("能耗查询与分析")}-${$T(
            "同环比"
          )}-${timeStr}`;
          break;
        case 2:
          this.batchExport.modelName = `${$T("能耗查询与分析")}-${$T(
            "分时分析"
          )}-${timeStr}`;
          break;
        case 3:
          this.batchExport.modelName = `${$T("能耗查询与分析")}-${$T(
            "节点对比"
          )}-${timeStr}`;
          break;
        case 4:
          this.batchExport.modelName = `${$T("能耗查询与分析")}-${$T(
            "能耗详情"
          )}-${timeStr}`;
          break;

        default:
          break;
      }

      // 综合能耗和CO2单独处理
      if (
        this.ElSelect_energyType.value === 13 ||
        this.ElSelect_energyType.value === 18
      ) {
        queryData.aggregationCycle = this.ElSelect_dateType.value;
        queryData.node = {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel
        };
        queryData.energyDataType = this.ElSelect_viewMethod.value;
        queryData.targetEnergyType = this.ElSelect_energyType.value;
        delete queryData.cycle;
        this.batchExport.treeData_in = this._.cloneDeep(
          this.CetTree_1.inputData_in
        );
        this.batchExport.defaultCheckedNodes = [
          this._.cloneDeep(this.currentNode)
        ];
        this.batchExport.confirmFn = async params => {
          const exportData = {
            ...queryData,
            ...params
          };

          const res = await commonApi.energyConsumptionStandardExportBatch(
            exportData
          );
          if (res.code !== 0) return;
          this.$message.success($T("操作成功"));
          this.showNoticeProgress(res);
        };
        this.batchExport.openTrigger_in = Date.now();
      } else if (this.ElSelect_analyseType.value === 1) {
        // 同比环比定制接口导出
        queryData.aggregationCycle = this.ElSelect_dateType.value;
        queryData.energyType = this.ElSelect_energyType.value;
        queryData.node = {
          modelLabel: this.currentNode.modelLabel,
          id: this.currentNode.id
        };
        queryData.energyDataType = this.ElSelect_viewMethod.value;
        if (!this.ElCheckbox_tb.value && !this.ElCheckbox_hb.value) {
          queryData.queryType = 0;
        } else if (this.ElCheckbox_tb.value && !this.ElCheckbox_hb.value) {
          queryData.queryType = 1;
        } else if (!this.ElCheckbox_tb.value && this.ElCheckbox_hb.value) {
          queryData.queryType = 2;
          if (this.ElSelect_dateType.value === 17) {
            queryData.queryType = 0;
          }
        } else {
          queryData.queryType = 3;
          if (this.ElSelect_dateType.value === 17) {
            queryData.queryType = 1;
          }
        }
        delete queryData.cycle;

        this.batchExport.treeData_in = this._.cloneDeep(
          this.CetTree_1.inputData_in
        );
        this.batchExport.defaultCheckedNodes = [
          this._.cloneDeep(this.currentNode)
        ];
        this.batchExport.confirmFn = async params => {
          const exportData = {
            ...queryData,
            ...params,
            returnExtremum: this.maximum,
            returnAverage: this.average
          };
          const res = await commonApi.energyConsumptionTbhbExportBatch(
            exportData
          );
          if (res.code !== 0) return;
          this.$message.success($T("操作成功"));
          this.showNoticeProgress(res);
        };
        this.batchExport.openTrigger_in = Date.now();
        return;
      } else if (
        this.ElSelect_analyseType.value === 2 &&
        this.ElSelect_timeScheme.value
      ) {
        // 分时
        queryData.aggregationCycle = this.ElSelect_dateType.value;
        queryData.energyType = this.ElSelect_energyType.value;
        queryData.nodes = [
          {
            id: this.currentNode.id,
            modelLabel: this.currentNode.modelLabel
          }
        ];
        queryData.tsSchemeId = this.ElSelect_timeScheme.value;
        queryData.energyDataType = this.ElSelect_viewMethod.value;
        const timeScheme = this.ElOption_timeScheme.options_in.find(
          i => i.timesharefeerecord_id === this.ElSelect_timeScheme.value
        );
        queryData.tsSchemeName = timeScheme?.tsRecordName;
        delete queryData.cycle;

        this.batchExport.treeData_in = this._.cloneDeep(
          this.CetTree_1.inputData_in
        );
        this.batchExport.defaultCheckedNodes = [
          this._.cloneDeep(this.currentNode)
        ];
        this.batchExport.confirmFn = async params => {
          const exportData = {
            ...queryData,
            ...params
          };

          const res =
            await commonApi.energyConsumptionTimeshareChildCycleExportBatch(
              exportData
            );
          if (res.code !== 0) return;
          this.$message.success($T("操作成功"));
          this.showNoticeProgress(res);
        };
        this.batchExport.openTrigger_in = Date.now();
      } else if (this.ElSelect_analyseType.value === 3) {
        // 节点对比导出
        queryData.energyType = this.ElSelect_energyType.value;
        const { nodes, timeRanges } = this.getSelectNode();
        queryData.nodes = nodes;
        queryData.aggregationCycle = this.ElSelect_dateType.value;
        queryData.energyDataType = this.ElSelect_viewMethod.value;
        queryData.dimConfigId = this.ElSelect_treeType.value;
        if (timeRanges?.length) {
          queryData.timeRanges = timeRanges;
        }
        delete queryData.cycle;
        common.downExcel(
          `/eembaseenergy/v2/energy/consumption/compare/export`,
          queryData
        );
      } else if (this.ElSelect_analyseType.value === 4) {
        // 能耗详情页面导出
        queryData.energyType = this.ElSelect_energyType.value;
        queryData.objectId = this.currentNode.id;
        queryData.objectLabel = this.currentNode.modelLabel;
        queryData.aggregationCycle = this.ElSelect_dateType.value;
        delete queryData.cycle;

        this.batchExport.treeData_in = this._.cloneDeep(
          this.CetTree_1.inputData_in
        );
        this.batchExport.defaultCheckedNodes = [
          this._.cloneDeep(this.currentNode)
        ];
        this.batchExport.confirmFn = async params => {
          const exportData = {
            ...queryData,
            ...params
          };
          const res = await commonApi.energyConsumptionDetailExportBatch(
            exportData
          );
          if (res.code !== 0) return;
          this.$message.success($T("操作成功"));
          this.showNoticeProgress(res);
        };
        this.batchExport.openTrigger_in = Date.now();
      }
    },
    /**
     * 自定义周期需要调用能耗对比组件导出
     */
    customCycleEnergyExport() {
      const queryData = this.$refs.customCycleEnergy.getParams();
      this.batchExport.modelName = `${$T("能耗查询与分析")}-${$T(
        "同环比"
      )}-${this.$moment().format("YYYY-MM-DD")}`;
      this.batchExport.treeData_in = this._.cloneDeep(
        this.CetTree_1.inputData_in
      );
      this.batchExport.defaultCheckedNodes = [
        this._.cloneDeep(this.currentNode)
      ];
      this.batchExport.confirmFn = async params => {
        const exportData = {
          ...queryData,
          ...params,
          returnExtremum: this.maximum,
          returnAverage: this.average,
          dimConfigId: this.ElSelect_treeType.value
        };
        const res = await commonApi.contrastExportBatch(exportData);
        if (res.code !== 0) return;
        this.$message.success($T("操作成功"));
        this.showNoticeProgress(res);
      };
      this.batchExport.openTrigger_in = Date.now();
    },
    CetButton_trendcurve_statusTrigger_out() {
      this.$router.push({
        name: "trendcurve",
        params: {
          energyType: this.ElSelect_energyType.value,
          currentNode: this._.cloneDeep(this.currentNode)
        }
      });
    },
    CetButton_prv_statusTrigger_out(val) {
      if (this.ElSelect_dateType.value === 14) {
        this.elDate.value = this.$moment(this.elDate.value)
          .subtract(1, "month")
          .valueOf();
      } else if (this.ElSelect_dateType.value === 17) {
        this.elDate.value = this.$moment(this.elDate.value)
          .subtract(1, "year")
          .valueOf();
      } else if (this.ElSelect_dateType.value === 12) {
        this.elDate.value = this.$moment(this.elDate.value)
          .subtract(1, "day")
          .valueOf();
      }
      // 多维度时间变化的时候需要去更新节点树
      if (this.ElSelect_treeType.value > 0) {
        this.getTreeData2(true);
      } else {
        this.getData();
      }
    },
    CetButton_next_statusTrigger_out(val) {
      if (this.ElSelect_dateType.value === 14) {
        this.elDate.value = this.$moment(this.elDate.value)
          .add(1, "month")
          .valueOf();
      } else if (this.ElSelect_dateType.value === 17) {
        this.elDate.value = this.$moment(this.elDate.value)
          .add(1, "year")
          .valueOf();
      } else if (this.ElSelect_dateType.value === 12) {
        this.elDate.value = this.$moment(this.elDate.value)
          .add(1, "day")
          .valueOf();
      }
      // 多维度时间变化的时候需要去更新节点树
      if (this.ElSelect_treeType.value > 0) {
        this.getTreeData2(true);
      } else {
        this.getData();
      }
    },
    ElSelect_dateType_change_out(val) {
      if (val === -1) {
        // 定义只有第一次进入自定义得时候才会有默认数据
        this.getDefaultData = true;
        // 选择自定义后需要先手动选择时间点
        this.ElSelect_statisticsType.value = 12;
        this.ElSelect_statisticsType_change_out();
        return;
      }

      var time = this.elDate.value;
      if (new Date(time) > new Date()) {
        this.elDate.value = this._.cloneDeep(new Date().getTime());
      }
      this.ElCheckbox_hb.disabled = false;
      if (val === 17) {
        this.ElCheckbox_hb.disabled = true;
        this.elDate.type = "year";
        this.elDate.pickerOptions.shortcuts[0].text = this.$T("今年");
      } else if (val === 14) {
        this.elDate.type = "month";
        this.elDate.pickerOptions.shortcuts[0].text = this.$T("当月");
      } else if (val === 12) {
        this.elDate.type = "date";
        this.elDate.pickerOptions.shortcuts[0].text = this.$T("今天");
      }
      this.handlerNextBtn();
      // 多维度时间变化的时候需要去更新节点树
      if (this.ElSelect_treeType.value > 0) {
        this.getTreeData2(true);
      } else {
        this.getData();
      }
    },
    CetTree_1_currentNode_out(val) {
      if (this.stopNodeOut) {
        this.stopNodeOut = false;
        return;
      }
      // 按照张壮要求，在通用3.5迭代中，重新添加成点击弹窗处理
      if (val.childSelectState == 2) {
        return this.$message.warning($T("没有该节点权限"));
      }
      this.currentNode = val;
      // 激活时节点输出两次 等待获取完能源类型那次接口请求调用之后再生效请求数据
      if (this.ajaxFlag) {
        // 自定义周期数据请求在组件内部
        if (this.showCustomCycleEnergy) return;
        if (this.ElSelect_analyseType.value === 3) {
          this.$refs.cetTree.$refs.tree.setCheckedKeys([
            this.currentNode.tree_id
          ]);
          this.copySelectNodes = [this.currentNode];
          this.getChartData3();
        } else if (this.ElSelect_analyseType.value === 2) {
          this.getTimeShareScheme();
        } else {
          this.getData();
        }
      }
    },
    handlerLegendClick(val) {
      if (this.currentModel === 2) {
        this.expandNode(val);
      } else if (this.currentModel === 3) {
        this.cancelSelect(val.name);
      }
    },
    cancelSelect(name) {
      this.$refs.cetTree.$refs.tree.setChecked(name, false);
      // 高亮取消的节点
      const nodesMap = this.$refs.cetTree.$refs.tree.store.nodesMap;
      Object.keys(nodesMap).forEach(key => {
        nodesMap[key].expanded = false;
        nodesMap[key].isCurrent = false;
      });
      const node = this.$refs.cetTree.$refs.tree.getNode(name);
      this.foucsNode = node;
      this.CetTree_1.defaultExpandedKeys = [node.parent.key];

      const legendList = this.CetChart_1.options.legend.data.filter(
        item => item !== name
      );
      const seriesList = this.CetChart_1.options.series.filter(
        item => item.name !== name
      );
      this.CetChart_1.options.legend.data = legendList;
      this.CetChart_1.options.series = seriesList;
    },
    expandNode(val) {
      this.$refs.CetChart.chart.dispatchAction({
        type: "legendSelect",
        name: val.name
      });

      //展开选中的节点，并将其他节点收缩，确保快速找到该节点
      const nodesMap = this.$refs.cetTree.$refs.tree.store.nodesMap;
      Object.keys(nodesMap).forEach(key => {
        nodesMap[key].expanded = false;
        nodesMap[key].isCurrent = false;
      });
      const node = this.$refs.cetTree.$refs.tree.getNode(val.name);
      this.foucsNode = node;
      this.CetTree_1.defaultExpandedKeys = [node.parent.key];
    },
    handlerChangeModel(val) {
      this.currentModel = val;
      // 切换时将所有隐藏的数据在图表中显示
      this.CetChart_1.options.legend.data.forEach(name => {
        this.$refs.CetChart.chart.dispatchAction({
          type: "legendSelect",
          name: name
        });
      });
    },
    CetTree_1_checkedNodes_out(val, checkData) {
      const iNodeChecked = checkData.checkedNodes || [];
      if (iNodeChecked.length > this.numberOfNodesCompared) {
        this.$message.warning(
          $T("最多对比{0}个节点", this.numberOfNodesCompared)
        );
        this.CetTree_1.checkedNodes = iNodeChecked.filter(
          item => item.tree_id !== val.tree_id
        );
        return;
      }
      if (this.ElSelect_analyseType.value !== 3) {
        return;
      }
      if (iNodeChecked.length < 2) {
        this.copySelectNodes = this._.cloneDeep(iNodeChecked);
      } else {
        let isFindOk = iNodeChecked.find(item => item.tree_id === val.tree_id);
        if (isFindOk) {
          this.copySelectNodes = this.copySelectNodes.filter(
            item => item.tree_id !== isFindOk.tree_id
          );
          // 添加节点
          if (val.tree_id === this.currentNode.tree_id) {
            // 如果添加的节点是当前选中的，放在头部，否则放在尾部
            this.copySelectNodes.unshift(val);
          } else {
            this.copySelectNodes.push(val);
          }
        } else {
          this.copySelectNodes = this.copySelectNodes.filter(
            item => item.tree_id !== val.tree_id
          );
        }
      }
      this.getChartData3();
    },
    //节点对比
    async getChartData3() {
      const _this = this;
      var queryBody = this.getParams(3);
      if (!queryBody || queryBody.nodes.length == 0 || !queryBody.energyType) {
        _this.filChartData3([]);
        return;
      }
      this.CetChart_1.options = {};
      queryBody.aggregationCycle = this.ElSelect_dateType.value;
      queryBody.energyDataType = this.ElSelect_viewMethod.value;
      delete queryBody.cycle;

      const response = await commonApi.getV2EmergyConsumptionCompare(queryBody);

      if (response.code !== 0) return;
      //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
      var data = _this._.get(response, ["data"], []);
      let sortData = [];
      _this.copySelectNodes.forEach(item => {
        let findObj = data.find(
          i => item.id === i.objectId && item.modelLabel === i.objectLabel
        );
        if (findObj) {
          sortData.push(findObj);
        }
      });
      _this.filChartData3(sortData);
    },
    filChartData3(pdata) {
      let legendArr = [];
      this.CetChart_1.options = {
        toolbox: {
          top: 2,
          right: 0,
          feature: {
            saveAsImage: {
              title: $T("保存为图片")
            }
          }
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          },
          // appendToBody: true,
          formatter: function (val) {
            const list = val || [];
            let formatterStr = "";
            for (let i = 0, len = list.length; i < len; i++) {
              if (i === 0) {
                formatterStr += `${val[i].name}`;
              }
              const legendName =
                legendArr.find(v => v.name === val[i].seriesName)?.text ??
                val[i].seriesName;
              formatterStr += `<br/>${val[i].marker}${legendName} : ${
                val[i].value || "--"
              }(${val[i].data.unit || "--"})`;
            }
            return formatterStr;
          }
        },
        grid: {
          top: "90",
          left: "16",
          right: "16",
          bottom: "8",
          containLabel: true
        },
        legend: {
          type: "scroll",
          data: [],
          width: "70%",
          formatter: name => {
            let showText = legendArr.find(v => v.name === name)?.text ?? name;
            return showText;
          }
        },
        xAxis: {
          type: "category",
          data: []
        },
        yAxis: {
          type: "value",
          min: 0,
          max: function (value) {
            if (value.max < 0) {
              return 0;
            }
            return null;
          },
          nameTextStyle: {
            align: "left"
          }
        },
        series: []
      };
      var _this = this;
      var data = pdata || [];
      var id = 0;
      var modelLabel = "";
      var legend = [];
      var series = [];
      const unit = (data.length > 0 && data[0].symbol) || "--";

      if (this.currentNode) {
        id = this.currentNode.id;
        modelLabel = this.currentNode.modelLabel;
      } else {
        this.$message.warning($T("请选择节点"));
        return;
      }
      var list = _this.copySelectNodes || [];
      var isHasNode = false;
      list.forEach(item => {
        if (
          item.id === this.currentNode.id &&
          item.modelLabel === this.currentNode.modelLabel
        ) {
          isHasNode = true;
        }
      });
      if (!isHasNode && list.length > 0) {
        id = list[0].id;
        modelLabel = list[0].modelLabel;
      }
      var cycle = this.ElSelect_dateType.value;
      var source = [];
      data.forEach(item => {
        if (item.objectId === id && item.objectLabel === modelLabel) {
          var itemData1 = item.dataList || [];
          itemData1.forEach(item1 => {
            var obj = {};
            obj.time = item1.logTime;
            obj.product = this.getAxixs(item1.logTime, cycle);
            obj[`yAxis${item.objectLabel}${item.objectId}`] =
              common.formatNumberWithPrecision(item1.value, 2);
            source.push(obj);
          });
        }
        const serie = {
          name: item.objectName,
          type: "line",
          smooth: true,
          encode: {
            x: "product",
            y: `yAxis${item.objectLabel}${item.objectId}`
          }
        };
        legend.push(item.objectName);
        series.push(serie);
      });
      data.forEach(item => {
        if (item.objectId != id || item.objectLabel !== modelLabel) {
          var itemData1 = item.dataList || [];
          itemData1.forEach(item1 => {
            source.forEach(item2 => {
              if (item2.time === item1.logTime) {
                item2[`yAxis${item.objectLabel}${item.objectId}`] =
                  common.formatNumberWithPrecision(item1.value, 2);
              }
            });
          });
        }
      });

      var xAxisData = [];
      var cloneLegend = [],
        cloneSeries = [];
      source.forEach(item => {
        xAxisData.push(item.product);
      });
      data.forEach((item, index) => {
        var yAxisData = [];
        source.forEach(item11 => {
          // yAxisData.push(item11[`yAxis${item.modelLabel}${item.id}`]);
          yAxisData.push({
            name: item11.product,
            time: item11.time,
            unit: unit,
            value: item11[`yAxis${item.objectLabel}${item.objectId}`]
          });
        });
        const name = `${item.objectLabel}_${item.objectId}`;
        const serie = {
          name: name,
          type: "line",
          smooth: true,
          data: yAxisData
          // encode: { x: "product", y: `yAxis${item.modelLabel}${item.id}` }
        };
        cloneLegend.push(name);
        legendArr.push({
          name: name,
          text: item.objectName
        });
        cloneSeries.push(serie);
      });
      // if (cloneSeries.length > 0) {
      //   cloneSeries[0].data.forEach(item => {
      //     if (Number(item.value) < 0) {
      //       item.value = 0;
      //     }
      //   });
      // }
      if (this.energyTypeObj) {
        _this.CetChart_1.options.yAxis.name = $T(
          "用{0}量（{1}）",
          this.energyTypeObj.name || "--",
          unit || "--"
        );
      } else {
        _this.CetChart_1.options.yAxis.name = "--";
      }
      this.CetChart_1.options.xAxis.data = xAxisData;
      this.CetChart_1.options.series = cloneSeries;
      this.CetChart_1.options.legend.data = cloneLegend;
      this.chartTypeChange();
    },
    // 图表类型切换
    chartTypeChange() {
      if (this.showCustomCycleEnergy) {
        this.$refs.customCycleEnergy.chartTypeChange();
        return;
      }
      this.CetChart_1.options.series.forEach(item => {
        item.type = this.chartType === 1 ? "bar" : "line";
      });
      if (this.ElSelect_analyseType.value === 1) {
        return;
      }
      this.CetChart_1.options.grid.bottom = this.chartType === 1 ? 50 : 8;
      this.CetChart_1.options.dataZoom =
        this.chartType === 1
          ? [
              {
                type: "inside",
                startValue: 0,
                endValue: 30,
                zoomOnMouseWheel: false
              },
              {
                startValue: 0,
                endValue: 30,
                brushSelect: false,
                handleSize: "30"
              }
            ]
          : null;
      this.CetChart_1.options.yAxis.scale = this.chartType !== 1;
      this.CetChart_1.options.yAxis.min = this.chartType === 1 ? 0 : null;
      this.CetChart_1.options.yAxis.max =
        this.chartType === 1
          ? function (value) {
              if (value.max < 0) {
                return 0;
              }
              return null;
            }
          : null;
      this.CetChart_1.options = this._.cloneDeep(this.CetChart_1.options);
    },
    // 分时分析tab页chartType改变
    timeSharechartTypeChange() {
      this.CetChart_1.options.xAxis[0].boundaryGap =
        this.timeShareChartType === 1;
      this.CetChart_1.options.series.forEach(item => {
        item.type =
          [$T("总量"), "总量"].includes(item.name) ||
          this.timeShareChartType === 2
            ? "line"
            : "bar";
        item.smooth = this.timeShareChartType === 1;

        // 堆叠面积图值为null时，需要改为0，确保线段连续
        item.data = item.data.map(it => {
          const actualValue =
            it.actualValue === undefined ? it.value : it.actualValue;
          return {
            ...it,
            value:
              this.timeShareChartType === 2 ? actualValue ?? 0 : actualValue,
            actualValue: actualValue
          };
        });
      });
    },
    //获取图表参数
    getParams() {
      if (!this.currentNode) {
        return null;
      }
      const startTime = this.getQueryTime(
        this.elDate.value,
        this.ElSelect_dateType.value
      ).startTime;
      const endTime = this.getQueryTime(
        this.elDate.value,
        this.ElSelect_dateType.value
      ).endTime;
      const cycle = this.ElSelect_dateType.value;
      const energyType = this.ElSelect_energyType.value;
      const { nodes, timeRanges } = this.getSelectNode();
      const params = {
        cycle: cycle,
        endTime: endTime,
        energyType: energyType,
        nodes: nodes,
        startTime: startTime,
        dimConfigId: this.ElSelect_treeType.value,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };
      if (timeRanges?.length) {
        params.timeRanges = timeRanges;
      }
      return params;
    },
    //节点对比中获取选中节点，进行分类
    getSelectNode() {
      var list = this.$refs.cetTree.$refs.tree.getCheckedNodes() || [];
      var objType = {}; //存放数组种类
      var resArr = []; //存放结果数组
      const timeRanges = [];
      for (var i = 0, len = list.length; i < len; i++) {
        var obj = {
          id: list[i].id,
          modelLabel: list[i].modelLabel,
          name: list[i].name
        };
        // if (objType[list[i].modelLabel]) {
        //   objType[list[i].modelLabel].push(obj);
        // } else {
        //   objType[list[i].modelLabel] = [];
        //   objType[list[i].modelLabel].push(obj);
        // }
        resArr.push(obj);
        if (list[i]?.effTimeList?.[0]?.startTime) {
          timeRanges.push({
            objectId: list[i].id,
            objectLabel: list[i].modelLabel,
            timeRanges: list[i].effTimeList
          });
        }
      }
      // for (var key in objType) {
      //   var resObj = {
      //     modelLabel: key,
      //     nodes: objType[key]
      //   };
      //   resArr.push(resObj);
      // }

      return { nodes: resArr, timeRanges };
    },
    async elDate_change(val) {
      // 多维度时间变化的时候需要去更新节点树
      if (this.ElSelect_treeType.value > 0) {
        await this.getTreeData2(true);
      } else {
        this.getData();
      }
    },
    async init() {
      this.ElSelect_viewMethod.value = 4;
      this.chartType = 1;
      this.maximum = true;
      this.average = false;
      this.getDefaultData = false;
      // 赋值当前非自然周期月
      this.elDate.value = this.$moment(this.startMonthTime)
        .startOf("month")
        .valueOf();
      this.tabsValue = "1";
      this.ElCheckbox_hb.disabled = false;
      this.CetTree_1.showCheckbox = false;
      this.ElCheckbox_tb.value = false;
      this.ElCheckbox_hb.value = false;
      this.ElSelect_dateType.value = 14;
      this.initDateTypeOptions();
      this.elDate.type = "month";
      this.elDate.pickerOptions.shortcuts[0].text = this.$T("当月");
      this.reset();
      this.ajaxFlag = false;
      await this.getRootNode();
      await commonApi.getProjectEnergy().then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          this.ElOption_energyType.options_in = data;
          let obj = data.find(i => i.energytype === 2);

          this.ElSelect_energyType.value = obj
            ? obj.energytype
            : this._.get(this.ElOption_energyType, "options_in[0].energytype");
        }
      });
      await this.queryTreeType();
      this.nextToDo1();
    },
    nextToDo1() {
      // 获取项目能源类型 折标煤13名称改为综合能耗，并默认选中
      this.ajaxFlag = true;
      // 如果选择的非综合能耗或CO2 请求分时方案
      if (
        this.ElSelect_energyType.value !== 13 &&
        this.ElSelect_energyType.value !== 18 &&
        this.ElSelect_analyseType.value === 2
      ) {
        this.getTimeShareScheme(true);
      }
      // this.getData();
    },
    async getRootNode() {
      this.ElOption_project.options_in = [];
      const res = await commonApi.rootNode();
      const data = res.data || [];
      this.ElOption_project.options_in = data;
      this.ElSelect_project.value = data?.[0]?.id;
    },
    initDateTypeOptions() {
      if (this.ElSelect_analyseType.value === 1) {
        // 同环比增加自定义周期
        // 20241021 张壮说表结构调整，先去掉自定义周期
        this.ElOption_dateType.options_in = [
          ...this.dateTypeOptions
          // {
          //   id: -1,
          //   text: $T("自定义")
          // }
        ];
      } else if (this.ElSelect_analyseType.value === 2) {
        // 分时分析去掉日选项
        this.ElOption_dateType.options_in = this.dateTypeOptions.filter(
          i => i.id !== 12
        );
        if (
          !this.ElOption_dateType.options_in.find(
            i => i.id === this.ElSelect_dateType.value
          )
        ) {
          this.ElSelect_dateType.value = 14;
          this.elDate.type = "month";
          this.elDate.pickerOptions.shortcuts[0].text = this.$T("当月");
        }
      } else {
        if (
          !this.dateTypeOptions.find(i => i.id === this.ElSelect_dateType.value)
        ) {
          this.ElSelect_dateType.value = 14;
          this.elDate.type = "month";
          this.elDate.pickerOptions.shortcuts[0].text = this.$T("当月");
        }
        this.ElOption_dateType.options_in = this.dateTypeOptions;
      }
    },
    reset() {
      // 对比图
      this.comparison1 = {
        title: $T("同比"),
        type: 1,
        ratio: "--",
        ratioType: "--",
        value: "--",
        oldValue: "--"
      };
      this.comparison2 = {
        title: $T("环比"),
        type: 1,
        ratio: "--",
        ratioType: "--",
        value: "--",
        oldValue: "--"
      };
      this.CetChart_1.options = {};
      this.CetChart_2.options = {};
      this.CetChart_2_legend = [];
    },
    async getTreeData1() {
      var _this = this;

      const queryData = {
        plugin: "eem-base",
        business: "energy-analysis",
        energyType: this.ElSelect_energyType.value,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };

      let queryFn = commonApi.getBusinessTree;
      if (this.ElSelect_viewMethod.value === 1) {
        queryFn = commonApi.getBusinessTreeSimpleSupplyTo;
      }

      const res = await queryFn(queryData);

      if (res.code !== 0) {
        return;
      }

      this.setTreeLeaf(res.data);
      _this.CetTree_1.inputData_in = res.data;
      // _this.CetTree_1.selectNode = res.data[0];
      // 选中第一个有数据 childSelectState = 1 的节点并展开节点
      const obj = this._.find(this.dataTransform(res.data), [
        "childSelectState",
        1
      ]);
      this.CetTree_1.selectNode = obj;
    },
    /**
     *  获取维度节点树数据
     * @param keepSelectNode 是否保持上次选择节点
     */
    async getTreeData2(keepSelectNode) {
      const queryData = this.getDimensionQueryParams();
      const res = await commonApi.dimensionTreeFilterByEnergytype(queryData);
      if (res.code !== 0) {
        return;
      }

      this.setTreeLeaf(res.data);

      this.CetTree_1.inputData_in = res.data;

      let selectNode,
        checkedNodes = [];

      const nodeList = this.dataTransform(res.data);

      if (keepSelectNode) {
        // 如果上次选择节点如果存在，则保持上次选择节点
        selectNode = this._.find(nodeList, [
          "tree_id",
          this.currentNode.tree_id
        ]);
        if (this.ElSelect_analyseType.value === 3) {
          // 节点对比上次勾选的节点如果存在，则保持
          this.CetTree_1.checkedNodes.forEach(item => {
            const node = this._.find(nodeList, ["tree_id", item.tree_id]);
            node && checkedNodes.push(node);
          });
        }
      }

      if (!keepSelectNode || !selectNode) {
        // 不保持上次选择节点或者找不到上次选择节点，则选中第一个有数据 childSelectState = 1 的节点
        selectNode = this._.find(nodeList, ["childSelectState", 1]);
      }

      if (!checkedNodes?.length) {
        checkedNodes.push(selectNode);
      }

      this.CetTree_1.selectNode = selectNode;
      if (keepSelectNode && this.ElSelect_analyseType.value === 3) {
        // 更新节点树时会输出一次当前选中节点，后续的逻辑会将勾选节点重置，这里需要阻止此逻辑
        this.stopNodeOut = true;
        this.CetTree_1.checkedNodes = checkedNodes;

        // 去除掉已不存在的节点
        const copySelectNodes = [];
        this.copySelectNodes.forEach(({ tree_id }) => {
          const item = checkedNodes.find(i => i.tree_id === tree_id);
          if (item) {
            copySelectNodes.push(item);
          }
        });
        this.copySelectNodes = copySelectNodes;

        this.CetTree_1_checkedNodes_out(selectNode, {
          checkedNodes
        });
      }
    },
    getDimensionQueryParams() {
      const queryData = {
        dimTreeConfigId: this.ElSelect_treeType.value,
        energyType: this.ElSelect_energyType.value,

        rootNode: {
          id: this.rootNode.id,
          modelLabel: this.rootNode.modelLabel
        }
      };

      if (this.ElSelect_dateType.value === -1) {
        // 自定义需要将时间点组装起来
        const customEnergyParams = this.$refs.customCycleEnergy?.getParams();
        queryData.aggregationCycle = customEnergyParams?.aggregationCycle ?? 12;
        const datePoint = this.datePointPicker.value;

        const logTimes = [];
        if (queryData.aggregationCycle === 7 && datePoint?.length) {
          // 小时特殊处理，返回的只有开始结束时间点，需要自己拼接出来
          let [start, end] = datePoint;
          while (
            this.$moment(start).startOf("H").valueOf() <
            this.$moment(end).endOf("H").valueOf() + 1
          ) {
            logTimes.push(this.$moment(start).startOf("H").valueOf());
            start = this.$moment(start).add(1, "H").startOf("H").valueOf();
          }
        } else if (datePoint?.length) {
          queryData.logTimes = datePoint;
        }

        queryData.logTimes = logTimes;
      } else {
        queryData.startTime = this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).startTime;
        queryData.endTime = this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).endTime;
        queryData.aggregationCycle = this.ElSelect_dateType.value;
      }

      return queryData;
    },
    // 获取第一个有权限的节点
    dataTransform(array) {
      const cloneData = this._.cloneDeep(array);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },
    // 设置节点禁用
    setTreeLeaf(nodesAll) {
      if (nodesAll && nodesAll.length > 0) {
        nodesAll.forEach(item => {
          if (item.childSelectState == 2) {
            this.$set(item, "disabled", true);
          } else if (item.childSelectState == 1) {
            this.$set(item, "disabled", false);
          }
          this.setTreeLeaf(this._.get(item, "children", []));
        });
      }
    },
    getData() {
      if (!this.ElSelect_energyType.value) {
        return;
      }
      // 综合能耗和CO2单独处理
      if (this.ElSelect_analyseType.value === 3) {
        this.getChartData3();
      } else if (this.ElSelect_analyseType.value === 4) {
        this.getChartData4();
      } else if (
        this.ElSelect_energyType.value === 13 ||
        this.ElSelect_energyType.value === 18
      ) {
        this.getSynthesizeConsume();
        this.getContrastData();
        this.handelTop5();
      } else if (this.ElSelect_analyseType.value === 1) {
        // 同比环比
        this.getTbhbData();
        this.getContrastData();
        this.handelTop5();
      } else if (
        this.ElSelect_analyseType.value === 2 &&
        this.ElSelect_timeScheme.value
      ) {
        // 分时
        this.getTimeChartData();
        this.handelProportion();
      } else {
        this.reset();
      }
    },
    // 查询综合能耗或者CO2
    getSynthesizeConsume() {
      var _this = this;
      var queryData = {
        aggregationCycle: this.ElSelect_dateType.value,
        endTime: this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).endTime,
        node: {
          id: this.currentNode?.id,
          modelLabel: this.currentNode?.modelLabel
        },
        startTime: this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).startTime,
        targetEnergyType: this.ElSelect_energyType.value,
        energyDataType: this.ElSelect_viewMethod.value,
        dimConfigId: this.ElSelect_treeType.value,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };
      if (this.currentNode?.effTimeList?.[0]?.startTime) {
        queryData.timeRanges = this.currentNode.effTimeList;
      }
      this.getTbAndHbDate();
      commonApi.getV2EmergyConsumptionStandard(queryData).then(response => {
        if (response.code === 0) {
          const data = this._.get(response, "data", []);
          const xAxisData = [];
          let yAxis = "";
          const seriesData = [];
          const symbol = this._.get(response, "data[0].symbol", "");
          // 坐标名
          yAxis = $T("用{0}量（{1}）", this.energyTypeObj.name, symbol);
          let flag = true;
          data.forEach(item => {
            var energyObj = this.ElOption_energyType.options_in.find(
              i => i.energytype === item.energyType
            );
            const seriesDataItem = {
              name: energyObj ? energyObj.name : "",
              type: "line",
              symbol: "circle",
              symbolSize: 2,
              smooth: true,
              data: []
            };
            const seriesDataItemData = item.energyDataList.map(item => {
              if (flag) {
                xAxisData.push(
                  this.getAxixs(item.logTime, this.ElSelect_dateType.value)
                );
              }
              return {
                time: item.logTime,
                unit: symbol,
                value: common.formatNumberWithPrecision(item.value, 2)
              };
            });
            // 综合能耗或者CO2  这两种要展示成折线
            if (item.energyType !== 13 && item.energyType !== 18) {
              seriesDataItem.stack = $T("总量");
              seriesDataItem.areaStyle = {
                opacity: 0.8
              };
            }
            flag = false;
            seriesDataItem.data = seriesDataItemData;
            seriesData.push(seriesDataItem);
          });

          this.CetChart_1.options = {
            toolbox: {
              top: 40,
              right: 30,
              feature: {
                saveAsImage: {
                  title: $T("保存为图片")
                }
              }
            },
            tooltip: {
              trigger: "axis",
              // appendToBody: true,
              formatter: function (val) {
                var list = val || [];
                var formatterStr = "";
                var formatVal = _this.getFormatVal();
                for (var i = 0, len = list.length; i < len; i++) {
                  if (i === 0) {
                    formatterStr += `${
                      formatVal
                        ? `${_this.formatSeriesName(val[i].data)}-${
                            val[i].name
                          }`
                        : val[i].name
                    } <br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                      val[i].color
                    };"></span>${val[i].seriesName} : ${val[i].value || "--"}(${
                      val[i].data.unit || "--"
                    })`;
                  } else {
                    formatterStr += `<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                      val[i].color
                    };"></span>${val[i].seriesName} : ${val[i].value || "--"}(${
                      val[i].data.unit || "--"
                    })`;
                  }
                }
                return formatterStr;
              }
            },
            legend: {
              top: "20px"
            },
            grid: {
              top: "80",
              left: "16",
              right: "16",
              bottom: "8",
              containLabel: true
            },
            xAxis: [
              {
                type: "category",
                boundaryGap: false,
                data: xAxisData,
                splitLine: {
                  show: false
                }
              }
            ],
            yAxis: [
              {
                type: "value",
                name: yAxis,
                min: 0,
                max: function (value) {
                  if (value.max < 0) {
                    return 0;
                  }
                  return null;
                },
                splitLine: {
                  show: false
                },
                nameTextStyle: {
                  align: "left"
                }
              }
            ],
            series: seriesData
          };
        }
      });
    },
    // 获取同比环比显示的时间
    getTbAndHbDate() {
      var currentName1, currentName2, currentName3;
      switch (this.ElSelect_dateType.value) {
        case 12:
          currentName1 = this.$moment(this.elDate.value).format("YYYY-MM-DD");
          currentName2 = this.$moment(this.elDate.value)
            .subtract(1, "M")
            .format("YYYY-MM-DD");
          currentName3 = this.$moment(this.elDate.value)
            .subtract(1, "d")
            .format("YYYY-MM-DD");
          break;
        case 14:
          currentName1 = this.$moment(this.elDate.value).format("YYYY-MM");
          currentName2 = this.$moment(this.elDate.value)
            .subtract(1, "Y")
            .format("YYYY-MM");
          currentName3 = this.$moment(this.elDate.value)
            .subtract(1, "M")
            .format("YYYY-MM");
          break;
        case 17:
          currentName1 = this.$moment(this.elDate.value).format("YYYY");
          currentName2 = this.$moment(this.elDate.value)
            .subtract(1, "Y")
            .format("YYYY");
          currentName3 = this.$moment(this.elDate.value)
            .subtract(1, "Y")
            .format("YYYY");
          break;
        default:
          break;
      }
      this.cudate = currentName1;
      this.tbdate = currentName2;
      this.hbdate = currentName3;
    },
    // 主图同比环比
    async getTbhbData() {
      this.chartType = 1;
      const _this = this;
      if (!this.currentNode) {
        return;
      }
      const queryData = {
        aggregationCycle: this.ElSelect_dateType.value,
        endTime: this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).endTime,
        energyType: this.ElSelect_energyType.value,
        node: {
          modelLabel: this.currentNode.modelLabel,
          id: this.currentNode.id
        },
        queryType: 0,
        startTime: this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).startTime,
        energyDataType: this.ElSelect_viewMethod.value,
        dimConfigId: this.ElSelect_treeType.value,
        rootNode: {
          id: this.rootNode.id,
          modelLabel: this.rootNode.modelLabel
        }
      };
      if (this.currentNode?.effTimeList?.[0]?.startTime) {
        queryData.timeRanges = this.currentNode.effTimeList;
      }
      if (!this.ElCheckbox_tb.value && !this.ElCheckbox_hb.value) {
        queryData.queryType = 0;
      } else if (this.ElCheckbox_tb.value && !this.ElCheckbox_hb.value) {
        queryData.queryType = 1;
      } else if (!this.ElCheckbox_tb.value && this.ElCheckbox_hb.value) {
        queryData.queryType = 2;
        if (this.ElSelect_dateType.value === 17) {
          queryData.queryType = 0;
        }
      } else {
        queryData.queryType = 3;
        if (this.ElSelect_dateType.value === 17) {
          queryData.queryType = 1;
        }
      }
      // 获取阈值
      const cycleObj = {
        17: 14,
        14: 12
      };
      commonApi.getV2EmergyConsumptionTbhb(queryData).then(response => {
        if (response.code === 0) {
          const currentdata = this._.get(response, "data.currentData", []);
          const tbdata = this._.get(response, "data.tbData", []);
          const hbdata = this._.get(response, "data.hbData", []);
          const thresholdData = this._.get(response, "data.thresholdData");
          const series1 = [];
          const series2 = [];
          const series3 = [];
          const xAxisData = [];
          const seriesData = [];
          const symbol = this._.get(response, "data.symbol", "");
          const thresholdSeries = [];

          var yAxisName = $T("用{0}量（{1}）", this.energyTypeObj.name, symbol);

          currentdata.forEach((item, index) => {
            var value1 =
              item.value || item.value === 0 ? item.value.toFixed2(2) : null;
            var value2 =
              tbdata[index] &&
              (tbdata[index].value || tbdata[index].value === 0)
                ? tbdata[index].value.toFixed2(2)
                : null;
            var value3 =
              hbdata[index] &&
              (hbdata[index].value || hbdata[index].value === 0)
                ? hbdata[index].value.toFixed2(2)
                : null;
            var product = this.getAxixs(
              item.logTime,
              this.ElSelect_dateType.value
            );
            let obj = {
              time: item.logTime,
              unit: symbol,
              name: product,
              value: value1
            };
            // 能耗阈值
            if (
              cycleObj[queryData.aggregationCycle] &&
              thresholdData &&
              queryData.energyDataType == 1
            ) {
              var thresholdValue =
                thresholdData[index] &&
                (thresholdData[index].value || thresholdData[index].value === 0)
                  ? thresholdData[index].value.toFixed2(2)
                  : null;
              if (
                thresholdValue !== null &&
                Number(value1) > Number(thresholdValue)
              ) {
                obj.itemStyle = {
                  color: this.themeLight ? "#FCAFAD" : "#FF8B8B"
                };
              }
              thresholdSeries.push({
                time: item.logTime,
                unit: symbol,
                name: product,
                value: thresholdValue
              });
            }
            series1.push(obj);
            series2.push({
              time: item.logTime,
              hb: true,
              unit: symbol,
              name: product,
              value: value2
            });
            series3.push({
              time: item.logTime,
              tb: true,
              unit: symbol,
              name: product,
              value: value3
            });
            xAxisData.push(product);
          });
          this.getTbAndHbDate();
          seriesData.push({
            name: this.cudate,
            type: "bar",
            stack: $T("总量"),
            symbol: "circle",
            symbolSize: 2,
            smooth: true,
            data: series1,
            markPoint: this.getMaximum(series1),
            markLine: this.getAverage()
          });
          if (
            cycleObj[queryData.aggregationCycle] &&
            thresholdData &&
            queryData.energyDataType == 1
          ) {
            seriesData.push({
              name: $T("能耗阈值"),
              type: "line",
              symbol: "circle",
              symbolSize: 5,
              smooth: true,
              data: thresholdSeries,
              itemStyle: {
                color: this.themeLight ? "#F95E5A" : "#FF3F3F"
              }
            });
          }
          if (queryData.queryType === 1) {
            seriesData.push({
              name: this.tbdate,
              type: "line",
              symbol: "circle",
              symbolSize: 2,
              smooth: true,
              data: series2
            });
          } else if (queryData.queryType === 2) {
            seriesData.push({
              name: this.hbdate,
              type: "line",
              symbol: "circle",
              symbolSize: 2,
              smooth: true,
              data: series3
            });
          } else if (queryData.queryType === 3) {
            seriesData.push({
              name: this.tbdate,
              type: "line",
              symbol: "circle",
              symbolSize: 2,
              smooth: true,
              data: series2
            });
            seriesData.push({
              name: this.hbdate,
              type: "line",
              symbol: "circle",
              symbolSize: 2,
              smooth: true,
              data: series3
            });
          }
          this.CetChart_1.options = {
            toolbox: {
              top: 40,
              right: 30,
              feature: {
                saveAsImage: {
                  title: $T("保存为图片")
                }
              }
            },
            tooltip: {
              trigger: "axis",
              // appendToBody: true,
              formatter: function (val) {
                var list = val || [];
                var formatterStr = "";
                for (var i = 0, len = list.length; i < len; i++) {
                  if (i === 0) {
                    formatterStr += `${
                      val[i].name
                    } <br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                      val[i].color
                    };"></span>${_this.formatSeriesName(val[i].data)} : ${
                      val[i].value || "--"
                    }(${val[i].data.unit || "--"})`;
                  } else {
                    let name;
                    if (val[i].seriesName === $T("能耗阈值")) {
                      name = $T("能耗阈值");
                    } else {
                      name = _this.formatSeriesName(val[i].data);
                    }
                    formatterStr += `<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                      val[i].color
                    };"></span>${name} : ${val[i].value || "--"}(${
                      val[i].data.unit || "--"
                    })`;
                  }
                }
                return formatterStr;
              }
            },
            legend: {
              top: "0"
            },
            grid: {
              top: "80",
              left: "16",
              right: "16",
              bottom: "8",
              containLabel: true
            },
            xAxis: [
              {
                type: "category",
                // boundaryGap: false,
                data: xAxisData,
                splitLine: {
                  show: false
                }
              }
            ],
            yAxis: [
              {
                type: "value",
                name: yAxisName,
                min: 0,
                max: function (value) {
                  if (value.max < 0) {
                    return 0;
                  }
                  return null;
                },
                splitLine: {
                  show: false
                },
                nameTextStyle: {
                  align: "left"
                }
              }
            ],
            series: seriesData
          };
        }
      });
    },
    getMaximum(val) {
      if (!this.maximum) return;
      const currentTheme = omegaTheme.theme;
      let bgColor = "";
      let borderColor = "";
      if (["dark", "blue"].includes(currentTheme)) {
        bgColor = "rgba(38, 41, 56, 0.7)";
        borderColor = "#414b6e";
      } else {
        bgColor = " rgba(255, 255, 255, 0.7)";
        borderColor = "#e0e4e8";
      }
      const data = val || this.CetChart_1.options.series[0].data;
      const max = this._.maxBy(data, "value");
      const min = this._.minBy(data, "value");
      return {
        data: [
          {
            type: "max",
            symbolSize: 30,
            name: $T("最大值"),
            label: {
              formatter(params) {
                return (
                  params.name +
                  ": " +
                  common.formatNumberWithPrecision(params.value, 2)
                );
              },
              position: "top",
              padding: 8,
              backgroundColor: bgColor,
              borderType: "solid",
              borderWidth: 1,
              borderColor: borderColor,
              borderRadius: 4
            },
            itemStyle: {
              color: max?.itemStyle?.color
            }
          },
          {
            type: "min",
            symbolSize: 30,
            name: $T("最小值"),
            label: {
              formatter(params) {
                return (
                  params.name +
                  ": " +
                  common.formatNumberWithPrecision(params.value, 2)
                );
              },
              position: "top",
              padding: 8,
              backgroundColor: bgColor,
              borderType: "solid",
              borderWidth: 1,
              borderColor: borderColor,
              borderRadius: 4
            },
            itemStyle: {
              color: min?.itemStyle?.color
            }
          }
        ]
      };
    },
    extremValueHandler() {
      this.CetChart_1.options.series[0].markPoint = this.getMaximum();
      this.CetChart_1.options = this._.cloneDeep(this.CetChart_1.options);
    },
    getAverage() {
      if (!this.average) return;
      return {
        label: {
          formatter: "{@value}({b})",
          position: "insideEndTop",
          color: this.themeLight ? "#333" : "#fff"
        },
        data: [
          {
            type: "average",
            name: $T("平均值")
          }
        ]
      };
    },
    averageHandler() {
      this.CetChart_1.options.series[0].markLine = this.getAverage();
      this.CetChart_1.options = this._.cloneDeep(this.CetChart_1.options);
    },
    // 分时主图
    getTimeChartData() {
      const _this = this;
      const queryData = {
        aggregationCycle: this.ElSelect_dateType.value,
        endTime: this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).endTime,
        energyType: this.ElSelect_energyType.value,
        nodes: [
          {
            id: this.currentNode.id,
            modelLabel: this.currentNode.modelLabel
          }
        ],
        startTime: this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).startTime,
        tsSchemeId: this.ElSelect_timeScheme.value,
        energyDataType: this.ElSelect_viewMethod.value,
        dimConfigId: this.ElSelect_treeType.value,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };
      if (this.currentNode?.effTimeList?.[0]?.startTime) {
        queryData.timeRanges = [
          {
            objectId: this.currentNode.id,
            objectLabel: this.currentNode.modelLabel,
            timeRanges: this.currentNode.effTimeList
          }
        ];
      }
      commonApi
        .getV2EmergyConsumptionTimeshareCycle(queryData)
        .then(response => {
          if (response.code === 0) {
            const data = this._.get(response, "data[0].energyDataList", []);
            const timeAndTsIds = this._.get(
              response,
              "data[0].timeAndTsIds",
              []
            );

            const xAxisData = [];
            let yAxis = "";
            const seriesData = [];
            const symbol = this._.get(response, "data[0].symbol", "");
            // 坐标名
            yAxis = $T("用{0}量（{1}）", this.energyTypeObj.name, symbol);

            let flag = true;
            var grosSeriesData = {
              name: $T("总量"),
              type: "line",
              symbol: "circle",
              symbolSize: 2,
              smooth: false,
              data: []
            };
            data.forEach(item => {
              const seriesDataItem = {
                name: item.tsId,
                type: "line",
                stack: $T("总量"),
                areaStyle: {
                  opacity: 0.8
                },
                symbol: "circle",
                symbolSize: 2,
                smooth: false,
                data: []
              };
              const seriesDataItemData = item.energyDataList.map(
                (energyDataListItem, index) => {
                  if (flag) {
                    xAxisData.push(
                      this.getAxixs(
                        energyDataListItem.logTime,
                        this.ElSelect_dateType.value
                      )
                    );
                  }
                  const timeAndTsId = timeAndTsIds.find(
                    tsIdItem => tsIdItem.logTime === energyDataListItem.logTime
                  );
                  const tsIds = timeAndTsId?.tsIds ?? [];

                  const hideTooltip = item.tsId && !tsIds.includes(item.tsId);
                  if (
                    [null, undefined, NaN].includes(energyDataListItem.value)
                  ) {
                    return {
                      time: energyDataListItem.logTime,
                      unit: symbol,
                      value: null,
                      hideTooltip: hideTooltip
                    };
                  } else {
                    let obj = {
                      time: energyDataListItem.logTime,
                      unit: symbol,
                      value: Number(energyDataListItem.value).toFixed2(2),
                      hideTooltip: hideTooltip
                    };
                    // if (
                    //   item.energyDataList[index - 1]?.value == null &&
                    //   item.energyDataList[index + 1]?.value == null
                    // ) {
                    //   obj.symbol = "emptyCircle";
                    //   obj.symbolSize = 14;
                    // }
                    return obj;
                  }
                }
              );
              flag = false;
              // 没名称的是总量
              if (!item.tsId) {
                grosSeriesData.data = seriesDataItemData;
              } else {
                seriesDataItem.data = seriesDataItemData;
                seriesData.push(seriesDataItem);
              }
            });
            seriesData.push(grosSeriesData);
            this.CetChart_1.options = {
              toolbox: {
                top: 40,
                right: 30,
                feature: {
                  saveAsImage: {
                    title: $T("保存为图片")
                  }
                }
              },
              tooltip: {
                trigger: "axis",
                // appendToBody: true,
                formatter: function (val) {
                  var list = val || [];
                  var formatterStr = "";
                  var formatVal = _this.getFormatVal();
                  for (var i = 0, len = list.length; i < len; i++) {
                    if (i === 0) {
                      formatterStr += `${
                        formatVal
                          ? `${_this.formatSeriesName(val[i].data)}-${
                              val[i].name
                            }`
                          : val[i].name
                      } `;
                    }
                    const hideTooltip = val[i].data.hideTooltip;
                    if (!hideTooltip) {
                      formatterStr += `<br/> ${val[i].marker} ${
                        val[i].seriesName
                      } : ${val[i].data.actualValue || "--"}(${
                        val[i].data.unit || "--"
                      })`;
                    }
                  }
                  return formatterStr;
                }
              },
              legend: {
                top: "20px"
              },
              grid: {
                top: "80",
                left: "16",
                right: "16",
                bottom: "8",
                containLabel: true
              },
              xAxis: [
                {
                  type: "category",
                  boundaryGap: false,
                  data: xAxisData,
                  splitLine: {
                    show: false
                  }
                }
              ],
              yAxis: [
                {
                  type: "value",
                  name: yAxis,
                  min: 0,
                  max: function (value) {
                    if (value.max < 0) {
                      return 0;
                    }
                    return null;
                  },
                  splitLine: {
                    show: false
                  },
                  nameTextStyle: {
                    align: "left"
                  }
                }
              ],
              series: seriesData
            };
            this.timeSharechartTypeChange();
          }
        });
    },
    // 处理排名top5
    handelTop5() {
      if (
        !this.currentNode ||
        !this.currentNode.children ||
        !this.currentNode.children.length
      ) {
        this.CetChart_2.options = {};
        this.CetChart_2_legend = [];
        return;
      }

      // nodes节点按照modelLabel分组
      let nodes = [];
      const groupNodes = _.groupBy(this.currentNode.children, "modelLabel");
      let timeRanges = [];
      for (let key in groupNodes) {
        let obj = {
          nodes: groupNodes[key].map(item => {
            return {
              id: item.id,
              name: item.name,
              modelLabel: item.modelLabel,
              effTimeList: item.effTimeList
            };
          })
        };
        obj.nodes.forEach(item => {
          if (item?.effTimeList?.[0]?.startTime) {
            timeRanges.push({
              objectId: item.id,
              objectLabel: item.modelLabel,
              timeRanges: item.effTimeList
            });
          }
          nodes.push(item);
        });
      }

      var queryData = {
        aggregationCycle: this.ElSelect_dateType.value,
        endTime: this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).endTime,
        energyTypes: [this.ElSelect_energyType.value],
        nodes: nodes,
        startTime: this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).startTime,
        limit: 5,
        energyDataType: this.ElSelect_viewMethod.value,
        dimConfigId: this.ElSelect_treeType.value,
        rootNode: {
          id: this.rootNode.id,
          modelLabel: this.rootNode.modelLabel
        }
      };
      if (timeRanges?.length) {
        queryData.timeRanges = timeRanges;
      }
      commonApi.getV2EmergyConsumptionTop(queryData).then(response => {
        if (response.code === 0) {
          const data = response.data?.[0]?.energyDataList ?? [];
          let chartData = [];

          chartData = data.map(item => {
            return {
              id: item.objectId,
              modelLabel: item.objectLabel,
              name: item.objectName,
              value:
                queryData.energyDataType == 1
                  ? item.usage
                    ? item.usage.toFixed2(2)
                    : 0
                  : item.total
                  ? item.total.toFixed2(2)
                  : 0,
              symbol: item.symbol || ""
            };
          });
          this.top5Data = this._.cloneDeep(chartData);
          this.filChartData(chartData);
        }
      });
    },
    filChartData(chartData) {
      let sum = 0;
      const pieSeries = [];
      let color = "rgba(180, 180, 180, 0.2)";
      // 最大值
      sum = this._.max(chartData.map(i => Number(i.value))) * 1.5;
      // 图表option整理
      chartData.forEach((v, i) => {
        pieSeries.push({
          name: "",
          type: "pie",
          clockWise: false,
          emphasis: {
            scale: false
          },
          radius: [70 - i * 10 + "%", 65 - i * 10 + "%"],
          center: ["25%", "50%"],
          label: {
            show: false
          },
          data: [
            {
              value: v.value,
              name: v.name,
              symbol: v.symbol
            },
            {
              value: sum ? sum - Number(v.value) : 1,
              name: v.name,
              symbol: v.symbol,
              hideTooltip: true,
              itemStyle: {
                color: "rgba(0,0,0,0)"
              }
            }
          ]
        });
        pieSeries.push({
          name: "",
          type: "pie",
          silent: true,
          z: 1,
          clockWise: false, //顺时加载
          emphasis: {
            scale: false
          },
          radius: [70 - i * 10 + "%", 65 - i * 10 + "%"],
          center: ["25%", "50%"],
          label: {
            show: false
          },
          data: [
            {
              value: 7.5,
              name: v.name,
              hideTooltip: true,
              itemStyle: {
                color: color
              }
            },
            {
              value: 2.5,
              name: v.name,
              hideTooltip: true,
              itemStyle: {
                color: "rgba(0,0,0,0)"
              }
            }
          ]
        });
        v.percent = ((v.value / sum) * 100).toFixed(1) + "%";
      });

      this.CetChart_2.options = {
        tooltip: {
          trigger: "item",
          formatter: function (val) {
            if (val?.data?.hideTooltip) {
              return;
            }
            var value = common.filNum(val.value, 2);
            return `${val.marker} ${val.name} : ${value} ${val.data.symbol}`;
          },
          confine: true
        },
        legend: {
          top: "center",
          right: "21%",
          orient: "vertical",
          icon: "circle",
          // itemGap: 5,
          formatter: function (name) {
            return echarts.format.truncateText(
              name,
              100,
              "14px Microsoft Yahei",
              "…"
            );
          },
          tooltip: {
            show: true
          }
        },
        yAxis: [
          {
            type: "value",
            inverse: true,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            }
          }
        ],
        xAxis: [
          {
            show: false
          }
        ],
        series: pieSeries
      };
      this.CetChart_2_legend = chartData.map(item => {
        return {
          value:
            item.value || item.value === 0 ? item.value + item.symbol : "--",
          name: item.name,
          id: item.id,
          modelLabel: item.modelLabel
        };
      });
    },
    // top5点击
    CetChartClick(val) {
      if (this.ElSelect_energyType.value) {
        if (
          this.ElSelect_energyType.value === 13 ||
          this.ElSelect_energyType.value === 18 ||
          this.ElSelect_analyseType.value === 1
        ) {
          var obj = this.top5Data.find(item => item.name === val.name);
          this.CetTree_1.selectNode = {
            id: obj.id,
            modelLabel: obj.modelLabel,
            tree_id: obj.modelLabel + "_" + obj.id
          };
        }
      }
    },
    // 底部同环比数据
    getContrastData() {
      if (!this.currentNode) {
        return;
      }
      var queryData = {
        cycle: this.ElSelect_dateType.value,
        endTime: this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).endTime,
        energyType: this.ElSelect_energyType.value,
        nodes: [
          {
            id: this.currentNode.id,
            modelLabel: this.currentNode.modelLabel
          }
        ],
        queryType: 3, //0:只查询本期成本值,1 查询同比 2 查询环比 3 同环比都查询
        startTime: this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).startTime,
        energyDataType: this.ElSelect_viewMethod.value,
        dimConfigId: this.ElSelect_treeType.value,
        rootNode: {
          id: this.rootNode.id,
          modelLabel: this.rootNode.modelLabel
        }
      };

      if (this.currentNode?.effTimeList?.[0]?.startTime) {
        queryData.timeRanges = [
          {
            objectId: this.currentNode.id,
            objectLabel: this.currentNode.modelLabel,
            timeRanges: this.currentNode.effTimeList
          }
        ];
      }
      commonApi.getV2EmergyConsumptionPeriod(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data[0]", {});
          var tbRatio = "";
          var hbRatio = "";
          var tbRatioType = "--";
          var hbRatioType = "--";
          const symbol = data.symbol || "--";
          if (data.value && data.tbValue) {
            tbRatio = (
              (Math.abs(data.value - data.tbValue) / data.tbValue) *
              100
            ).toFixed2(2);
          }
          if (tbRatio && data.value > data.tbValue) {
            tbRatioType = true;
          } else if (tbRatio && data.value < data.tbValue) {
            tbRatioType = false;
          }
          this.comparison1 = {
            title: $T("同比"),
            type: 1,
            ratio: tbRatio ? tbRatio + "%" : "--",
            ratioType: tbRatioType,
            value: data.value ? Number(data.value).toFixed2(2) + symbol : "--",
            oldValue: data.tbValue
              ? Number(data.tbValue).toFixed2(2) + symbol
              : "--"
          };
          if (data.value && data.hbValue) {
            hbRatio = (
              (Math.abs(data.value - data.hbValue) / data.hbValue) *
              100
            ).toFixed2(2);
          }
          if (hbRatio && data.value > data.hbValue) {
            hbRatioType = true;
          } else if (hbRatio && data.value < data.hbValue) {
            hbRatioType = false;
          }
          this.comparison2 = {
            title: $T("环比"),
            type: 1,
            ratio: hbRatio ? hbRatio + "%" : "--",
            ratioType: hbRatioType,
            value: data.value ? Number(data.value).toFixed2(2) + symbol : "--",
            oldValue: data.hbValue
              ? Number(data.hbValue).toFixed2(2) + symbol
              : "--"
          };
        }
      });
    },
    // 获取分时方案
    getTimeShareScheme(noGetData) {
      if (!this.currentNode.id || !this.ElSelect_energyType.value) {
        return;
      }
      commonApi
        .getTimeShareScheme(
          {
            rootNode: {
              id: this.rootNode.id,
              modelLabel: this.rootNode.modelLabel
            },
            node: {
              id: this.currentNode.id,
              modelLabel: this.currentNode.modelLabel
            },
            energyType: this.ElSelect_energyType.value
          },
          this.ElSelect_energyType.value
        )
        .then(res => {
          if (res.code === 0) {
            this.ElOption_timeScheme.options_in =
              this._.get(res, "data", []) || [];
            if (this.ElOption_timeScheme.options_in.length > 0) {
              this.ElSelect_timeScheme.value =
                this.ElOption_timeScheme.options_in[0].timesharefeerecord_id;
            } else {
              this.ElSelect_timeScheme.value = null;
            }
            if (!noGetData) {
              // 获取完分时方案后请求分时能耗
              this.getData();
            }
          }
        });
    },
    // 处理分时占比
    handelProportion() {
      const queryData = {
        aggregationCycle: this.ElSelect_dateType.value,
        endTime: this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).endTime,
        energyType: this.ElSelect_energyType.value,
        node: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel
        },
        startTime: this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).startTime,
        tsSchemeId: this.ElSelect_timeScheme.value,
        energyDataType: this.ElSelect_viewMethod.value,
        dimConfigId: this.ElSelect_treeType.value,
        rootNode: {
          id: this.rootNode.id,
          modelLabel: this.rootNode.modelLabel
        }
      };
      if (this.currentNode?.effTimeList?.[0]?.startTime) {
        queryData.timeRanges = this.currentNode.effTimeList;
      }
      commonApi.getV2EmergyConsumptionTimeshare(queryData).then(response => {
        if (response.code === 0) {
          const data = [];
          const grossObj = this._.get(response, "data", []).filter(
            item => !item.tsId
          );
          let totalNum = 0;
          totalNum =
            this._.get(grossObj, "[0].energyDataList[0].value", 0) || 0;
          this._.get(response, "data", []).forEach(item => {
            if (item.tsId) {
              data.push({
                value: this._.get(item, "energyDataList[0].value", 0)
                  ? Number(
                      this._.get(item, "energyDataList[0].value", 0).toFixed2(2)
                    )
                  : 0,
                name: item.tsId || "",
                symbol: item.symbol || ""
              });
            }
          });
          this.filProportionChartData(data, totalNum);
          // 处理峰谷比
          this.handelTimeComparisonRule(data);
        }
      });
    },
    filProportionChartData(data, totalNum) {
      var _this = this;
      var sumNum = totalNum.toFixed2(2);
      _this.CetChart_2.options = {
        tooltip: {
          show: true,
          transitionDuration: 0
        },
        legend: {
          type: "scroll",
          top: "center",
          right: "30%",
          orient: "vertical",
          formatter(name) {
            return echarts.format.truncateText(
              name,
              _this.isEN ? 60 : 100,
              "14px Microsoft Yahei",
              "…"
            );
          },
          tooltip: {
            show: true
          }
        },
        series: [
          {
            name: "",
            type: "pie",
            radius: ["50%", "80%"],
            center: ["24%", "50%"],
            roseType: "radius",
            label: {
              show: true,
              position: "center",
              align: "center",
              formatter: function (val) {
                return _this.isEN
                  ? `{c|${sumNum}${val.data.symbol}}\n{b|}\n{a|${$T("总")} ${
                      _this.energyTypeObj && _this.energyTypeObj.name
                    }\n${$T("用量")}}`
                  : `{c|${sumNum}${val.data.symbol}}\n{b|}\n{a|总用${
                      _this.energyTypeObj && _this.energyTypeObj.name
                    }量}`;

                // 只输出一次总用电量 要不然会出现重影
                /* if (val.dataIndex === 0) {
                  return `{c|${sumNum}${val.data.symbol}}\n{b|}\n{a|总用${_this.energyTypeObj && _this.energyTypeObj.name}量}`;
                } else {
                  return "";
                } */
              },
              rich: {
                a: {
                  fontSize: 14,
                  padding: [0, 0, 5, 0]
                },
                b: {
                  height: 1,
                  width: 80
                },
                c: {
                  fontSize: 14,
                  fontWeight: "bold",
                  padding: [0, 0, 5, 0]
                }
              }
            },
            emphasis: {
              scale: false,
              label: {
                show: true
              }
            },
            data: data
          }
        ]
      };
      _this.CetChart_2_legend = data.map(item => {
        if (Number(item.value) && Number(sumNum)) {
          return {
            value:
              ((Number(item.value) / Number(sumNum)) * 100).toFixed2(2) +
              "% " +
              item.value.toFixed2(2) +
              item.symbol
          };
        } else {
          return { value: "0 % " + item.value + item.symbol };
        }
      });
    },
    /*
     峰谷比展示
      一组数据展示 --
      两组数据先 匹配自定义配置方案，如果有则按配置方案展示，无则第一位比第二位
      三组及以上 匹配自定义配置方案，如果有则按配置方案展示，无则第一位和第二位；第一位和第三位展示
      匹配规则 配置方案中的名称只要存在返回的数据中即为匹配成功
    */
    handelTimeComparisonRule(data) {
      if (data.length > 1) {
        const rule = this.matchingRule(data);
        if (rule) {
          this.customTimeComparisonShow(data, rule);
        } else {
          this.defaultTimeComparisonShow(data);
        }
      } else {
        this.comparison1 = {
          title: "--",
          type: 2,
          ratio: "--",
          ratioType: "--",
          value: "",
          oldValue: ""
        };
        this.comparison2 = {
          title: "--",
          type: 2,
          ratio: "--",
          ratioType: "--",
          value: "",
          oldValue: ""
        };
      }
    },
    // 匹配方案
    matchingRule(data) {
      if (
        this.customTimeComparisonRule &&
        this.customTimeComparisonRule.length
      ) {
        let rule = false;
        const nameArr = data.map(item => item.name);
        rule = this.customTimeComparisonRule.find(item => {
          if (
            nameArr.indexOf(item.denominator2) !== -1 ||
            nameArr.indexOf(item.denominator1) !== -1 ||
            nameArr.indexOf(item.molecule1) !== -1 ||
            nameArr.indexOf(item.molecule2) !== -1
          ) {
            return true;
          }
        });
        return rule;
      } else {
        return false;
      }
    },
    // 无方案展示
    defaultTimeComparisonShow(data) {
      if (data.length === 2) {
        if (data[0].value && data[1].value) {
          this.comparison1 = {
            title: `${data[0].name}${data[1].name} ${$T("比")}`,
            type: 2,
            ratio: `${((data[0].value / data[1].value) * 100).toFixed2(1)}%`,
            ratioType: "--",
            value: "",
            oldValue: ""
          };
          this.comparison2 = {
            title: `${data[1].name}${data[0].name} ${$T("比")}`,
            type: 2,
            ratio: `${((data[1].value / data[0].value) * 100).toFixed2(1)}%`,
            ratioType: "--",
            value: "",
            oldValue: ""
          };
        } else {
          // 不存在比值
          this.comparison1 = {
            title: `${data[0].name}${data[1].name} ${$T("比")}`,
            type: 2,
            ratio: `--`,
            ratioType: "--",
            value: "",
            oldValue: ""
          };
          this.comparison2 = {
            title: `${data[1].name}${data[0].name} ${$T("比")}`,
            type: 2,
            ratio: `--`,
            ratioType: "--",
            value: "",
            oldValue: ""
          };
        }
      } else {
        // 左
        if (data[0].value && data[1].value) {
          this.comparison1 = {
            title: `${data[0].name}${data[1].name} ${$T("比")}`,
            type: 2,
            ratio: `${((data[0].value / data[1].value) * 100).toFixed2(1)}%`,
            ratioType: "--",
            value: "",
            oldValue: ""
          };
        } else {
          // 不存在比值
          this.comparison1 = {
            title: `${data[0].name}${data[1].name} ${$T("比")}`,
            type: 2,
            ratio: `--`,
            ratioType: "--",
            value: "",
            oldValue: ""
          };
        }
        // 右
        if (data[0].value && data[2].value) {
          this.comparison2 = {
            title: `${data[0].name}${data[2].name} ${$T("比")}`,
            type: 2,
            ratio: `${((data[0].value / data[2].value) * 100).toFixed2(1)}%`,
            ratioType: "--",
            value: "",
            oldValue: ""
          };
        } else {
          // 不存在比值
          this.comparison2 = {
            title: `${data[0].name}${data[2].name} ${$T("比")}`,
            type: 2,
            ratio: `--`,
            ratioType: "--",
            value: "",
            oldValue: ""
          };
        }
      }
    },
    // 有方案展示
    customTimeComparisonShow(data, rule) {
      var denominator1;
      var denominator2;
      var molecule1;
      var molecule2;
      var ratio1 = "--";
      var ratio2 = "--";
      denominator1 = data.find(item => item.name === rule.denominator1);
      denominator2 = data.find(item => item.name === rule.denominator2);
      molecule1 = data.find(item => item.name === rule.molecule1);
      molecule2 = data.find(item => item.name === rule.molecule2);
      if (denominator1 && denominator1.value) {
        if (molecule1.value) {
          ratio1 =
            ((molecule1.value / denominator1.value) * 100).toFixed2(1) + "%";
        }
      }
      if (denominator2 && denominator2.value) {
        if (molecule2.value) {
          ratio2 =
            ((molecule2.value / denominator2.value) * 100).toFixed2(1) + "%";
        }
      }
      this.comparison1 = {
        title: `${rule.molecule1}${rule.denominator1} ${$T("比")}`,
        type: 2,
        ratio: ratio1,
        ratioType: "--",
        value: "",
        oldValue: ""
      };
      this.comparison2 = {
        title: `${rule.molecule2}${rule.denominator2} ${$T("比")}`,
        type: 2,
        ratio: ratio2,
        ratioType: "--",
        value: "",
        oldValue: ""
      };
    },
    //过滤获取图表x轴对应值
    getAxixs(date, type) {
      date = new Date(date);
      var y = date.getFullYear();
      var M = date.getMonth() + 1;
      var d = date.getDate();
      var h = date.getHours();
      var m = date.getMinutes();
      if (M < 10) {
        M = "0" + M;
      }
      if (d < 10) {
        d = "0" + d;
      }
      if (h < 10) {
        h = "0" + h;
      }
      if (m < 10) {
        m = "0" + m;
      }
      if (type === 12) {
        return h + ":" + m;
      } else if (type === 14) {
        return d + "";
      } else if (type === 17) {
        return M + "";
      } else {
        return y + "-" + M + "-" + d;
      }
    },
    // 获取查询开始结束时间
    getQueryTime(time, cycle) {
      var startTime, endTime;
      switch (cycle) {
        case 12:
          startTime = this.$moment(time).startOf("day").valueOf();
          endTime = this.$moment(time).endOf("day").valueOf() + 1;
          break;
        case 14:
          startTime = this.$moment(time).startOf("month").valueOf();
          endTime = this.$moment(time).endOf("month").valueOf() + 1;
          break;
        case 17:
          startTime = this.$moment(time).startOf("year").valueOf();
          endTime = this.$moment(time).endOf("year").valueOf() + 1;
          break;
        default:
          break;
      }
      return {
        startTime,
        endTime
      };
    },
    getFormatVal() {
      //17年，14月，12日，20自定义
      var cycle = this.ElSelect_dateType.value;
      switch (cycle) {
        case 17:
          return "YYYY";
        case 14:
          return "YYYY-MM";
        case 12:
          return "YYYY-MM-DD";
        default:
          return false;
      }
    },
    // 时间转换
    formatSeriesName(data) {
      var formatVal = this.getFormatVal();
      var tbArr = {
        17: "year",
        14: "month",
        12: "day"
      };
      var hbArr = {
        17: "year",
        14: "year",
        12: "month"
      };
      if (data.tb) {
        return this.$moment(data.time)
          .subtract(1, tbArr[this.ElSelect_dateType.value])
          .format(formatVal);
      } else if (data.hb) {
        return this.$moment(data.time)
          .subtract(1, hbArr[this.ElSelect_dateType.value])
          .format(formatVal);
      } else {
        return this.$moment(data.time).format(formatVal);
      }
    },
    ElSelect_statisticsType_change_out() {
      this.setPointPickerType();
    },
    async datePointPicker_change(val) {
      if (
        this.datePointPicker.value?.length &&
        this.ElSelect_treeType.value > 0
      ) {
        //多维度时间变化的时候需要去更新节点树
        await this.getTreeData2(true);
      }
      this.logTimes = val;
    },
    async setPointPickerType() {
      const dataTypes = {
        17: { type: "years", selectNumMax: 20 },
        14: { type: "months", selectNumMax: 24 },
        13: { type: "weeks", selectNumMax: 20 },
        12: { type: "dates", selectNumMax: 60 },
        7: { type: "times", selectNumMax: 20 }
      };
      this.datePointPicker.type =
        dataTypes[this.ElSelect_statisticsType.value].type;
      this.datePointPicker.selectNumMax =
        dataTypes[this.ElSelect_statisticsType.value].selectNumMax;

      let value = [];
      if (this.ElSelect_statisticsType.value === 12 && this.getDefaultData) {
        for (let i = 0; i < 7; i++) {
          value.push(
            this.$moment().subtract(i, "days").startOf("day").valueOf()
          );
        }
        this.getDefaultData = false;
      }
      this.datePointPicker.value = value;
      await this.datePointPicker_change(value);
    },
    // 选择能耗详情查询图表数据
    getChartData4() {
      if (!this.currentNode) {
        return;
      }
      this.CetChart_1.options = {};
      const queryData = {
        aggregationCycle: this.ElSelect_dateType.value,
        endTime: this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).endTime,
        energyType: this.ElSelect_energyType.value,
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel,
        startTime: this.getQueryTime(
          this.elDate.value,
          this.ElSelect_dateType.value
        ).startTime,
        dimConfigId: this.ElSelect_treeType.value,
        rootNode: {
          id: this.rootNode.id,
          modelLabel: this.rootNode.modelLabel
        }
      };
      if (this.currentNode?.effTimeList?.[0]?.startTime) {
        queryData.timeRanges = this.currentNode.effTimeList;
      }
      commonApi.getV2EmergyConsumptionDetail(queryData).then(response => {
        if (response.code === 0) {
          let data = this._.get(response, "data", {});
          this.filChartData4(data);
        }
      });
    },
    // 通过数据产生图表展示
    filChartData4(data) {
      let vm = this;
      this.CetChart_1.options = {
        toolbox: {
          top: 40,
          right: 30,
          feature: {
            saveAsImage: {
              title: $T("保存为图片")
            }
          }
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          },
          // appendToBody: true,
          formatter: function (val) {
            const list = val || [];
            let formatterStr = "";
            for (let i = 0, len = list.length; i < len; i++) {
              const value = vm.formatNumFixed(val[i].data.actualValue, 2, true);
              if (i === 0) {
                let disTime = vm.getAxixs(
                  list[i].data.time,
                  vm.ElSelect_dateType.value
                );
                formatterStr += `${disTime}`;
              }
              formatterStr += `<br/>${val[i].marker}${
                val[i].seriesName
              } : ${value}(${val[i].data.unit || "--"})`;
            }
            return formatterStr;
          }
        },
        grid: {
          top: "80",
          left: "16",
          right: "16",
          bottom: "8",
          containLabel: true
        },
        legend: {
          data: []
        },
        xAxis: {
          type: "category",
          data: [],
          // 将x轴放至最下方负数
          axisLine: {
            show: true,
            onZero: false
          }
        },
        yAxis: {
          type: "value",
          max: function (value) {
            if (value.max < 0) {
              return 0;
            }
            return null;
          },
          nameTextStyle: {
            align: "left"
          }
        },
        series: []
      };
      let legend = [];
      let xAxisData = [];
      let seriesData = [];
      //此处调整为没有方案不展示对应的分摊信息，故增加一个参数ndeExistType 0-不展示分摊，1-公共分摊，2-损耗分摊，3-公共分摊+损耗分摊
      let testSeriesData = [
        {
          name: $T("总能耗"),
          type: "line",
          showSymbol: false,
          color: "#158AFF",
          data: []
        },
        {
          name: $T("能耗"),
          type: "line",
          stack: "Total",
          showSymbol: false,
          color: "rgba(74,253,167,0.20)",
          areaStyle: {},
          data: []
        },
        {
          name: $T("公共分摊能耗"),
          type: "line",
          stack: "Total",
          showSymbol: false,
          color: "rgba(255,209,47,0.20)",
          areaStyle: {},
          data: []
        },
        {
          name: $T("损耗分摊能耗"),
          type: "line",
          stack: "Total",
          showSymbol: false,
          areaStyle: {},
          color: "rgba(128,83,255,0.20)",
          data: []
        }
      ];
      const testLegend = [
        { name: $T("总能耗") },
        { name: $T("能耗"), icon: "rect", itemStyle: { color: "#4AFDA7" } },
        {
          name: $T("公共分摊能耗"),
          icon: "rect",
          itemStyle: { color: "#FFD12F" }
        },
        {
          name: $T("损耗分摊能耗"),
          icon: "rect",
          itemStyle: { color: "#8053FF" }
        }
      ];
      var cycle = this.ElSelect_dateType.value;
      if (
        data.energyConsumptionDataList &&
        data.energyConsumptionDataList.length > 0
      ) {
        let num = _.get(data, "ndeExistType", 4);
        legend = this.getList(testLegend, num);
        seriesData = this.getList(_.cloneDeep(testSeriesData), num);
        data.energyConsumptionDataList.forEach(item => {
          xAxisData.push(this.getAxixs(item.logTime, cycle));
          // 堆叠面积图值为null时，需要改为0，确保线段连续
          seriesData[0].data.push({
            name: this.currentNode.name,
            actualValue: item.total, //总能耗
            value: item.total ?? 0,
            unit: data.symbol,
            time: item.logTime
          });
          seriesData[1].data.push({
            name: this.currentNode.name,
            actualValue: item.usage, //能耗
            value: item.usage ?? 0,
            unit: data.symbol,
            time: item.logTime
          });
          if (num !== 0) {
            const text = num === 2 ? "loss" : "commonShare";
            seriesData[2].data.push({
              name: this.currentNode.name,
              actualValue: item[text],
              value: item[text] ?? 0,
              unit: data.symbol,
              time: item.logTime
            });
          }
          if ([3, 4].includes(num)) {
            seriesData[3].data.push({
              name: this.currentNode.name,
              actualValue: item.loss, //损耗分摊
              value: item.loss ?? 0,
              unit: data.symbol,
              time: item.logTime
            });
          }
        });
      }
      let unit = data.symbol;
      if (this.energyTypeObj) {
        this.CetChart_1.options.yAxis.name = $T(
          "用{0}量（{1}）",
          this.energyTypeObj.name || "--",
          unit || "--"
        );
      } else {
        this.CetChart_1.options.yAxis.name = "--";
      }
      this.CetChart_1.options.xAxis.data = xAxisData;
      this.CetChart_1.options.series = seriesData;
      this.CetChart_1.options.legend.data = legend;
    },
    getList(data, num) {
      return [3, 4].includes(num)
        ? data
        : [0, 1].includes(num)
        ? data.slice(0, 2 + num)
        : [...data.slice(0, 2), data[3]];
    },
    getFixedValue(value, decs) {
      if (value || value === 0) {
        return Number(value).toFixed(decs);
      } else {
        return "--";
      }
    },
    // 处理过长数据展示千位数格式化
    formatNumFixed(value, precision, type = false) {
      if (value || value === 0) {
        if (!_.isNumber(value)) {
          //先转换成数字
          value = parseFloat(value);
        }
        if (type) {
          return common.formatNum(value.toFixed(precision));
        }
        return value.toFixed(precision);
      } else {
        return "--";
      }
    },
    showNoticeProgress(res) {
      this.$store.dispatch("importProgress/noticeProgress", {
        vm: this,
        initialProcessInfo: res.data
      });
    },
    effTimeFormat(data) {
      const { effTimeList } = data;
      const customEnergyParams = this.$refs.customCycleEnergy?.getParams();
      const cycle = customEnergyParams?.aggregationCycle ?? 12;

      let dateFormat = "YYYY-MM-DD";
      if (this.ElSelect_dateType.value === -1) {
        switch (cycle) {
          case 17:
            dateFormat = "yyyy";
            break;
          case 14:
            dateFormat = "yyyy-MM";
            break;
          case 7:
            dateFormat = "MM-DD HH:mm";
            break;
          default:
            dateFormat = "yyyy-MM-DD";
            break;
        }
      }

      return effTimeList
        .map(({ startTime, endTime }) => {
          return `${this.$moment(startTime).format(dateFormat)}~${this.$moment(
            endTime
          ).format(dateFormat)}`;
        })
        .join(",");
    }
  },
  mounted() {
    const obj = {
      bar: 1,
      line: 2
    };
    this.timeShareChartType =
      obj[this.energyQueryAndAnalysisTimeShareChartType] ?? 2;
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.page {
  height: 100%;
}
.datePicker {
  display: inline-block;
  width: 150px;
}
.proportion {
  @include margin_top(J3);
  .proportion-item {
    position: relative;
    .itemBox-empty {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      @include font_size(H3);
    }
    &.comparison1 {
      background: url("./assets/img3.png") no-repeat 10% 0%;
      @include background_color(BG1);
      background-size: 50% 95%;
      .ratio {
        position: absolute;
        top: 20%;
        left: 52px;
        right: 200px;
        font-size: 18px;
        font-weight: bold;
        text-align: center;
      }
      .ratio.up {
        color: #ec3b3b;
        .arrow {
          display: inline-block;
          width: 15px;
          height: 20px;
          background: url("./assets/up.png") no-repeat center center;
          background-size: 10px;
          transform: translateY(3px);
        }
      }
      .ratio.down {
        color: #5adb7a;
        .arrow {
          display: inline-block;
          width: 15px;
          height: 20px;
          background: url("./assets/down.png") no-repeat left center;
          background-size: 10px;
          transform: translateY(3px);
        }
      }
      .valueBox {
        display: block;
        float: right;
        text-align: right;
        margin-top: 10%;
        margin-right: 15px;
        line-height: 30px;
        .text {
          margin-top: 30px;
        }
        .value {
          display: inline-block;
          width: 100px;
          line-height: 18px;
        }
      }
    }
    &.comparison1-bg-light {
      background: url("./assets/img3-light.png") no-repeat 10% 0%;
    }
    &.comparison2 {
      background-size: 38%;
      .ratio {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 18px;
        font-weight: bold;
      }
      .valueBox {
        display: none;
      }
    }
    &.comparison2_1 {
      background: url("./assets/img1.png") no-repeat center center;
      @include background_color(BG1);
    }
    &.comparison2_2 {
      background: url("./assets/img2.png") no-repeat center center;
      @include background_color(BG1);
    }
    .CetChart_2_legend1 {
      position: absolute;
      left: 80%;
      top: 50%;
      right: 0%;
      transform: translate(0, -50%);
      & > div {
        height: 24px;
        line-height: 24px;
        cursor: pointer;
      }
    }
    .CetChart_2_legend2 {
      position: absolute;
      left: 70%;
      top: 50%;
      right: 0%;
      transform: translate(0, -50%);
      & > div {
        height: 24px;
        line-height: 24px;
        cursor: pointer;
      }
    }
    .no-number {
      text-align: center;
      padding-top: 80px;
      & > span {
        font-size: 18px;
      }
    }
  }
}
.tabs {
  box-sizing: border-box;
  :deep(.el-tabs__header.is-top) {
    margin-bottom: 0;
  }
}
.chartHandle {
  z-index: 1;
}
.cetTree {
  .focus-node {
    @include font_color(ZS);
    @include background_color(BG4);
  }
  :deep(
      .el-tree-node:has(.is-checked)
        > .el-tree-node__content
        .el-checkbox__inner
    ) {
    @include border_color(ZS);
    @include background_color(ZS);
  }
  .custom-tree-node {
    .icon {
      visibility: hidden;
      position: absolute;
      right: 0;
      top: 4px;
      @include background_color(BG1);
      .edit {
        @include font_color(ZS);
      }
      .delete {
        @include font_color(Sta3);
      }
    }
  }
  :deep(.el-tree-node__content) {
    position: relative;
    &:hover {
      .custom-tree-node .icon {
        visibility: visible;
      }
    }
  }
}
.legend-model {
  position: absolute;
  top: 0;
  right: 40px;
  z-index: 1001;
}
.cet-aside :deep(.cet-content-aside-container) {
  background-color: var(--BG1);
  border-radius: var(--Ra);
}
</style>
