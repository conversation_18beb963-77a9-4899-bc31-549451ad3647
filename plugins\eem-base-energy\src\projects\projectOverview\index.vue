<template>
  <div class="fullheight flex-col flex">
    <div class="header">
      <div class="title">{{ $T("能耗概览") }}</div>
      <div class="filter">
        <customElSelect
          :prefix_in="$T('所属项目')"
          v-model="ElSelect_project.value"
          v-bind="ElSelect_project"
          v-on="ElSelect_project.event"
          class="mr-J3"
          v-show="ElOption_project.options_in?.length > 1"
        >
          <ElOption
            v-for="item in ElOption_project.options_in"
            :key="item[ElOption_project.key]"
            :label="item[ElOption_project.label]"
            :value="item[ElOption_project.value]"
            :disabled="item[ElOption_project.disabled]"
          ></ElOption>
        </customElSelect>
        <el-tooltip :content="path" :visible-arrow="false" placement="top">
          <div class="eem-cascader mr-J3">
            <span class="eem-cascader-label">{{ $T("选择节点") }}</span>
            <el-cascader
              class="cascader"
              ref="cascader"
              v-model="nodeValue"
              :options="nodeOptions"
              :props="nodeProps"
              :show-all-levels="false"
              popper-class="projectOverview-cascader-tree"
              @change="handleNodeChange"
            ></el-cascader>
          </div>
        </el-tooltip>
        <CetDateSelect
          v-bind="CetDateSelect_time"
          v-on="CetDateSelect_time.event"
        ></CetDateSelect>
      </div>
    </div>
    <div class="flex-auto main">
      <div class="p-J4 bg-BG1 side flex-col flex">
        <div class="button-arrow" :class="{ disable: left }" @click="onUpClick">
          <i class="el-icon-arrow-up"></i>
        </div>
        <el-carousel
          height="100%"
          direction="vertical"
          indicator-position="none"
          arrow="never"
          :interval="15000"
          class="mt-J3 mb-J3"
          ref="sideCarousel"
        >
          <el-carousel-item
            v-for="(list, index) in consumptionList"
            :key="index"
          >
            <div class="carosal-page">
              <div v-for="(item, i) in list" :key="i">
                <OverviewCard
                  :type="getType(i)"
                  :item="item"
                  :cycle="dateObj.type"
                ></OverviewCard>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
        <div
          class="button-arrow"
          :class="{ disable: left }"
          @click="ondownClick"
        >
          <i class="el-icon-arrow-down"></i>
        </div>
      </div>
      <div class="content">
        <div
          class="filter bg-BG1"
          :class="{ 'justify-end': this.energyShowType === 2 }"
        >
          <el-radio-group
            v-if="this.energyShowType === 1"
            v-model="selectRadio"
            class="radio fl"
          >
            <el-radio-button
              v-for="item in radioList"
              :key="item.energytype"
              :label="item.energytype"
            >
              <el-tooltip
                :content="item.name"
                :visible-arrow="false"
                placement="top"
              >
                <span class="text-ellipsis radio-item">
                  {{ item.name }}
                </span>
              </el-tooltip>
            </el-radio-button>
          </el-radio-group>
          <customElSelect
            v-else
            v-model="ElSelect_2.value"
            v-bind="ElSelect_2"
            v-on="ElSelect_2.event"
            :prefix_in="$T('能源类型')"
            popper-class="projectOverview-type-select"
            class="fr"
          >
            <ElOption
              v-for="item in ElOption_2.options_in"
              :key="item[ElOption_2.key]"
              :label="item[ElOption_2.label]"
              :value="item[ElOption_2.value]"
              :title="item[ElOption_2.label]"
              :disabled="item[ElOption_2.disabled]"
            ></ElOption>
          </customElSelect>
        </div>
        <div class="charts">
          <chartCard
            :node="checkedNode"
            :dateObj="dateObj"
            :eneryType="eneryType"
            :rootNode_in="rootNode"
          />
        </div>
        <div class="analysis rounded-Ra bg-BG1">
          <AnalysisBlock
            :node="checkedNode"
            :dateObj="dateObj"
            :eneryType="eneryType"
            :rootNode_in="rootNode"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import OverviewCard from "./components/OverviewCard";
import AnalysisBlock from "./components/AnalysisBlock";
import chartCard from "./components/chartCard.vue";
import customApi from "@/api/custom";

export default {
  name: "projectOverview",
  components: { OverviewCard, AnalysisBlock, chartCard },
  computed: {
    eneryType() {
      return this.energyShowType === 1
        ? this.selectRadio
        : this.ElSelect_2.value;
    },
    left() {
      return [0, 1].includes(this.consumptionList.length);
    },
    rootNode() {
      const id = this.ElSelect_project.value;
      const list = this.ElOption_project.options_in;
      return list.find(item => item.id === id);
    }
  },
  data() {
    return {
      energyShowType: 1,
      selectRadio: null,
      radioList: null,
      checkedNode: null,
      nodeValue: null,
      nodeOptions: null,
      nodeProps: {
        label: "name",
        value: "tree_id",
        checkStrictly: true
      },
      dateTypeOpt: {
        5: 17,
        3: 14,
        1: 12
      },
      dateObj: {
        type: 14,
        time: this.getToday()
      },
      CetDateSelect_time: {
        value: { dateType: "3", value: new Date().getTime() },
        typeList: ["day", "month", "year"],
        layout: "button",
        align: "right",
        yearDateOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          },
          shortcuts: [
            {
              text: this.$T("今年"),
              onClick(picker) {
                picker.$emit("pick", new Date());
              }
            }
          ]
        },
        monthDateOption: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          },
          shortcuts: [
            {
              text: this.$T("当月"),
              onClick(picker) {
                picker.$emit("pick", new Date());
              }
            }
          ]
        },
        dayDateOption: {
          firstDayOfWeek: 1,
          disabledDate(time) {
            return time.getTime() > Date.now();
          },
          shortcuts: [
            {
              text: this.$T("今天"),
              onClick(picker) {
                picker.$emit("pick", new Date());
              }
            }
          ]
        },
        event: {
          date_out: this.CetDateSelect_time_date_out
        }
      },
      ElSelect_2: {
        value: null,
        event: {}
      },
      ElOption_2: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      consumptionList: [],
      path: "",
      ElSelect_project: {
        value: 0,
        style: {},
        event: {
          change: this.ElSelect_project_change_out
        }
      },
      ElOption_project: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  methods: {
    getType(i) {
      return i % 2 === 0 ? "one" : "two";
    },
    getToday() {
      let start = this.$moment().startOf("month").valueOf();
      let end = this.$moment().endOf("month").valueOf() + 1;
      return [start, end];
    },
    onUpClick() {
      if (this.left) return;
      this.$refs.sideCarousel.prev();
    },
    ondownClick() {
      if (this.left) return;
      this.$refs.sideCarousel.next();
    },
    async getConsumption() {
      let params = {
        aggregationCycle: this.dateObj.type,
        endTime: this.dateObj.time[1],
        node: {
          id: this.checkedNode?.id,
          modelLabel: this.checkedNode?.modelLabel
        },
        startTime: this.dateObj.time[0],
        energyDataType: 4
      };
      const { data = [] } = await customApi.getConsumption(params);
      this.consumptionList = this.splitArrChunks(data, 4);
    },
    splitArrChunks(arr, chunk) {
      let result = [];
      let temp = null;
      for (let i = 0; i < arr?.length; i += chunk) {
        temp = arr.slice(i, i + chunk);
        result.push(temp);
      }
      return result;
    },
    async getNodeTree() {
      const queryData = {
        plugin: "eem-base",
        business: "energy-analysis",
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };
      const { data = [] } = await customApi.getBusinessTree(queryData);
      let optionList = this.getOption(data);
      this.nodeOptions = optionList;
      let obj = this._.find(this.dataTransform(data), ["childSelectState", 1]);
      this.checkedNode = obj;
      this.nodeValue = [];
      this.findParent(optionList, obj?.tree_id, this.nodeValue);
      this.path = this.getPath(this.nodeValue);
    },
    findParent(data, target, result) {
      for (let item of data) {
        if (item.tree_id === target) {
          result.unshift(item.tree_id);
          return true;
        }
        if (item.children && item.children.length > 0) {
          let isFind = this.findParent(item.children, target, result);
          if (isFind) {
            result.unshift(item.tree_id);
            return true;
          }
        }
      }
      return false;
    },
    getOption(data) {
      let list = data || [];
      for (let i = 0; i < list.length; i++) {
        list[i].disabled = list[i].childSelectState === 2;
        if (!_.isEmpty(list[i].children)) {
          this.getOption(list[i].children);
        }
      }
      return list;
    },
    dataTransform(array) {
      const cloneData = this._.cloneDeep(array);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
        }
        return arr;
      };
      return expanded(cloneData);
    },
    async getEngryType() {
      const { data = [] } = await customApi.getEneryType();
      let obj = data.find(i => i.id === 2);
      if (data.length > 6) {
        this.energyShowType = 2;
        this.ElOption_2.options_in = data;
        this.ElSelect_2.value = obj ? obj.energytype : data[0]?.energytype;
      } else {
        this.energyShowType = 1;
        this.radioList = data;
        this.selectRadio = obj ? obj.energytype : data[0]?.energytype;
      }
    },
    handleNodeChange(val) {
      this.checkedNode = this.$refs.cascader.getCheckedNodes()[0]?.data;
      this.path = this.getPath(val);
      this.getConsumption(val);
    },
    getPath(list) {
      let data = this.nodeOptions || [];
      let str = [];
      list.forEach(item => {
        let obj = data.find(i => i.tree_id === item);
        if (obj) {
          str.push(obj.name);
          data = obj.children || [];
        }
      });
      return str.join("/");
    },
    CetDateSelect_time_date_out(time, type) {
      this.dateObj = {
        type: this.dateTypeOpt[type],
        time: [time[0], time[1] + 1]
      };
      this.getConsumption();
    },
    async init() {
      await this.getRootNode();
      await this.getNodeTree();
      await this.getEngryType();
      await this.getConsumption();
    },
    ElSelect_project_change_out() {
      this.getNodeTree();
    },
    async getRootNode() {
      this.ElOption_project.options_in = [];
      const res = await customApi.rootNode();
      const data = res.data || [];
      this.ElOption_project.options_in = data;
      this.ElSelect_project.value = data?.[0]?.id;
    }
  },
  mounted() {
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.header {
  padding-top: 4px;
  padding-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    font-weight: bold;
    font-size: 18px;
  }
  .filter {
    display: flex;
  }

  :deep(.el-button-group) {
    display: flex !important;
  }
}
.main {
  display: flex;
}
.side {
  width: 370px;
  height: 100%;
  box-sizing: border-box;
  border-radius: 4px;
  .disable {
    cursor: not-allowed;
    i {
      @include font_color(T6);
    }
  }
}
.content {
  width: calc(100% - 386px);
  height: 100%;
  margin-left: 16px;
  .filter {
    height: 80px;
    padding-left: 24px;
    padding-right: 24px;
    border-radius: 4px;
    display: flex;
    align-items: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .charts {
    margin-top: 16px;
    height: calc(40% - 56px);
  }
  .analysis {
    margin-top: 16px;
    height: calc(60% - 56px);
  }
}

.carosal-page {
  height: 100%;
  display: grid;
  grid-template-rows: repeat(4, 1fr);
  row-gap: 16px;
}

.el-carousel {
  height: 100%;
}

.button-arrow {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 16px;
  cursor: pointer;
  @include background_color(BG2);
  border-radius: 12px 12px 12px 12px;
}

.card-bottom {
  grid-row: 3/4;
  grid-column: 2/4;
}
.eem-cascader {
  position: relative;
  .eem-cascader-label {
    position: absolute;
    z-index: 1;
    height: 32px;
    line-height: 32px;
    left: 12px;
    @include font_color(ZS);
  }
  :deep(.el-cascader .el-input .el-input__inner) {
    padding-left: 91px;
  }
}
.radio-item {
  display: inline-block;
  max-width: 80px;
}
</style>
<style lang="scss">
.projectOverview-cascader-tree {
  .el-scrollbar__wrap {
    max-width: 180px !important;
    overflow-x: scroll;
  }
  .el-cascader-node__label {
    overflow: visible;
  }
  .el-cascader-node__postfix {
    position: relative;
  }
}
.projectOverview-type-select {
  .el-select-dropdown__item {
    max-width: 282px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
