<template>
  <div class="device-management-container">
    <div class="device-management">
      <!-- 筛选栏 -->
      <div class="filter-bar vpp-filter-bar">
        <el-input
          class="vpp-search-input"
          v-model="searchKeyword"
          :placeholder="$T('请输入设备名称')"
          prefix-icon="el-icon-search"
          size="small"
          clearable
        />
        <div class="vpp-select-group">
          <div class="vpp-select-item">
            <custom-el-select
              v-model="selectedCategory"
              :placeholder="$T('全部')"
              size="small"
              class="w-25"
              clearable
              :prefix_in="$T('设备类型')"
            >
              <el-option :label="$T('全部')" value=""></el-option>
              <el-option
                v-for="option in deviceTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              ></el-option>
            </custom-el-select>
          </div>
        </div>
        <div class="vpp-action-buttons">
          <el-button
            type="danger"
            size="small"
            :disabled="selectedDevices.length === 0"
            @click="handleBatchDelete"
            plain
          >
            {{ $T("批量删除") }}
          </el-button>
          <el-button type="primary" size="small" @click="handleAdd">
            {{ $T("新建") }}
          </el-button>
        </div>
      </div>

      <!-- 设备表格 -->
      <el-table
        class="flex1"
        :data="filteredDevices"
        :stripe="false"
        height="true"
        highlight-current-row
        style="width: 100%"
        v-loading="tableLoading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column :label="$T('序号')" width="80" align="center">
          <template slot-scope="scope">
            {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="deviceName"
          :label="$T('设备名称')"
          min-width="200"
        />
        <el-table-column
          prop="deviceType"
          :label="$T('设备类型')"
          width="150"
        />
        <el-table-column
          prop="ratedPower"
          :label="$T('额定功率（kW）')"
          width="160"
        />
        <el-table-column :label="$T('操作')" width="200">
          <template slot-scope="scope">
            <span
              class="action-link detail-link"
              @click="handleDetail(scope.row)"
            >
              {{ $T("详情") }}
            </span>
            <span class="action-link edit-link" @click="handleEdit(scope.row)">
              {{ $T("编辑") }}
            </span>
            <span
              class="action-link delete-link"
              @click="handleDelete(scope.row)"
            >
              {{ $T("删除") }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="table-footer">
        <span>
          {{ $T("共") }}
          <span class="total-highlight">{{ totalDevices }}</span>
          {{ $T("个") }}
        </span>
        <el-pagination
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :total="totalDevices"
          :page-sizes="[10, 20, 50]"
          layout="sizes, prev, pager, next, jumper"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 新增/编辑设备弹窗 -->
    <AddEditDeviceDialog
      :visible.sync="addDeviceDialogVisible"
      :is-edit-mode="isEditMode"
      :device-data="currentEditDeviceData"
      :selected-network-devices="selectedNetworkDevices"
      @close="handleDialogClose"
      @confirm="handleConfirm"
      @device-type-change="handleDeviceTypeChange"
      @select-network-device="selectNetworkDevice"
    />

    <!-- 管网设备选择弹窗 -->
    <NetworkDeviceSelectDialog
      :visible.sync="networkDeviceDialogVisible"
      :network-devices="networkDevices"
      :loading="networkDeviceLoading"
      :pre-selected-devices="selectedNetworkDevices"
      @search="handleNetworkDeviceSearch"
      @confirm="confirmNetworkDeviceSelection"
    />

    <!-- 设备详情抽屉 -->
    <DeviceDetailDrawer
      :visible.sync="deviceDetailDrawerVisible"
      :device-detail="currentDeviceDetail"
      @close="handleDetailDrawerClose"
    />
  </div>
</template>
<script>
// 导入API和组件
import {
  getDevicePage,
  createDevice,
  getDeviceById,
  updateDevice,
  deleteDevice,
  batchDeleteDevices
} from "@/api/device-management";
import {
  getMonitorDevicesByRoom,
  getDeviceMonitorRelations
} from "@/api/base-config";
import { getSiteById } from "@/api/site-management";

import { getEnumOptions, getEnumLabel } from "@/utils/enumManager";

// 导入弹窗组件
import AddEditDeviceDialog from "./AddEditDeviceDialog.vue";
import NetworkDeviceSelectDialog from "./NetworkDeviceSelectDialog.vue";
import DeviceDetailDrawer from "./DeviceDetailDrawer.vue";

export default {
  name: "VppDeviceManagement",
  components: {
    AddEditDeviceDialog,
    NetworkDeviceSelectDialog,
    DeviceDetailDrawer
  },
  props: {
    siteId: {
      type: Number,
      default: null
    },
    vppId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      searchKeyword: "",
      selectedCategory: "",
      currentPage: 1,
      pageSize: 10,
      total: 0,
      selectedDevices: [], // 选中的设备列表
      devices: [], // 设备列表数据
      loading: false,
      tableLoading: false,
      searchTimer: null,

      // 新增设备弹窗相关
      addDeviceDialogVisible: false, // 弹窗显示状态
      isEditMode: false, // 是否为编辑模式
      currentEditDeviceId: null, // 当前编辑的设备ID
      currentEditDeviceData: {}, // 当前编辑的设备数据

      // 设备详情抽屉相关
      deviceDetailDrawerVisible: false, // 抽屉显示状态
      currentDeviceDetail: null, // 当前查看的设备详情

      // 管网设备选择相关
      networkDeviceDialogVisible: false,
      networkDevices: [],
      networkDeviceLoading: false,
      selectedNetworkDevices: [],
      siteInfo: null, // 站点信息，包含房间信息
      deviceMonitorRelations: [], // 电厂设备与管网设备关联关系
      allowedNetworkDeviceTypes: [] // 当前设备类型允许关联的管网设备类型
    };
  },
  mounted() {
    // 数据加载由siteId的watch处理（immediate: true），避免重复调用
  },
  watch: {
    searchKeyword: {
      handler() {
        this.debounceSearch();
      }
    },
    selectedCategory: {
      handler() {
        this.currentPage = 1;
        this.loadDevices();
      }
    },
    siteId: {
      handler() {
        if (this.siteId) {
          this.loadDevices();
        }
      },
      immediate: true
    }
  },
  computed: {
    filteredDevices() {
      // 如果是按站点加载，使用客户端过滤
      if (this.siteId) {
        let filtered = this.devices;

        // 按设备名称搜索
        if (this.searchKeyword) {
          filtered = filtered.filter(device =>
            device.deviceName
              .toLowerCase()
              .includes(this.searchKeyword.toLowerCase())
          );
        }

        // 按设备类型筛选
        if (this.selectedCategory) {
          filtered = filtered.filter(
            device => device.deviceType === this.selectedCategory
          );
        }

        // 分页
        const start = (this.currentPage - 1) * this.pageSize;
        const end = start + this.pageSize;
        return filtered.slice(start, end);
      } else {
        // 服务端分页，直接返回设备列表
        return this.devices;
      }
    },
    totalDevices() {
      return this.siteId ? this.devices.length : this.total;
    },
    deviceTypeOptions() {
      return getEnumOptions("VPP_DEVICE_TYPE");
    }
  },
  methods: {
    // 防抖搜索
    debounceSearch() {
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.currentPage = 1;
        this.loadDevices();
      }, 500);
    },

    // 数据转换：将API返回的设备数据转换为组件显示格式
    transformDeviceData(apiDevice) {
      return {
        id: apiDevice.id,
        deviceName: apiDevice.deviceName,
        deviceType: this.getDeviceTypeNameById(apiDevice.deviceType), // 转换为中文名称显示
        deviceSubtype: apiDevice.deviceSubType,
        deviceStatus: apiDevice.deviceStatus,
        ratedPower: apiDevice.ratedPower,
        deviceId: apiDevice.deviceId,
        siteId: apiDevice.siteId,
        manufacturer: apiDevice.manufacturer,
        model: apiDevice.model,
        maxWorkingPower: apiDevice.maxWorkingPower,
        minWorkingPower: apiDevice.minWorkingPower,
        position: apiDevice.position,
        installationDate: apiDevice.installationDate,
        operationDate: apiDevice.operationDate,
        createTime: apiDevice.createTime,
        updateTime: apiDevice.updateTime,
        // 保留原始API数据用于编辑
        originalData: apiDevice
      };
    },

    // 加载设备列表
    async loadDevices() {
      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize
      };

      // 添加搜索条件
      if (this.searchKeyword) {
        params.deviceName = this.searchKeyword;
      }
      if (this.selectedCategory) {
        // selectedCategory现在已经是数字ID了，直接使用
        params.deviceType = this.selectedCategory;
      }
      if (this.siteId) {
        params.siteId = this.siteId;
      }

      const response = await getDevicePage(params);

      if (response.code === 0) {
        this.devices = response.data.records.map(device =>
          this.transformDeviceData(device)
        );
        this.total = response.data.total;
        this.currentPage = response.data.pageNum;
        this.pageSize = response.data.pageSize;
      }
    },

    // 打开新增设备弹窗
    handleAdd() {
      this.isEditMode = false;
      this.currentEditDeviceId = null;
      this.currentEditDeviceData = {};
      this.selectedNetworkDevices = [];
      this.addDeviceDialogVisible = true;
    },

    // 弹窗关闭处理
    handleDialogClose() {
      this.isEditMode = false;
      this.currentEditDeviceId = null;
      this.currentEditDeviceData = {};
      this.selectedNetworkDevices = [];
    },

    // 确定按钮处理
    handleConfirm(formData) {
      // 根据设备类型名称获取枚举ID
      const deviceTypeId = this.getDeviceTypeIdByName(formData.deviceType);

      // 构造提交数据
      const deviceData = {
        deviceName: formData.deviceName.trim(),
        deviceType: deviceTypeId || formData.deviceType, // 使用设备类型枚举ID
        siteId: this.siteId // 添加站点ID
      };

      // 如果选择了管网设备，添加管网设备关联信息
      if (
        this.selectedNetworkDevices &&
        this.selectedNetworkDevices.length > 0
      ) {
        deviceData.monitorDeviceRelations = this.selectedNetworkDevices.map(
          device => ({
            id: device.deviceId || device.id, // 兼容不同的字段名
            name: device.name,
            modelLabel: device.modelLabel || device.modellabel // 兼容不同的字段名
          })
        );
      }

      // 调用API保存设备数据
      this.saveDevice(deviceData);
    },

    // 保存设备数据
    async saveDevice(deviceData) {
      let res;
      if (this.isEditMode) {
        // 编辑模式：调用更新API
        res = await updateDevice(this.currentEditDeviceId, deviceData);
      } else {
        // 新增模式：调用创建API
        res = await createDevice(deviceData);
      }

      if (res.code === 0) {
        this.$message.success(
          this.isEditMode ? $T("编辑设备成功") : $T("新增设备成功")
        );
        this.addDeviceDialogVisible = false;
        this.isEditMode = false;
        this.currentEditDeviceId = null;
        this.loadDevices(); // 重新加载设备列表

        // 通知父组件刷新树形结构
        this.$emit("refresh-tree");
      }
    },

    // 选择管网设备
    async selectNetworkDevice() {
      // 检查是否有允许关联的管网设备类型
      if (!this.allowedNetworkDeviceTypes.length) {
        this.$message.warning($T("当前设备类型无可关联的管网设备类型"));
        return;
      }

      // 首先获取站点信息
      if (!this.siteInfo && this.siteId) {
        await this.loadSiteInfo();
      }

      if (!this.siteInfo || !this.siteInfo.roomId) {
        this.$message.warning($T("当前站点未关联房间，无法查询管网设备"));
        return;
      }
      // 加载管网设备列表
      await this.loadNetworkDevices();
      // 打开管网设备选择弹窗
      this.networkDeviceDialogVisible = true;
    },

    // 查看设备详情
    async handleDetail(row) {
      // 显示抽屉并清空之前的数据
      this.deviceDetailDrawerVisible = true;
      this.currentDeviceDetail = null;

      // 调用API获取设备详情
      const response = await getDeviceById(row.id);

      if (response.code === 0) {
        // 设置设备详情数据
        this.currentDeviceDetail = response.data;
      } else {
        this.deviceDetailDrawerVisible = false;
      }
    },

    // 详情抽屉关闭处理
    handleDetailDrawerClose() {
      this.currentDeviceDetail = null;
    },

    async handleEdit(row) {
      // 设置编辑模式
      this.isEditMode = true;
      this.currentEditDeviceId = row.id;

      // 调用API获取设备详情
      const response = await getDeviceById(row.id);

      if (response.code === 0) {
        const deviceData = response.data;

        // 设置编辑数据
        this.currentEditDeviceData = {
          deviceName: deviceData.deviceName || "",
          deviceType: deviceData.deviceType || ""
        };

        // 重置已选择的管网设备
        this.selectedNetworkDevices = [];

        // 处理关联的管网设备信息
        if (
          deviceData.monitor_device_relations &&
          deviceData.monitor_device_relations.length > 0
        ) {
          // 将已关联的设备加入选择列表
          this.selectedNetworkDevices = deviceData.monitor_device_relations.map(
            relation => {
              return {
                deviceId: relation.deviceId,
                name: relation.name,
                modelLabel: relation.modelLabel,
                treeId: relation.treeId
              };
            }
          );
        }

        // 更新允许的管网设备类型
        if (deviceData.deviceType) {
          this.updateAllowedNetworkDeviceTypes(deviceData.deviceType);
        }

        // 显示弹窗
        this.addDeviceDialogVisible = true;
      }
    },

    handleDelete(row) {
      // 弹出删除确认对话框
      this.$confirm(
        $T("确定要删除设备「{0}」吗？删除后将无法恢复。", row.deviceName),
        $T("删除确认"),
        {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        }
      )
        .then(async () => {
          // 构造删除参数
          const deleteNode = {
            parentId: this.siteId, // 站点ID作为parentId
            ids: [row.id] // 设备ID数组
          };

          const response = await deleteDevice(deleteNode);

          if (response.code === 0) {
            this.$message.success($T("删除设备成功"));

            // 重新加载设备列表
            this.loadDevices();

            // 通知父组件刷新树形结构
            this.$emit("refresh-tree");
          }
        })
        .catch(() => {
          this.$message.info($T("已取消删除"));
        });
    },

    handleSelectionChange(selection) {
      this.selectedDevices = selection;
    },
    handleBatchDelete() {
      if (this.selectedDevices.length === 0) {
        this.$message.warning($T("请选择要删除的设备"));
        return;
      }

      this.$confirm(
        $T("确定要删除选中的 {0} 个设备吗？", this.selectedDevices.length),
        $T("批量删除确认"),
        {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        }
      )
        .then(async () => {
          const deviceIds = this.selectedDevices.map(device => device.deviceId);

          // 构造删除参数
          const deleteNode = {
            parentId: this.siteId, // 站点ID作为parentId
            ids: deviceIds // 设备ID数组
          };

          const response = await batchDeleteDevices(deleteNode);

          if (response.code === 0) {
            this.$message.success(
              $T("成功删除 {0} 个设备", this.selectedDevices.length)
            );

            // 清空选中状态
            this.selectedDevices = [];

            // 重新加载数据
            this.loadDevices();

            // 通知父组件刷新树形结构
            this.$emit("refresh-tree");
          }
        })
        .catch(() => {
          this.$message.info($T("已取消删除"));
        });
    },
    handleSizeChange(newSize) {
      this.pageSize = newSize;
      this.currentPage = 1;
      this.loadDevices();
    },
    handleCurrentChange(newPage) {
      this.currentPage = newPage;
      this.loadDevices();
    },
    getStatusClass(status) {
      switch (status) {
        case "正常运行":
          return "status-normal";
        case "故障":
          return "status-error";
        case "维护中":
          return "status-maintenance";
        default:
          return "";
      }
    },

    // 加载站点信息
    async loadSiteInfo() {
      if (!this.siteId) {
        return;
      }

      const res = await getSiteById(this.siteId);
      if (res.code === 0) {
        this.siteInfo = res.data;
      }
    },

    // 加载管网设备列表
    async loadNetworkDevices() {
      if (!this.siteInfo || !this.siteInfo.roomId) {
        this.networkDevices = [];
        return;
      }

      const queryData = {
        roomId: this.siteInfo.roomId,
        roomType: this.siteInfo.roomType || "",
        siteId: this.siteId, // 添加站点ID
        deviceTypes: this.allowedNetworkDeviceTypes, // 根据设备类型过滤管网设备类型
        vppDeviceId: this.currentEditDeviceId || null // 当前电厂设备ID，用于获取已关联和允许关联的设备
      };
      const res = await getMonitorDevicesByRoom(queryData);

      if (res.code === 0) {
        this.networkDevices = res.data;
      } else {
        this.networkDevices = [];
      }
    },

    // 管网设备搜索
    handleNetworkDeviceSearch() {
      // 这里可以根据关键词重新加载管网设备列表
      this.loadNetworkDevices();
    },

    // 确认管网设备选择
    confirmNetworkDeviceSelection(selectedDevices) {
      this.selectedNetworkDevices = selectedDevices;
      this.$message.success(
        $T("成功选择 {0} 个管网设备", selectedDevices.length)
      );
    },

    // 加载电厂设备与管网设备关联关系
    async loadDeviceMonitorRelations() {
      const res = await getDeviceMonitorRelations();
      if (res.code === 0) {
        this.deviceMonitorRelations = res.data || [];
      }
    },

    // 设备类型改变处理
    handleDeviceTypeChange(deviceType) {
      // 清空之前选择的管网设备
      this.selectedNetworkDevices = [];

      // 根据设备类型获取允许关联的管网设备类型
      this.updateAllowedNetworkDeviceTypes(deviceType);
    },

    // 更新允许关联的管网设备类型
    updateAllowedNetworkDeviceTypes(deviceType) {
      this.allowedNetworkDeviceTypes = [];
      if (!deviceType || !this.deviceMonitorRelations.length) {
        return;
      }

      // deviceType现在已经是数字ID了，不需要转换
      const deviceTypeId = deviceType;
      if (!deviceTypeId) {
        return;
      }

      // 查找当前电厂设备类型对应的管网设备类型
      // 根据API文档，需要根据devicetype字段匹配，然后收集monitorlabel字段
      const monitorLabels = [];
      this.deviceMonitorRelations.forEach(relation => {
        // 使用设备类型ID进行匹配
        if (relation.devicetype === deviceTypeId) {
          monitorLabels.push(relation.monitorlabel);
        }
      });

      this.allowedNetworkDeviceTypes = [...new Set(monitorLabels)]; // 去重
    },

    // 判断设备类型是否匹配
    isDeviceTypeMatch(deviceTypeId, deviceTypeName) {
      // 根据设备类型名称找到对应的ID
      const deviceTypeOption = this.deviceTypeOptions.find(
        option => option.label === deviceTypeName
      );

      if (!deviceTypeOption) {
        return false;
      }

      // 比较ID是否匹配
      return deviceTypeOption.value === deviceTypeId;
    },

    // 根据设备类型名称获取ID
    getDeviceTypeIdByName(deviceTypeName) {
      const deviceTypeOptions = getEnumOptions("VPP_DEVICE_TYPE");
      const option = deviceTypeOptions.find(
        opt => opt.label === deviceTypeName
      );
      return option ? option.value : null;
    },

    // 根据设备类型ID获取名称
    getDeviceTypeNameById(deviceTypeId) {
      return getEnumLabel("VPP_DEVICE_TYPE", deviceTypeId) || $T("未知类型");
    },

    // 根据管网设备类型标识获取中文名称
    getNetworkDeviceTypeName(modelLabel) {
      if (!modelLabel) return $T("未知类型");

      // 从deviceTypeUtils.js中获取管网设备类型映射
      const { MODEL_LABEL_MAP } = require("@/utils/deviceTypeUtils");
      return MODEL_LABEL_MAP[modelLabel] || modelLabel || $T("未知类型");
    }
  },
  created() {
    this.loadDeviceMonitorRelations();
    // loadDevices由siteId的watch处理（immediate: true），避免重复调用
  },
  beforeDestroy() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }
};
</script>
<style scoped>
.device-management-container {
  width: 100%;
  height: 100%;
  background: var(--BG1);
  border-radius: var(--Ra);
  padding: var(--J4);
  margin-bottom: var(--J3);
}

.device-management-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.device-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--J3);
  margin-bottom: var(--J3);
}

.vpp-search-input {
  width: 240px;
  height: var(--J5);
}

.vpp-search-input .el-input__inner {
  height: var(--J5);
  line-height: var(--J5);
  padding: 1px 1px 1px var(--J1);
  border: 1px solid var(--B1);
  border-radius: var(--Ra);
  background: var(--BG1);
  font-size: var(--Aa);
  color: var(--T1);
  box-shadow: none;
}

.vpp-search-input .el-input__inner::placeholder {
  color: var(--T4);
  font-size: var(--Aa);
}

.vpp-search-input .el-input__inner:focus {
  border-color: var(--ZS);
  box-shadow: none;
}

.vpp-search-input .el-input__prefix {
  left: var(--J1);
}

.vpp-search-input .el-input__prefix .el-input__icon {
  color: var(--T4);
  font-size: var(--J2);
  line-height: var(--J5);
}

.vpp-select-group {
  display: flex;
  gap: var(--J2);
  flex: 1;
}

.vpp-select-item {
  display: flex;
  align-items: center;
  gap: var(--J1);
  min-width: 120px;
}

.vpp-select-label {
  color: var(--ZS);
  font-size: var(--Ab);
  font-weight: 400;
  line-height: var(--J2);
  white-space: nowrap;
}

.vpp-select-item .el-select .el-input__inner {
  border: 1px solid var(--B1);
  border-radius: var(--Ra);
  background: var(--BG1);
  color: var(--T1);
  font-size: var(--Aa);
  padding: 0 var(--J1);
  height: var(--J5);
  line-height: var(--J5);
}

.vpp-select-item .el-select .el-input__inner:focus {
  border-color: var(--ZS);
  box-shadow: none;
}

.vpp-select-item .el-select .el-input__suffix {
  right: 0;
}

.vpp-select-item .el-select .el-input__suffix .el-input__icon {
  color: var(--T3);
  font-size: var(--Ab);
}

/* 按钮组样式 */
.vpp-action-buttons {
  display: flex;
  align-items: center;
}

/* 表格样式 */
.device-management .el-table {
  background: var(--BG) 1;
  border-radius: var(--Ra);
}

.device-management .el-table th {
  background: var(--BG1);
  border-bottom: 1px solid var(--B2);
  color: var(--T3);
  font-size: var(--Ab);
  font-weight: 400;
  line-height: var(--J2);
  padding: var(--J1) var(--J3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.device-management .el-table th .cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.device-management .el-table td {
  border-bottom: 1px solid var(--B2);
  color: var(--T1);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
  padding: var(--J1) var(--J3);
}

/* 表格无数据状态样式 */
.device-management .el-table__empty-block {
  background: var(--BG1);
}

.device-management .el-table__empty-text {
  color: var(--BG1);
}

.device-management .el-table__body-wrapper {
  background: var(--BG1);
}

/* 设备状态样式 */
.status-normal {
  color: var(--Sta1);
  background: var(--BG4);
  padding: var(--J0) var(--J1);
  border-radius: var(--Ra);
  font-size: var(--Ab);
  font-weight: 500;
}

.status-error {
  color: var(--Sta3);
  background: var(--BG4);
  padding: var(--J0) var(--J1);
  border-radius: var(--Ra);
  font-size: var(--Ab);
  font-weight: 500;
}

.status-maintenance {
  color: var(--F2);
  background: var(--BG4);
  padding: var(--J0) var(--J1);
  border-radius: var(--Ra);
  font-size: var(--Ab);
  font-weight: 500;
}

/* 操作链接样式 */
.action-link {
  font-size: var(--Aa);
  cursor: pointer;
  margin-right: var(--J2);
  text-decoration: none;
  transition: opacity 0.2s;
}

.detail-link {
  color: var(--ZS);
}

.detail-link:hover {
  opacity: 0.8;
}

.edit-link {
  color: var(--ZS);
}

.edit-link:hover {
  opacity: 0.8;
}

.delete-link {
  color: var(--Sta3);
}

.delete-link:hover {
  opacity: 0.8;
}

/* 分页器样式 */
.table-footer {
  display: flex;
  align-items: center;
  justify-content: right;
  gap: var(--J2);
  margin-top: var(--J2);
  font-size: var(--Aa);
  color: var(--T1);
}

.total-highlight {
  color: var(--ZS);
  font-weight: 500;
}

.table-footer .el-pagination {
  display: flex;
  align-items: center;
  gap: var(--J1);
}

.table-footer .el-pagination .el-pager li {
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  border-radius: var(--Ra);
  border: 1px solid var(--B2);
  background: var(--BG1);
  color: var(--T1);
  font-size: var(--Aa);
  margin: 0;
}

.table-footer .el-pagination .el-pager li.active {
  background: var(--BG2);
  color: var(--T1);
  border-color: var(--B2);
}

.table-footer .el-pagination .btn-prev,
.table-footer .el-pagination .btn-next {
  width: 32px;
  height: 32px;
  line-height: 32px;
  border-radius: var(--Ra);
  border: 1px solid var(--B2);
  background: var(--BG1);
  color: var(--T1);
}

.table-footer .el-pagination .el-pagination__jump {
  margin-left: var(--J2);
  color: var(--T1);
  font-size: var(--Aa);
}

.table-footer .el-pagination .el-pagination__jump .el-input__inner {
  width: 48px;
  height: 32px;
  border: 1px solid var(--B2);
  border-radius: var(--Ra);
  text-align: center;
}

/* flex1 样式 - 让表格占用剩余高度 */
.flex1 {
  flex: 1;
  min-width: 0;
  min-height: 0;
}
</style>
