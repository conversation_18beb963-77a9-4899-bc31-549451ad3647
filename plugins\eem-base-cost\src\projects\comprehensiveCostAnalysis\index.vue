<template>
  <CetAside class="cet-aside">
    <template #aside>
      <div class="flex flex-col h-full">
        <customElSelect
          :prefix_in="$T('所属项目')"
          v-model="ElSelect_project.value"
          v-bind="ElSelect_project"
          v-on="ElSelect_project.event"
          class="mb-J1"
        >
          <ElOption
            v-for="item in ElOption_project.options_in"
            :key="item[ElOption_project.key]"
            :label="item[ElOption_project.label]"
            :value="item[ElOption_project.value]"
            :disabled="item[ElOption_project.disabled]"
          ></ElOption>
        </customElSelect>
        <customElSelect
          v-model="ElSelect_treeType.value"
          v-bind="ElSelect_treeType"
          v-on="ElSelect_treeType.event"
          :prefix_in="$T('节点树类型')"
          v-if="multidimensional"
          v-show="ElOption_treeType.options_in?.length > 1"
          class="mb-J1"
        >
          <ElOption
            v-for="item in ElOption_treeType.options_in"
            :key="item[ElOption_treeType.key]"
            :label="item[ElOption_treeType.label]"
            :value="item[ElOption_treeType.value]"
            :disabled="item[ElOption_treeType.disabled]"
          ></ElOption>
        </customElSelect>
        <CetTree
          class="flex-auto cetTree"
          :selectNode.sync="CetTree_leftTree.selectNode"
          :checkedNodes.sync="CetTree_leftTree.checkedNodes"
          :searchText_in.sync="CetTree_leftTree.searchText_in"
          v-bind="CetTree_leftTree"
          v-on="CetTree_leftTree.event"
        >
          <span
            class="custom-tree-node el-tree-node__label"
            slot-scope="{ node }"
            :level="node.level"
          >
            <span
              :style="{
                color: filNodeColor(node)
              }"
            >
              {{ node.label }}
            </span>
            <div class="icon" v-if="node?.data?.changeStatus">
              <el-tooltip effect="light" :content="effTimeFormat(node.data)">
                <omega-icon symbolId="collect-lin" />
              </el-tooltip>
            </div>
          </span>
        </CetTree>
      </div>
    </template>
    <template #container>
      <div class="flex flex-col h-full">
        <el-tabs v-model="selectedMenu" class="eltabs">
          <el-tab-pane
            :label="$T('成本趋势分析')"
            name="costTrend"
          ></el-tab-pane>
          <el-tab-pane :label="$T('成本核算')" name="costAccount"></el-tab-pane>
        </el-tabs>
        <div class="flex flex-col flex-auto p-J4">
          <div class="flex flex-row justify-end mb-J3">
            <time-tool
              :typeID="14"
              :val.sync="startTime"
              @change="changeQueryTime"
              :timeType_in="timeType"
              :shortcuts="pickerOptions.shortcuts"
            ></time-tool>
            <CetButton
              v-if="selectedMenu === 'costAccount'"
              v-bind="CetButton_export"
              v-on="CetButton_export.event"
              class="ml-J1"
            ></CetButton>
          </div>

          <div class="flex-auto">
            <costTrend
              v-if="selectedMenu === 'costTrend'"
              :selectTime="selectTime"
              :currentNode="currentNode"
              :queryTime="queryTime"
              :dimConfigId="ElSelect_treeType.value"
              :rootNode="rootNode"
            ></costTrend>
            <costAccount
              v-if="selectedMenu === 'costAccount'"
              :selectTime="selectTime"
              :currentNode="currentNode"
              :queryTime="queryTime"
              :dimConfigId="ElSelect_treeType.value"
              :rootNode="rootNode"
            ></costAccount>
          </div>
        </div>
      </div>
    </template>
  </CetAside>
</template>

<script>
import common from "eem-base/utils/common";
import TimeTool from "@/components/TimeTool.vue";
import customApi from "@/api/custom";
import costTrend from "./costTrend.vue";
import costAccount from "./costAccount.vue";
import omegaI18n from "@omega/i18n";

export default {
  name: "comprehensiveCostAnalysis",
  components: {
    TimeTool,
    costTrend,
    costAccount
  },
  computed: {
    language() {
      return omegaI18n.locale === "en";
    },
    multidimensional() {
      return this.$store.state.multidimensional;
    },
    rootNode() {
      const id = this.ElSelect_project.value;
      const list = this.ElOption_project.options_in;
      return list.find(item => item.id === id);
    }
  },
  data() {
    return {
      startTime: new Date().getTime(),
      queryTime: {},
      timeType: [
        {
          type: "month",
          text: $T("月"),
          typeID: 14,
          number: 1,
          unit: "M"
        },
        {
          type: "year",
          text: $T("年"),
          number: 1,
          typeID: 17,
          unit: "y"
        }
      ],
      ElSelect_treeType: {
        value: -1,
        style: {},
        event: {
          change: this.ElSelect_treeType_change_out
        }
      },
      ElOption_treeType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      selectTime: "",
      currentNode: null,
      CetTree_leftTree: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this._.debounce(
            this.CetTree_leftTree_currentNode_out,
            300
          )
        }
      },
      selectedMenu: "costTrend",
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      pickerOptions: {
        // 添加回到今天的快捷键
        shortcuts: [
          {
            text: this.$T("当月"),
            onClick(picker) {
              picker.$emit("pick", new Date());
            }
          }
        ]
      },
      ElSelect_project: {
        value: 0,
        event: {
          change: this.changeProject
        }
      },
      ElOption_project: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  methods: {
    async getRootNode() {
      this.ElOption_project.options_in = [];
      const res = await customApi.rootNode();
      const data = res.data || [];
      this.ElOption_project.options_in = data;
      this.ElSelect_project.value = data?.[0]?.id;

      this.queryTreeType();
    },
    changeProject() {
      this.queryTreeType();
    },
    //通过节点childSelectState属性，调整字体颜色
    filNodeColor(node) {
      const state = this._.get(node, "data.childSelectState", null);
      return state !== 1 ? "#989898" : undefined;
    },
    ElSelect_treeType_change_out(val) {
      val === -1 ? this.getTreeData1() : this.getTreeData2();
    },
    changeQueryTime({ val, timeOption }) {
      const date = this.$moment(val);
      const queryTime = {
        cycle: timeOption.typeID,
        startTime: date.startOf(timeOption.unit).valueOf(),
        endTime: date.endOf(timeOption.unit).valueOf() + 1
      };
      const mothStr = this.language ? "YYYY-MM" : "YYYY年MM月";
      const yearStr = this.language ? "YYYY" : "YYYY年";
      this.selectTime =
        timeOption.typeID === 14
          ? date.format(mothStr)
          : timeOption.typeID === 17
          ? date.format(yearStr)
          : "--";
      this.pickerOptions.shortcuts[0].text =
        timeOption.typeID === 14 ? this.$T("当月") : this.$T("今年");
      // 多维度时间变化的时候需要去更新节点树
      if (this.ElSelect_treeType.value > 0) {
        this.getTreeData2(true, queryTime);
      } else {
        this.queryTime = queryTime;
      }
    },
    CetTree_leftTree_currentNode_out(val) {
      if (val.childSelectState === 2) {
        return this.$message.warning($T("没有该节点权限"));
      }
      this.currentNode = this._.cloneDeep(val);
    },
    getTreeData1() {
      this.CetTree_leftTree.inputData_in = [];
      const data = {
        nodeTreeGroupId: 2,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };
      customApi.queryElectricityTree(data).then(res => {
        if (res.code === 0) {
          this.CetTree_leftTree.inputData_in = res.data;
          if (res.data && res.data.length) {
            // 选中第一个有数据 childSelectState = 1 的节点并展开节点
            const obj = this._.find(this.dataTransform(res.data), [
              "childSelectState",
              1
            ]);
            this.CetTree_leftTree.selectNode = obj;
          }
        }
      });
    },
    // 获取节点树类型下拉框
    async queryTreeType() {
      const queryData = {
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        },
        status: true
      };
      const res = await customApi.getAttributeDimensionTreeNodeConfig(
        queryData
      );
      this.ElOption_treeType.options_in = (
        res?.data || [
          {
            id: -1,
            name: $T("固定管理层级")
          }
        ]
      ).map(i => ({
        id: i.id,
        name: i.name
      }));
      const flag = this.ElOption_treeType.options_in.find(i => i.id === -1);
      this.ElSelect_treeType.value = flag
        ? -1
        : this.ElOption_treeType.options_in?.[0]?.id ?? null;
      this.ElSelect_treeType_change_out(this.ElSelect_treeType.value);
    },
    // 获取维度节点树数据
    async getTreeData2(keepSelectNode, queryTime) {
      const queryData = {
        dimTreeConfigId: this.ElSelect_treeType.value,
        startTime: queryTime?.startTime ?? this.queryTime?.startTime,
        endTime: queryTime?.endTime ?? this.queryTime?.endTime,
        aggregationCycle: this.queryTime.cycle,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };
      const res = await customApi.queryCostcheckplanMultitree(queryData);
      this.CetTree_leftTree.inputData_in = res?.data || [];
      let obj;
      if (keepSelectNode) {
        obj = this._.find(this.dataTransform(res.data), [
          "tree_id",
          this.currentNode.tree_id
        ]);
      }
      if (!obj) {
        // 选中第一个有数据 childSelectState = 1 的节点并展开节点
        obj = this._.find(this.dataTransform(res.data), [
          "childSelectState",
          1
        ]);
      }
      if (queryTime) {
        this.queryTime = queryTime;
      }
      this.CetTree_leftTree.selectNode = obj;
    },
    // 获取第一个有权限的节点
    dataTransform(array) {
      const cloneData = this._.cloneDeep(array);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },
    // export输出
    CetButton_export_statusTrigger_out() {
      if (this._.isEmpty(this.currentNode)) {
        return;
      }
      const params = {
        node: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          name: this.currentNode.name
        },
        energyType: 13,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        },
        ...this.queryTime,
        dimConfigId: this.ElSelect_treeType.value
      };
      if (this.currentNode?.effTimeList?.[0]?.startTime) {
        params.timeRanges = this.currentNode.effTimeList;
      }
      common.downExcel("/eembasecost/v1/calculate/check/export", params);
    },
    effTimeFormat(data) {
      const { effTimeList } = data;
      return effTimeList
        .map(item => {
          const { startTime, endTime } = item;
          return `${this.$moment(startTime).format(
            "YYYY-MM-DD"
          )}~${this.$moment(endTime).format("YYYY-MM-DD")}`;
        })
        .join(",");
    }
  },
  mounted() {
    // this.queryTreeType();
    this.getRootNode();
  }
};
</script>

<style lang="scss" scoped>
.cet-aside :deep(.cet-content-aside-container) {
  background-color: var(--BG1);
  border-radius: var(--Ra);
  padding: 0;
}
.eltabs {
  :deep() {
    .el-tabs__header {
      margin-bottom: 0;
      line-height: 46px;
    }
    .el-tabs__nav-scroll {
      padding-left: var(--J4);
    }
    .el-tabs__content {
      flex: 1;
      min-height: 0;
    }
  }
}
.cetTree {
  .custom-tree-node {
    .icon {
      visibility: hidden;
      position: absolute;
      right: 0;
      top: 4px;
      @include background_color(BG1);
      .edit {
        @include font_color(ZS);
      }
      .delete {
        @include font_color(Sta3);
      }
    }
  }
  :deep(.el-tree-node__content) {
    position: relative;
    &:hover {
      .custom-tree-node .icon {
        visibility: visible;
      }
    }
  }
}
</style>
