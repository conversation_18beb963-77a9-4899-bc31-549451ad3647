export function timer(cb, interval = 4e3, { immediate = true } = {}) {
  let isRun = true;
  let timerId = null;

  const exec = timeout => {
    return new Promise((resolve, reject) => {
      timerId = setTimeout(() => {
        if (isRun) {
          resolve(cb(cancel));
        }
      }, timeout || interval);
    });
  };

  const run = async () => {
    try {
      if (immediate) {
        await exec(0);
      }
    } finally {
      // eslint-disable-next-line no-unmodified-loop-condition
      while (isRun) {
        try {
          await exec();
        } finally {
          await exec();
        }
      }
    }
  };

  const cancel = () => {
    isRun = false;
    if (timerId) {
      window.clearTimeout(timerId);
    }
  };

  run();
  return cancel;
}

/**
 * 是否为开发环境
 */
export function isDev() {
  return process.env.NODE_ENV === "development";
}
/**
 * 是否为Debug环境
 */
export function isDebug() {
  return isDev() || window.location.search.includes("?debug");
}
export function error(desc, ...argv) {
  if (isDebug()) {
    console.log(
      `%c mh_error: ${desc}`,
      "background:#F56C6C;color:#fff;border-radius:2px",
      ...argv
    );
  }
}

export default {
  timer,
  error
};
