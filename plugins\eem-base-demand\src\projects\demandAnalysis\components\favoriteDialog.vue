<template>
  <div>
    <CetDialog v-bind="CetDialog_add" v-on="CetDialog_add.event">
      <span slot="footer">
        <CetButton
          class="mr-J1"
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_preserve"
          v-on="CetButton_preserve.event"
        ></CetButton>
      </span>
      <div class="m-J1 flex-auto">
        <CetForm
          :data.sync="CetForm_add.data"
          v-bind="CetForm_add"
          v-on="CetForm_add.event"
        >
          <el-form-item :label="$T('储存位置')" prop="templateId">
            <el-select v-model="CetForm_add.data.templateId" class="left">
              <el-option
                v-for="item in folderList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <CetButton
              class="ml-J3"
              v-bind="CetButton_add"
              v-on="CetButton_add.event"
            ></CetButton>
          </el-form-item>
          <el-form-item :label="$T('自定义名称')" prop="name">
            <el-input
              class="left"
              v-model.trim="CetForm_add.data.name"
              :placeholder="$T('请输入')"
            />
          </el-form-item>
        </CetForm>
      </div>
    </CetDialog>
    <addFolder v-bind="addFolder" v-on="addFolder.event" />
  </div>
</template>
<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
import addFolder from "./addFolder.vue";

export default {
  components: { addFolder },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    node: {
      type: Object
    }
  },
  data() {
    return {
      CetDialog_add: {
        title: $T("收藏"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        width: "600px",
        event: {}
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("收藏"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("新建文件夹"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      CetForm_add: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: []
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        labelPosition: "top",
        rules: {
          templateId: [
            {
              required: true,
              message: $T("请选择存储位置"),
              trigger: ["blur", "change"]
            }
          ],
          name: [
            {
              required: true,
              message: $T("请输入模板名称"),
              trigger: ["blur", "change"]
            },
            {
              min: 1,
              max: 50,
              message: $T("长度在 1 到 {0} 个字符", 50),
              trigger: ["blur", "change"]
            },
            common.pattern_name
          ]
        },
        event: {
          saveData_out: this.CetForm_add_saveData_out
        }
      },
      folderList: null,
      addFolder: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          saveData_out: this.addFolder_saveData_out
        }
      }
    };
  },
  watch: {
    openTrigger_in(val) {
      this.CetDialog_add.openTrigger_in = val;
      this.CetForm_add.data = {
        name: this.node.text
      };
      this.CetForm_add.resetTrigger_in = new Date().getTime();
      this.getTemplateTree();
    }
  },
  methods: {
    //获取存储位置选项
    async getTemplateTree(init = false) {
      const { data = [] } = await customApi.queryFavorite({
        dataType: 1,
        onlyFavoritesNode: true
      });
      this.folderList = data;
      if (init) {
        this.CetForm_add.data.templateId = data[data.length - 1].id;
      }
    },
    async saveData(val) {
      const res = await customApi.saveNewFavorite({
        name: this.CetForm_add.data.name,
        dataType: 1,
        nodeType: 2,
        parentId: this.CetForm_add.data.templateId,
        details: [
          {
            detail: JSON.stringify({
              deviceId: this.node.nodeId
            })
          }
        ]
      });
      if (res.code !== 0) return;
      this.$message.success($T("保存成功"));
      this.CetDialog_add.closeTrigger_in = Date.now();
      this.$emit("saveData_out", this._.cloneDeep(val));
    },
    CetButton_add_statusTrigger_out(val) {
      this.addFolder.openTrigger_in = val;
    },
    CetForm_add_saveData_out(val) {
      this.saveData(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      this.CetForm_add.localSaveTrigger_in = this._.cloneDeep(val);
    },
    addFolder_saveData_out(val) {
      this.getTemplateTree(true);
      this.$emit("addFolder", val);
    }
  }
};
</script>
<style lang="scss" scoped>
.left {
  width: calc(100% - 108px);
}
</style>
