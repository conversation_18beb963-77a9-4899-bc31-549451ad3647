<template>
  <div v-if="$checkPermission('demandaccount_update')" class="clearfix mt-J3">
    <CetButton
      class="fr"
      v-bind="{ ...CetButton_2, title: $T('新建分组') }"
      @statusTrigger_out="CetButton_2_statusTrigger_out"
    ></CetButton>
    <CetButton
      class="fr ml-J1"
      v-bind="{ ...CetButton_7, title: $T('编辑') }"
      @statusTrigger_out="CetButton_7_statusTrigger_out"
    ></CetButton>
    <CetButton
      class="fr delete"
      v-bind="{ ...CetButton_1, title: $T('删除') }"
      @statusTrigger_out="CetButton_1_statusTrigger_out"
    ></CetButton>
    <addGrouping
      :visibleTrigger_in="addGrouping.visibleTrigger_in"
      :closeTrigger_in="addGrouping.closeTrigger_in"
      :inputData_in="addGrouping.inputData_in"
      @finishTrigger_out="refersh(false)"
    />
  </div>
</template>

<script>
import customApi from "@/api/custom";
import addGrouping from "./addGrouping.vue";

export default {
  components: {
    addGrouping
  },
  props: {
    currentNode: {
      type: Object,
      default: null
    }
  },
  watch: {
    currentNode: {
      handler(val) {
        if (!val) {
          this.CetButton_1.disable_in = true;
          return;
        }
        this.CetButton_1.disable_in = false;
        this.CetButton_1.visible_in = false;
        this.CetButton_2.visible_in = false;
        this.CetButton_7.visible_in = false;
        if (val.modelLabel == "project") {
          this.CetButton_2.visible_in = true;
        } else if (val.modelLabel == "demandgroup") {
          this.CetButton_1.visible_in = true;
          this.CetButton_7.visible_in = true;
        }
        if (val?.children?.length > 0) {
          this.CetButton_1.disable_in = true;
        } else {
          this.CetButton_1.disable_in = false;
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      CetButton_2: {
        visible_in: false,
        disable_in: false,
        type: "primary",
        plain: false
      },
      CetButton_7: {
        visible_in: false,
        disable_in: false,
        type: "primary",
        plain: false
      },
      CetButton_1: {
        visible_in: true,
        disable_in: true,
        plain: true,
        type: "danger"
      },
      addGrouping: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      }
    };
  },
  methods: {
    // 新建账户分组
    CetButton_2_statusTrigger_out(val) {
      const { modelLabel: fatherModelLabel, id: fatherId } = this.currentNode;
      this.addGrouping.inputData_in = { fatherId, fatherModelLabel };
      this.addGrouping.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 编辑账户分组
    CetButton_7_statusTrigger_out(val) {
      const { fatherId, fatherModelLabel, name, id } = this.currentNode;
      this.addGrouping.inputData_in = { fatherId, fatherModelLabel, name, id };
      this.addGrouping.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 删除用户组
    CetButton_1_statusTrigger_out(val) {
      this.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        cancelButtonClass: "btn-custom-cancel",
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: async (action, instance, done) => {
          if (action == "confirm") {
            const response = await customApi.demandGroupDel([
              this.currentNode.id
            ]);
            if (response.code === 0) {
              this.$message({
                message: $T("删除成功"),
                type: "success"
              });
              this.refersh(true);
            }
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action != "confirm") {
            this.$message({
              type: "info",
              message: $T("取消删除")
            });
          }
        }
      });
    },
    refersh(selectFirstNodeFlag) {
      this.$emit("refresh", selectFirstNodeFlag);
    }
  }
};
</script>

<style></style>
