<template>
  <div class="login" :style="primartBackgroudStyle">
    <div class="login-main">
      <div class="login-main-right">
        <div class="login-right-content">
          <div class="login-logo">
            <div class="login-logo-main">
              <div class="login-logo-img" :style="logoBackgroundStyle" />
              <div class="login-logo-text" :style="resolveName()">
                {{ systemName }}
              </div>
              <div class="login-logo-subtitle" v-if="subTitle">
                {{ subTitle }}
              </div>
            </div>
          </div>
          <div class="login-form">
            <LoginNormalWithCode v-if="isCodeLogin" />
            <LoginNormal v-else />
          </div>
        </div>
      </div>
      <div class="login-main-left" :style="mainLeftBackgroundStyle">
        <div
          class="loginSetCodeBox"
          @mouseover="showCode = true"
          @mouseout="showCode = false"
          v-if="codeSrc"
        >
          <div class="imageCode">
            <omega-icon class="codeIcon" symbolId="scan"></omega-icon>
          </div>
          <div class="downloadBox">
            <p>{{ $T("扫码下载App") }}</p>
            <p>Download App</p>
          </div>
          <img :src="codeSrc" v-if="showCode" alt="" class="codeImg" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LoginNormal from "./components/loginNormal.vue";
import LoginNormalWithCode from "./components/loginNormalWithCode.vue";
import { conf, store } from "@omega/app";
import $ from "jquery";
import { allConfig, getImg } from "@altair/blade";
import omegaI18n from "@omega/i18n";
export default {
  name: "Login",
  components: {
    LoginNormal,
    LoginNormalWithCode
  },
  computed: {
    isCodeLogin() {
      return allConfig?.system?.code_login || false;
    },
    subTitle() {
      return allConfig?.system?.subtitle;
    },
    systemName() {
      return store.state.systemName || $T("Matterhorn综合能源管理平台");
    },
    mainLeftBackgroundStyle() {
      return conf.state.resource.login_background_image_url
        ? `background-image: url(${conf.state.resource.login_background_image_url})`
        : "";
    },
    logoBackgroundStyle() {
      return conf.state.resource.login_logo_image_url
        ? `background-image: url(${conf.state.resource.login_logo_image_url})`
        : "";
    },
    primartBackgroudStyle() {
      return conf.state.resource.login_primary_image_url
        ? `background-image: url(${conf.state.resource.login_primary_image_url})`
        : "";
    }
  },
  data() {
    return {
      codeSrc: null,
      showCode: false
    };
  },
  methods: {
    resolveName() {
      const length = this.systemName.length;
      const locale = omegaI18n.locale;
      let size_zh = 30,
        size_en = 30;
      let newline_zh = false,
        newline_en = false;
      if (length < 15) {
        size_zh = 30;
      } else if (length < 19) {
        size_zh = 24;
      } else if (length < 23) {
        size_zh = 20;
      } else if (length > 22) {
        size_zh = 20;
        newline_zh = true;
      }
      if (length < 26) {
        size_en = 30;
      } else if (length < 31) {
        size_en = 24;
      } else if (length < 41) {
        size_en = 20;
      } else if (length > 40) {
        size_en = 20;
        newline_en = true;
      }
      const size = locale === "zh_cn" ? size_zh : size_en;
      const newline = locale === "zh_cn" ? newline_zh : newline_en;
      return {
        fontSize: `${size}px`,
        whiteSpace: newline ? "pre-line" : "normal"
      };
    }
  },
  async mounted() {
    const cb = evt => {
      if (evt.key === "Enter") {
        $(this.$el).find(".login-form .login-btn").click();
      }
    };

    const $document = $(window.document);
    $document.on("keyup", cb);
    this.$on("hook:beforeDestroy", () => $document.off("keyup", cb));
    this.codeSrc = await getImg("Qrcode");
  }
};
</script>

<style lang="scss" scoped>
.login {
  width: 100%;
  height: 100%;
  @include background_static(LOGIN_BG);
  background-size: cover !important;
  .loginSetCodeBox {
    position: absolute;
    top: 24px;
    left: 32px;
    margin: 20px 20px 0px 0px;
    width: 200px;
    height: 48px;
    .imageCode {
      width: 48px;
      height: 48px;
      position: absolute;
      top: 0px;
      cursor: pointer;
      .codeIcon {
        width: 48px;
        height: 48px;
        color: var(--T2);
      }
    }
    .codeImg {
      width: 150px;
      height: 150px;
      z-index: 10001;
      position: absolute;
      top: 60px;
      left: 10px;
    }
    .downloadBox {
      display: flex;
      flex-direction: column;
      position: absolute;
      left: 58px;
      top: 0px;
      color: var(--T2);
      p {
        margin: 0px;
      }
      p:first-child {
        margin-bottom: var(--J1);
      }
    }
  }
  &-main {
    position: absolute;
    top: 50%;
    margin-top: -382px;
    left: 50%;
    margin-left: -800px;
    width: 1600px;
    height: 764px;
    &-right {
      position: absolute;
      width: 640px;
      height: 100%;
      top: 0px;
      right: 0px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-repeat: no-repeat;
      border-top-right-radius: 32px;
      border-bottom-right-radius: 32px;

      @include background_color(BG1);
      @include box_shadow(S1);
      background-size: 100% 100%;
      z-index: 1000;
      .login-right-content {
        width: 440px;
      }
    }
    &-left {
      position: absolute;
      height: 100%;
      top: 0px;
      right: 640px;
      left: 0px;
      background-size: 100% 100%;
      border-top-left-radius: 32px;
      border-bottom-left-radius: 32px;
      @include background_image_static(LOGIN_CAROUSEL_IMG);
    }
  }
}

.login-logo {
  position: relative;
  // height: 194px;
  display: flex;
  justify-content: center;
  // align-items: center;
  &-main {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  &-img {
    background: no-repeat center bottom;
    background-size: contain;
    width: 300px;
    height: 100px;
    @include background_image_static(LOGIN_LOGO_IMG);
    @include margin_bottom(J3);
  }
  &-text {
    max-width: 440px;
    // @include line_height(H1);
    color: var(--T2);
    text-align: center;
    padding: 0;
    font-weight: 400;
    //超过两行即显示省略号
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  &-subtitle {
    max-width: 440px;
    color: var(--T3);
    text-align: center;
    font-weight: 400;
    //超过两行即显示省略号
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
}
.login-form {
  height: calc(100% - 280px);
  padding: var(--J2) 52px;
}
.login-form::v-deep .el-tabs {
  .el-tabs__nav {
    width: 100%;
  }
  .el-tabs__item {
    width: 33%;
    text-align: center;
    padding-right: 0;
    &:not(.is-active) {
      @include font_color(T3);
    }
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
}
</style>
