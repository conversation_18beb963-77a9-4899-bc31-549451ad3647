import fetch from "eem-base/utils/fetch";

//获取项目能源类型
export function getProjectEnergy() {
  return fetch({
    url: `/eembasecost/v1/energy-type/projectEnergy`,
    method: "GET"
  });
}

// 获取不包含折标能源及扩展能源的租户能源类型
export function getNoStandardizedEnergy(queryExtendEnergyType = false) {
  return fetch({
    url: `/eembasecost/v1/energy-type/projectEnergy/no-standardized`,
    method: "GET",
    params: {
      queryExtendEnergyType: queryExtendEnergyType
    }
  });
}
