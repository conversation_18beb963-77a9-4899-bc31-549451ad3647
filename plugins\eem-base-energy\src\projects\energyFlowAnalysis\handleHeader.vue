<template>
  <div class="flex flex-row items-center">
    <customElSelect
      class="mr-J3"
      v-model="ElSelect_1.value"
      v-bind="ElSelect_1"
      v-on="ElSelect_1.event"
      :prefix_in="$T('能源类型')"
    >
      <ElOption
        v-for="item in ElOption_1.options_in"
        :key="item[ElOption_1.key]"
        :label="item[ElOption_1.label]"
        :value="item[ElOption_1.value]"
        :disabled="item[ElOption_1.disabled]"
      ></ElOption>
    </customElSelect>
    <customElSelect
      :prefix_in="$T('所属项目')"
      v-model="ElSelect_project.value"
      v-bind="ElSelect_project"
      v-on="ElSelect_project.event"
      class="mr-J3"
      v-show="ElOption_project.options_in?.length > 1"
    >
      <ElOption
        v-for="item in ElOption_project.options_in"
        :key="item[ElOption_project.key]"
        :label="item[ElOption_project.label]"
        :value="item[ElOption_project.value]"
        :disabled="item[ElOption_project.disabled]"
      ></ElOption>
    </customElSelect>
    <div class="eem-cascader mr-J3">
      <span class="eem-cascader-label">{{ $T("选择起始节点") }}</span>
      <el-cascader
        ref="cascader"
        :options="cascaderOptions"
        :props="{ checkStrictly: true, label: 'name', value: 'tree_id' }"
        v-model="treeNodeId"
        :show-all-levels="false"
        filterable
        @change="setParams(3)"
        clearable
        popper-class="start-node-cascader"
      >
        <template slot-scope="{ data }">
          <el-tooltip
            :content="data.name"
            placement="top-start"
            :disabled="data.name?.length < 12"
          >
            <span>{{ data.name }}</span>
          </el-tooltip>
        </template>
      </el-cascader>
    </div>
    <el-autocomplete
      v-if="!multidimensional"
      ref="autocomplete"
      popper-class="tree-popper"
      v-model="filterValue"
      suffix-icon="el-icon-search"
      :fetch-suggestions="querySearch"
      :placeholder="$T('请输入节点名称进行搜索')"
      clearable
      @clear="handleClear"
      @select="handleSelect"
      class="w-[320px] mr-J3"
    >
      <template>
        <div class="tree-wrapper">
          <el-tree
            ref="selectTree"
            :data="filterTreeData"
            :props="treeProps"
            :filter-node-method="filterNode"
            node-key="value"
            highlight-current
            @node-click="handleNodeClick"
          />
        </div>
      </template>
    </el-autocomplete>
    <customElSelect
      v-show="showType"
      class="fl mr-J3"
      v-model="ElSelect_type.value"
      v-bind="ElSelect_type"
      v-on="ElSelect_type.event"
      :prefix_in="$T('展示类型')"
    >
      <ElOption
        v-for="item in ElOption_type.options_in"
        :key="item[ElOption_type.key]"
        :label="item[ElOption_type.label]"
        :value="item[ElOption_type.value]"
        :disabled="item[ElOption_type.disabled]"
      ></ElOption>
    </customElSelect>
    <customElSelect
      class="fl mr-J3"
      :style="{ width: language ? '240px' : '160px' }"
      v-model="ElSelect_2.value"
      v-bind="ElSelect_2"
      v-on="ElSelect_2.event"
      :prefix_in="$T('分析周期')"
    >
      <ElOption
        v-for="item in ElOption_2.options_in"
        :key="item[ElOption_2.key]"
        :label="item[ElOption_2.label]"
        :value="item[ElOption_2.value]"
        :disabled="item[ElOption_2.disabled]"
      ></ElOption>
    </customElSelect>
    <CetButton
      class="fl mr-J0"
      v-bind="CetButton_prv"
      v-on="CetButton_prv.event"
    ></CetButton>
    <CustomElDatePicker
      :prefix_in="$T('时间')"
      v-model="CetDatePicker_1.val"
      v-bind="CetDatePicker_1.config"
      :clearable="false"
    ></CustomElDatePicker>
    <CetButton
      class="fl ml-J0"
      v-bind="CetButton_next"
      v-on="CetButton_next.event"
    ></CetButton>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import DomCalc from "eem-base/utils/domCalc.js";
import omegaI18n from "@omega/i18n";
export default {
  name: "handleHeader",
  props: {
    standardEnergyTypes: {
      type: Array
    }
  },
  components: {},
  computed: {
    systemCfg() {
      return this.$store.state.systemCfg;
    },
    language() {
      return omegaI18n.locale === "en";
    },
    treeHideModelLabels() {
      return (
        this.$store.state.systemCfg.energyFlowAnalysisHideTreeModelLabels || []
      );
    },
    multidimensional() {
      return this.$store.state.multidimensional;
    },
    rootNode() {
      const id = this.ElSelect_project.value;
      const list = this.ElOption_project.options_in;
      return list.find(item => item.id === id);
    }
  },
  data() {
    const en = omegaI18n.locale === "en";
    return {
      searchValue: undefined,
      filterValue: undefined,
      filterTreeData: [],
      selectNode: null,
      treeProps: {
        label: "label",
        id: "value"
      },
      initTreeData: null,
      showType: false,
      ElSelect_1: {
        value: "",
        style: {
          width: "200px"
        },
        size: "small",
        filterable: true,
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_type: {
        value: 1,
        style: {
          width: en ? "280px" : "200px"
        },
        size: "small",
        filterable: true,
        event: {
          change: this.ElSelect_type_change_out
        }
      },
      ElOption_type: {
        options_in: [
          {
            id: 1,
            name: $T("能流图")
          },
          {
            id: 2,
            name: $T("拓扑图")
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: 14,
        size: "small",
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [
          {
            id: 12,
            text: $T("日")
          },
          {
            id: 14,
            text: $T("月")
          },
          {
            id: 17,
            text: $T("年")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetDatePicker_1: {
        disable_in: false,
        val: "",
        config: {
          type: "month",
          format: "yyyy-MM",
          rangeSeparator: $T("至"),
          size: "small",
          style: {
            width: "220px"
          },
          pickerOptions: {
            disabledDate(time) {
              // return time.getTime() > Date.now();
            },
            // 添加回到今天的快捷键
            shortcuts: [
              {
                text: this.$T("当月"),
                onClick(picker) {
                  picker.$emit("pick", new Date());
                }
              }
            ]
          }
        }
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: "",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      params: {
        energytype: null,
        startTime: null,
        endTime: null
      },
      isInit: true,
      treeNodeId: [],
      cascaderOptions: [],
      ElSelect_project: {
        value: 0,
        style: {},
        event: {
          change: this.ElSelect_project_change_out
        }
      },
      ElOption_project: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  watch: {
    "CetDatePicker_1.val": function (val, old) {
      if (val) {
        this.setParams();
      }
    }
  },

  methods: {
    //清空搜索框
    handleClear() {
      if (!this.$refs.selectTree) return;
      this.$refs.selectTree.setCurrentKey(null);
      this.selectNode = null;
      this.setParams();
    },
    //清空选中的树节点
    clearSelectNode() {
      this.filterValue = undefined;
      if (!this.$refs.selectTree) return;
      this.$refs.selectTree.setCurrentKey(null);
      this.selectNode = null;
    },
    //过滤节点
    filterNode(value, data) {
      if (!value) return true;
      const text = _.isEmpty(this.treeNodeId) ? "name" : "label";
      return data[text].toLowerCase().includes(value.toLowerCase());
    },
    //模糊搜索过滤
    querySearch(str, cb) {
      this.$refs.selectTree?.filter(str);
      cb([{}]);
    },
    //选中
    handleSelect() {},
    //点击树节点
    handleNodeClick(node) {
      //再次点击取消选中
      const text = _.isEmpty(this.treeNodeId) ? "tree_id" : "value";
      const label = _.isEmpty(this.treeNodeId) ? "name" : "label";
      if (this.selectNode && this.selectNode === node[text]) {
        this.$refs.selectTree.setCurrentKey(null);
        this.selectNode = null;
        this.filterValue = undefined;
      } else {
        this.selectNode = node[text];
        this.filterValue = node[label];
      }
      this.$refs.selectTree?.filter(this.filterValue);
      this.setParams(1);
    },
    //切换能耗类型，重新筛选节点树，不显示拓扑图的能源类型改为由接口获取
    async ElSelect_1_change_out(val) {
      this.ElSelect_type.value = 1;
      this.showType = !this.standardEnergyTypes.includes(val);
      await this.clearSelectNode();
      await this.getTreeData();
      await this.setParams();
    },
    //切换展示类型
    ElSelect_type_change_out(val) {
      this.setParams();
    },
    // 切换查询时段
    ElSelect_2_change_out(val) {
      if (val === 12) {
        this.CetDatePicker_1.config.type = "date";
        this.CetDatePicker_1.config.format = "yyyy-MM-dd";
        this.CetDatePicker_1.config.pickerOptions.shortcuts[0].text =
          this.$T("今天");
      } else if (val === 14) {
        this.CetDatePicker_1.config.type = "month";
        this.CetDatePicker_1.config.format = "yyyy-MM";
        this.CetDatePicker_1.config.pickerOptions.shortcuts[0].text =
          this.$T("当月");
      } else {
        this.CetDatePicker_1.config.type = "year";
        this.CetDatePicker_1.config.format = "yyyy";
        this.CetDatePicker_1.config.pickerOptions.shortcuts[0].text =
          this.$T("今年");
      }
      this.setParams();
    },
    //点击上一时段按钮
    CetButton_prv_statusTrigger_out(val) {
      if (this.CetDatePicker_1.val) {
        const date = this.$moment(this.CetDatePicker_1.val);
        if (this.ElSelect_2.value === 17) {
          this.CetDatePicker_1.val = date.subtract(1, "y")._d;
        } else if (this.ElSelect_2.value === 14) {
          this.CetDatePicker_1.val = date.subtract(1, "M")._d;
        } else if (this.ElSelect_2.value === 12) {
          this.CetDatePicker_1.val = date.subtract(1, "d")._d;
        }
      } else {
        this.CetDatePicker_1.val = new Date();
      }
    },
    //点击下一段时间按钮
    CetButton_next_statusTrigger_out(val) {
      if (this.CetDatePicker_1.val) {
        const date = this.$moment(this.CetDatePicker_1.val);
        if (this.ElSelect_2.value === 17) {
          this.CetDatePicker_1.val = date.add(1, "y")._d;
        } else if (this.ElSelect_2.value === 14) {
          this.CetDatePicker_1.val = date.add(1, "M")._d;
        } else if (this.ElSelect_2.value === 12) {
          this.CetDatePicker_1.val = date.add(1, "d")._d;
        }
      } else {
        this.CetDatePicker_1.val = new Date();
      }
    },
    // 获取能耗类型
    async queryProjectEnergyList_out() {
      const res = await customApi.getProjectEnergy();
      if (res.code === 0) {
        //energytype===13，就是综合能耗
        let resData = res.data || [];
        if (resData.length === 0) {
          this.$message({
            type: "warning",
            message: $T("请先配置项目能源类型")
          });
        }
        let energyList = [];
        this.standardEnergyTypes.forEach(type => {
          const obj = resData.find(item => item.energytype === type);
          if (obj) {
            energyList.push(obj);
          }
        });
        resData = resData.filter(
          item => !this.standardEnergyTypes.includes(item.energytype)
        );
        energyList.forEach(obj => {
          resData.unshift(obj);
        });
        this.ElOption_1.options_in = resData;
        this.ElSelect_1.value = resData[0] && resData[0].energytype;
        this.showType = !this.standardEnergyTypes.includes(
          this.ElSelect_1.value
        );
        this.setParams();
        this.$emit("getEnergyTypeList", resData);
      } else {
        this.ElOption_1.options_in = [];
        this.ElSelect_1.value = null;
      }
    },
    setParams(val = 2) {
      if (this.isInit) {
        return;
      }
      if (!this.multidimensional && val === 3) {
        if (_.isEmpty(this.treeNodeId)) {
          this.treeProps.label = "name";
          this.treeProps.id = "tree_id";
          this.filterTreeData = _.cloneDeep(this.initTreeData);
        } else {
          this.treeProps.label = "label";
          this.treeProps.id = "value";
          this.filterTreeData = this.$refs.cascader.getCheckedNodes();
        }
        this.clearSelectNode();
      }
      const type = this.ElSelect_2.value;
      const date = this.$moment(this.CetDatePicker_1.val);
      const param = {
        aggregationCycle: type,
        energyType: this.ElSelect_1.value,
        time: null,
        type: this.ElSelect_type.value,
        energyTypeName: "",
        selectId: this.selectNode,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };
      if (!this.treeNodeId?.length) {
        return;
      }

      if (this.treeNodeId.length) {
        const tree_id = this.treeNodeId[this.treeNodeId.length - 1];
        const [modelLabel, id] = tree_id.split("_");
        param.node = {
          id: +id,
          modelLabel
        };
        param.dimTreeConfigId = this.multidimensional
          ? +this.treeNodeId[0].split("_")?.[1]
          : -1;
      }
      if (type === 17) {
        param.time = date.startOf("year").valueOf();
      } else if (type === 14) {
        param.time = date.startOf("month").valueOf();
      } else if (type === 12) {
        param.time = date.startOf("day").valueOf();
      }
      let findObj = this.ElOption_1.options_in.find(
        item => item.energytype === this.ElSelect_1.value
      );
      if (findObj) {
        param.energyTypeName = findObj.name;
      }
      this.$emit("handleHeader", param);
    },
    async getTreeData() {
      this.treeNodeId = null;
      this.cascaderOptions = [];
      this.filterTreeData = [];

      const treeType = await this.getTreeType();
      if (!treeType?.length) return;
      const queryData = {
        plugin: "eem-base",
        business: "energy-analysis",
        energyType: this.ElSelect_1.value,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };
      const res = await customApi.getBusinessTree(queryData);
      const data = res?.data ?? [];
      if (!data?.length) return;
      let cascaderOptions, treeNodeId;
      if (this.treeHideModelLabels.includes("project")) {
        cascaderOptions = data[0].children;
      } else {
        cascaderOptions = data;
      }

      // 禁选叶子节点、及无全部子节点权限的节点（childSelectState为1有全部子节点权限、为2没有）
      cascaderOptions.forEach(item => {
        this.disableNode(item);
      });

      let parent = [];
      this.getFirstOptionalNode(cascaderOptions, parent, 0);
      treeNodeId = _.cloneDeep(parent);

      const manageType = treeType.find(i => i.id === -1);
      if (manageType) {
        manageType.children = cascaderOptions;
        manageType.disabled = true;
        treeNodeId.unshift(manageType.tree_id);
      } else if (this.multidimensional) {
        const nodeM = this.getFirstNode(treeType);
        treeNodeId = nodeM ? nodeM.tree_id : null;
      }
      if (!this.multidimensional) {
        this.cascaderOptions = cascaderOptions;
        this.treeNodeId = parent;
      } else {
        this.cascaderOptions = treeType;
        this.treeNodeId = treeNodeId;
      }

      this.treeProps.label = "label";
      this.treeProps.id = "value";
      this.initTreeData = _.cloneDeep(this.cascaderOptions);
      this.$nextTick(() => {
        this.filterTreeData = this.$refs.cascader.getCheckedNodes();
      });
    },
    // 一级一级的选择可选的第一个节点
    getFirstOptionalNode(list, parent, num) {
      for (let i = 0; i < list.length; i++) {
        if (list[i].disabled === false) {
          parent[num] = list[i].tree_id;
          return;
        }
      }
      for (let i = 0; i < list.length; i++) {
        if (list[i].children) {
          parent[num] = list[i].tree_id;
          this.getFirstOptionalNode(list[i].children, parent, ++num);
        }
      }
    },
    getFirstNode(list) {
      for (let i = 0; i < list.length; i++) {
        if (list[i].children) {
          return list[i];
        }
      }
      return null;
    },
    disableNode(node) {
      if (node.children) {
        node.disabled = node.childSelectState !== 1;
        node.children.forEach(item => {
          this.disableNode(item);
        });
      } else {
        node.disabled = true;
      }
    },
    async getTreeType() {
      const queryData = {
        status: true,
        rootNode: {
          id: this.rootNode.id,
          modelLabel: this.rootNode.modelLabel
        }
      };
      const res = await customApi.getAttributeDimensionTreeNodeConfig(
        queryData
      );
      const data = res?.data ?? [];
      data.forEach(item => {
        item.tree_id = `${item.modelLabel}_${item.id}`;
      });
      return data;
    },

    ElSelect_project_change_out() {
      this.getTreeData();
    },

    async getRootNode() {
      this.ElOption_project.options_in = [];
      const res = await customApi.rootNode();
      const data = res.data || [];
      this.ElOption_project.options_in = data;
      this.ElSelect_project.value = data?.[0]?.id;
    }
  },
  async mounted() {
    const textWidth = DomCalc.calcTextWidthPad($T("选择起始节点"), 16);
    const dom = this.$refs.cascader.$el.querySelector(
      ".el-cascader .el-input .el-input__inner"
    );
    dom.style.paddingLeft = `${textWidth}px`;
    await this.queryProjectEnergyList_out();
    await this.getRootNode();
    await this.getTreeData();
    this.isInit = true;
    this.ElSelect_type.value = 1;
    this.CetDatePicker_1.val = new Date();
    this.filterValue = undefined;
    this.selectNode = null;
    this.isInit = false;
  }
};
</script>
<style lang="scss" scoped>
.eem-cascader {
  position: relative;
  width: 220px;
  .eem-cascader-label {
    position: absolute;
    z-index: 1;
    height: 32px;
    line-height: 32px;
    left: 12px;
    @include font_color(ZS);
  }
}
:deep(.el-icon-circle-close) {
  color: var(--T3);
}
:deep(.el-icon-circle-close:hover) {
  color: var(--ZS);
}
</style>
<style lang="scss">
.tree-popper {
  li {
    padding: 0;
    overflow: visible;
  }
}
.start-node-cascader {
  .el-cascader-node__label {
    max-width: 180px;
  }
}
</style>
