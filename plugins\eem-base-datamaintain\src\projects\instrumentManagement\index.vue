<template>
  <div class="page">
    <el-container class="flex flex-col fullheight eem-container padding0">
      <el-tabs v-model="selectedTabs">
        <el-tab-pane
          v-for="item in tabsList"
          :label="item.label"
          :name="item.name"
          :key="item.id"
        ></el-tab-pane>
      </el-tabs>
      <el-container class="flex flex-col flex-auto eem-container">
        <Ledger v-if="selectedTabs === 'ledger'"></Ledger>
        <Verification v-if="selectedTabs === 'verification'"></Verification>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import Ledger from "./ledger";
import Verification from "./verification";
export default {
  components: {
    Ledger,
    Verification
  },
  data() {
    return {
      selectedTabs: "ledger",
      tabsList: [
        // { id: 1, label: $T("仪表通讯管理"), name: "communication" },
        { id: 2, label: $T("计量仪表台账"), name: "ledger" },
        { id: 3, label: $T("计量仪表检定"), name: "verification" }
      ]
    };
  }
};
</script>

<style lang="scss" scoped>
.page {
  height: 100%;
  box-sizing: border-box;
  background-color: var(--BG1);
  :deep(.el-tabs__item) {
    height: 48px;
    line-height: 48px;
  }
  :deep(.el-tabs__item.is-top:nth-child(2)) {
    padding-left: 16px;
  }
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tab-pane) {
    border-top: 1px solid;
    @include border_color(B2);
  }
}
</style>
