<template>
  <el-drawer
    class="detail-drawer"
    :title="$T('详情')"
    :visible.sync="drawerVisible"
    destroy-on-close
    size="960px"
  >
    <div class="drawer-content">
      <!-- 基础信息 -->

      <el-row :gutter="24">
        <el-col :span="8">
          <div class="info-item">
            <div class="label">{{ $T("机组名称") }}</div>
            <div>{{ detailData.unitName || "--" }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <div class="label">{{ $T("机组类型") }}</div>
            <div>{{ detailData.unitType || "--" }}</div>
          </div>
        </el-col>
      </el-row>

      <!-- 资源列表 -->
      <div class="resource-list-section">
        <div class="section-title">
          {{ detailData.unitType + $T("资源列表") }}
        </div>

        <!-- 资源表格 -->
        <div class="resource-table-wrapper">
          <el-table :data="tableData" class="resource-table" height="640">
            <el-table-column
              type="index"
              :label="$T('序号')"
              width="60"
            ></el-table-column>

            <el-table-column
              prop="resourceId"
              :label="$T('资源ID')"
              min-width="50"
              show-overflow-tooltip
            />

            <el-table-column
              prop="resourceName"
              :label="$T('资源名称')"
              min-width="100"
              show-overflow-tooltip
            />

            <el-table-column
              prop="address"
              :label="$T('地址')"
              min-width="80"
              show-overflow-tooltip
            />
            <el-table-column
              prop="districtName"
              :label="$T('区域')"
              min-width="140"
              show-overflow-tooltip
            />

            <el-table-column
              prop="capacity"
              :label="$T('报装容量（kVA）')"
              min-width="120"
            />

            <el-table-column
              prop="directControl"
              :label="$T('平台直控')"
              min-width="60"
            >
              <template slot-scope="scope">
                {{ scope.row.directControl ? $T("是") : $T("否") }}
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页器 -->
        <div class="text-right">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 40, 50]"
            :page-size="pageSize"
            :total="totalResourceCount"
            layout="total, sizes,prev, pager, next, jumper"
            @current-change="handlePageChange"
            @size-change="handlePageSizeChange"
          />
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { unbindResource, getUnitResources } from "@/api/resource-aggregation";
import { getGeographicalData } from "@/api/base-config";
import { getGeographicalNameByCode } from "@/utils/geographicalUtils";

export default {
  name: "DetailDrawer",
  props: {
    showDetail: {
      type: Number,
      default: 0
    },
    detailData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 抽屉控制
      drawerVisible: false,

      // 分页相关
      currentPage: 1,
      pageSize: 20,
      // 资源数据
      tableData: [],
      totalResourceCount: 0,
      // 区域数据
      areaOptions: []
    };
  },
  watch: {
    showDetail() {
      this.drawerVisible = true;
      this.currentPage = 1; // 重置分页
      this.loadResourceData();
    }
  },
  methods: {
    // 加载资源数据
    async loadResourceData() {
      if (!this.detailData || !this.detailData.unitId) {
        this.tableData = [];
        this.totalResourceCount = 0;
        return;
      }

      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        unitId: this.detailData.unitId
      };

      const response = await getUnitResources(params);

      if (response.code === 0 && response.data) {
        // 适配返回的数据格式
        const resourceList = response.data || [];
        this.tableData = resourceList.map(item => ({
          resourceId: item.id,
          resourceName: item.resource_name,
          district: item.district,
          districtName: getGeographicalNameByCode(
            this.areaOptions,
            item.province,
            item.city,
            item.district
          ),
          capacity: item.registered_capacity,
          directControl: item.platform_direct_control,
          address: item.address || "--"
        }));

        this.totalResourceCount = response?.total || 0;
      } else {
        this.tableData = [];
        this.totalResourceCount = 0;
      }
    },
    // 加载区域列表
    async loadDistricts() {
      const response = await getGeographicalData();
      if (response.code === 0 && response.data) {
        this.areaOptions = response.data || [];
      }
    },

    // 分页变化
    handlePageChange(page) {
      this.currentPage = page;
      this.loadResourceData();
    },

    // 页面大小变化
    handlePageSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1; // 重置到第一页
      this.loadResourceData();
    }
  },
  mounted() {
    this.loadDistricts();
  }
};
</script>

<style lang="scss" scoped>
.detail-drawer {
  .drawer-content {
    padding: var(--J4);
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--J4);

    .info-item {
      .label {
        line-height: 22px;
        color: var(--T4);
      }
    }

    .resource-list-section {
      .section-title {
        font-weight: 700;
        line-height: 22px;
        margin-bottom: var(--J3);
      }

      .resource-table-wrapper {
        margin-bottom: var(--J1);
        .resource-table {
          .unbind-btn {
            columns: var(--ZS);
            padding: 0;
          }
        }
      }
    }
  }
}
</style>
