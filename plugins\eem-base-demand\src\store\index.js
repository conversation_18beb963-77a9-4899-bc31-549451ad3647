import { modules, state, mutations, actions } from "eem-base/store/index.js";
import customApi from "@/api/custom";

export default {
  modules: { ...modules },
  state: {
    ...state,
    systemCfg: {
      timingTime: 60000
    },
    treeData: []
  },
  getters: {
    treeData(state) {
      return state.treeData;
    }
  },
  mutations: {
    ...mutations,
    setTreeData(state, val) {
      state.treeData = val;
    }
  },
  actions: {
    ...actions,
    getDemandNodes({ commit }, { id, modelLabel }) {
      commit("setTreeData", []);
      if (id == "" || modelLabel == "") {
        return Promise.reject();
      }
      return new Promise(resolve => {
        customApi.getDemandNodes({ id, modelLabel }).then(response => {
          const { code, data } = response;
          if (code === 0 && data) {
            data.forEach(item => {
              item.children?.forEach(el => {
                el["fatherId"] = item.id;
                el["fatherModelLabel"] = item.modelLabel;
              });
            });
            commit("setTreeData", data);
            resolve(data);
          }
        });
      });
    }
  }
};
