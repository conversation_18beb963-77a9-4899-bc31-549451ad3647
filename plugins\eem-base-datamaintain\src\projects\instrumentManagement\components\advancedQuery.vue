<template>
  <div>
    <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
      <el-main class="eem-cont-c1 fullheight">
        <CetForm
          :data.sync="CetForm_1.data"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
          class="form"
        >
          <el-row :gutter="20" class="row-box">
            <el-col :span="8" v-for="(item, index) in templateArr" :key="index">
              <el-form-item :label="`${item.alias}`" :prop="item.name">
                <!-- 测量对象使用级联选择 -->
                <el-cascader
                  v-if="item.name == 'monitorname' || item.name == 'deviceid'"
                  v-model="CetForm_1.data[item.name]"
                  :props="item.name == 'monitorname' ? simpleProps : props"
                  :options="item.name == 'monitorname' ? simpleList : options"
                  :show-all-levels="false"
                  collapse-tags
                  class="w-[250px]"
                  popper-class="monitor-cascader"
                >
                  <template slot-scope="{ node, data }">
                    <el-tooltip
                      :content="data.text || data.name"
                      placement="top-start"
                      :disabled="(data.text || data.name)?.length < 12"
                    >
                      <span>{{ data.text || data.name }}</span>
                    </el-tooltip>
                  </template>
                </el-cascader>
                <ElInput
                  v-else-if="item.datatype == 'string'"
                  v-model.trim="CetForm_1.data[item.name]"
                  v-bind="ElInput_string"
                  v-on="ElInput_string.event"
                ></ElInput>
                <!-- 单独处理检定/校准周期 增加周期选项 -->
                <div
                  v-else-if="
                    item.datatype == 'int4' &&
                    item.name === 'plannedverificationcycle'
                  "
                >
                  <ElInputNumber
                    v-model="CetForm_1.data[item.name]"
                    :min="
                      item.name === 'plannedverificationcycle' ? 1 : -99999999
                    "
                    v-bind="ElInputNumber_num1"
                    v-on="ElInputNumber_num1.event"
                  ></ElInputNumber>
                  <el-select
                    v-model="CetForm_1.data.verificationcycle"
                    style="width: 64px"
                    class="cycleSelect"
                    placeholder=""
                  >
                    <ElOption
                      v-for="item in cycleList"
                      :key="item.id"
                      :label="item.text"
                      :value="item.id"
                    ></ElOption>
                  </el-select>
                </div>
                <ElInputNumber
                  v-else-if="item.datatype == 'int4'"
                  v-model="CetForm_1.data[item.name]"
                  v-bind="ElInputNumber_num1"
                  v-on="ElInputNumber_num1.event"
                ></ElInputNumber>
                <ElInputNumber
                  v-else-if="item.datatype == 'int8'"
                  v-model="CetForm_1.data[item.name]"
                  v-bind="ElInputNumber_num1"
                  v-on="ElInputNumber_num1.event"
                ></ElInputNumber>
                <ElInputNumber
                  v-else-if="item.datatype == 'float'"
                  v-model="CetForm_1.data[item.name]"
                  v-bind="ElInputNumber_num2"
                  v-on="ElInputNumber_num2.event"
                ></ElInputNumber>
                <el-date-picker
                  style="width: 250px !important"
                  v-else-if="item.datatype == 'date'"
                  v-model="CetForm_1.data[item.name]"
                  :placeholder="$T('选择日期')"
                  value-format="timestamp"
                  type="daterange"
                  :range-separator="$T('至')"
                  :start-placeholder="$T('开始日期')"
                  :end-placeholder="$T('结束日期')"
                ></el-date-picker>
                <ElSelect
                  v-else-if="item.datatype == 'boolean'"
                  v-model="CetForm_1.data[item.name]"
                  v-bind="ElSelect_boolean"
                  v-on="ElSelect_boolean.event"
                >
                  <ElOption
                    v-for="item in ElOption_boolean.options_in"
                    :key="item[ElOption_boolean.key]"
                    :label="item[ElOption_boolean.label]"
                    :value="item[ElOption_boolean.value]"
                    :disabled="item[ElOption_boolean.disabled]"
                  ></ElOption>
                </ElSelect>
                <ElSelect
                  v-else-if="
                    (item.datatype == 'enum' ||
                      (item.enumerationvalue &&
                        item.enumerationvalue.length)) &&
                    item.name !== 'metertype'
                  "
                  v-model="CetForm_1.data[item.name]"
                  v-bind="ElSelect_select"
                  v-on="ElSelect_select.event"
                >
                  <ElOption
                    v-for="(item, index) in item.enumerationvalue"
                    :key="index"
                    :label="item.text"
                    :value="item.id"
                  ></ElOption>
                </ElSelect>
                <el-cascader
                  ref="cascader"
                  style="width: 250px"
                  v-if="item.datatype == 'enum' && item.name === 'metertype'"
                  clearable
                  v-model="CetForm_1.data[item.name]"
                  :props="metertypeProps"
                  :options="metertypeOptions"
                  collapse-tags
                  :show-all-levels="false"
                  popper-class="pecevent-type-cascader"
                  @visible-change="visiblePeceventType"
                ></el-cascader>
              </el-form-item>
            </el-col>
          </el-row>
        </CetForm>
      </el-main>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_reset"
          v-on="CetButton_reset.event"
        ></CetButton>
        <CetButton
          class="ml-J1"
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="ml-J1"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import common from "eem-base/utils/common.js";

export default {
  name: "templateAdmin",
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    template_in: {
      type: Array,
      default() {
        return [];
      }
    },
    queryCondition_in: {
      type: Object,
      default() {
        return {};
      }
    },
    monitorOptions: {
      type: Array
    },
    metertypeOptions: {
      type: Array,
      default: () => []
    },
    simpleList: {
      type: Array,
      default: () => []
    }
  },
  data(vm) {
    return {
      props: {
        label: "text",
        value: "id",
        children: "children",
        multiple: true
      },
      simpleProps: {
        label: "name",
        value: "id",
        children: "children",
        multiple: true
      },
      options: null,
      templateArr: [],
      // 1表单组件
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelPosition: "top",
        labelWidth: "180px",
        rules: {},
        event: {},
        inline: true
      },
      // string组件
      ElInput_string: {
        value: "",
        clearable: true,
        placeholder: $T("请输入"),
        style: {
          width: "250px"
        },
        event: {}
      },
      ElSelect_select: {
        value: "",
        clearable: true,
        style: {
          width: "250px"
        },
        event: {}
      },
      ElSelect_boolean: {
        value: "",
        clearable: true,
        style: {
          width: "250px"
        },
        event: {}
      },
      ElOption_boolean: {
        options_in: [
          {
            value: true,
            label: $T("是")
          },
          {
            value: false,
            label: $T("否")
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      ElInputNumber_num1: {
        ...common.check_numberInt,
        value: "",
        controls: false,
        clearable: true,
        placeholder: $T("请输入"),
        style: {
          width: "250px"
        },
        event: {}
      },
      ElInputNumber_num2: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        clearable: true,
        placeholder: $T("请输入"),
        style: {
          width: "250px"
        },
        event: {}
      },
      // 1弹窗组件
      CetDialog_1: {
        title: $T("高级查询"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        width: "900px",
        event: {}
      },
      CetButton_reset: {
        visible_in: true,
        disable_in: false,
        title: $T("重置"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_reset_statusTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("查询"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      cycleList: [
        {
          id: 12,
          text: $T("天")
        },
        {
          id: 14,
          text: $T("月")
        },
        {
          id: 17,
          text: $T("年")
        }
      ],
      metertypeProps: {
        expandTrigger: "hover",
        children: "child",
        multiple: true,
        label: "text",
        value: "id"
      },
      nodeType: null
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.options = _.cloneDeep(this.monitorOptions);
      vm.createCondition();
      vm.CetForm_1.data = _.merge(vm._.cloneDeep(vm.queryCondition_in));
      vm.nodeType = null;
      vm.cascaderWidthStyle();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },
  methods: {
    // 构造查询表单 过滤出既启用并且显示项
    createCondition() {
      this.templateArr = this.template_in.filter(item => {
        if (!item.allowdeactivation) {
          return item.display;
        } else {
          return item.display && item.active;
        }
      });
    },
    reset() {
      this.templateArr.forEach(item => {
        this.$set(this.CetForm_1.data, item.name, undefined);
      });
    },
    CetButton_reset_statusTrigger_out(val) {
      this.reset();
    },
    processAndMerge(arr) {
      if (!arr?.length) return [];
      const processed = arr.map(subArr =>
        subArr.map(str => parseInt(str.split("_")[1], 10))
      );
      return [...new Set(processed.flat())];
    },
    CetButton_confirm_statusTrigger_out(val) {
      const params = this._.cloneDeep(this.CetForm_1.data);
      if (params?.monitorname?.length) {
        const formattedNodeList =
          this.findMatchedObjects(params.monitorname, this.simpleList) || [];
        params.monitorNodeList = formattedNodeList;
      }
      params.type = 3;

      this.$emit("queryCondition_out", params);
      this.CetDialog_1.closeTrigger_in = val;
    },
    findMatchedObjects(array, responseData) {
      if (!array?.length || !responseData?.length) return null;
      const matchedObjects = [];
      const idsToMatch = array.map(item => item[item.length - 1]);
      for (const id of idsToMatch) {
        let found = false;
        function traverse(nodes) {
          for (const node of nodes) {
            if (node.id === id) {
              matchedObjects.push(node);
              found = true;
              return;
            }
            if (node.children && Array.isArray(node.children)) {
              traverse(node.children);
              if (found) return;
            }
          }
        }
        traverse(responseData);
        found = false;
      }
      return matchedObjects;
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = val;
    },
    getParentIds(data, targetId) {
      if (!data.length || !targetId) return null;
      function findPath(node, targetId, path = []) {
        for (const item of node) {
          if (item.id === targetId) {
            return [...path, item.id];
          }
          if (item.child && item.child.length > 0) {
            const result = findPath(item.child, targetId, [...path, item.id]);
            if (result) return result;
          }
        }
        return null;
      }

      return findPath(data, targetId);
    },
    visiblePeceventType(val) {
      if (val) {
        // 弹出级联面板，在面板底部添加按钮
        const ref = this.$refs.cascader;
        let popper = ref[0].$refs.popper;
        // 避免重复插入按钮
        if (
          !Array.from(popper.children).some(
            v => v.className === "custom-buttom-row"
          )
        ) {
          const div = document.createElement("div");
          div.className = "custom-buttom-row";

          const all = document.createElement("span");
          all.className = "custom-buttom";
          all.innerHTML = $T("全选");
          div.appendChild(all);

          const clear = document.createElement("span");
          clear.className = "custom-buttom";
          clear.innerHTML = $T("清空");
          div.appendChild(clear);
          popper.appendChild(div);
          all.onclick = () => {
            if (!this.metertypeOptions.length) return;
            let selected = [];
            this.metertypeOptions.forEach((item, index) => {
              if (item.child && item.child.length > 0) {
                item.child.forEach(data => {
                  selected.push([item.id, data.id]);
                });
              } else {
                selected.push([item.id]);
              }
            });
            this.$set(this.CetForm_1.data, "metertype", selected);
          };
          clear.onclick = () => {
            this.$set(this.CetForm_1.data, "metertype", null);
          };
        }
      }
    },
    getLastUniqueElements(arr) {
      return [...new Set(arr.flat())];
    },
    async cascaderWidthStyle() {
      await this.$nextTick();
      const cascaderRef = this.$refs.cascader;
      if (cascaderRef && cascaderRef.$el) {
        const dom = cascaderRef.$el.querySelector(
          ".el-cascader .el-input .el-input__inner"
        );
        const tags = cascaderRef.$el.querySelector(".el-cascader__tags");
        if (dom && tags) {
          dom.style.paddingLeft = "72px";
          tags.style.paddingLeft = "72px";
        }
      }
    }
  },
  activated: function () {}
};
</script>
<style lang="scss" scoped>
.form {
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
  .row-box {
    display: flex;
    flex-wrap: wrap;
    margin-left: 0px !important;
  }
}
:deep(.el-range-separator) {
  width: 30px;
}
</style>
<style lang="scss" scoped>
.monitor-cascader {
  .el-cascader-node__label {
    max-width: 180px;
  }
}

.cycleSelect {
  position: absolute;
  right: 1px;
  :deep(.el-input__inner) {
    border: 0;
    height: 30px;
    line-height: 30px;
    @include background_color(BG);
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
}
.pecevent-type-cascader {
  .custom-buttom-row {
    display: flex;
    justify-content: end;
    align-items: center;
    height: 48px;
    border-top: 1px solid;
    @include border_color(B1);
  }
  .custom-buttom {
    width: 60px;
    text-align: center;
    margin-right: 8px;
    cursor: pointer;
  }
  .el-cascader-menu__wrap {
    height: 330px;
  }
}
</style>
<style lang="scss">
.pecevent-type-cascader {
  .custom-buttom-row {
    display: flex;
    justify-content: end;
    align-items: center;
    height: 48px;
    border-top: 1px solid;
    @include border_color(B1);
  }
  .custom-buttom {
    width: 60px;
    text-align: center;
    margin-right: 8px;
    cursor: pointer;
  }
  .el-cascader-menu__wrap {
    height: 330px;
  }
}
</style>
