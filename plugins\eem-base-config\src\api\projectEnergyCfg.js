import fetch from "eem-base/utils/fetch";

/**
 * 获取项目能源类型
 */
export function queryProjectEnergyList() {
  return fetch({
    url: `/eembaseconfig/v1/energy-type/projectEnergy`,
    method: "GET"
  });
}
/**
 * 删除能耗类型
 */
export function delEnergy(data) {
  return fetch({
    url: `/eem-service/v1/energy/consume`,
    method: "DELETE",
    data
  });
}

/**
 * 添加能耗
 */
export function addEnergy(data) {
  return fetch({
    url: `/eem-service/v1/energy/consume`,
    method: "POST",
    data
  });
}
/**
 * 修改能耗
 */
export function editEnergy(data) {
  return fetch({
    url: `/eem-service/v1/energy/consume`,
    method: "PUT",
    data
  });
}

/**
 * 获取项目产品列表
 */
export function queryProductList() {
  return fetch({
    url: `/eembaseconfig/v1/product`,
    method: "GET"
  });
}

/**
 * 添加产品
 */
export function addProduct(data) {
  return fetch({
    url: `eem-base/config/v1/product`,
    method: "POST",
    data
  });
}
/**
 * 修改产品
 */
export function editProduct(data) {
  return fetch({
    url: `eem-base/config/v1/product`,
    method: "PUT",
    data
  });
}
/**
 * 删除产品
 */
export function delProduct(data) {
  return fetch({
    url: `eem-base/config/v1/product`,
    method: "DELETE",
    data
  });
}

/**
 * 获取折标系数
 */
export function queryConvertedstandardcoalcoefTable(params) {
  return fetch({
    url: `/eem-service/v1/global/setting/convertedStandardCoalCoef`,
    method: "GET",
    params
  });
}

/**
 * 删除折标系数
 */
export function delConvertedstandardcoalcoef(data, params) {
  return fetch({
    url: `/eem-service/v1/global/setting/convertedStandardCoalCoef`,
    method: "DELETE",
    data,
    params
  });
}
/**
 * 添加折标系数
 */
export function addConvertedstandardcoalcoef(data, params) {
  return fetch({
    url: `/eem-service/v1/global/setting/convertedStandardCoalCoef`,
    method: "POST",
    data,
    params
  });
}
/**
 * 修改折标系数
 */
export function editConvertedstandardcoalcoef(data, params) {
  return fetch({
    url: `/eem-service/v1/global/setting/convertedStandardCoalCoef`,
    method: "PUT",
    data,
    params
  });
}
// 获取单位转换列表
export function getUnitTransition(data) {
  return fetch({
    url: `/eem-service/v1/global/setting/defineUnit`,
    method: "POST",
    data
  });
}

/**
 * 删除单位转换配置
 */
export function deleteUnitTransition(data) {
  return fetch({
    url: `/eem-service/v1/global/setting/defineUnit`,
    method: "DELETE",
    data
  });
}

/**
 * 根据能源类型查询预制的单位
 */
export function getDefaultUnitSetting(data, params) {
  return fetch({
    url: `/eem-service/v1/global/setting/unit`,
    method: "POST",
    data,
    params
  });
}

/**
 * 新增单位转换
 */
export function addUnitTransition(data) {
  return fetch({
    url: `/eem-service/v1/global/setting/defineUnit`,
    method: "PUT",
    data
  });
}
