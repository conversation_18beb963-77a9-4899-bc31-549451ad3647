import { modules, state, mutations, actions } from "eem-base/store/index.js";
import customApi from "@/api/custom";

export default {
  modules: { ...modules },
  state: {
    ...state,
    multidimensional: false // 是否展示多维度功能
  },
  mutations: {
    ...mutations,
    setMultidimensional(state, val) {
      state.multidimensional = val;
    }
  },
  actions: {
    ...actions,
    async getConfig({ commit }) {
      const res = await customApi.getConfigInfo();

      const multi = res?.data?.multidimensional || false;
      commit("setMultidimensional", multi);
    }
  }
};
