var BMapGLLib=window.BMapGLLib=BMapGLLib||{};
(function(){function m(a,b,d){0!=b.indexOf("on")&&(b="on"+b);b=new c.lang.Event(b);if(d)for(var f in d)b[f]=d[f];a.dispatchEvent(b)}function r(a){if(a){var b=a.attributes;if(b)for(var d=0,f=b.length;d<f;d++){var g=b[d].name;"function"===typeof a[g]&&(a[g]=null)}if(b=a.childnodes)for(d=0,f=b.length;d<f;d++)r(a.childnodes[d])}}function p(a){a=window.event||a;a.stopPropagation?a.stopPropagation():a.cancelBubble=!0;return c.preventDefault(a)}var c=c||{guid:"$BAIDU$"};(function(){window[c.guid]={};c.extend=
function(a,b){for(var d in b)b.hasOwnProperty(d)&&(a[d]=b[d]);return a};c.lang=c.lang||{};c.lang.guid=function(){return"TANGRAM__"+(window[c.guid]._counter++).toString(36)};window[c.guid]._counter=window[c.guid]._counter||1;window[c.guid]._instances=window[c.guid]._instances||{};c.lang.Class=function(a){this.guid=a||c.lang.guid();window[c.guid]._instances[this.guid]=this};window[c.guid]._instances=window[c.guid]._instances||{};c.lang.isString=function(a){return"[object String]"==Object.prototype.toString.call(a)};
c.isString=c.lang.isString;c.lang.isFunction=function(a){return"[object Function]"==Object.prototype.toString.call(a)};c.lang.Event=function(a,b){this.type=a;this.returnValue=!0;this.target=b||null;this.currentTarget=null};c.lang.Class.prototype.addEventListener=function(a,b,d){if(c.lang.isFunction(b)){!this.__listeners&&(this.__listeners={});var f=this.__listeners;if("string"==typeof d&&d){if(/[^\w\-]/.test(d))throw"nonstandard key:"+d;var g=b.hashCode=d}0!=a.indexOf("on")&&(a="on"+a);"object"!=
typeof f[a]&&(f[a]={});g=g||c.lang.guid();b.hashCode=g;f[a][g]=b}};c.lang.Class.prototype.removeEventListener=function(a,b){if(c.lang.isFunction(b))b=b.hashCode;else if(!c.lang.isString(b))return;!this.__listeners&&(this.__listeners={});0!=a.indexOf("on")&&(a="on"+a);var d=this.__listeners;d[a]&&d[a][b]&&delete d[a][b]};c.lang.Class.prototype.dispatchEvent=function(a,b){c.lang.isString(a)&&(a=new c.lang.Event(a));!this.__listeners&&(this.__listeners={});b=b||{};for(var d in b)a[d]=b[d];var f=this.__listeners,
g=a.type;a.target=a.target||this;a.currentTarget=this;0!=g.indexOf("on")&&(g="on"+g);c.lang.isFunction(this[g])&&this[g].apply(this,arguments);if("object"==typeof f[g])for(d in f[g])f[g][d].apply(this,arguments);return a.returnValue};c.dom=c.dom||{};c.dom._g=function(a){return c.lang.isString(a)?document.getElementById(a):a};c._g=c.dom._g;c.event=c.event||{};c.event._listeners=c.event._listeners||[];c.event.on=function(a,b,d){b=b.replace(/^on/i,"");a=c.dom._g(a);var f=function(e){d.call(a,e)},g=c.event._listeners,
n=c.event._eventFilter,l=b;b=b.toLowerCase();n&&n[b]&&(f=n[b](a,b,f),l=f.type,f=f.listener);a.addEventListener?a.addEventListener(l,f,!1):a.attachEvent&&a.attachEvent("on"+l,f);g[g.length]=[a,b,d,f,l];return a};c.on=c.event.on;c.event.un=function(a,b,d){a=c.dom._g(a);b=b.replace(/^on/i,"").toLowerCase();for(var f=c.event._listeners,g=f.length,n=!d,l,e;g--;)l=f[g],l[1]!==b||l[0]!==a||!n&&l[2]!==d||(e=l[4],l=l[3],a.removeEventListener?a.removeEventListener(e,l,!1):a.detachEvent&&a.detachEvent("on"+
e,l),f.splice(g,1));return a};c.un=c.event.un;c.preventDefault=c.event.preventDefault=function(a){a.preventDefault?a.preventDefault():a.returnValue=!1}})();var k=BMapGLLib.RichMarker=function(a,b,d){a&&b&&b instanceof BMapGL.Point&&(this._map=null,this._content=a,this._position=b,this._size=this._container=null,d=d||{},this._opts=c.extend(c.extend(this._opts||{},{enableDragging:!1,anchor:new BMapGL.Size(0,0)}),d))};k.prototype=new BMapGL.Overlay;k.prototype.initialize=function(a){var b=this._container=
document.createElement("div");this._map=a;c.extend(b.style,{position:"absolute",zIndex:BMapGL.Overlay.getZIndex(this._position.lat),background:"#FFF",cursor:"pointer"});a.getPanes().labelPane.appendChild(b);this._appendContent();this._setEventDispath();this._getContainerSize();return b};k.prototype.draw=function(){var a=this._opts.anchor,b=this._map.pointToOverlayPixel(this._position);this._container.style.left=b.x+a.width+"px";this._container.style.top=b.y+a.height+"px"};k.prototype.enableDragging=
function(){this._opts.enableDragging=!0};k.prototype.disableDragging=function(){this._opts.enableDragging=!1};k.prototype.isDraggable=function(){return this._opts.enableDragging};k.prototype.getPosition=function(){return this._position};k.prototype.setPosition=function(a){!a instanceof BMapGL.Point||(this._position=a,this.draw())};k.prototype.getAnchor=function(){return this._opts.anchor};k.prototype.setAnchor=function(a){!a instanceof BMapGL.Size||(this._opts.anchor=a,this.draw())};k.prototype._appendContent=
function(){var a=this._content;if("string"==typeof a){var b=document.createElement("DIV");b.innerHTML=a;if(1==b.childNodes.length)a=b.removeChild(b.firstChild);else for(a=document.createDocumentFragment();b.firstChild;)a.appendChild(b.firstChild)}this._container.innerHTML="";this._container.appendChild(a)};k.prototype.getContent=function(){return this._content};k.prototype.setContent=function(a){a&&(this._content=a,this._appendContent())};k.prototype._getContainerSize=function(){this._container&&
(this._size=new BMapGL.Size(this._container.offsetWidth,this._container.offsetHeight))};k.prototype.getWidth=function(){if(this._size)return this._size.width};k.prototype.setWidth=function(a){this._container&&(this._container.style.width=a+"px",this._getContainerSize())};k.prototype.getHeight=function(){if(this._size)return this._size.height};k.prototype.setHeight=function(a){this._container&&(this._container.style.height=a+"px",this._getContainerSize())};k.prototype._setEventDispath=function(){function a(e){e=
window.event||e;e=new BMapGL.Pixel(e.pageX||e.clientX||0,e.pageY||e.clientY||0);var h=b._map.pixelToPoint(e);return{pixel:e,point:h}}var b=this,d=b._container,f=!1,g=null;c.on(d,"onclick",function(e){m(b,"onclick");p(e)});c.on(d,"ontouchend",function(e){m(b,"ontouchend");m(b,"onclick");p(e)});c.on(d,"ondblclick",function(e){var h=a(e);m(b,"ondblclick",{point:h.point,pixel:h.pixel});p(e)});d.onmouseover=function(e){var h=a(e);m(b,"onmouseover",{point:h.point,pixel:h.pixel});p(e)};d.onmouseout=function(e){var h=
a(e);m(b,"onmouseout",{point:h.point,pixel:h.pixel});p(e)};var n=function(e){var h=a(e);m(b,"onmouseup",{point:h.point,pixel:h.pixel});b._container.releaseCapture?(c.un(d,"onmousemove",l),c.un(d,"onmouseup",n)):(c.un(window,"onmousemove",l),c.un(window,"onmouseup",n));b._opts.enableDragging&&(b._container.releaseCapture&&b._container.releaseCapture(),m(b,"ondragend",{point:h.point,pixel:h.pixel}),f=!1,g=null,b._setCursor("dragend"),b._container.style.MozUserSelect="",b._container.style.KhtmlUserSelect=
"",b._container.style.WebkitUserSelect="",b._container.unselectable="off",b._container.onselectstart=function(){});p(e)},l=function(e){if(b._opts.enableDragging&&f){var h=a(e),q=b._map.pointToPixel(b._position),t=h.pixel.x-g.x+q.x;q=h.pixel.y-g.y+q.y;g=h.pixel;b._position=b._map.pixelToPoint(new BMapGL.Pixel(t,q));b.draw();b._setCursor("dragging");m(b,"ondragging",{point:h.point,pixel:h.pixel});p(e)}};c.on(d,"onmousedown",function(e){var h=a(e);m(b,"onmousedown",{point:h.point,pixel:h.pixel});b._container.setCapture?
(c.on(d,"onmousemove",l),c.on(d,"onmouseup",n)):(c.on(window,"onmousemove",l),c.on(window,"onmouseup",n));b._opts.enableDragging&&(g=h.pixel,m(b,"ondragstart",{point:h.point,pixel:h.pixel}),f=!0,b._setCursor("dragstart"),b._container.setCapture&&b._container.setCapture(),b._container.style.MozUserSelect="none",b._container.style.KhtmlUserSelect="none",b._container.style.WebkitUserSelect="none",b._container.unselectable="on",b._container.onselectstart=function(){return!1});p(e)})};k.prototype._setCursor=
function(a){var b={dragstart:"-moz-grab",dragging:"-moz-grabbing",dragend:"pointer"},d={dragstart:"move",dragging:"move",dragend:"pointer"};a=-1!==navigator.userAgent.indexOf("Gecko/")?b[a]:d[a];this._container.style.cursor!=a&&(this._container.style.cursor=a)};k.prototype.remove=function(){m(this,"onremove");this._container&&r(this._container);this._container&&this._container.parentNode&&this._container.parentNode.removeChild(this._container)}})();