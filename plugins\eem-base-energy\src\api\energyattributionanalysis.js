import fetch from "eem-base/utils/fetch";

// 获取多维度树
export function nodeTreeByTime(data) {
  return fetch({
    url: `/eembaseenergy/v1/dimension-tree/time`,
    method: "POST",
    data
  });
}

// 获取同比环比
export function dimEnergyTbhb(data) {
  return fetch({
    url: `/eembaseenergy/v1/dimEnergy/tbhb`,
    method: "POST",
    data
  });
}

// 获取分时统计
export function dimEnergyTimeshare(data) {
  return fetch({
    url: `/eembaseenergy/v1/dimEnergy/timeshare`,
    method: "POST",
    data
  });
}

// 获取节点对比
export function dimEnergyCompare(data) {
  return fetch({
    url: `/eembaseenergy/v1/dimEnergy/compare`,
    method: "POST",
    data
  });
}
