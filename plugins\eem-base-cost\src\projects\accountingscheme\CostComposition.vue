<template>
  <div class="fullheight">
    <div class="flex flex-col fullheight">
      <div class="flex flex-row items-center mb-J3">
        <span class="flex-auto font-bold text-H2">{{ $T("成本构成") }}</span>
        <CetButton
          :disable_in="currentSchemeItem ? false : true"
          v-bind="CetButton_1"
          v-on="CetButton_1.event"
          v-permission="'cost_checkitem_edit'"
        ></CetButton>
      </div>
      <CetTable
        class="flex-auto"
        :data.sync="CetTable_1.data"
        :dynamicInput.sync="CetTable_1.dynamicInput"
        v-bind="CetTable_1"
        v-on="CetTable_1.event"
      >
        <template v-for="item in Columns_scheme">
          <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
        </template>
        <ElTableColumn v-bind="ElTableColumn_edit">
          <template slot-scope="scope">
            <span
              class="handel fl mr-J3"
              @click.stop="handleEdit(scope.$index, scope.row)"
              v-permission="'cost_checkitem_edit'"
            >
              {{ $T("编辑") }}
            </span>
            <span
              class="handel delete fl mr-J3"
              @click.stop="handleDelete(scope.$index, scope.row)"
              v-permission="'cost_checkitem_edit'"
            >
              {{ $T("删除") }}
            </span>
          </template>
        </ElTableColumn>
      </CetTable>
    </div>
    <AddConstComposition
      :visibleTrigger_in="AddConstComposition.visibleTrigger_in"
      :closeTrigger_in="AddConstComposition.closeTrigger_in"
      :queryId_in="AddConstComposition.queryId_in"
      :inputData_in="AddConstComposition.inputData_in"
      @finishData_out="AddConstComposition_finishData_out"
      :basicfeerateFlag="basicfeerateFlag"
      :powertarifffeerateFlag="powertarifffeerateFlag"
      :rootNode="rootNode"
    />
  </div>
</template>
<script>
import common from "eem-base/utils/common";
import AddConstComposition from "./dialog/AddConstComposition.vue";
import commonApi from "@/api/custom";
export default {
  name: "CostComposition",
  components: {
    AddConstComposition
  },
  props: ["currentSchemeItem", "allTypeList"],

  computed: {
    rootNode() {
      return {
        id: this.currentSchemeItem?.rootnodeid,
        modelLabel: this.currentSchemeItem?.rootnodelabel
      };
    }
  },

  data() {
    return {
      energyTypeArr: [],
      basicfeerateFlag: false,
      powertarifffeerateFlag: false,
      currentTabItem: null,
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: $T("添加成本构成"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      AddConstComposition: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      CetTable_1: {
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {
          record_out: this.CetTable_1_record_out
        }
      },
      Columns_scheme: [
        {
          type: "index", // selection 勾选 index 序号
          label: $T("序号"),
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "70"
        },
        {
          prop: "constName", // 支持path a[0].b
          label: $T("成本项名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol(),
          width: "150"
        },
        {
          prop: "energytype$text", // 支持path a[0].b
          label: $T("能源类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "feeratetype$text", // 支持path a[0].b
          label: $T("费用类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("费率方案"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "feesubratetype$text", // 支持path a[0].b
          label: $T("费率类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "costcheckitem$text", // 支持path a[0].b
          label: $T("成本计算项"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ],
      ElTableColumn_edit: {
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "130" //绝对宽度
      }
    };
  },
  watch: {
    "CetTable_1.data": {
      handler: function (val) {
        this.basicfeerateFlag = false;
        this.powertarifffeerateFlag = false;
        if (val?.length) {
          val.forEach(item => {
            if (item.feeratetype === 1) {
              // 电度电费
              this.basicfeerateFlag = true;
            } else if (item.feeratetype === 3) {
              // 力调电费
              this.powertarifffeerateFlag = true;
            }
          });
        }
      },
      deep: true
    },
    currentSchemeItem: {
      deep: true,
      handler(val) {
        if (val) {
          this.getSchemeDetail(val.id);
        } else {
          this.CetTable_1.data = [];
        }
      }
    }
  },

  methods: {
    async AddConstComposition_finishData_out(val) {
      const arr = this._.cloneDeep(this.CetTable_1.data);
      if (val.edit) {
        arr.forEach(item => {
          if (item.id === val.id) {
            item.costcheckitem = val.costcheckitem;
            item.constName = val.constName;
          }
        });
      } else {
        // 新增成本项
        arr.push(val);
      }
      // 验证成本项名称不能重复
      const target = arr.filter(item => item.constName === val.constName);
      if (target.length > 1) {
        return this.$message({
          message: $T("成本项名称重复"),
          type: "warning"
        });
      }
      const data = {
        costcheckitem_model: [],
        createtime: +this.$moment(),
        id: this.currentSchemeItem.id,
        name: this.currentSchemeItem.name,
        rootnodeid: this.rootNode.id,
        rootnodelabel: this.rootNode.modelLabel
      };
      arr.forEach(item => {
        data.costcheckitem_model.push({
          energyType: item.energytype,
          feeRateType: item.feeratetype,
          feescheme_id: item.feescheme_id,
          id: item.id,
          name: item.constName,
          costcheckitem: item.costcheckitem
        });
      });
      const response = await commonApi.costCheckPlanSave(data);
      if (response.code !== 0) {
        return;
      }
      this.$message({
        message: $T("保存成功"),
        type: "success"
      });
      this.AddConstComposition.closeTrigger_in = new Date().getTime();
      this.getSchemeDetail(this.currentSchemeItem.id);
    },
    CetButton_1_statusTrigger_out(val) {
      this.AddConstComposition.inputData_in = {};
      this.AddConstComposition.visibleTrigger_in = this._.cloneDeep(val);
    },
    CetTable_1_record_out(val) {
      this.currentTabItem = val.id !== -1 ? val : null;
    },
    handleEdit(index, row) {
      this.AddConstComposition.inputData_in = this._.cloneDeep({
        ...row,
        edit: true
      });
      this.AddConstComposition.visibleTrigger_in = new Date().getTime();
    },
    handleDelete(index, row) {
      this.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        beforeClose: async (action, instance, done) => {
          if (action === "confirm") {
            const params = {
              costCheckPlanId: this.currentSchemeItem.id,
              costCheckItemIds: [row.id]
            };
            const response = await commonApi.costCheckPlanComponentDelete(
              params
            );
            if (response.code !== 0) {
              return;
            }
            this.$message({
              message: $T("删除成功"),
              type: "success"
            });
            this.getSchemeDetail(this.currentSchemeItem.id);
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action !== "confirm") {
            this.$message({
              type: "info",
              message: $T("取消删除")
            });
          }
        }
      });
    },
    /**
     * 获取核算方案详情
     * @param id 方案id
     */
    async getSchemeDetail(id) {
      const response = await commonApi.getCostCheckPlanDetail(id);
      this.CetTable_1.data = [];
      if (response.code !== 0) return;

      const costcheckitem = response.data.costcheckitem_model || [];
      this.$emit("setCostcheckitem", costcheckitem);
      if (costcheckitem && costcheckitem.length > 0) {
        costcheckitem.forEach(item => {
          // 能源类型
          const target = this.allTypeList.find(
            type => type.energyType === item.energyType
          );
          item.energytype$text = target && target.energyTypeName;

          if (target?.feeRateTypesList?.length) {
            // 费用类型
            const feeRateTarget = target.feeRateTypesList.find(
              fee => fee.feeRateType === item.feeRateType
            );
            item.feeratetype$text =
              feeRateTarget && feeRateTarget.feeRateTypeName;

            // 费率类型
            if (feeRateTarget?.feeRateSubTypesList?.length) {
              const feeRateSubTarget = feeRateTarget.feeRateSubTypesList.find(
                fee => fee.feeRateSubType === item.feeratesubtype
              );
              item.feesubratetype$text =
                feeRateSubTarget && feeRateSubTarget.feeRateSubTypeName;
            }

            item.energytype = item.energyType;
            item.constName = item.name;
            item.name = item.feeSchemeName;
            item.feeratetype = item.feeRateType;
            item.costcheckitem$text =
              JSON.parse(item.costcheckitem)
                .calculateitem.join("、")
                .replace(1, $T("能耗"))
                .replace(2, $T("损耗"))
                .replace(3, $T("分摊")) || "--";
          }
        });
      }
      this.CetTable_1.data = costcheckitem;
    }
  }
};
</script>
<style lang="scss" scoped>
.handel {
  cursor: pointer;
  @include font_color(ZS);
  &.delete {
    @include font_color(Sta3);
  }
}
</style>
