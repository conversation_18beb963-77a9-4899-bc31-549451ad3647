import fetch from "eem-base/utils/fetch";
const version = "v1";
const service = "eembasedemand";

export function queryDayDemand(data) {
  return fetch({
    url: `/${service}/${version}/device-demand`,
    method: "POST",
    data
  });
}

export function queryMonthDemand(data) {
  return fetch({
    url: `/${service}/${version}/device-demand/daily-max-demand`,
    method: "POST",
    data
  });
}

export function queryDemandTree(data) {
  return fetch({
    url: `/${service}/${version}/device-demand/demand-node-tree`,
    method: "POST",
    data
  });
}

export function queryFavorite(data) {
  return fetch({
    url: `/${service}/${version}/favorites-node/node-tree`,
    method: "POST",
    data
  });
}

export function saveNewFavorite(data) {
  return fetch({
    url: `/${service}/${version}/favorites-node/add`,
    method: "POST",
    data
  });
}

export function deleteFavorite(data) {
  return fetch({
    url: `/${service}/${version}/favorites-node/delete`,
    method: "POST",
    data
  });
}

export function updateFavorite(data) {
  return fetch({
    url: `/${service}/${version}/favorites-node/update`,
    method: "POST",
    data
  });
}

export function getDemandNodes(data) {
  return fetch({
    url: `/${service}/${version}/node/tree`,
    method: "POST",
    data
  });
}
export function demandGroupUpsert(data) {
  return fetch({
    url: `/${service}/${version}/node/upsert/demandGroup`,
    method: "POST",
    data
  });
}
export function demandGroupDel(data) {
  return fetch({
    url: `/${service}/${version}/node/delete/demandGroup`,
    method: "POST",
    data
  });
}

export function delDemandAccount(data) {
  return fetch({
    url: `/${service}/${version}/node/delete/demandAccount`,
    method: "POST",
    data
  });
}
export function monitorAccountData(data) {
  return fetch({
    url: `/${service}/${version}/node/account/list`,
    method: "POST",
    data
  });
}
export function monitorLinesOfAccount(data) {
  return fetch({
    url: `/${service}/${version}/node/linesOfAccount`,
    method: "POST",
    data
  });
}
export function monitorAccount(data) {
  return fetch({
    url: `/${service}/${version}/node/upsert/demandAccount`,
    method: "POST",
    data
  });
}
export function getDemandMaintainData(data) {
  return fetch({
    url: `/${service}/${version}/maintain/query`,
    method: "POST",
    data
  });
}
export function importDemandMaintainData(data, params) {
  return fetch({
    url: `/${service}/${version}/maintain/import`,
    method: "POST",
    data,
    params
  });
}
export function saveDemandMaintainData(data, params) {
  return fetch({
    url: `/${service}/${version}/maintain/edit`,
    method: "POST",
    data,
    params
  });
}
export function getVolumeOrChargingChangeRecord(params) {
  return fetch({
    url: `/${service}/${version}/maintain/getVolumeOrChargingChangeRecord`,
    method: "GET",
    params
  });
}
