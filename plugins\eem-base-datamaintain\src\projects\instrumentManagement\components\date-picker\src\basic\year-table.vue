<template>
  <table @click="handleYearTableClick" class="el-year-table el-month-table">
    <tbody>
      <tr>
        <td class="available" :class="getCellStyle(startYear + 0)">
          <div>
            <a class="cell">{{ startYear }}</a>
          </div>
        </td>
        <td class="available" :class="getCellStyle(startYear + 1)">
          <div>
            <a class="cell">{{ startYear + 1 }}</a>
          </div>
        </td>
        <td class="available" :class="getCellStyle(startYear + 2)">
          <div>
            <a class="cell">{{ startYear + 2 }}</a>
          </div>
        </td>
        <td class="available" :class="getCellStyle(startYear + 3)">
          <div>
            <a class="cell">{{ startYear + 3 }}</a>
          </div>
        </td>
      </tr>
      <tr>
        <td class="available" :class="getCellStyle(startYear + 4)">
          <div>
            <a class="cell">{{ startYear + 4 }}</a>
          </div>
        </td>
        <td class="available" :class="getCellStyle(startYear + 5)">
          <div>
            <a class="cell">{{ startYear + 5 }}</a>
          </div>
        </td>
        <td class="available" :class="getCellStyle(startYear + 6)">
          <div>
            <a class="cell">{{ startYear + 6 }}</a>
          </div>
        </td>
        <td class="available" :class="getCellStyle(startYear + 7)">
          <div>
            <a class="cell">{{ startYear + 7 }}</a>
          </div>
        </td>
      </tr>
      <tr>
        <td class="available" :class="getCellStyle(startYear + 8)">
          <div>
            <a class="cell">{{ startYear + 8 }}</a>
          </div>
        </td>
        <td class="available" :class="getCellStyle(startYear + 9)">
          <div>
            <a class="cell">{{ startYear + 9 }}</a>
          </div>
        </td>
        <td></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</template>

<script type="text/babel">
import { hasClass } from "element-ui/src/utils/dom";
import {
  isDate,
  range,
  nextDate,
  getDayCountOfYear
} from "element-ui/src/utils/date-util";
import {
  arrayFindIndex,
  coerceTruthyValueToArray
} from "element-ui/src/utils/util";

const datesInYear = year => {
  const numOfDays = getDayCountOfYear(year);
  const firstDay = new Date(year, 0, 1);
  return range(numOfDays).map(n => nextDate(firstDay, n));
};

export default {
  props: {
    limitationMaxFn: Function,
    disabledDate: {},
    value: {},
    defaultValue: {
      validator(val) {
        // null or valid Date Object
        return val === null || (val instanceof Date && isDate(val));
      }
    },
    date: {},
    selectionMode: {}
  },

  computed: {
    startYear() {
      return Math.floor(this.date.getFullYear() / 10) * 10;
    }
  },

  methods: {
    getCellStyle(year) {
      const style = {};
      const today = new Date();

      style.disabled =
        typeof this.disabledDate === "function"
          ? datesInYear(year).every(this.disabledDate)
          : false;
      style.current =
        arrayFindIndex(
          coerceTruthyValueToArray(this.value),
          date => date.getFullYear() === year
        ) >= 0;
      style.today = today.getFullYear() === year;
      style.default =
        this.defaultValue && this.defaultValue.getFullYear() === year;

      if (style.current && this.selectionMode === "years") {
        style["in-range"] = true;
        style["start-date"] = true;
        style["end-date"] = true;
        style.current = false;
      }
      return style;
    },

    handleYearTableClick(event) {
      const target = event.target;
      if (target.tagName === "A") {
        if (hasClass(target.parentNode, "disabled")) return;
        const year = target.textContent || target.innerText;
        if (this.selectionMode === "years") {
          const value = this.value || [];
          const idx = arrayFindIndex(
            value,
            date => date.getFullYear() === Number(year)
          );
          const newValue =
            idx > -1
              ? [...value.slice(0, idx), ...value.slice(idx + 1)]
              : [...value, new Date(year)];

          if (this.limitationMaxFn && newValue.length > value.length) {
            const flag = this.limitationMaxFn();
            if (!flag) return;
          }
          this.$emit("pick", newValue);
        } else {
          this.$emit("pick", Number(year));
        }
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.el-year-table {
  :deep(td.in-range) .cell:hover {
    color: inherit;
  }
  :deep(td) {
    padding: 5px;
  }
  :deep(div) {
    height: 44px;
  }
}
</style>
