<template>
  <!-- 1弹窗组件 -->
  <CetDialog
    v-bind="CetDialog_1"
    v-on="CetDialog_1.event"
    class="small el_dialog"
  >
    <CetForm
      :data.sync="CetForm_1.data"
      v-bind="CetForm_1"
      v-on="CetForm_1.event"
      label-position="top"
    >
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item :label="$T('进线名称')" prop="name">
            <ElInput
              v-model.trim="CetForm_1.data.name"
              v-bind="ElInput_1"
              v-on="ElInput_1.event"
            ></ElInput>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$T('户号')" prop="accountno">
            <ElInput
              v-model="CetForm_1.data.accountno"
              v-bind="ElInput_2"
              v-on="ElInput_2.event"
            ></ElInput>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item prop="key3">
        <template slot="label">
          <div class="flex items-center">
            {{ $T("关联表计") }}
            <span
              v-if="CetTree_1.checkedNodes.length > 0"
              class="flex-auto pl-J1 pr-J1 text-left text-Ab"
            >
              （{{ CetTree_1.checkedNodes.map(item => item.name).join(",") }}）
            </span>
          </div>
        </template>
        <CetTree
          :style="{ height: isEdit ? '200px' : '400px' }"
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          :searchText_in.sync="CetTree_1.searchText_in"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
        ></CetTree>
      </el-form-item>
      <el-form-item v-if="isEdit" prop="key3" :label="$T('所属账户组')">
        <CetTree
          style="height: 200px"
          :selectNode.sync="CetTree_2.selectNode"
          :checkedNodes.sync="CetTree_2.checkedNodes"
          v-bind="CetTree_2"
          v-on="CetTree_2.event"
        ></CetTree>
      </el-form-item>
    </CetForm>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        class="ml-J1"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
import { mapGetters } from "vuex";
export default {
  name: "addInletwire",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    projectSelect: {
      type: Object
    }
  },
  data() {
    return {
      CetTree_2: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ treeId: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ treeId: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        defaultExpandAll: true,
        nodeKey: "treeId",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: true,
        checkStrictly: true,
        event: {}
      },
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入进线名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          accountno: [
            {
              required: true,
              message: $T("请输入户号"),
              trigger: ["blur", "change"]
            },
            {
              pattern: /^[0-9]+$/,
              trigger: ["blur", "change"],
              message: $T("用户编号只允许输入整数")
            },
            {
              min: 1,
              max: 100,
              message: $T("长度在 1 到 100 个字符"),
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ treeId: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ treeId: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        defaultExpandAll: true,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: true,
        checkStrictly: true,
        event: {}
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {},
        event: {}
      },
      ElInput_2: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {},
        event: {}
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      vm.getTree();
      vm.CetForm_1.resetTrigger_in = new Date().getTime();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    async inputData_in(val) {
      this.CetForm_1.data = this._.cloneDeep(val);
      if (this.CetForm_1.data.accountno) {
        this.CetForm_1.data.accountno = String(this.CetForm_1.data.accountno);
      }
      this.CetTree_1.checkedNodes = [];
      this.CetTree_2.checkedNodes = [];
      if (val.id) {
        this.CetDialog_1.title = $T("编辑进线");
        this.editData = await this.getLinesOfAccount(val?.id);
      } else {
        this.CetDialog_1.title = $T("新增进线");
      }
    },
    treeData: {
      handler(val) {
        let data = this._.cloneDeep(val);
        this.setTreeLeaf(data, "group");
        //所属账户组
        this.CetTree_2.inputData_in = data;
      },
      immediate: true
    }
  },
  computed: {
    ...mapGetters(["treeData"]),
    isEdit() {
      const id = this.inputData_in?.id;
      return !!id || id === 0;
    }
  },
  methods: {
    // 获取用户进线
    getLinesOfAccount(id) {
      this.CetTree_1.inputData_in = [];
      return new Promise(resolve => {
        customApi.monitorLinesOfAccount([id]).then(response => {
          const { code, data } = response;
          const { linesegmentwithswitch_model, demandgroup_model } = data[0];
          if (code === 0) {
            // 关联表计
            if (
              linesegmentwithswitch_model &&
              linesegmentwithswitch_model.length > 0
            ) {
              this.CetTree_1.checkedNodes = linesegmentwithswitch_model.map(
                item => {
                  return {
                    id: item.id,
                    modelLabel: item.modelLabel,
                    tree_id: item.modelLabel + "_" + item.id,
                    name: item.name
                  };
                }
              );
            }
            // 所属账户组
            if (demandgroup_model && demandgroup_model.length > 0) {
              this.CetTree_2.checkedNodes = demandgroup_model.map(item => {
                return {
                  id: item.id,
                  modelLabel: item.modelLabel,
                  treeId: item.modelLabel + "_" + item.id,
                  name: item.name
                };
              });
            }
            resolve(data);
          }
        });
      });
    },
    // 获取关联表计树
    async getTree() {
      this.CetTree_1.inputData_in = [];
      this.CetTree_1.searchText_in = "";
      const response = await customApi.commonChildLabelQuery({
        parentNode: this.projectSelect,
        subCondition: [
          {
            expressions: [
              {
                operator: "EQ",
                prop: "roomtype",
                limit: 1
              }
            ],
            modelLabel: "room"
          },
          {
            modelLabel: "linesegmentwithswitch"
          }
        ]
      });
      if (response.code === 0) {
        // 过滤掉其他配电室的配电设备;
        const { data } = response;
        const nodes = data.map(item => {
          var obj = this._.cloneDeep(item);
          obj.children =
            item?.children
              ?.filter(el => {
                return el.modelLabel == "room";
              })
              .map(el => el) || [];
          return obj;
        });
        this.setTreeLeaf(nodes, "line");
        this.CetTree_1.inputData_in = nodes;
      }
    },
    setTreeLeaf(nodes, type) {
      if (!nodes || nodes.length === 0) {
        return;
      }
      const allowMap = {
        line: "linesegmentwithswitch", //关联表计可选modelLabel：linesegmentwithswitch
        group: "demandgroup" //所属账户组可选modelLabel：demandgroup
      };
      nodes.forEach(item => {
        item.disabled = item.modelLabel !== allowMap[type];
        this.setTreeLeaf(this._.get(item, "children", []), type);
      });
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    async CetForm_1_saveData_out(val) {
      const {
        rootnodeid: rootNodeId,
        rootnodelabel: rootNodeLabel,
        groupid: groupId
      } = val;

      let data = {
        accountId: val.id || 0, // 新建传0
        name: val.name,
        accountno: val.accountno,
        lineIds: this.CetTree_1.checkedNodes.map(item => item.id),
        groupId: this.isEdit ? this.CetTree_2.checkedNodes[0]?.id : groupId, // 新建时传入选中节点
        rootNodeId,
        rootNodeLabel
      };
      //编辑：选多个/不选->拦截；选1个->保存
      if (this.isEdit && this.CetTree_2.checkedNodes.length !== 1) {
        this.$message({
          message: $T("所属账户组必须选择一个分组"),
          type: "warning"
        });
        return;
      }
      const res1 = await customApi.monitorAccount(data);
      if (res1.code === 0) {
        this.$message({
          message: $T("保存成功"),
          type: "success"
        });
        this.CetDialog_1.closeTrigger_in = new Date().getTime();
        this.$emit("finishTrigger_out", false);
      }
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: break-all;
  overflow: hidden;
}
.el_dialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
    border-radius: var(--C1);
  }
}
:deep(.el-dialog) {
  margin-top: 10vh !important;
}
:deep(.el-tree) {
  height: calc(100% - 40px) !important;
}
</style>
