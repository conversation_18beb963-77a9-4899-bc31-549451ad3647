//能管基础
import commonApi from "eem-base/api";
import * as tree from "./tree";
import * as energy from "./energy";
import * as energyAlarmConfig from "./energyAlarmConfig";
import * as cycleconfig from "./cycleconfig";
import * as timesharingConfig from "./timesharingConfig";
import * as publicSchemeConfig from "./publicSchemeConfig";
import * as dimensionConfiguration from "./dimensionConfiguration";
import * as projectOverview from "./projectOverview";
import * as energyQueryAndAnalysis from "./energyQueryAndAnalysis";
import * as subEnergyConsumption from "./subEnergyConsumption.js";
import * as energyattributionanalysis from "./energyattributionanalysis";
import * as energyFlowAnalysis from "./energyFlowAnalysis";
import * as energyConsumptionEvent from "./energyConsumptionEvent";
import * as user from "./user";
import * as config from "./config";

export default {
  ...commonApi,
  ...tree,
  ...energy,
  ...energyAlarmConfig,
  ...cycleconfig,
  ...timesharingConfig,
  ...publicSchemeConfig,
  ...dimensionConfiguration,
  ...projectOverview,
  ...energyQueryAndAnalysis,
  ...subEnergyConsumption,
  ...energyattributionanalysis,
  ...energyFlowAnalysis,
  ...energyConsumptionEvent,
  ...user,
  ...config
};
