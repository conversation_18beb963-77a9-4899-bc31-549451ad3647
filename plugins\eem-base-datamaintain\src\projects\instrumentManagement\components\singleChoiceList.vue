<template>
  <div>
    <div class="filter">
      <el-input
        v-model="searchValue"
        suffix-icon="el-icon-search"
        class="w-[164px]"
        placeholder="请输入"
        @change="handlerFilter"
      />
    </div>
    <div class="max-h-[216px] overflow-auto mt-J0 pl-J1 pr-J0">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="item"
        @click="clickItem(item)"
        :class="{ 'select-item': item.id === selectValue }"
      >
        <el-tooltip
          :content="item.text"
          placement="top"
          :disabled="item.text?.length < 8"
        >
          <div class="text-ellipsis">
            {{ item.text }}
          </div>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    type: {
      type: Number,
      default: 1
    },
    options: {
      type: Array
    },
    value: {
      type: [Number, Boolean]
    }
  },
  data() {
    return {
      searchValue: undefined,
      selectValue: null,
      initList: null,
      list: null,
      booleanOptions: [
        {
          id: true,
          text: $T("是")
        },
        {
          id: false,
          text: $T("否")
        }
      ]
    };
  },
  watch: {
    options: {
      handler: function (val) {
        if (this.type === 2) {
          this.initList = val;
          this.list = _.cloneDeep(val);
        }
      },
      immediate: true
    },
    value: {
      handler: function (val) {
        this.selectValue = val;
      },
      immediate: true
    }
  },
  methods: {
    handlerFilter() {
      if (!this.searchValue) {
        this.list = _.cloneDeep(this.initList);
      } else {
        const cur = this.initList.filter(item =>
          item.text.toLowerCase().includes(this.searchValue.toLowerCase())
        );
        this.list = cur;
      }
    },
    clickItem(item) {
      this.selectValue = this.selectValue === item.id ? null : item.id;
      this.$emit("update:value", this.selectValue);
    }
  },
  mounted() {
    if (this.type === 1) {
      this.list = _.cloneDeep(this.booleanOptions);
      this.initList = this.booleanOptions;
    }
  }
};
</script>
<style lang="scss" scoped>
.filter {
  padding: 8px 8px 4px;
  border-bottom: 1px solid;
  @include border_color(B2);
}
.item {
  width: 164px;
  height: 40px;
  line-height: 40px;
  padding: 0 8px;
  box-sizing: border-box;
  border-radius: 3px 2px;
  cursor: pointer;
}
.item + .item {
  margin-top: 4px;
}
.select-item {
  @include background_color(BG4);
  @include font_color(ZS);
}
.item:hover {
  @include background_color(BG4);
}
/* 整个滚动条 */
::-webkit-scrollbar {
  width: 4px !important;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background: transparent !important;
}

/* 滚动条上的滑块 */
::-webkit-scrollbar-thumb {
  width: 4px !important;
  border-radius: 4px !important;
}
</style>
