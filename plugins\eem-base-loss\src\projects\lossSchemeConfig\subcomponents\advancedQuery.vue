<template>
  <CustomElDrawer
    class="drawer"
    :title="$T('高级查询')"
    :visible.sync="openDrawer"
    destroy-on-close
    size="640px"
    :confirmText_in="$T('确定')"
    @confirm_out="CetButton_confirm_statusTrigger_out"
  >
    <div class="flex-auto m-J1 flex-col">
      <div class=" ">
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="label">
              {{ $T("被分摊能源类型") }}
              <span style="color: red">*</span>
            </div>
            <div class="value mt-J1">
              <ElSelect
                class="mr-J1"
                v-model="ElSelect_1.value"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in ElOption_1.options_in"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </ElSelect>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="label">
              {{ $T("分摊方式") }}
              <span style="color: red">*</span>
            </div>
            <div class="value mt-J1">
              <ElSelect
                class="mr-J1"
                v-model="ElSelect_2.value"
                v-bind="ElSelect_2"
                v-on="ElSelect_2.event"
              >
                <ElOption
                  v-for="item in ElOption_2.options_in"
                  :key="item[ElOption_2.key]"
                  :label="item[ElOption_2.label]"
                  :value="item[ElOption_2.value]"
                  :disabled="item[ElOption_2.disabled]"
                ></ElOption>
              </ElSelect>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="16" class="mt-J2">
          <el-col :span="24">
            <div class="label">
              {{ $T("筛选时段") }}
              <span style="color: red">*</span>
            </div>
            <div class="value mt-J1">
              <CustomElDatePicker
                class="mr-J1 el-data-picker"
                style="width: 100%"
                v-bind="CetDatePicker_time.config"
                v-model="CetDatePicker_time.val"
              />
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="mt-J3" style="height: 80%">
        <customElSelect
          class="mr-J1"
          :prefix_in="$T('能源类型')"
          v-model="ElSelect_3.value"
          v-bind="ElSelect_3"
          v-on="ElSelect_3.event"
          v-if="false"
        >
          <ElOption
            v-for="item in ElOption_3.options_in"
            :key="item[ElOption_3.key]"
            :label="item[ElOption_3.label]"
            :value="item[ElOption_3.value]"
            :disabled="item[ElOption_3.disabled]"
          ></ElOption>
        </customElSelect>
        <CetTree
          class="tree-style"
          style="height: calc(100% - 32px)"
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          :searchText_in.sync="CetTree_1.searchText_in"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
        ></CetTree>
      </div>
    </div>
  </CustomElDrawer>
</template>

<script>
import customApi from "@/api/custom";
import CustomElDrawer from "eem-base/components/customElComponent/customElDrawer.vue";

export default {
  name: "advancedQuery",
  components: {
    CustomElDrawer
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    energyOptions: {
      type: Array
    },
    rootNode_in: Object
  },
  data() {
    return {
      openDrawer: false,
      // 被分摊能源类型
      ElSelect_1: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        label: "text",
        value: "id",
        disabled: "disabled"
      },
      // 分摊方式
      ElSelect_2: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      // 能源类型
      ElSelect_3: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_2: {
        options_in: [
          {
            id: 0,
            text: $T("全部")
          },
          {
            id: 1,
            text: $T("固定比例")
          },
          {
            id: 2,
            text: $T("动态分摊")
          }
        ],
        key: "id",
        label: "text",
        value: "id",
        disabled: "disabled"
      },
      ElOption_3: {
        options_in: [],
        key: "id",
        label: "text",
        value: "id",
        disabled: "disabled"
      },
      // 选择时段
      CetDatePicker_time: {
        val: null,
        config: {
          valueFormat: "timestamp",
          type: "daterange",
          clearable: false,
          unlinkPanels: true,
          rangeSeparator: $T("至")
        }
      },
      // 1树组件
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      // 当前选中节点
      currentNode: {}
    };
  },
  watch: {
    openTrigger_in(val) {
      let updateEnergyOptions = this._.cloneDeep(this.energyOptions);
      updateEnergyOptions.shift();
      this.ElOption_1.options_in = updateEnergyOptions;
      this.ElSelect_1.value =
        updateEnergyOptions && updateEnergyOptions.length > 0
          ? updateEnergyOptions[0].id
          : "";
      this.getTreeData();
      this.init();
      this.openDrawer = true;
    },
    closeTrigger_in(val) {
      this.openDrawer = false;
    }
  },
  methods: {
    // 初始化数据
    init() {
      this.ElSelect_2.value = 0;
      this.CetDatePicker_time.val = [
        this.$moment().startOf("year").valueOf(),
        this.$moment().endOf("year").valueOf()
      ];
    },
    // 获取节点树
    getTreeData() {
      let data = {
        energyType: this.ElSelect_1.value,
        rootNode: {
          id: this.rootNode_in?.id,
          modelLabel: this.rootNode_in?.modelLabel
        }
      };
      customApi
        .lossConfigProjectTreeWithoutgroup(data, { keepTransformer: false })
        .then(response => {
          if (response.code === 0) {
            let data = response.data?.[0];
            let treeData = [];
            if (data && data.children && data.children.length > 0) {
              treeData.push(data);
              this.CetTree_1.inputData_in = treeData[0].children;
              this.CetTree_1.selectNode = treeData[0].children[0];
            } else {
              this.CetTree_1.inputData_in = [];
              this.CetTree_1.selectNode = {};
            }
          }
        });
    },
    CetTree_1_currentNode_out(val) {
      this.currentNode = val;
    },
    ElSelect_1_change_out() {
      this.getTreeData();
    },
    CetButton_confirm_statusTrigger_out() {
      if (!this.CetDatePicker_time.val) {
        this.$message.warning($T("请选择筛选时段"));
      }
      if (!(this.currentNode && this.currentNode.id)) {
        this.$message.warning($T("请选择查询节点"));
      }
      let params = {
        shareEnergyType: this.ElSelect_1.value,
        energyShareMethod: this.ElSelect_2.value,
        startTime: this.CetDatePicker_time.val[0],
        endTime: this.CetDatePicker_time.val[1],
        baseVo: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel
        }
      };
      this.$emit("query_update", params);
      this.openDrawer = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}
.label {
  font-weight: 400;
  font-size: 14px;
  @include font_color(T3);
}
.tree-style {
  :deep(.el-tree) {
    width: 100%;
  }
}
.el-data-picker :deep(.el-date-editor--daterange.el-input__inner) {
  width: 100%;
}
</style>
