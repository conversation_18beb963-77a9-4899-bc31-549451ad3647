<template>
  <CetDialog v-bind="CetCollecSynchronous" v-on="CetCollecSynchronous.event">
    <div class="eem-cont-c1">
      <el-checkbox v-model="checked" class="mb-J1" @change="checkedChange">
        {{ $T("默认选中子节点") }}
      </el-checkbox>
      <CetTree
        :selectNode.sync="CetTree_1.selectNode"
        :checkedNodes.sync="CetTree_1.checkedNodes"
        v-bind="CetTree_1"
        v-on="CetTree_1.event"
        class="switch-tree"
      ></CetTree>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>

<script>
import customApi from "@/api/custom.js";
export default {
  props: {
    openTrigger_in: Number,
    inputData_in: Object,
    selectSource: Array
  },
  watch: {
    openTrigger_in(val) {
      this.checked = false;
      this.CetTree_1.props.disabled = this.setDisabled;
      this.CetTree_1.checkedNodes = [];
      this.getTreeData();
      this.CetCollecSynchronous.openTrigger_in = this._.cloneDeep(val);
    }
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    projectTenantId() {
      return this.$store.state.projectTenantId;
    },
    synchronousNodeLimit() {
      return this.$store.state.systemCfg.synchronousNodeLimit || 200;
    }
  },
  data() {
    return {
      CetCollecSynchronous: {
        title: $T("同步"),
        showClose: true,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        width: "600px",
        "append-to-body": true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("同步"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "text",
          children: "children",
          disabled: this.setDisabled
        },
        highlightCurrent: false,
        showCheckbox: true,
        checkStrictly: false,
        event: {}
      },
      checked: false
    };
  },
  methods: {
    setDisabled(data, node) {
      return data.nodeType !== 269619472;
    },
    async getTreeData() {
      const params = {
        loadDevice: true,
        nodeId: 0,
        nodeType: 0,
        async: false,
        tenantId: this.projectTenantId
      };
      const res = await customApi.queryPecTree(params);
      if (res.code !== 0) return;
      this.CetTree_1.inputData_in = this._.get(res, "data", []) || [];
      this.CetTree_1.checkedNodes = [];
      await this.$nextTick();
      this.CetTree_1.checkedNodes = this._.cloneDeep(this.selectSource);
    },
    CetButton_confirm_statusTrigger_out(val) {
      const nodeCheck = this.CetTree_1.checkedNodes || [];
      if (!nodeCheck?.length) {
        this.$message.warning($T("请选择节点"));
        return;
      }
      if (nodeCheck.length > this.synchronousNodeLimit) {
        this.$message.warning(
          $T("最多同步{0}个节点", this.synchronousNodeLimit)
        );
        // this.CetTree_1.checkedNodes = nodeCheck.filter(
        //   item => item.tree_id !== val.tree_id
        // );
        return;
      }
      let params = this.CetTree_1.checkedNodes.reduce((acc, item) => {
        if (item.nodeType === 269619472) {
          acc.push(item.nodeId);
        }
        return acc;
      }, []);
      this.$emit("save_out", params);
      this.CetCollecSynchronous.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetCollecSynchronous.closeTrigger_in = this._.cloneDeep(val);
    },
    checkedChange(e) {
      this.CetTree_1.props.disabled = e ? false : this.setDisabled;
    }
  }
};
</script>

<style lang="scss" scoped>
.switch-tree {
  height: 520px;
  :deep(.el-tree) {
    overflow: auto;
  }
}
</style>
