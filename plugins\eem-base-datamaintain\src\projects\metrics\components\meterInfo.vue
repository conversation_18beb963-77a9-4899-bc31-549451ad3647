<template>
  <div class="h-[40px] mt-J3 mb-J3 flex justify-between text-sm">
    <div
      class="w-[48.4%] flex items-center justify-between total-block brC"
      :class="`total-block${theme}`"
    >
      <div
        v-for="(item, index) in meterInfoList"
        :key="item.value"
        class="flex items-center justify-between"
        :class="index === 0 ? 'w-[26%] pr-J4' : 'w-[20%]'"
      >
        <div class="fcT3 shrink-0 mr-J0">{{ item.label }}</div>
        <div class="flex items-center justify-end flex-1 overflow-hidden">
          <el-tooltip :content="meterData[item.value]" placement="top">
            <div class="num text-ellipsis">{{ meterData[item.value] }}</div>
          </el-tooltip>
          <div class="fcT3 shrink-0">{{ $T("台") }}</div>
        </div>
      </div>
    </div>
    <div
      class="w-[25%] flex items-center justify-between brC online-block"
      :class="`online-block${theme}`"
    >
      <div class="flex items-center justify-between w-[calc(50%-12px)]">
        <div class="fcT3 shrink-0 mr-J0">{{ $T("主用表计在线数量") }}</div>
        <div class="flex items-center justify-end flex-1 overflow-hidden">
          <el-tooltip :content="meterData.onlineNum" placement="top">
            <div class="num text-ellipsis">{{ meterData.onlineNum }}</div>
          </el-tooltip>
          <div class="fcT3 shrink-0">{{ $T("台") }}</div>
        </div>
      </div>
      <div class="flex items-center justify-between w-[calc(50%-12px)]">
        <div class="flex items-center fcT3">
          {{ $T("在线率") }}
          <el-tooltip
            placement="top"
            :content="
              $T(
                '在线率=主用表计在线数量/（主用表计在线数量+主用表计离线数量），备用表计数量不参与在线率计算'
              )
            "
          >
            <omega-icon symbolId="question" class="ml-J0" />
          </el-tooltip>
        </div>
        <div class="flex items-center">
          <div class="num">{{ meterData.onlineRate }}</div>
          <div class="fcT3">%</div>
        </div>
      </div>
    </div>
    <div
      class="w-[25%] flex items-center justify-between brC offline-block"
      :class="`offline-block${theme}`"
    >
      <div class="w-[calc(50%-12px)] flex items-center justify-between">
        <div class="fcT3 shrink-0 mr-J0">{{ $T("主用表计离线数量") }}</div>
        <div class="flex items-center justify-end flex-1 overflow-hidden">
          <el-tooltip :content="meterData.abnormalOfflineNum" placement="top">
            <div class="num text-ellipsis">
              {{ meterData.abnormalOfflineNum }}
            </div>
          </el-tooltip>
          <div class="fcT3 shrink-0">{{ $T("台") }}</div>
        </div>
      </div>
      <div class="w-[calc(50%-12px)] flex items-center justify-between">
        <div class="flex items-center fcT3">
          {{ $T("离线率") }}
          <el-tooltip
            placement="top"
            :content="
              $T(
                '离线率=主用表计离线数量/（主用表计在线数量+主用表计离线数量），备用表计数量不参与离线率计算'
              )
            "
          >
            <omega-icon symbolId="question" class="ml-J0" />
          </el-tooltip>
        </div>
        <div class="flex items-center">
          <div class="num">{{ meterData.offlineRate }}</div>
          <div class="fcT3">%</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import commonApi from "@/api/custom.js";
import omegaTheme from "@omega/theme";

export default {
  props: {
    energyType: {
      type: [Number, String]
    }
  },
  data() {
    return {
      meterInfoList: [
        {
          label: $T("总表计数量"),
          value: "total"
        },
        {
          label: $T("主用表计数量"),
          value: "masterMeter"
        },
        {
          label: $T("备用表计数量"),
          value: "backupNum"
        },
        {
          label: $T("未关联数量"),
          value: "unrelatedNum"
        }
      ],
      meterData: {}
    };
  },
  computed: {
    theme() {
      return omegaTheme.theme === "light" ? "-light" : "";
    }
  },
  watch: {
    energyType(val) {
      this.getMeterData();
    }
  },
  methods: {
    async getMeterData() {
      if (!this.energyType) return;
      const params = {
        projectId: this.$store.state.projectId,
        energyType: this.energyType
      };
      let resData = {};
      const res = await commonApi.getNetWorkStatistics(params);
      if (res.code === 0) {
        resData = res.data || {};
        resData.masterMeter = resData.onlineNum + resData.abnormalOfflineNum;
      }

      let obj = {};
      Object.keys(resData).forEach(key => {
        obj[key] = ["onlineRate", "offlineRate"].includes(key)
          ? this.formatNum(resData[key], 1)
          : this.formatNum(resData[key], 2);
      });
      this.meterData = obj;
    },
    formatNum(val, type) {
      if (type === 2) {
        return val || val === 0 ? val.toString() : "--";
      } else {
        return val || val === 0 ? val.toFixed(2) : "--";
      }
    }
  },
  activated() {
    this.getMeterData();
  }
};
</script>
<style lang="scss" scoped>
.num {
  font-family: "Barlow";
  font-size: 16px;
  font-weight: 700;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent;
  margin-right: 2px;
}
.total-block {
  padding: 0 16px 0 52px;
  box-sizing: border-box;
  background-image: url(../assets/total.png);
  background-size: 100% 100%;
  .num {
    text-shadow: 0 0 4px #ffffff80;
    background: linear-gradient(180deg, #fff 27.27%, #d1d1d1 77.27%);
  }
}
.total-block-light {
  background-image: url(../assets/total-light.png);
  .num {
    text-shadow: 0 0 4px #9aa8b1;
    background: linear-gradient(180deg, #000 27.27%, #797979 77.27%);
  }
}
.online-block {
  padding: 0 16px;
  box-sizing: border-box;
  background-image: url(../assets/online.png);
  background-size: 100% 100%;
  .num {
    text-shadow: 0 0 4px #24ed8d;
    background: linear-gradient(180deg, #fff 25%, #b7ffdc 77.27%);
  }
}
.online-block-light {
  background-image: url(../assets/online-light.png);
  .num {
    text-shadow: 0 0 4px #70d9a6;
    background: linear-gradient(180deg, #000 25%, #25ad6b 77.27%);
  }
}
.offline-block {
  padding: 0 16px;
  box-sizing: border-box;
  background-image: url(../assets/offline.png);
  background-size: 100% 100%;
  .num {
    text-shadow: 0 0 4px #ff3f3f;
    background: linear-gradient(180deg, #fff 22.73%, #ffd7d7 77.27%);
  }
}
.offline-block-light {
  background-image: url(../assets/offline-light.png);
  .num {
    text-shadow: 0 0 4px #faafaf;
    background: linear-gradient(180deg, #000 27.27%, #a23d3d 77.27%);
  }
}
</style>
