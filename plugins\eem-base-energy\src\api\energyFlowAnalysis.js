import fetch from "eem-base/utils/fetch";
import { http } from "@omega/http";

// 能源流向
export function queryEnergyFlow(data) {
  return fetch({
    url: `/eembaseenergy/v1/energy/flow/query`,
    method: "POST",
    data
  });
}

export function queryStandardEnergyTypes() {
  return fetch({
    url: `/eembaseenergy/v1/energy-type/standardEnergyTypes`,
    method: "GET"
  });
}

//能源流向-根据节点查询关联的节点内容
export function queryEnergyConnection(data) {
  return fetch({
    url: `/eembaseenergy/v1/connect/connection`,
    method: "POST",
    data
  });
}

export function queryEnergyFlowNoLoading(data) {
  return http({
    url: `/eembaseenergy/v1/energy/flow/query`,
    method: "POST",
    data
  });
}
