import {
  beforeAppBoot,
  afterAppLogin,
  completeAppBoot
} from "eem-base/omega/afterAppLogin.js";
import omegaApp from "@omega/app";
import { loadAllEnums } from "@/utils/enumManager.js";

omegaApp.plugin.register(
  class CustomAuthPlugin {
    constructor({ conf, plugin, router, store }, option) {
      this.store = store;
      this.conf = conf;
      this.plugin = plugin;
      this.router = router;
      plugin._hasAuth = true;
    }
    async beforeAppBoot() {
      await beforeAppBoot.call(this);
    }

    async afterAppLogin() {
      await afterAppLogin.call(this);
    }

    async completeAppBoot() {
      await completeAppBoot.call(this);
      loadAllEnums();
    }
  }
);
