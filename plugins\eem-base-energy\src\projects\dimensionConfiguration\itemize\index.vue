<template>
  <el-container class="page">
    <el-aside
      class="p-J4 mainBox border-solid border-r-[1px] border-t-0 border-b-0 border-l-0 border-B1"
    >
      <el-header height="32px" style="padding: 0px; line-height: 32px">
        <span class="title">{{ $T("维度列表") }}</span>
        <CetButton
          v-permission="'dimattributeconfig_treeupdate'"
          class="fr"
          v-bind="CetButton_addTree"
          v-on="CetButton_addTree.event"
          :disable_in="!rootNode"
        ></CetButton>
        <customElSelect
          :prefix_in="$T('所属项目')"
          v-model="ElSelect_project.value"
          v-bind="ElSelect_project"
          v-on="ElSelect_project.event"
          class="mr-J3 fr"
          v-show="ElOption_project.options_in?.length > 1"
        >
          <ElOption
            v-for="item in ElOption_project.options_in"
            :key="item[ElOption_project.key]"
            :label="item[ElOption_project.label]"
            :value="item[ElOption_project.value]"
            :disabled="item[ElOption_project.disabled]"
          ></ElOption>
        </customElSelect>
      </el-header>
      <el-main style="height: calc(100% - 48px); width: 100%" class="p0 mt-J3">
        <CetTable
          :data.sync="CetTable_treeList.data"
          :dynamicInput.sync="CetTable_treeList.dynamicInput"
          v-bind="CetTable_treeList"
          v-on="CetTable_treeList.event"
        >
          <ElTableColumn width="50">
            <template slot-scope="scope">
              <el-radio
                v-model="radio"
                :label="CetTable_treeList.data[scope.$index].id"
              ></el-radio>
            </template>
          </ElTableColumn>
          <ElTableColumn
            type="index"
            :label="$T('序号')"
            width="70"
          ></ElTableColumn>
          <ElTableColumn
            :label="$T('节点树名称')"
            prop="name"
            :show-overflow-tooltip="true"
          ></ElTableColumn>
          <ElTableColumn :label="$T('操作')" width="130">
            <template slot-scope="scope">
              <span
                class="text-ZS cursor-pointer"
                @click.stop="editNameHandle(scope.row)"
              >
                {{ $T("编辑") }}
              </span>
              <span
                class="text-Sta3 cursor-pointer ml-J1"
                @click.stop="dleteTree(scope.row)"
              >
                {{ $T("删除") }}
              </span>
            </template>
          </ElTableColumn>
        </CetTable>
      </el-main>
    </el-aside>
    <el-main class="p-J4 ml-J3 bg1 mainBox">
      <el-header
        height="32px"
        style="padding: 0px; line-height: 32px; display: flex"
      >
        <span
          class="title text-ellipsis"
          style="flex: 1"
          :title="(currentRow.name || '') + $T('配置')"
        >
          {{ (currentRow.name || "") + $T("配置") }}
        </span>
      </el-header>
      <el-main style="height: calc(100% - 48px); width: 100%" class="p0 mt-J3">
        <CetGiantTree
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
        ></CetGiantTree>
      </el-main>
    </el-main>
    <addTree
      v-bind="addTree"
      @finishTrigger_out="getTableData"
      :treeDataList="CetTable_treeList.data"
      :rootNode_in="rootNode"
    />
  </el-container>
</template>

<script>
import customApi from "@/api/custom";
import addTree from "./addTree.vue";
export default {
  name: "allocateNodeConfiguration",
  components: { addTree },
  computed: {
    rootNode() {
      const id = this.ElSelect_project.value;
      const list = this.ElOption_project.options_in;
      return list.find(item => item.id === id);
    }
  },
  data() {
    return {
      addTree: {
        inputData_in: {},
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime()
      },
      radio: "",
      currentRow: {},
      CetTable_treeList: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_treeList_record_out
        }
      },
      CetButton_addTree: {
        visible_in: true,
        disable_in: false,
        title: $T("新建分项节点树"),
        type: "primary",
        plain: false,
        icon: "el-icon-plus",
        event: {
          statusTrigger_out: this.CetButton_addTree_statusTrigger_out
        }
      },
      currentNode: {},
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out //选中单行输出
        }
      },
      ElSelect_project: {
        value: 0,
        style: {},
        event: {
          change: this.getTableData
        }
      },
      ElOption_project: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  methods: {
    async init() {
      await this.getRootNode();
      this.getTableData();
    },
    async getRootNode() {
      this.ElOption_project.options_in = [];
      const res = await customApi.rootNode();
      const data = res.data || [];
      this.ElOption_project.options_in = data;
      this.ElSelect_project.value = data?.[0]?.id;
    },
    async getTableData() {
      const queryData = {
        rootNode: {
          id: this.rootNode.id,
          modelLabel: this.rootNode.modelLabel
        }
      };
      const res = await customApi.dimensionAll(queryData);
      this.CetTable_treeList.data = res?.data ?? [];
    },
    async queryTreeData() {
      if (!this.currentRow?.id || !this.currentRow.modelLabel) {
        this.CetGiantTree_1 && (this.CetGiantTree_1.inputData_in = []);
        return;
      }
      const res = await customApi.dimensionTreeById({
        dimensionId: this.currentRow.id
      });
      const data = res?.data;
      this.CetGiantTree_1.inputData_in = data ? [data] : [];
    },
    CetButton_addTree_statusTrigger_out() {
      this.addTree.inputData_in = null;
      this.addTree.openTrigger_in = Date.now();
    },
    CetTable_treeList_record_out(val) {
      this.currentRow = this._.cloneDeep(val);
      this.radio = val.id;
      this.queryTreeData();
    },
    CetGiantTree_1_currentNode_out(val) {
      this.currentNode = this._.cloneDeep(val);
    },
    async dleteTree(val) {
      this.$confirm($T("是否确认删除?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(async () => {
          const res = await customApi.deleteDimension({
            id: val.id
          });
          if (res?.code) return;
          this.$message.success($T("删除成功"));
          this.getTableData();
        })
        .catch(() => {
          this.$message.info($T("已取消"));
        });
    },
    editNameHandle(val) {
      this.addTree.inputData_in = this._.cloneDeep(val);
      this.addTree.openTrigger_in = Date.now();
    }
  },
  mounted() {
    this.init();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: var(--Ra);
  .mainBox {
    border-radius: var(--Ra);
  }
  .el-aside,
  .el-main {
    width: 0;
    flex: 1;
  }
  .title {
    font-weight: bold;
    @include font_color(T2);
    @include font_size(H3);
  }
  :deep() {
    .el-radio__label {
      display: none;
    }
  }
  .gianttree {
    height: calc(100% - 16px);
  }
}
</style>
