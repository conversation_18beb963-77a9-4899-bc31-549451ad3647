import fetch from "eem-base/utils/fetch";
export function getNodeTreeSimple(data) {
  return fetch({
    url: `/eem-service/v1/node/nodeTree/simple`,
    method: "POST",
    data
  });
}

/**
 * 复制节点
 */
export function copyNodes(data) {
  return fetch({
    url: "/eem-service/v1/node/copyNodes",
    method: "POST",
    data
  });
}

/**
 * 删除节点
 */
export function manageNodeDelete(data) {
  return fetch({
    url: "/eem-service/v1/project/manageNode",
    method: "DELETE",
    data
  });
}

/**
 * 删除物理量
 */
export function deleteQuantityObject(data) {
  return fetch({
    url: "/eem-service/v1/quantity/quantityObject",
    method: "DELETE",
    data
  });
}

/**
 * 删除工作区间配置
 */
export function deleteWorkSections(data) {
  return fetch({
    url: `/eem-service/v1/parameterConfig/workSections`,
    method: "DELETE",
    data
  });
}

/**
 * 删除设备时删除运行效率曲线
 */
export function deleteOperatingEfficiencyCurve(data) {
  return fetch({
    url: `/eem-service/v1/parameterConfig/operatingEfficiencyCurve`,
    method: "DELETE",
    data
  });
}

/**
 * 获取所有节点类型
 */
export function topologyNodeTypes(params) {
  return fetch({
    url: `/eem-service/v1/topology/nodeType`,
    method: "POST",
    params
  });
}

/**
 * 添加节点权限
 */
export function addModelNodePermission(data) {
  return fetch({
    url: `/eem-service/v1/node/modelNode`,
    method: "POST",
    data
  });
}

/**
 * 查询工作区间配置
 */
export function queryWorkSection(data) {
  return fetch({
    url: `/eem-service/v1/parameterConfig/workSection`,
    method: "POST",
    data
  });
}

/**
 * 新建或者修改节点
 */
export function createEndEditNode(data) {
  return fetch({
    url: `/eem-service/v1/project/manageNode`,
    method: "PUT",
    data: data
  });
}

/**
 * 写入工作区间配置
 */
export function writeWorkSection(params, data) {
  return fetch({
    url: `/eem-service/v1/parameterConfig/workSection`,
    method: "PUT",
    params,
    data
  });
}

/**
 * 创建物理量
 */
export function quantityObjectSetting(data) {
  return fetch({
    url: `/eem-service/v1/quantity/quantityObjectSetting`,
    method: "POST",
    data
  });
}

/**
 * 导入运行效率曲线
 */
export function importOperatingEfficiencyCurve(params, data) {
  return fetch({
    url: `/eem-service/v1/parameterConfig/importOperatingEfficiencyCurve`,
    method: "POST",
    params,
    data
  });
}

/**
 * 根据名称对数据进行过滤，支持事件归类、设备归类、关键词的过滤
 */
export function getEventClassification(params) {
  return fetch({
    url: `/eem-service/v1/expert/query/name`,
    method: "GET",
    params
  });
}

/**
 * 获取节点树
 */
export function getNodeTree(data) {
  return fetch({
    url: `/eem-service/v1/node/nodeTree`,
    method: "POST",
    data
  });
}

/**
 * 拓扑导入节点-异步
 */
export function topologyImportNodeAsync(data, params) {
  return fetch({
    url: `/eem-service/v1/topology/importNode/async`,
    method: "POST",
    params,
    data
  });
}

/**
 * 批量移动节点
 */
export function batchMoveNode(data) {
  return fetch({
    url: `/eem-service/v1/node/moveNode`,
    method: "PUT",
    data
  });
}

/**
 * 省市区查询
 */
export function districtData(data) {
  return fetch({
    url: `/eembaseconfig/v1/common/district`,
    method: "GET",
    data
  });
}

/**
 * 查询设备分类数据
 */
export function deviceClassification() {
  return fetch({
    url: `/eembaseconfig/v1/node/deviceClassification`,
    method: "GET"
  });
}
