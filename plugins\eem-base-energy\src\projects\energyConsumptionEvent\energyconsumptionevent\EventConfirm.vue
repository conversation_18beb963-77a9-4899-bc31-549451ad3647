<template>
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="small">
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        class="ml-J1"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
    <div
      class="eem-cont-c1"
      style="overflow: auto; max-height: calc(84vh - 156px)"
    >
      <div class="confirm-opition clearfix">
        <span class="fl">{{ $T("确认意见") }}</span>
      </div>
      <ElInput
        class="confirm-input"
        v-model="ElInput_1.value"
        v-bind="ElInput_1"
        v-on="ElInput_1.event"
      ></ElInput>
      <div class="mt-J1 clearfix">
        <span class="fl">{{ $T("确认时间") }}：</span>
        <span class="fl value-color">
          {{ this.$moment().format("YYYY-MM-DD HH:mm:ss") }}
        </span>
      </div>
      <div class="mt-J1 clearfix">
        <span class="fl">{{ $T("操作人") }}：</span>
        <span class="fl value-color">{{ userInfoName }}</span>
      </div>
    </div>
  </CetDialog>
</template>
<script>
import customApi from "@/api/custom.js";
export default {
  name: "EventConfirm",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: [Array, Object]
    }
  },

  computed: {
    userInfo() {
      return this.$store.state.userInfo;
    },
    userInfoName() {
      return `${this.userInfo.nicName || "--"}(${this.userInfo.name || "--"})`;
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: $T("事件确认"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: true,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        style: {},
        rows: 8,
        type: "textarea",
        maxlength: 255,
        showWordLimit: true,
        required: true,
        placeholder: $T("请输入内容（必需）"),
        event: {
          input: this.ElInput_1_input_out
        }
      }
    };
  },
  watch: {
    visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.ElInput_1.value = "";
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      var eventList = this.inputData_in.eventlist;
      this.eventConfirm_out(eventList);
    },
    ElInput_1_input_out(val) {
      this.CetButton_confirm.disable_in = !val.length;
    },

    getConfirmEventData_out(events) {
      if (!events?.length) {
        return;
      }
      events.forEach(item => {
        item.modelLabel = item.modelLabel ? item.modelLabel : "systemevent";
        item.confirmeventstatus = 3;
        item.operator = this.userInfo.nicName;
        item.operator_id = this.userInfo.id;
        item.remark = this.ElInput_1.value;
        item.updatetime = new Date().getTime();
      });
      return events;
    },

    async eventConfirm_out(events) {
      const list = this.getConfirmEventData_out(events);
      if (!list || list.length === 0) {
        return;
      }

      const res = await customApi.confirmEvents(list);
      if (res.code !== 0) {
        return;
      }

      this.$message.success($T("操作成功"));
      this.CetDialog_1.closeTrigger_in = new Date().getTime();
      this.$emit("confirmedTrigger_out");
    }
  }
};
</script>
<style lang="scss" scoped>
.confirm-opition {
  box-sizing: border-box;
  border: 1px solid;
  padding: var(--J2);
  border-radius: var(--Ra) var(--Ra) 0 0;
  @include border_color(B1);
}
.confirm-input {
  border: solid;
  border-width: 0 1px 1px;
  border-radius: 0 0 var(--Ra) var(--Ra);
  box-sizing: border-box;
  @include border_color(B1);
  :deep(.el-textarea__inner) {
    border-width: 0;
  }
}
</style>
