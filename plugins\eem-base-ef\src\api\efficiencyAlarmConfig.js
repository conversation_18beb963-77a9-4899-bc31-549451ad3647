import fetch from "eem-base/utils/fetch";

// 获取能效指标数据
export function alarmIndexData(params) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/event/node/efData`,
    method: "GET",
    params
  });
}

// 获取能效报警方案
export function alarmSchemeData(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/event/alarmScheme`,
    method: "POST",
    data
  });
}

// 保存能效报警方案
export function saveAlarmSchemeData(data) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/event/saveAlarmSchemes`,
    method: "POST",
    data
  });
}
