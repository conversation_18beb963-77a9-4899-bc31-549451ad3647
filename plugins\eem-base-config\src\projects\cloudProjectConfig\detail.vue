<template>
  <ElDrawer
    :title="$T('详情')"
    :visible.sync="openDrawer"
    destroy-on-close
    size="640px"
  >
    <div ref="detailWrap" class="h-full overflow-y-auto">
      <div class="font-bold text-H3">{{ $T("基本信息") }}</div>
      <el-row :gutter="16">
        <template v-for="item in showFieldList">
          <el-col
            v-if="
              !['pic', 'document', 'nodeType', 'rangeSelection'].includes(
                item.type
              ) &&
              (!item.show || item.show(detailInfo))
            "
            :span="8"
            :key="item.propertyLabel"
            class="mt-J3"
          >
            <div class="detail-label mb-J1">
              {{ item.name }} {{ item.unit ? `(${item.unit})` : "" }}
            </div>
            <ElTooltip placement="top" :content="findDataByField(item)">
              <div class="value text-ellipsis">{{ findDataByField(item) }}</div>
            </ElTooltip>
          </el-col>
          <!-- 区间选择在详情里面将最大最小分开展示 -->
          <template v-if="item.type === 'rangeSelection'">
            <el-col :span="8" :key="item.propertyLabel" class="mt-J3">
              <div class="detail-label mb-J1">
                {{ item.detailMinName }}
              </div>
              <ElTooltip
                placement="top"
                :content="formatNumFloat(detailInfo[item.propertyLabel])"
              >
                <div class="value text-ellipsis">
                  {{ formatNumFloat(detailInfo[item.propertyLabel]) }}
                </div>
              </ElTooltip>
            </el-col>
            <el-col :span="8" :key="item.relatedLabel[0]" class="mt-J3">
              <div class="detail-label mb-J1">
                {{ item.detailMaxName }}
              </div>
              <ElTooltip
                placement="top"
                :content="formatNumFloat(detailInfo[item.relatedLabel[0]])"
              >
                <div class="value text-ellipsis">
                  {{ formatNumFloat(detailInfo[item.relatedLabel[0]]) }}
                </div>
              </ElTooltip>
            </el-col>
          </template>
        </template>

        <el-col :span="24" v-if="documentField" class="mt-J3">
          <div class="detail-label mb-J1">{{ documentField.name }}</div>
          <div class="value">
            <span class="dcm-btn-class-label" :title="documentName">
              {{ documentName || "--" }}
            </span>
            <el-button
              size="mini"
              style="position: absolute"
              :disabled="!documentName"
              type="primary"
              @click="importDocument()"
            >
              {{ $T("导出文档") }}
            </el-button>
          </div>
        </el-col>

        <template v-for="item in picFields">
          <el-col :span="24" class="mt-J3" :key="item.propertyLabel">
            <div class="detail-label mb-J1">
              {{ item.name }}
            </div>
            <div class="value">
              <UploadImg
                class="img"
                :static_in="true"
                :imgUrl.sync="detailInfo[item.propertyLabel]"
              />
            </div>
          </el-col>
        </template>
      </el-row>
      <div
        class="font-bold text-H3 mt-J3"
        v-show="detailInfo?.modelLabel === 'building'"
      >
        {{ $T("层级信息") }}
      </div>
      <div
        style="overflow: hidden"
        v-show="detailInfo?.modelLabel === 'building'"
        class="rounded-Ra1 mt-J1 rowBox pt-J3"
      >
        <el-row :gutter="16">
          <el-col :span="24">
            <div class="detail-label mb-J1">{{ $T("层级对象") }}</div>
            <div class="value">
              {{
                `${detailInfo?.project || detailInfo?.sectionarea || "--"} > ${
                  detailInfo?.name || "--"
                }`
              }}
            </div>
          </el-col>
        </el-row>
      </div>
      <div
        class="font-bold text-H3 mt-J3"
        v-show="detailInfo?.modelLabel === 'building'"
      >
        {{ $T("关联的设备") }}
      </div>
      <div v-show="detailInfo?.modelLabel === 'building'">
        <div class="pt-J3" style="height: 300px">
          <CetTable
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
          >
            <template v-for="item in Columns_1">
              <ElTableColumn
                headerAlign="left"
                align="left"
                showOverflowTooltip
                :key="item.label"
                v-bind="item"
              ></ElTableColumn>
            </template>
          </CetTable>
        </div>
      </div>
    </div>
  </ElDrawer>
</template>
<script>
import common from "eem-base/utils/common";
import UploadImg from "@/components/uploadImg.vue";
import customApi from "@/api/custom";
import { getFields } from "@/utils/projectTreeField.js";
export default {
  name: "cloudProjectConfigDetail",
  components: {
    UploadImg
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object,
      default() {
        return {};
      }
    },
    projectId_in: Number
  },
  computed: {
    Fields() {
      return getFields();
    },
    enumerations() {
      return this.$store.state.enumerations;
    },
    token() {
      return this.$store.state.token;
    },
    fieldList() {
      const modelLabel = this._.get(this.inputData_in, "modelLabel");
      if (!modelLabel) return;
      const item = this.Fields.find(item => {
        if (modelLabel === "room") {
          let roomType = this._.get(this.inputData_in, "roomtype", null);
          if (roomType) {
            roomType = Number(roomType);
          } else {
            roomType = null;
          }
          return item.modelLabel === modelLabel && item.roomType === roomType;
        } else {
          return item.modelLabel === modelLabel;
        }
      });
      const node_fields = item ? item.node_fields : [];
      let addFields = [
        {
          name: $T("模型ID"),
          propertyLabel: "id",
          type: "string"
        },
        {
          name: $T("模型名称"),
          propertyLabel: "modelLabel",
          type: "string"
        }
      ];
      if (modelLabel === "room") {
        addFields.push({
          name: $T("房间类型"),
          propertyLabel: "roomtype",
          type: "enums",
          enumLabel: "roomtype"
        });
      }
      return [...node_fields, ...addFields];
    },
    picFields() {
      if (!this._.isArray(this.fieldList)) return [];
      return this.fieldList.filter(item => item.type === "pic");
    },
    documentField() {
      if (!this._.isArray(this.fieldList)) return [];
      return this.fieldList.find(item => item.type === "document");
    },
    documentName() {
      if (!this.documentField) return;
      const documentPath = this._.get(
        this.detailInfo,
        this.documentField.propertyLabel
      );
      if (!documentPath) return;

      const fileName = documentPath.split("\\")[3];
      let fileNameArr = fileName.split("_");
      fileNameArr.length = fileNameArr.length - 1;
      return `${fileNameArr.join("_")}.${fileName.split(".")[1]}`;
    }
  },
  data() {
    return {
      detailInfo: {},
      openDrawer: false,
      customEnums: {},
      showFieldList: [],
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        // defaultSort: null, // { prop: "code"  order: "descending" },
        event: {},
        style: "text-align: center;height:100%;"
      },
      Columns_1: [
        {
          prop: "name", // 支持path a[0].b
          label: $T("设备名称"), //列名
          minWidth: "120",
          formatter: common.formatTextCol()
        },
        {
          prop: "code", // 支持path a[0].b
          label: $T("编号"), //列名
          minWidth: "120",
          formatter: common.formatTextCol()
        },
        {
          prop: "location", // 支持path a[0].b
          label: $T("安装位置"), //列名
          minWidth: "180",
          formatter: common.formatTextCol()
        },
        {
          prop: "brand", // 支持path a[0].b
          label: $T("品牌"), //列名
          minWidth: "120",
          formatter: common.formatTextCol()
        },
        {
          prop: "model", // 支持path a[0].b
          label: $T("型号"), //列名
          minWidth: "120",
          formatter: common.formatTextCol()
        },
        {
          prop: "commissiondate", // 支持path a[0].b
          label: $T("投运时间"), //列名
          minWidth: "170",
          formatter: common.formatDateCol("YYYY-MM-DD")
        },
        {
          prop: "voltagelevel", // 支持path a[0].b
          label: $T("电压等级"), //列名
          minWidth: "120",
          formatter: (row, col, cellValue) => {
            let obj = this.$store.state.enumerations.voltagelevel.find(
              item => item.id === cellValue
            );
            return obj ? obj.text : "--";
          }
        }
      ]
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in() {
      this.openDrawer = true;
      this.init();
      this.$nextTick(() => {
        $(this.$refs.detailWrap).scrollTop(0);
      });
    },
    closeTrigger_in() {
      this.openDrawer = false;
    }
  },
  methods: {
    async init() {
      if (this.inputData_in.modelLabel === "building") {
        this.getDeviceList();
      }
      this.showFieldList = [];
      this.customEnums = {};
      await this.customAnalysis();
      this.showFieldList = this.fieldList;
      const queryData = [
        {
          modelLabel: this.inputData_in.modelLabel,
          id: this.inputData_in.id
        }
      ];
      const res = await customApi.getNodeDetail(queryData);
      this.detailInfo = res?.data?.[0] ?? {};
    },
    // 自定义字段需要通过后端接口返回解析
    async customAnalysis() {
      if (this.fieldList.find(i => i.type === "energytype")) {
        this.customEnums.energytype = await this.projectEnergy();
      }
      if (this.fieldList.find(i => i.type === "deviceclassification")) {
        this.customEnums.deviceclassification =
          await this.getDeviceclassification();
      }
      if (this.fieldList.find(i => i.type === "busbarseg")) {
        this.customEnums.busbarseg = await this.getBusbarseg();
      }
      if (this.fieldList.find(i => i.type === "product")) {
        this.customEnums.product = await this.getProduct();
      }
      const customEnumsFieldList = this.fieldList.filter(item => {
        return item.type === "customEnums";
      });
      const promiseAll = customEnumsFieldList.map(item => {
        return new Promise(res => {
          if (this._.isArray(item.customEnums)) {
            res(item.customEnums);
          } else {
            item.customEnums().then(data => {
              res(data);
            });
          }
        });
      });
      const customEnumsAll = await Promise.all(promiseAll);
      customEnumsFieldList.forEach((item, index) => {
        item.customEnums_ElOption = customEnumsAll[index];
      });
    },
    async projectEnergy() {
      const res = await customApi.queryProjectEnergyList();
      if (res.code !== 0) return [];
      return res.data || [];
    },
    async getDeviceclassification() {
      const res = await customApi.deviceClassification();
      if (res.code !== 0) return [];
      return res.data || [];
    },
    async getBusbarseg() {
      const params = {
        parentNode: {
          modelLabel: "project",
          id: this.projectId_in
        },
        subLabelList: ["busbarsection"]
      };

      const res = await customApi.queryChildNodeByLabel(params);
      if (res.code !== 0) return [];
      return this._.get(res.data, "[0].children", []);
    },
    async getProduct() {
      const res = await customApi.queryProductList();
      if (res.code !== 0) return [];
      return this._.get(res, "data", []);
    },
    findDataByField(field) {
      const { propertyLabel, type, enumLabel } = field;
      let showText,
        value = this.detailInfo[propertyLabel],
        obj,
        enumsArr;
      if (value || value == 0) {
        switch (type) {
          case "string":
          case "textarea":
            showText = value;
            break;
          case "numberInt":
            showText = value.toFixed2(0);
            break;
          case "numberFloat":
            showText = value.toFixed2(2);
            break;
          case "latitude":
          case "longitude":
            showText = value;
            break;
          case "datePicker":
            showText = this.$moment(value).format("YYYY-MM-DD");
            break;
          case "boolean":
            showText = value ? $T("是") : $T("否");
            break;
          case "enums":
            enumsArr = this.enumerations[enumLabel] || [];
            obj = enumsArr.find(i => i.id === value);
            showText = obj?.text;
            break;
          case "rangeSelection":
            showText = "rangeSelection";
            break;
          case "energytype":
            obj = this.customEnums.energytype.find(i => i.energytype === value);
            showText = obj?.name;
            break;
          case "deviceclassification":
            obj = this.customEnums.deviceclassification.find(
              i => i.id === value
            );
            showText = obj?.name;
            break;
          case "busbarseg":
            obj = this.customEnums.busbarseg.find(i => i.id === value);
            showText = obj?.name;
            break;
          case "product":
            obj = this.customEnums.product.find(i => i.producttype === value);
            showText = obj?.name;
            break;
          case "customEnums":
            obj = field.customEnums_ElOption.find(i => i.id === value);
            showText = obj?.text;
            break;

          default:
            break;
        }
      }
      if (!showText && showText !== 0) {
        return "--";
      } else {
        return showText.toString();
      }
    },
    importDocument: function () {
      const documentPath = this._.get(
        this.detailInfo,
        this.documentField.propertyLabel
      );
      if (!documentPath) {
        return;
      }
      var url = "/eembaseconfig/v1/common/downloadFile?path=" + documentPath;
      common.downExcelGET(url, {}, this.token, this.projectId_in);
    },
    //获取关联用能设备列表
    async getDeviceList() {
      const params = {
        subLayerConditions: [
          {
            modelLabel: "manuequipment"
          }
        ],
        rootLabel: "building",
        rootID: this.inputData_in.id,
        treeReturnEnable: true
      };
      const res = await customApi.getNodeTree(params);
      this.CetTable_1.data = this._.get(res, "data[0].children", []) || [];
    },
    formatNumFloat(val) {
      if (!val && val !== 0) return "--";
      return val.toFixed2(2);
    }
  }
};
</script>
<style lang="scss" scoped>
.detail-label {
  color: var(--T3);
  line-height: 1;
}
.value {
  line-height: 1.5;
}
.img {
  height: 80px;
  width: 80px;
}
.dcm-btn-class-label {
  padding-right: 0px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 150px;
  display: inline-block;
}
</style>
