<template>
  <div class="h-full flex flex-row">
    <div
      class="w-[315px] bg-BG1 rounded-Ra p-J4 box-border mr-J3 flex flex-col"
    >
      <div class="text-H3 mb-J3 font-bold">{{ $T("选择对象") }}</div>
      <customElSelect
        :prefix_in="$T('所属项目')"
        v-model="ElSelect_project.value"
        v-bind="ElSelect_project"
        v-on="ElSelect_project.event"
        class="mb-J3"
        v-show="ElOption_project.options_in?.length > 1"
      >
        <ElOption
          v-for="item in ElOption_project.options_in"
          :key="item[ElOption_project.key]"
          :label="item[ElOption_project.label]"
          :value="item[ElOption_project.value]"
          :disabled="item[ElOption_project.disabled]"
        ></ElOption>
      </customElSelect>
      <customElSelect
        :prefix_in="$T('能源类型')"
        class="mb-J3"
        v-model="ElSelect_1.value"
        v-bind="ElSelect_1"
        v-on="ElSelect_1.event"
      >
        <ElOption
          v-for="item in ElOption_1.options_in"
          :key="item[ElOption_1.key]"
          :label="item[ElOption_1.label]"
          :value="item[ElOption_1.value]"
          :disabled="item[ElOption_1.disabled]"
        ></ElOption>
      </customElSelect>
      <customElSelect
        v-model="ElSelect_treeType.value"
        v-bind="ElSelect_treeType"
        v-on="ElSelect_treeType.event"
        class="mb-J3"
        :prefix_in="$T('节点树类型')"
        v-if="multidimensional"
        v-show="ElOption_treeType.options_in?.length > 1"
      >
        <ElOption
          v-for="item in ElOption_treeType.options_in"
          :key="item[ElOption_treeType.key]"
          :label="item[ElOption_treeType.label]"
          :value="item[ElOption_treeType.value]"
          :disabled="item[ElOption_treeType.disabled]"
        ></ElOption>
      </customElSelect>
      <CetTree
        class="flex-auto"
        :key="treeKey"
        :selectNode.sync="CetTree_1.selectNode"
        :checkedNodes.sync="CetTree_1.checkedNodes"
        v-bind="CetTree_1"
        v-on="CetTree_1.event"
      ></CetTree>
    </div>
    <div class="flex-auto bg-BG1 rounded-Ra p-J4 box-border flex flex-col">
      <div class="text-H3 mb-J3 font-bold">{{ $T("配置报警阈值") }}</div>
      <div class="flex flex-row justify-between items-center mb-J3">
        <div class="flex flex-row items-center">
          <el-radio-group
            v-model="saveData.thresholdgeneratemethod"
            @change="thresholdgeneratemethodChange"
            class="mr-J3"
          >
            <el-radio-button :label="1" v-if="alarmConfigTypes.includes(1)">
              {{ $T("Al智能报警设置") }}
            </el-radio-button>
            <el-radio-button :label="2" v-if="alarmConfigTypes.includes(2)">
              {{ $T("自定义报警设置") }}
            </el-radio-button>
          </el-radio-group>
          <div
            v-if="
              saveData.thresholdgeneratemethod === 1 &&
              alarmConfigTypes.length > 1
            "
            class="text-Aa bg-Sta2 p-J0 rounded-Ra text-T5"
          >
            {{
              $T(
                "默认由软件自动设置动态阈值AI智能模式；月阈值提前一个月给出，日阈值提前一天给出"
              )
            }}
          </div>
          <div
            v-if="
              saveData.thresholdgeneratemethod === 2 &&
              alarmConfigTypes.length > 1
            "
            class="text-Aa bg-Sta2 p-J0 rounded-Ra text-T5"
          >
            {{ $T("选择自定义模式，系统判定停用AI智能模式阈值计划！") }}
          </div>
        </div>

        <CetButton
          v-bind="CetButton_grade"
          v-on="CetButton_grade.event"
        ></CetButton>
      </div>
      <div class="flex-auto flex flex-col">
        <ElForm
          :model="saveData"
          :rules="formRules"
          label-position="top"
          ref="form"
        >
          <el-form-item :label="$T('方案名称')" prop="name">
            <el-input
              class="w-[250px]"
              v-model.trim="saveData.name"
              :placeholder="$T('输入方案名称')"
            ></el-input>
          </el-form-item>
        </ElForm>
        <div class="flex-auto">
          <AiAlarmConfig
            v-show="saveData.thresholdgeneratemethod === 1"
            ref="aiAlarmConfig"
            v-bind="aiAlarmConfig"
            v-on="aiAlarmConfig.event"
            :selectYearTime.sync="selectYearTime"
          />
          <CustomAlarmConfig
            v-show="saveData.thresholdgeneratemethod === 2"
            ref="customAlarmConfig"
            v-bind="customAlarmConfig"
            v-on="customAlarmConfig.event"
            :selectYearTime.sync="selectYearTime"
          />
        </div>
      </div>
      <div class="mt-J3 flex flex-row justify-between items-center">
        <div class="flex flex-row items-center">
          <customElSelect
            :prefix_in="$T('收敛')"
            v-model="saveData.convergence"
            :placeholder="$T('请选择')"
            class="w-[200px] mr-J3"
          >
            <el-option :label="$T('是')" :value="true"></el-option>
            <el-option :label="$T('否')" :value="false"></el-option>
          </customElSelect>

          <el-checkbox v-model="saveData.ispredict">
            {{ $T("启用预警") }}
          </el-checkbox>

          <el-checkbox v-model="saveData.isalarm">
            {{ $T("启用告警") }}
          </el-checkbox>
        </div>
        <el-button class="fr mlJ1" type="primary" @click="saveAlarmConfig">
          {{ $T("保存") }}
        </el-button>
      </div>
    </div>
    <EventWarningGradeConfig
      v-bind="eventWarningGradeConfig"
      v-on="eventWarningGradeConfig.event"
    />
  </div>
</template>

<script>
import customApi from "@/api/custom";
import common from "eem-base/utils/common";
import AiAlarmConfig from "./aiAlarmConfig.vue";
import CustomAlarmConfig from "./customAlarmConfig.vue";
import EventWarningGradeConfig from "./EventWarningGradeConfig.vue";
export default {
  name: "efficiencyAlarmConfig",
  components: {
    AiAlarmConfig,
    CustomAlarmConfig,
    EventWarningGradeConfig
  },
  computed: {
    multidimensional() {
      return this.$store.state.multidimensional;
    },
    alarmConfigTypes() {
      return this.$store.state.alarmConfigTypes || [1, 2];
    },
    defaultAlarmType() {
      if (this.alarmConfigTypes.some(i => i === 2)) {
        return 2;
      }
      return 1;
    },
    rootNode() {
      const id = this.ElSelect_project.value;
      const list = this.ElOption_project.options_in;
      return list.find(item => item.id === id);
    }
  },

  data() {
    return {
      treeKey: 1,
      currentNode: null,
      saveData: {
        name: "",
        thresholdgeneratemethod: 1,
        convergence: true,
        isalarm: true,
        ispredict: true,
        id: 0
      },
      formRules: {
        name: [
          {
            required: true,
            message: $T("请输入方案名称"),
            trigger: ["blur", "change"]
          },
          common.check_name,
          common.check_pattern_name
        ]
      },
      selectYearTime: Date.now(),
      selectMonthTime: Date.now(),
      ElSelect_1: {
        value: null,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_1: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_treeType: {
        value: -1,
        event: {
          change: this.ElSelect_treeType_change_out
        }
      },
      ElOption_treeType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this._.debounce(this.CetTree_1_currentNode_out, 300)
        }
      },
      CetButton_grade: {
        visible_in: true,
        disable_in: false,
        title: $T("报警等级设置"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_grade_statusTrigger_out
        }
      },
      customAlarmConfig: {
        data_in: null,
        initDate_in: Date.now(),
        event: {
          yearTimeChange: this.yearTimeChange,
          monthTimeChange: this.monthTimeChange
        }
      },
      aiAlarmConfig: {
        data_in: null,
        initDate_in: Date.now(),
        event: {
          yearTimeChange: this.yearTimeChange,
          monthTimeChange: this.monthTimeChange
        }
      },
      eventWarningGradeConfig: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        event: {
          alarmlevelconfig_model_out: this.alarmlevelconfig_model_out
        }
      },
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },

      CetButton_import: {
        visible_in: true,
        disable_in: false,
        title: $T("导入"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_import_statusTrigger_out
        }
      },
      ElSelect_project: {
        value: 0,
        style: {},
        event: {
          change: this.ElSelect_project_change_out
        }
      },
      ElOption_project: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  methods: {
    async init() {
      await this.getProjectEnergy();
      await this.getRootNode();
      await this.queryTreeType();
      this.ElSelect_treeType_change_out(this.ElSelect_treeType.value);
    },
    async getRootNode() {
      this.ElOption_project.options_in = [];
      const res = await customApi.rootNode();
      const data = res.data || [];
      this.ElOption_project.options_in = data;
      this.ElSelect_project.value = data?.[0]?.id;
    },
    async ElSelect_project_change_out() {
      await this.queryTreeType();
      this.ElSelect_treeType_change_out(this.ElSelect_treeType.value);
    },
    /**
     * 获取项目能源类型
     */
    async getProjectEnergy() {
      const response = await customApi.getProjectEnergy();
      if (response.code !== 0) {
        return;
      }
      const data = response.data || [];
      this.ElOption_1.options_in = data;
      const hasElectricity = data.some(item => item.energytype === 2);
      this.ElSelect_1.value = hasElectricity ? 2 : data[0]?.energytype;
    },
    /**
     * 查询树类型
     */
    async queryTreeType() {
      const queryData = {
        status: true,
        rootNode: {
          id: this.rootNode.id,
          modelLabel: this.rootNode.modelLabel
        }
      };
      const res = await customApi.getAttributeDimensionTreeNodeConfig(
        queryData
      );
      if (res.code !== 0) {
        return;
      }
      const data = res?.data || [
        {
          id: -1,
          name: $T("固定管理层级")
        }
      ];
      this.ElOption_treeType.options_in = data;
      const flag = data.some(i => i.id === -1);
      this.ElSelect_treeType.value = flag ? -1 : data?.[0]?.id ?? null;
    },
    /**
     * 树类型切换
     */
    ElSelect_treeType_change_out(val) {
      val === -1 ? this.getTreeData1() : this.getTreeData2();
    },
    /**
     * 获取固定管理层级树
     */
    async getTreeData1() {
      const queryData = {
        plugin: "eem-base",
        business: "energy-analysis",
        energyType: this.ElSelect_1.value,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };
      const res = await customApi.getBusinessTree(queryData);
      if (res.code !== 0) {
        return;
      }
      const treeData = res.data || [];
      this.CetTree_1.inputData_in = treeData;
      this.CetTree_1.selectNode = treeData[0];
      this.treeKey++;
    },

    /**
     * 获取多维度树
     */
    async getTreeData2() {
      const queryData = {
        dimTreeConfigId: this.ElSelect_treeType.value,
        energyType: this.ElSelect_1.value
      };
      const res = await customApi.dimensionTreeFilterByEnergytype(queryData);
      if (res.code !== 0) {
        return;
      }
      const treeData = res.data || [];
      this.CetTree_1.inputData_in = treeData;
      this.CetTree_1.selectNode = treeData[0];
      this.treeKey++;
    },
    CetTree_1_currentNode_out(val) {
      if (!val) {
        return;
      }
      this.currentNode = val;
      this.aiAlarmConfig.initDate_in = Date.now();
      this.selectYearTime = Date.now();
    },
    /**
     * 获取方案详情
     */
    async getAlarmScheme() {
      if (!this.currentNode || !this.ElSelect_1.value) {
        this.initSchemeConfig(this.defaultAlarmType);
        return;
      }

      const queryData = {
        alarmType: 1,
        energyType: this.ElSelect_1.value,
        indexId: 0, // 能耗没有指标
        nodeId: this.currentNode.id,
        nodeLabel: this.currentNode.modelLabel,
        startTime: +this.$moment(this.selectYearTime).startOf("year"),
        endTime: +this.$moment(this.selectYearTime).endOf("year") + 1
      };
      const res = await customApi.alarmSchemeData(queryData);
      if (res.code !== 0) {
        return;
      }
      this.handleScheme(res.data[0]);
    },
    handleScheme(data) {
      if (!data) {
        this.initSchemeConfig(this.defaultAlarmType);
        return;
      }
      const {
        thresholdgeneratemethod,
        convergence,
        name,
        id,
        isalarm,
        ispredict,
        alarmlevelconfig_model
      } = data;

      this.saveData = {
        name,
        thresholdgeneratemethod,
        convergence,
        isalarm,
        ispredict,
        alarmlevelconfig_model,
        id
      };

      this.customAlarmConfig.data_in = {
        benchmarkset_model: data.benchmarkset_model
      };
      this.aiAlarmConfig.data_in = {
        benchmarkset_model: data.benchmarkset_model
      };
    },
    /**
     * 重置预警配置
     * @param {Number} thresholdgeneratemethod 方案类型
     */
    async initSchemeConfig(thresholdgeneratemethod) {
      this.saveData = {
        name: "",
        thresholdgeneratemethod: thresholdgeneratemethod,
        convergence: true,
        isalarm: true,
        ispredict: true,
        alarmlevelconfig_model: [
          {
            alarmcolorset_id: 1,
            alarmlevel: $T("报警等级一"),
            isactive: false,
            name: $T("红色报警"),
            rate: 100,
            id: 0
          },
          {
            alarmcolorset_id: 2,
            alarmlevel: $T("报警等级二"),
            isactive: false,
            name: $T("橙色报警"),
            rate: 85,
            id: 0
          },
          {
            alarmcolorset_id: 3,
            alarmlevel: $T("报警等级三"),
            isactive: false,
            name: $T("黄色报警"),
            rate: 80,
            id: 0
          }
        ],
        id: 0
      };
      this.customAlarmConfig.data_in = null;
      this.aiAlarmConfig.data_in = null;

      await this.$nextTick();
      this.$refs.form.clearValidate();
    },
    /**
     * 方案类型切换
     */
    thresholdgeneratemethodChange(val) {
      const text =
        val == 1
          ? $T(
              "您切换到AI智能模式，自定义方案将不再生效，只能选择一种方案配置能效报警计划！"
            )
          : $T(
              "您切换到自定义模式，AI智能报警方案将不再生效，只能选择一种方案配置能效报警计划！"
            );
      const backValue = val == 1 ? 2 : 1;

      this.$confirm(text, $T("尊敬的用户："), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false,
        showClose: false
      })
        .then(() => {
          const oldId = this.saveData.id;
          // 清理已配置数据
          this.initSchemeConfig(val);
          this.saveData.id = oldId;
        })
        .catch(() => {
          this.saveData.thresholdgeneratemethod = backValue;
        });
    },
    /**
     * 打开报警等级配置
     */
    CetButton_grade_statusTrigger_out() {
      this.eventWarningGradeConfig.inputData_in = this._.cloneDeep(
        this.saveData.alarmlevelconfig_model
      );
      this.eventWarningGradeConfig.visibleTrigger_in = Date.now();
    },
    /**
     * 报警等级配置输出
     */
    alarmlevelconfig_model_out(val) {
      this.saveData.alarmlevelconfig_model = val;
    },
    /**
     * 保存方案
     */
    async saveAlarmConfig() {
      let alarmlevelconfig = this.saveData.alarmlevelconfig_model;
      const flag = alarmlevelconfig.some(item => item.isactive);
      if (!flag) {
        this.$confirm($T("是否已勾选报警等级?"), $T("提示"), {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        })
          .then(() => {
            this.saveHandle();
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: $T("已取消")
            });
          });
        return;
      }
      this.saveHandle();
    },
    async saveHandle() {
      const saveData = {
        alarmtype: 1,
        relatedNodes: [
          {
            id: this.currentNode.id,
            modelLabel: this.currentNode.modelLabel,
            name: this.currentNode.name
          }
        ],
        thresholdgeneratemethod: this.saveData.thresholdgeneratemethod,
        id: this.saveData.id,
        efId: 0,
        energytype: this.ElSelect_1.value,
        benchmarkset_model: [],
        alarmlevelconfig_model: this.saveData.alarmlevelconfig_model,
        convergence: this.saveData.convergence || true,
        notificationmethod: "",
        isalarm: this.saveData.isalarm,
        ispredict: this.saveData.ispredict,
        name: this.saveData.name,
        rootnodeid: this.rootNode.id,
        rootnodelabel: this.rootNode.modelLabel
      };
      const params = {
        startTime: +this.$moment(this.selectYearTime).startOf("year"),
        endTime: +this.$moment(this.selectYearTime).endOf("year") + 1
      };
      const valid = await this.$refs.form.validate();
      if (!valid) return;

      let ref = this.$refs.customAlarmConfig;
      if (this.saveData.thresholdgeneratemethod === 1) {
        ref = this.$refs.aiAlarmConfig;
      }

      const { benchmarkset_model } = ref.getSaveData();
      saveData.benchmarkset_model = benchmarkset_model;

      if (this.saveData.thresholdgeneratemethod === 1 && !saveData.id) {
        // 如果是新建AI方案，前端组成默认的benchmarkset_model
        saveData.benchmarkset_model = ref.defaultBenchmarkset();
      }

      const res = await customApi.saveAlarmSchemeData(saveData, params);
      if (res.code !== 0) {
        return;
      }

      this.$message.success($T("保存成功"));
      this.getAlarmScheme();
    },

    CetButton_export_statusTrigger_out() {
      let queryTime = +this.$moment(this.selectMonthTime).startOf("month");
      const queryData = {
        alarmType: 1,
        energyType: this.ElSelect_1.value,
        queryTime: queryTime,
        rootNode: {
          id: this.rootNode.id,
          modelLabel: this.rootNode.modelLabel
        }
      };
      common.downExcel(`/eembaseenergy/v1/alarm/alarmScheme/export`, queryData);
    },
    CetButton_import_statusTrigger_out() {
      this.uploadDialog.openTrigger_in = new Date().getTime();
    },
    async uploadDialog_uploadFile(val) {
      const formData = new FormData();
      formData.append("file", val.file);
      const params = {
        alarmType: 1,
        energyType: this.ElSelect_1.value
      };
      const response = await customApi.importAlarmScheme(formData, params);
      if (response.code !== 0) {
        return;
      }
      this.$message({
        type: "success",
        message: $T("上传成功！")
      });
      this.getAlarmScheme();
      this.uploadDialog.closeTrigger_in = Date.now();
    },
    /**
     * 切换年时间
     */
    yearTimeChange() {
      this.getAlarmScheme();
    },
    /**
     * 切换月时间
     */
    monthTimeChange(date) {
      this.selectMonthTime = date;
    }
  },
  mounted() {
    this.init();
  }
};
</script>
