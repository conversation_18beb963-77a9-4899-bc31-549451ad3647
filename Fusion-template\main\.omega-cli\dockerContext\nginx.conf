
user nginx;
worker_processes 1;

error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;


events {
    worker_connections 1024;
}


http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
    '$status $body_bytes_sent "$http_referer" '
    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    #tcp_nopush     on;
    # 不显示nginx的版本
    server_tokens off;
    #启动xss保护，并在检查到xss攻击时，停止渲染页面
    add_header X-XSS-Protection "1; mode=block";
    keepalive_timeout 65;

    # 开启gzip
    gzip on;
    # 启用gzip压缩的最小文件；小于设置值的文件将不会被压缩
    gzip_min_length 1k;
    # gzip 压缩级别 1-10
    gzip_comp_level 2;
    # 进行压缩的文件类型。
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
    # 是否在http header中添加Vary: Accept-Encoding，建议开启
    gzip_vary on;

    #gzip  on;

    #include /etc/nginx/conf.d/*.conf;
    server {
        #定义网站的ip 端口号
        listen 80;
        server_name localhost;

        #charset koi8-r;
        charset utf-8;
        charset_types text/xml text/plain text/vnd.wap.wml application/javascript application/rss+xml text/css;
        # 修复安全漏洞：CVE-2016-2183
        ssl_ciphers HIGH:!aNULL:!MD5:!3DES;
        #access_log  logs/host.access.log  main;
        #前端部署静态资源服务器
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm index.php;
            client_max_body_size 20m;
        }
        # html文件不缓存,禁止给html文件注入
        location ~* /.html$ {
            add_header Cache-Control "max-age=0, no-cache, no-store, must-revalidate";
            add_header Content-Security-Policy "default-src 'self'; script-src 'self';";
        }
        #网关
        location /gateway {
            root /usr/share/nginx/html;
            index index.html index.htm index.php;
            proxy_pass http://gateway1:4001/;
            proxy_redirect default;
            client_max_body_size 1000m;
        }
        #权限服务
        location /auth {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://gateway1:4001/cloud-auth-service/cloud/api/auth;
            proxy_redirect default;
            client_max_body_size 1000m;
        }
        location /fusion-service {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://gateway1:4001/fusion-matrix/fusion;
            proxy_redirect default;
            client_max_body_size 1000m;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        location /fusion-gateway {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://gateway1:4001/;
            proxy_redirect default;
            client_max_body_size 1000m;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        #模型
        location /model-meta {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://gateway1:4001/model-service/model-meta/;
            proxy_redirect default;
            client_max_body_size 1000m;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        location /model {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://gateway1:4001/model-service/model/;
            proxy_redirect default;
            client_max_body_size 1000m;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        # 设备数据服务
        location /devicedata {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://gateway1:4001/device-data-service;
            proxy_redirect default;
            client_max_body_size 1000m;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # 设备数据服务2
        location /device-data {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://gateway1:4001/device-data-service;
            proxy_redirect default;
            client_max_body_size 1000m;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location /bff {
            proxy_pass http://bff-service:3006;
            client_max_body_size 1000m;
            proxy_set_header Host $host;
            proxy_set_header Authorization $http_authorization;
        }

        #视频服务
        location /video {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://video:8082/video;
            proxy_redirect default;
            client_max_body_size 1000m;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location /live {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://video/live;
            proxy_redirect default;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }


        # 消息通知服务--- 没走网关
        location /messageServer {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://notice1:5070/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # 消息通知服务--- 走网关
        location /notice {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://gateway1:4001/notice-service;
            proxy_redirect default;
            client_max_body_size 1000m;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        }

        # onlyReport报表服务
        location /only-report/api {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://gateway1:4001/only-report-docker/only-report/api;
            proxy_redirect default;
            client_max_body_size 1000m;
            proxy_set_header Host $host:$proxy_port;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location /onlinePreviewHtml {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://kkfileview:8012/;
            proxy_redirect default;
            client_max_body_size 1000m;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location /only-report {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://kkfileview:8012/only-report;
            proxy_redirect default;
            client_max_body_size 1000m;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}
