<template>
  <div class="fullfilled">
    <div class="h-[32px] flex">
      <div
        v-for="(item, index) in legendList"
        :key="index"
        class="flex items-center mr-J2"
      >
        <div
          class="w-[10px] h-[10px] mr-J0"
          :style="{ backgroundColor: item.color }"
        ></div>
        <div>{{ item.name }}</div>
      </div>
    </div>
    <div class="h-[calc(100%-32px)] overflow-auto" id="container">
      <div
        class="chart-page"
        style="display: block; overflow: hidden"
        :style="{ height: containerHeight, minHeight: '100%' }"
      >
        <CetChart
          ref="cetChart"
          v-bind="CetChart_1"
          @click="clickChart"
        ></CetChart>
      </div>
    </div>
  </div>
</template>

<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
import omegaTheme from "@omega/theme";
const colors = [
  // "#aaa",
  // "#0d86ff",
  // "#7befa3",
  // "#efb92e",
  // "#b5cfe9",
  // "#5eb54d",
  "#f79340",
  "#6eb6ff",
  "#19aaaa",
  "#8680f6",
  "#a8f7cf",
  "#c3d866",
  "#e8e392",
  "#1087ff",
  "#15fcfc",
  "#26ec86",
  "#2485c0",
  "#8680f6",
  "#5cbbeb",
  "#659ded",
  "#e8e392",
  "#ffc83a",
  "#84f4fa",
  "#116dd3",
  "#fba44b",
  "#ff671a",
  "#fe6b6b",
  "#ba82e5",
  "#8471dd",
  "#5671e2",
  "#83afda",
  "#19aaaa"
];

export default {
  name: "sankeyChart",
  props: {
    inputData_in: {
      type: Object
    },
    energyTypeList: {
      type: Array
    },
    imageName: {
      type: String
    },
    isTotalEnergyType: {
      type: Boolean
    },
    params: {
      type: Object
    }
  },
  data(vm) {
    return {
      containerHeight: 0,
      energyColor: {}, // 给每种能源类型添加颜色
      // 1组件
      CetChart_1: {
        //组件输入项
        inputData_in: null,
        options: {
          title: {
            subtext: "",
            left: "center"
          },
          toolbox: {
            top: 30,
            right: 30,
            feature: {
              saveAsImage: {
                name: vm.imageName,
                title: $T("保存为图片")
              }
            }
          },
          tooltip: {
            trigger: "item",
            triggerOn: "mousemove",
            formatter(val) {
              return vm.formatTooltip(val);
            }
          },
          series: [
            {
              type: "sankey",
              layout: "none",
              draggable: false,
              layoutIterations: 0,
              emphasis: {
                focus: "adjacency"
              },
              nodeGap: 14,
              nodeAlign: "right",
              // focusNodeAdjacency: "allEdges", // 鼠标划上时高亮的节点和连线，allEdges表示鼠标划到节点上点亮节点上的连线及连线对应的节点
              label: {
                formatter: value => {
                  const data = value.data || {};
                  let nodeName = data.nodeName;
                  const maxlength = 10;
                  const result = this.isExpand.find(
                    item => item.id === data.treeId
                  );
                  let tip = "";
                  if (data.expend) {
                    tip = `(${$T("可展开")})`;
                  }
                  if (result && result.expanded) {
                    tip = "";
                  }
                  if ((nodeName && nodeName.length) > maxlength) {
                    nodeName = nodeName.substring(0, maxlength - 1) + "...";
                  }
                  return (
                    `${nodeName}   ${common.formatNum(
                      data.totalValue.toFixed(2)
                    )}(${data.unit})${
                      data.percentage ? `(${data.percentage})` : ""
                    }` + tip
                  );
                }
              },
              data: [],
              links: []
            }
          ]
        }
      },
      energyShow: false, // 点击能源类型显示单一能源流向
      isExpand: [],
      initInputData: {}, //初始化数据存放
      handInputData: {}, //过滤使用数据存放
      singleEnergyType: null, //点击展示单能源类型时，保存对应展示的能源类型
      markNum: 0,
      maxIindex: 0,
      childrenData: {}, //子节点数据
      initIsExpand: [],
      legendList: [] //图例信息
    };
  },
  watch: {
    inputData_in: {
      handler(val) {
        if (!val || !val.energyFlowNodeDataList || !val.linkNodeList) return;
        if (Array.isArray(val.energyFlowNodeDataList)) {
          val.energyFlowNodeDataList.forEach((item, index) => {
            item.iindex = index;
          });
          this.maxIindex = val.energyFlowNodeDataList.length;
        }
        val.linkNodeList.forEach(item => {
          if ([undefined, null, NaN].includes(item.value)) {
            item.value = 0;
          }
        });
        this.initInputData = this._.cloneDeep(val);
        //搜索定位节点时，不需仅展示三层节点
        if (_.isEmpty(val.selectId)) {
          this.handInputData = this.filterInitData(val);
        } else {
          this.handInputData = _.cloneDeep(val);
        }
        this.setChartData(this.handInputData, val.selectId);
      },
      deep: true
    },
    energyTypeList: {
      handler(val) {
        val.forEach((item, index) => {
          this.energyColor[item.energytype] = colors[index % colors.length];
        });
        this.energyColor = {
          ...this.energyColor,
          2: "#0d86ff", // 电
          3: "#7befa3", // 水
          14: "#efb92e", // 燃气
          16: "#b5cfe9", // 压缩空气
          24: "#5eb54d", // 冷量
          46: "#f79360" // 热量
        };
        // 热量
        // this.energyColor[46] = colors[val.length % colors.length];
      },
      immediate: true
    }
  },
  computed: {
    isLight() {
      return omegaTheme.theme === "light";
    }
  },
  methods: {
    setChartData(val) {
      this.isExpand = [];
      if (!val || !val.energyFlowNodeDataList) return;
      let depth = 0;
      // 先默认所有节点都可展开
      val.energyFlowNodeDataList.forEach(item => {
        item.expend = true;
        if (item.depth > depth) {
          depth = item.depth;
        }
      });
      //如果是最后一层子节点，不需要显示-可展示
      val.energyFlowNodeDataList.forEach(item => {
        let expanded = false;
        if (item.depth < depth) {
          expanded = true;
        } else {
          let getIsChild = this.initInputData.linkNodeList.filter(ii => {
            return item.treeId === ii.source;
          });
          expanded = getIsChild.length === 0;
        }
        //这里默认层级小于最后一层的均展开，但搜索定位时搜索节点各级的兄弟节点均需为收缩状态
        if (
          this.inputData_in.selectId &&
          item.depth <= depth - 1 &&
          item.depth > 0 &&
          item.treeId !== this.inputData_in.selectId
        ) {
          const getIsChild = this.initInputData.linkNodeList.filter(ii => {
            return item.treeId === ii.source;
          });
          expanded =
            getIsChild.length === 0
              ? true
              : this.getIsExpend(getIsChild[0].target);
        }
        this.isExpand.push({ id: item.treeId, expanded: expanded });
      });
      this.initIsExpand = _.cloneDeep(this.isExpand);
      const chartData = [];
      const legends = [];
      // 桑基图data
      // 现节点颜色的赋值逻辑改为节点有多种能源类型时，节点颜色赋为综合能源的颜色；为单一能源时，节点颜色赋为该能源的颜色
      val.energyFlowNodeDataList.forEach((item, index) => {
        const values = item.energyValues;
        let color = this.getNodeColor(values);
        // 将综合能源放到最前面
        if (color === "#848BA1") {
          legends.unshift({
            name: "综合能源",
            color: color
          });
        } else {
          legends.push({
            name: values[0].name,
            color: color
          });
        }
        if (
          this.inputData_in.selectId &&
          item.treeId === this.inputData_in.selectId
        ) {
          color = this.isLight ? "#505F6E" : "#FAFBFB";
        }
        chartData.push({
          ...item,
          name: item.treeId,
          unit: val.unit,
          itemStyle: {
            color,
            borderWidth: 1,
            borderColor: color
          }
        });
      });
      // 桑基图link
      val.linkNodeList.forEach(item => {
        const source = val.energyFlowNodeDataList.find(
          flow => flow.treeId === item.source
        );
        const target = val.energyFlowNodeDataList.find(
          flow => flow.treeId === item.target
        );
        item.sourceName = (source && source.nodeName) || "";
        item.targetName = (target && target.nodeName) || "";
        item.unit = val.unit;
        item.lineStyle = {
          color: this.energyColor[item.energyType] || "source",
          opacity: 0.4
        };
        if (this.energyColor[item.energyType]) {
          legends.push({
            name: item.energyName,
            color: this.energyColor[item.energyType]
          });
        }
      });
      this.calcHeight(chartData);
      this.CetChart_1.options.toolbox.feature.saveAsImage.name = this.imageName;
      this.CetChart_1.options.series[0].data = chartData;
      this.CetChart_1.options.series[0].links = val.linkNodeList;
      this.legendList = this.getLegend(legends);

      // 部分选中节点需拖动滚动条展示
      if (!_.isEmpty(this.inputData_in.selectId)) {
        this.markNum = 0;
        this.handlerFocusNode();
      }
    },
    getNodeColor(values) {
      if (values.length > 1) {
        //存在多种能源类型，取综合能源的颜色
        return "#848BA1";
      }
      return this.energyColor[values[0].energyType];
    },
    getLegend(val) {
      const map = new Map();
      const newArr = val.filter(
        v => !map.has(v.name) && v.name && map.set(v.name, v)
      );
      return newArr;
    },
    getIsExpend(target) {
      const nodeObj = this.initInputData.energyFlowNodeDataList.find(
        item => item.treeId === target
      );
      return !_.isEmpty(nodeObj);
    },
    handlerFocusNode() {
      const myChart = this.$refs["cetChart"].chart;
      if (!myChart) return;

      myChart.on("finished", () => {
        //选中节点id
        if (_.isEmpty(this.inputData_in.selectId)) return;
        if (this.markNum > 1) {
          myChart.off("finished");
          return;
        }
        this.markNum++;

        const nodes = myChart
          .getModel()
          .getSeriesByType("sankey")[0]
          .getGraph().nodes;
        const findNodes = nodes.filter(
          node => node.id === this.inputData_in.selectId
        );
        if (findNodes.length > 0) {
          // 通过图形元素获取
          const shape = findNodes[0].getGraphicEl();
          const rectStroke = shape._rectStroke;
          const { x, y, height, width } = rectStroke;
          const centerX = x + width / 2;
          const centerY = y + height / 2;

          const container = document.getElementById("container");
          //节点坐标与容器滚动长度间需加上组件与容器的间距，此处未设置则默认5%
          container.scrollTo({
            left:
              container.scrollWidth * 0.05 +
              centerX -
              container.clientWidth * 0.5,
            top:
              container.scrollHeight * 0.05 +
              centerY -
              container.clientHeight * 0.5,
            behavior: "smooth"
          });
        }
      });
    },
    formatTooltip(val) {
      const data = val.data || {};
      let str = "";
      if (val.dataType === "node") {
        if (data.depth === 1) {
          // 动力站房浮动单独处理，显示流入流出
          const target = this.initInputData.linkNodeList.find(
            item => item.source === data.treeId
          );
          const energyType = target && target.energyType;
          str = `${data.nodeName}<br />`;
          const otherTypes = [16, 24, 46]; // 压缩空气、冷量、热量处理
          // 流入的能源类型
          const flowInto = data.energyValues.filter(
            item => !otherTypes.includes(item.energyType)
          );
          flowInto.forEach((item, index) => {
            if (index === 0) {
              str += `${$T("流入")}: ${item.name}: ${common.formatNum(
                item.value.toFixed(2)
              )} ${data.unit};<br />`;
            } else {
              str += `&emsp;&emsp;&ensp;${item.name}: ${common.formatNum(
                item.value.toFixed(2)
              )} ${data.unit};<br />`;
            }
          });
          // 流出能源类型
          const flowOut = data.energyValues.find(
            item => item.energyType === energyType
          );
          str += flowOut
            ? `${$T("流出")}:  ${flowOut.name}: ${common.formatNum(
                flowOut.value.toFixed(2)
              )} ${data.unit};`
            : "";
        } else {
          str = `${data.nodeName}: ${common.formatNum(
            data.totalValue.toFixed(2)
          )} ${data.unit}`;
        }
      } else if (val.dataType === "edge") {
        str = `${data.sourceName}   >  ${data.targetName}(${
          data.energyName
        }) :  ${common.formatNum(val.value.toFixed(2))} ${data.unit}`;
      }
      return str;
    },
    // 点击节点展开
    async clickChart(val) {
      if (val.dataType === "edge") return;

      const vm = this;
      const data = this._.cloneDeep(val.data);
      let index = 0;
      const result = this.isExpand.find((item, idx) => {
        index = idx;
        return item.id === data.treeId;
      });
      //如果是点击最后一层子节点，直接return退出处理
      let getIsChild = this.initInputData.linkNodeList.filter(ii => {
        return data.treeId === ii.source;
      });
      //单能源类型下，需要排除其他能源类型下子节点
      if (this.energyShow && this.singleEnergyType) {
        getIsChild = getIsChild.filter(
          item => item.energyType === this.singleEnergyType
        );
      }
      if (getIsChild.length === 0) {
        return;
      }
      const obj = this.initIsExpand.find(item => item.id === data.treeId);
      const mark = obj ? obj.expanded : false;
      const target = this.initInputData.linkNodeList.find(
        item => item.source === data.treeId
      );
      const energyType = target && target.energyType;
      const otherTypes = [16, 24, 46]; // 压缩空气、冷量、热量处理
      //判断是否可展开,点击能源类型时只显示当前的能源流向
      if (
        data.depth === 0 ||
        (data.depth === 1 && otherTypes.includes(energyType))
      ) {
        if (!this.isTotalEnergyType) {
          return;
        }
        this.energyShow = !this.energyShow;
        if (this.energyShow) {
          this.singleEnergyType = energyType;
          const chartData = [];
          const legends = [];
          this.handInputData.energyFlowNodeDataList.forEach((item, index) => {
            let color = this.energyColor[energyType];
            legends.push({
              name: target?.energyName,
              color
            });

            // 找到只有当前能源类型的节点
            if (this._.find(item.energyValues, ["energyType", energyType])) {
              chartData.push({
                ...item,
                name: item.treeId,
                unit: this.handInputData.unit,
                itemStyle: {
                  color,
                  borderWidth: 1,
                  borderColor: color
                }
              });
            }
          });
          const links = this.handInputData.linkNodeList.filter(
            item => item.energyType === energyType
          );
          //单能源类型下，对可展示列表进行重新排序
          this.isExpand = [];
          chartData.forEach(item => {
            let expanded = false;
            if (item.depth < 3) {
              expanded = true;
            } else {
              let getIsChild = this.initInputData.linkNodeList.filter(ii => {
                return item.treeId === ii.source;
              });
              //单能源类型下，需要排除其他能源类型下子节点
              getIsChild = getIsChild.filter(
                item => item.energyType === energyType
              );
              expanded = getIsChild.length === 0;
            }
            this.isExpand.push({ id: item.treeId, expanded: expanded });
          });
          vm.calcHeight(chartData);
          this.CetChart_1.options.series[0].links = links;
          this.CetChart_1.options.series[0].data = chartData;
          this.legendList = this.getLegend(legends);
        } else {
          this.setChartData(this.handInputData);
        }
      } else if (result) {
        // 收缩，根节点不能收缩
        if (result.expanded && data.depth !== 0) {
          this.isExpand[index].expanded = false;
          const nodeData = this._.cloneDeep(
            this.CetChart_1.options.series[0].data
          );
          const linkData = this._.cloneDeep(
            this.CetChart_1.options.series[0].links
          );
          this.handleNode(linkData, data, nodeData);
          vm.calcHeight(nodeData);
          this.CetChart_1.options.series[0].data = nodeData;
          this.CetChart_1.options.series[0].links = linkData;
          // 展开
        } else if (!result.expanded) {
          this.isExpand[index].expanded = true;
          let childNodeData = [];
          if (this.inputData_in.selectId && mark === false) {
            await this.getChildNodeDataNew(val);
            childNodeData = this.childrenData;
            this.initInputData.linkNodeList =
              this.initInputData.linkNodeList.concat(
                childNodeData.linkNodeList
              );
            this.initInputData.energyFlowNodeDataList =
              this.initInputData.energyFlowNodeDataList.concat(
                childNodeData.energyFlowNodeDataList
              );
          } else {
            childNodeData = this.getChildNodeData(data);
          }

          const chartData = [];
          // 桑基图data
          const energyFlowNodeDataList =
            childNodeData.energyFlowNodeDataList.filter(item => {
              return item.treeId !== data.treeId;
            });
          const expand = energyFlowNodeDataList;
          // 先默认所有节点都可展开
          expand.forEach(item => {
            item.expend = true;
          });
          //如果是最后一层子节点，不需要显示/可展示
          expand.forEach(item => {
            let expanded = false;
            let getIsChild = this.initInputData.linkNodeList.filter(ii => {
              return item.treeId === ii.source;
            });
            //单能源类型下，需要排除其他能源类型下子节点
            if (this.energyShow && this.singleEnergyType) {
              getIsChild = getIsChild.filter(
                item => item.energyType === this.singleEnergyType
              );
            }
            expanded = getIsChild.length === 0;
            vm.isExpand.push({ id: item.treeId, expanded: expanded });
          });
          let linkNodeList = vm._.cloneDeep(childNodeData.linkNodeList);
          const target = linkNodeList.find(item => item.source === data.treeId);
          const energyType = target && target.energyType;
          const legends = [];
          energyFlowNodeDataList.forEach((item, index) => {
            let color = this.getNodeColor(item.energyValues);
            // 将综合能源放到最前面
            if (color === "#848BA1") {
              legends.unshift({
                name: "综合能源",
                color: color
              });
            } else {
              legends.push({
                name: target?.energyName,
                color: color
              });
            }
            if (
              this.inputData_in.selectId &&
              item.treeId === this.inputData_in.selectId
            ) {
              color = this.isLight ? "#505F6E" : "#FAFBFB";
            }
            chartData.push({
              ...item,
              iindex:
                this.inputData_in.selectId && mark === false
                  ? this.maxIindex++
                  : item.iindex,
              name: item.treeId,
              unit: childNodeData.unit,
              itemStyle: {
                color,
                borderWidth: 1,
                borderColor: color
              }
            });
          });
          if (this.energyShow && this.singleEnergyType) {
            linkNodeList = linkNodeList.filter(
              item => item.energyType === this.singleEnergyType
            );
          }
          // 桑基图link
          linkNodeList.forEach(item => {
            const target = energyFlowNodeDataList.find(
              flow => flow.treeId === item.target
            );
            item.sourceName = data.nodeName || "";
            item.targetName = (target && target.nodeName) || "";
            item.unit = childNodeData.unit;
            item.lineStyle = {
              color: this.energyColor[item.energyType] || "source",
              opacity: 0.4
            };
            if (this.energyColor[item.energyType]) {
              legends.push({
                name: item.energyName,
                color: this.energyColor[item.energyType]
              });
            }
          });
          let nodes = vm.CetChart_1.options.series[0].data.concat(chartData);
          const links =
            vm.CetChart_1.options.series[0].links.concat(linkNodeList);

          nodes = this.sortDatafilter(nodes);
          vm.calcHeight(nodes);
          vm.CetChart_1.options.series[0].data = nodes;
          vm.CetChart_1.options.series[0].links = links;
          this.legendList = this.getLegend(this.legendList.concat(legends));
        }
      }
    },
    //点击缩收删除选中节点下子节点
    handleNode(linkData, data, nodeData) {
      const toDelete = [];
      // linkData中找到所有source为当前点击的节点
      const deleteArr = linkData.filter(item => {
        return data && item.source === data.treeId;
      });
      if (deleteArr.length) {
        // linkData中删除deleteArr数据
        deleteArr.forEach(item => {
          this.deleteFun(linkData, item, toDelete);
        });
      }

      if (toDelete.length) {
        // linkData中被删除的数据继续调用handleNode
        toDelete.forEach(item => {
          const deleteData = nodeData.find(it => {
            return it.treeId === item.target;
          });
          this.handleNode(linkData, deleteData, nodeData);
        });
      }
      // nodeData中删除deleteArr中target节点，可展开节点中同步删除
      deleteArr.forEach(item => {
        const idx = nodeData.findIndex(it => {
          return item.target === it.treeId;
        });
        const it = this.isExpand.findIndex(i => {
          return item.target === i.id;
        });
        if (idx >= 0) {
          nodeData.splice(idx, 1);
        }
        if (it >= 0) {
          this.isExpand.splice(it, 1);
        }
      });
    },
    deleteFun(linkData, item, toDelete) {
      const obj = this.initIsExpand.find(node => node.id === item.source);
      const mark = obj ? obj.expanded : false;
      const idx = linkData.findIndex(it => {
        return this._.isEqual(it, item);
      });
      if (idx >= 0 && this.inputData_in.selectId && mark === false) {
        toDelete.push(linkData[idx]);
      } else if (idx >= 0) {
        toDelete.push(linkData.splice(idx, 1)[0]);
      }
    },
    calcHeight(data) {
      let height = 0;
      height = data.length * 20;
      this.containerHeight = height + "px";
    },
    //过滤初始化返回数据，只需要显示三层数据结构;depth ===0和depth === 1归类为父节点
    filterInitData(val) {
      let filDepth = 3;
      if (!this.isTotalEnergyType) {
        filDepth = 2;
      }
      let energyFlowNodeDataList = val.energyFlowNodeDataList || [],
        linkNodeList = val.linkNodeList || [];
      let filEnergyFlowNodeDataList = energyFlowNodeDataList.filter(
        item => item.depth <= filDepth
      );
      let filLinkNodeList = linkNodeList.filter(item => {
        // 之前的过滤方式，跨到3层后面的关联关系无法展示，故调整过滤关联关系的逻辑
        let isOk = false;
        for (let i = 0, len = filEnergyFlowNodeDataList.length; i < len; i++) {
          if (item.source === filEnergyFlowNodeDataList[i].treeId) {
            isOk = true;
            break;
          }
        }
        return isOk;
      });
      return {
        energyFlowNodeDataList: filEnergyFlowNodeDataList,
        linkNodeList: filLinkNodeList,
        unit: val.unit
      };
    },
    //获取点击选择节点下一子层级
    getChildNodeData(data) {
      let energyFlowNodeDataList =
          this.initInputData.energyFlowNodeDataList || [],
        linkNodeList = this.initInputData.linkNodeList || [];
      // linkData中找到所有source为当前点击的节点
      let childLinkData = linkNodeList.filter(item => {
        return data && item.source === data.treeId;
      });
      // nodeData中删除deleteArr中target节点，可展开节点中同步删除
      let childData = energyFlowNodeDataList.filter(item => {
        let isOk = false;
        for (let i = 0, len = childLinkData.length; i < len; i++) {
          if (item.treeId === childLinkData[i].target) {
            isOk = true;
            break;
          }
        }
        return isOk;
      });
      return {
        energyFlowNodeDataList: childData,
        linkNodeList: childLinkData,
        unit: this.initInputData.unit
      };
    },
    //调用接口获取子级数据
    async getChildNodeDataNew(data) {
      const { searchNode, ...params } = this.params;
      const list = data.name.split("_") || [];
      params.node = list
        ? {
            id: Number(list[0]),
            modelLabel: list[1]
          }
        : null;
      params.depth = 1;
      params.startDepth = data.data.depth;
      let nodeList = [];
      let linkList = [];
      const res = await customApi.queryEnergyFlowNoLoading(params);
      if (res.code === 0) {
        nodeList = res.data.energyFlowNodeDataList || [];
        linkList = res.data.linkNodeList || [];
      }
      nodeList = nodeList.filter(node => node.treeId !== data.name);
      linkList = linkList.filter(link => link.source !== data.name);
      this.childrenData = {
        energyFlowNodeDataList: nodeList,
        linkNodeList: linkList,
        unit: this.initInputData.unit
      };
    },
    //对桑基图节点列表重新排序顺序
    sortDatafilter(data) {
      data = data || [];
      let obj = {},
        newArr = [];
      data.forEach(item => {
        obj[item.iindex] = item;
      });
      for (let i in obj) {
        newArr.push(obj[i]);
      }
      return newArr;
    }
  },
  mounted() {},
  activated() {}
};
</script>

<style lang="scss" scoped>
.chart-page {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
