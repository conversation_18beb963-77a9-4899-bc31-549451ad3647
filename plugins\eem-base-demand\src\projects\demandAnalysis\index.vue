<template>
  <div class="h-full">
    <CetAside class="cet-aside">
      <template #aside>
        <div class="h-full flex flex-col">
          <el-radio-group
            v-model="treeMode"
            @change="radioChange"
            class="w-full radio flex"
          >
            <el-radio-button label="meter" class="flex-1">
              {{ $T("查询需量表计") }}
            </el-radio-button>
            <el-radio-button label="collect" class="flex-1">
              {{ $T("收藏夹") }}
            </el-radio-button>
          </el-radio-group>
          <div v-if="treeMode === 'meter'" class="flex-auto">
            <CetTree
              :selectNode.sync="CetTree_m.selectNode"
              :checkedNodes.sync="CetTree_m.checkedNodes"
              v-bind="CetTree_m"
              v-on="CetTree_m.event"
            >
              <div
                class="custom-tree-node el-tree-node__label"
                slot-scope="{ node, data }"
              >
                <span>{{ node.label }}</span>
                <div v-if="data.nodeType === 269619472">
                  <el-tooltip
                    :visible-arrow="false"
                    :content="$T('收藏表计')"
                    placement="bottom"
                  >
                    <div
                      class="el-icon-star-off icon mr-J1"
                      style="color: #ffc531"
                      @click="onClickFavorite(data)"
                    ></div>
                  </el-tooltip>
                </div>
              </div>
            </CetTree>
          </div>
          <div v-else-if="treeMode === 'collect'" class="flex-1">
            <favoritesMode
              :tree="favoriteData"
              @updateFavorites="getFavoritesData"
              @currentNode_out="selectNode"
            />
          </div>
        </div>
      </template>
      <template #container>
        <div class="h-full flex-column">
          <div v-if="!isEmpty" class="h-full">
            <demandAnalysis :id="deviceId" />
          </div>
          <div
            v-else
            class="right-side-error h-full"
            :class="{ light: lightTheme }"
          >
            <div class="empty">
              <img class="img" :src="emptyImg" alt="" />
              <div class="text-description">
                {{ $T("请选择设备节点进行查询！") }}
              </div>
            </div>
          </div>
        </div>
      </template>
    </CetAside>
    <favoriteDialog v-bind="favoriteDialog" v-on="favoriteDialog.event" />
  </div>
</template>
<script>
import customApi from "@/api/custom";
import favoriteDialog from "./components/favoriteDialog.vue";
import demandAnalysis from "./components/demandAnalysis.vue";
import favoritesMode from "./components/favoritesMode.vue";
import omegaTheme from "@omega/theme";
import { api } from "@altair/knight";
export default {
  name: "DemandQueryAndAnalysis",
  components: {
    favoriteDialog,
    demandAnalysis,
    favoritesMode
  },
  computed: {
    lightTheme() {
      return omegaTheme.theme === "light";
    },
    emptyImg() {
      return this.lightTheme
        ? require("./assets/empty_light.png")
        : require("./assets/empty_dark.png");
    }
  },
  data: function () {
    return {
      tenantId: "",
      treeMode: "meter",
      CetTree_m: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        nodeKey: "id",
        props: {
          label: "text",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: true,
        event: {
          currentNode_out: this.CetTree_m_currentNode_out
        }
      },
      isEmpty: true,
      favoriteList: [],
      deviceId: null,
      favoriteDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        node: null,
        event: {
          saveData_out: this.getFavoritesData,
          addFolder: this.getFavoritesData
        }
      },
      favoriteData: null
    };
  },
  methods: {
    radioChange(val) {
      this.isEmpty = true;
      if (val === "collect") {
        this.getFavoritesData();
        this.CetTree_m.selectNode = null;
        this.CetTree_m_currentNode_out({});
      } else if (val === "meter" && this.CetTree_m.inputData_in.length > 0) {
        this.getTreeData();
      }
    },
    async getTreeData() {
      const { data = [] } = await customApi.queryDemandTree({
        async: false,
        loadDevice: true,
        nodeId: 0,
        nodeType: 0,
        tenantId: this.tenantId
      });
      this.CetTree_m.inputData_in = data;
      if (data?.length > 0) {
        this.CetTree_m.selectNode = data[0].children[0].children[0];
      }
    },
    async getFavoritesData() {
      const { data = [] } = await customApi.queryFavorite({
        dataType: 1
      });
      this.favoriteData = data;
      this.favoriteList = [];
      this.getChild(data, this.favoriteList);
    },
    getChild(data, list) {
      if (!data) return;
      data.forEach(item => {
        if (item.children) {
          this.getChild(item.children, list);
        }
        if (item.details) {
          const { deviceId } = JSON.parse(item.details[0].detail);
          list.push(deviceId);
        }
      });
    },
    CetTree_m_currentNode_out(val) {
      this.selectNode(val);
    },
    selectNode(val) {
      if (val.nodeType === 269619472) {
        //表计节点
        this.deviceId = val.nodeId;
        this.isEmpty = false;
      } else if (val.nodeType === 2) {
        //收藏节点
        const { deviceId } = JSON.parse(val.details[0].detail);
        this.deviceId = deviceId;
        this.isEmpty = false;
      } else {
        this.deviceId = null;
        this.isEmpty = true;
      }
    },
    onClickFavorite(data) {
      this.favoriteDialog.node = data;
      this.favoriteDialog.openTrigger_in = Date.now();
    }
  },
  created() {
    const user = api.getUser()?._user;
    if (user) {
      this.tenantId = user.tenantId;
    }
  },
  mounted() {
    this.getTreeData();
    this.getFavoritesData();
  }
};
</script>
<style lang="scss" scoped>
.radio {
  margin-bottom: 8px;
  :deep(.el-radio-button__inner) {
    width: 100%;
  }
}
.right-side-error {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  @include background_color(BG1);
  border-radius: 4px;
  .img {
    height: 315px;
  }
  .text-description {
    @include font_size(H5);
    font-weight: bold;
    text-align: center;
  }
}
.custom-tree-node {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .icon {
    visibility: hidden;
  }
  &:hover {
    .icon {
      visibility: visible;
    }
  }
}
.cet-aside :deep(.cet-content-aside-container) {
  border-radius: var(--Ra);
  padding: 0;
}
</style>
