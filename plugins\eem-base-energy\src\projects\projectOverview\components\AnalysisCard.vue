<template>
  <div :class="blockClass">
    <div class="top">
      <div class="top-left">
        <div :class="iconClass"></div>
        <el-tooltip
          :content="item.tagName || '--'"
          :visible-arrow="false"
          placement="top"
        >
          <div class="name text-ellipsis">
            {{ item.tagName || "--" }}
          </div>
        </el-tooltip>
      </div>
      <div class="top-right">
        <span class="value text-ellipsis" :title="getValue(item.value)">
          {{ getValue(item.value) }}
        </span>
        <span class="top-unit">
          {{ item.unitName ? item.unitName : "--" }}
        </span>
      </div>
    </div>
    <div class="bottom">
      <div :class="cardClass">
        <span class="label">{{ $T("同比") }}</span>
        <span v-if="item.yearOnYear >= 0" class="card-num-color1">
          <omega-icon symbolId="up" />
          <span class="per">
            {{
              item.yearOnYear
                ? (Number(item.yearOnYear) * 100).toFixed(2)
                : "--"
            }}
          </span>
          <span class="unit">%</span>
        </span>
        <span v-else class="card-num-color2">
          <omega-icon symbolId="down" />
          <span class="per">
            {{
              item.yearOnYear
                ? (-Number(item.yearOnYear) * 100).toFixed(2)
                : "--"
            }}
          </span>
          <span class="unit">%</span>
        </span>
      </div>
      <div :class="cardClass" class="ml-J1">
        <span class="label">{{ $T("环比") }}</span>
        <span v-if="item.chain >= 0" class="card-num-color1">
          <omega-icon symbolId="up" />
          <span class="per">
            {{ item.chain ? (Number(item.chain) * 100).toFixed(2) : "--" }}
          </span>
          <span class="unit">%</span>
        </span>
        <span v-else class="card-num-color2">
          <omega-icon symbolId="down" />
          <span class="per">
            {{ item.chain ? (-Number(item.chain) * 100).toFixed(2) : "--" }}
          </span>
          <span class="unit">%</span>
        </span>
      </div>
    </div>
  </div>
</template>
<script>
import omegaTheme from "@omega/theme";

export default {
  props: {
    item: {
      type: Object
    },
    index: {
      type: Number
    },
    eneryType: {
      type: Number
    }
  },
  data() {
    return {};
  },
  computed: {
    theme() {
      return omegaTheme.theme === "light" ? "light" : "dark";
    },
    blockClass() {
      return ["block", `block_${this.theme}`];
    },
    iconClass() {
      return ["icon", `icon_${this.theme}_${this.index + 1}`];
    },
    cardClass() {
      return ["card", `card_${this.theme}`];
    },
    unitInfo() {
      return this.$store.state.unitInfo;
    }
  },
  methods: {
    getValue(val) {
      let str = "--";
      if (val || val === 0) {
        str = val.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,");
      }
      return str;
    }
  }
};
</script>
<style lang="scss" scoped>
.block {
  width: 100%;
  height: 96px;
  padding: 12px 16px;
  border-radius: 12px;
  box-sizing: border-box;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  &_dark {
    background-image: url("../assets/analysis_dark.png");
  }
  &_light {
    background-image: url("../assets/analysis_light.png");
  }
}
.top {
  flex: none;
  width: 100%;
  display: flex;
  justify-content: space-between;
  .name {
    font-weight: bold;
    font-size: 14px;
    line-height: 24px;
    margin-left: 8px;
    max-width: 90px;
  }
  .value {
    font-weight: bold;
    font-size: 18px;
  }
  .top-unit {
    font-size: 14px;
    @include font_color(T2);
    margin-left: 4px;
    flex: none;
  }
  .top-right {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    margin-left: 4px;
    overflow: hidden;
  }
  .top-left {
    flex: none;
    display: flex;
  }
}
.bottom {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  .card {
    width: calc(100% - 4px);
    height: 41px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .per {
      font-weight: bold;
      margin-right: 2px;
      -webkit-background-clip: text !important;
      -webkit-text-fill-color: transparent;
    }
    .label {
      padding-left: 12px;
      font-size: 12px;
    }
    .unit {
      padding-right: 8px;
      font-size: 12px;
      @include font_color(T2);
    }
    .omega-icon {
      width: 7px;
      height: 7px;
      margin-right: 4px;
    }
  }
}
.card_dark {
  background: rgba(255, 255, 255, 0.05);
  .card-num-color1 {
    display: flex;
    align-items: center;
    .omega-icon {
      color: #ff8b8b;
    }
    .per {
      background: linear-gradient(180deg, #ffe1e1 0%, #ff8b8b 100%);
    }
  }
  .card-num-color2 {
    display: flex;
    align-items: center;
    .omega-icon {
      color: #05f974;
    }
    .per {
      background: linear-gradient(180deg, #e5fff1 0%, #78ffb6 100%);
    }
  }
}
.card_light {
  background: rgba(255, 255, 255, 0.5);
  .card-num-color1 {
    display: flex;
    align-items: center;
    .omega-icon {
      color: #f95e5a;
    }
    .per {
      background: linear-gradient(180deg, #ffa8a6 0%, #f95e5a 100%);
    }
  }
  .card-num-color2 {
    display: flex;
    align-items: center;

    .omega-icon {
      color: #29b061;
    }
    .per {
      background: linear-gradient(180deg, #58db8f 0%, #29b061 100%);
    }
  }
}
.icon {
  width: 24px;
  height: 24px;
  background-size: 100% 100%;
  &_light {
    @for $i from 1 through 8 {
      &_#{$i} {
        background-image: url(#{"../assets/light-" + $i + ".png"});
      }
    }
  }
  &_dark {
    @for $i from 1 through 8 {
      &_#{$i} {
        background-image: url(#{"../assets/dark-" + $i + ".png"});
      }
    }
  }
}
</style>
