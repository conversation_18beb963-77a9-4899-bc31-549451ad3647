import fetch from "eem-base/utils/fetch";
let version = "v1";

function postFile(url, fileObj, projectId) {
  let data = new FormData();
  data.append("multipartFile", fileObj);
  return fetch({
    url: url,
    method: "POST",
    headers: { projectId },
    data
  });
}

export function powerUseImport(projectId, id, label, fileObj) {
  let url = `/eem-service/${version}/bill/bill/import/${id}/${label}`;
  return postFile(url, fileObj, projectId);
}

export function powerBillImport(projectId, fileObj, params) {
  let url = `/eem-service/${version}/bank/custom/input/bankdata/${params.objectId}/${params.modelLabel}/${params.overwrite}`;
  return postFile(url, fileObj, projectId);
}

function downloadFile(url, projectId, data) {
  // return Axios.post(url, data, {
  //   responseType: 'arraybuffer',
  //   headers: {
  //     Authorization: store.state.token,
  //     projectId: projectId
  //   }
  // })
  return fetch({
    url: url,
    method: "POST",
    responseType: "arraybuffer",
    headers: {
      "User-ID": 1,
      projectId
    },
    data
  });
}

//产品导出+能耗导出+账单能耗
export function downloadProductExport(prjId, iptInfo, ndInfo, measureTypes) {
  let url = `/eem-service/${version}/system/data/export`;
  let params = {
    ...iptInfo,
    ...ndInfo,
    measureTypes
  };
  return downloadFile(url, prjId, params);
}

//计划产量导出
export function downloadPlanExport(prjId, iptInfo, ndInfo, measureTypes) {
  let url = `/eem-service/${version}/system/data/export/plan`;
  let params = {
    ...iptInfo,
    ...ndInfo,
    measureTypes
  };
  return downloadFile(url, prjId, params);
}

//账单能耗导出
export function downloadPowerExport(prjId, iptInfo, ndInfo, energyTypes) {
  let url = `/eem-service/${version}/bill/bill/export`;
  let params = {
    ...iptInfo,
    node: ndInfo,
    energyTypes
  };
  return downloadFile(url, prjId, params);
}

//获取产品展示数据
export function getProductData(iType, data) {
  let url = `/eembasedatamaintain/system/data/input/query/new`;
  if (iType === "2") {
    url = `/eem-service/${version}/bill/bill`;
  } else if (iType === "3") {
    url = `/eem-service/${version}/bank/custom/query/data`;
  } else if (iType === "4") {
    url = `/eembasedatamaintain/energy/plan/input/query/new`;
  }
  return fetch({
    url,
    method: "POST",
    dataType: "json",
    data
  });
}

export function saveProduct(data) {
  return fetch({
    url: `/eembasedatamaintain/system/data/input/new`,
    method: "POST",
    dataType: "json",
    headers: {},
    data
  });
}

//  计划产量新增接口的修改，按照通用迭代3.5需求处理
export function saveProductPlan(data) {
  return fetch({
    url: `/eembasedatamaintain/energy/plan/input/new`,
    method: "POST",
    dataType: "json",
    data
  });
}

export function saveBill(data) {
  return fetch({
    url: `/eem-service/${version}/bill/bill`,
    method: "PUT",
    dataType: "json",
    headers: {
      // "User-ID": 1
    },
    data
  });
}

// 批量导入录入数据
export function importMultiNodes(data, fileObj, projectId) {
  let url = `/eem-service/${version}/system/data/import/multiNodes?dataEntryType=${data.dataEntryType}`;
  return postFile(url, fileObj, projectId);
}

export function getDataEntryConfig(data) {
  return fetch({
    url: `/eembasedatamaintain/system/data/dataEntry/config`,
    method: "POST",
    dataType: "json",
    data
  });
}

// 导入产量数据(3.5迭代中跟骆海瑞联调)
export function importProductionData(fileObj) {
  let url = `/eembasedatamaintain/system/data/import/productionData`;
  let data = new FormData();
  data.append("file", fileObj);
  return fetch({
    url,
    method: "POST",
    data
  });
}

// 导入产量数据(3.5迭代中跟骆海瑞联调)
export function planImportAsync(fileObj) {
  let url = `/eembasedatamaintain/energy/plan/import/async`;
  let data = new FormData();
  data.append("file", fileObj);
  return fetch({
    url,
    method: "POST",
    data
  });
}
