/** @type {import("vue-router").RouteConfig[]} */

import main from "./main.vue";

export const appRoutes = [
  {
    path: "/",
    component: main,
    children: [
      // 用电需量分析
      {
        path: "/demandAnalysis",
        component: () => import("@/projects/demandAnalysis/index.vue")
      },
      // 进线管理
      {
        path: "/inletwireManage",
        component: () => import("@/projects/inletwireManage/index.vue")
      },
      // 需量计划
      {
        path: "/declareproposal",
        name: "declareproposal",
        component: () => import("@/projects/declareproposal/index.vue")
      }
    ]
  }
];

export default {
  // mode: "history",
  routes: appRoutes
};
