import fetch from "eem-base/utils/fetch";
const version = "v1";

// 获取分时方案
export function getTimeShareScheme(data, energyType) {
  return fetch({
    url: `/eembaseenergy/v1/timeshare-config/timeShareRelationship/node?energyType=${energyType}`,
    method: "POST",
    data: data
  });
}

// 能耗对比数据 - 自定义周期已隐藏，未联调
export function energyContrast(data) {
  return fetch({
    url: `/eem-service/${version}/energy/contrast`,
    method: "POST",
    data
  });
}

// 通用迭代v3.5中修改能耗查询中同环比接口查询

// 聚合同比环比数据 (查询中间图表的数据信息)
export function getV2EmergyConsumptionTbhb(data) {
  return fetch({
    url: `/eembaseenergy/v2/energy/consumption/tbhb`,
    method: "POST",
    data: data
  });
}

// 同期同比环比能耗数据 (查询左下侧两个图表的数据信息)
export function getV2EmergyConsumptionPeriod(data) {
  return fetch({
    url: `/eembaseenergy/v2/energy/consumption/period`,
    method: "POST",
    data: data
  });
}

// 能耗TOP数据 (查询top排名图表的数据信息)
export function getV2EmergyConsumptionTop(data) {
  return fetch({
    url: `/eembaseenergy/v2/energy/consumption/top`,
    method: "POST",
    data: data
  });
}

// 专门的折标煤查询接口 (查询中间图表数据)
export function getV2EmergyConsumptionStandard(data) {
  return fetch({
    url: `/eembaseenergy/v2/energy/consumption/standard`,
    method: "POST",
    data: data
  });
}

// 分时能耗（只有单能耗查询数据,查中间图表的数据）
export function getV2EmergyConsumptionTimeshareCycle(data) {
  return fetch({
    url: `/eembaseenergy/v2/energy/consumption/timeshare/child/cycle`,
    method: "POST",
    data: data
  });
}

// 分时时段能耗查询（只有单能耗查询数据,查下方图表的数据）
export function getV2EmergyConsumptionTimeshare(data) {
  return fetch({
    url: `/eembaseenergy/v2/energy/consumption/timeshare`,
    method: "POST",
    data: data
  });
}

// 总能耗详情图表数据查询
export function getV2EmergyConsumptionDetail(data) {
  return fetch({
    url: `/eembaseenergy/v2/energy/consumption/detail`,
    method: "POST",
    data: data
  });
}

// 节点对比
export function getV2EmergyConsumptionCompare(data) {
  return fetch({
    url: `/eembaseenergy/v2/energy/consumption/compare`,
    method: "POST",
    data: data
  });
}

// 节点对比导出 - 自定义周期已隐藏，未联调
export function contrastExportBatch(data) {
  return fetch({
    url: `/eem-service/v1/energy/contrast/export/batch`,
    method: "POST",
    data: data
  });
}

// 综合能耗批量导出 - 自定义周期已隐藏，未联调
export function energyConsumptionStandardExportBatch(data) {
  return fetch({
    url: `/eembaseenergy/v2/energy/consumption/standard/export`,
    method: "POST",
    data: data
  });
}

// 同环比批量导出
export function energyConsumptionTbhbExportBatch(data) {
  return fetch({
    url: `/eembaseenergy/v2/energy/consumption/tbhb/export/batch`,
    method: "POST",
    data
  });
}

// 分时批量导出
export function energyConsumptionTimeshareChildCycleExportBatch(data) {
  return fetch({
    url: `/eembaseenergy/v2/energy/consumption/timeshare/child/cycle/export/batch`,
    method: "POST",
    data: data
  });
}

// 能耗详情批量导出
export function energyConsumptionDetailExportBatch(data) {
  return fetch({
    url: `/eembaseenergy/v2/energy/consumption/detail/export/batch`,
    method: "POST",
    data: data
  });
}
