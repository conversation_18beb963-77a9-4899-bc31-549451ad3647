import { modules, state, mutations, actions } from "eem-base/store/index.js";
import customApi from "@/api/custom";

export default {
  modules: { ...modules },
  state: {
    ...state,
    systemCfg: {
      hideEnergyLossOverview: false,
      alarmAggregationType: null
    },
    standardEnergyType: [],
    compareNodeLimit: 4,
    exportMaxCount: 10000
  },
  mutations: {
    ...mutations,
    setSystemCfg(state, val) {
      state.systemCfg = val;
    },
    setStandardEnergyType(state, val) {
      state.standardEnergyType = val;
    },
    setCompareNodeLimit(state, val) {
      state.compareNodeLimit = val;
    },
    setExportMaxCount(state, val) {
      state.exportMaxCount = val;
    }
  },
  actions: {
    ...actions,
    async getConfig({ commit }) {
      const res = await customApi.configProperties();
      const config = res.data || {};
      const systemCfg = {
        hideEnergyLossOverview: !!config.hideOverViewStatistics,
        alarmAggregationType: config.alarmAggregationType
      };
      const standardEnergyType = config.standardEnergyType || [];
      commit("setSystemCfg", systemCfg);
      commit("setStandardEnergyType", standardEnergyType);
      commit("setCompareNodeLimit", config.compareNodeLimit);
      commit("setExportMaxCount", config.exportMaxCount);
    }
  }
};
