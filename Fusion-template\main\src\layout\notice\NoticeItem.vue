<template>
  <div class="notice-item">
    <div class="notice-item-left">
      <el-tag
        size="small"
        class="notice-item-tag"
        :style="formatterEventLevelStyle(item)"
        effect="light"
      >
        {{ formatterEventLevelName(item) }}
      </el-tag>
    </div>
    <div class="notice-item-main">
      <div class="notice-item-main-head">
        <div class="notice-item-main-content" :title="item.description">
          {{ item.description }}
        </div>
        <div class="notice-item-operate">
          <el-link
            v-if="checkIsShowGoto(item)"
            class="notice-item-operate-btn"
            type="primary"
            @click="evGotoClick(item)"
          >
            {{ $T("转到") }}
          </el-link>
          <el-link
            class="notice-item-operate-btn"
            :underline="false"
            type="primary"
            @click="evReadClick(item)"
          >
            {{ $T("已读") }}
          </el-link>
        </div>
      </div>
      <div class="notice-item-time">
        {{ formatterTime(item.time) }}
      </div>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import { EVENT_LEVEL, PLATPROJECTID } from "@/config/const.js";

import { conf } from "@omega/app";

import { appState } from "@altair/lord";
import { user } from "@omega/auth";

export default {
  name: "NoticeItem",
  props: {
    item: {
      type: Object
    }
  },
  data() {
    return {};
  },
  methods: {
    /**
     * @description: 已读
     */
    evReadClick(item) {
      this.$emit("handleRemoveItem", item);
    },

    /**
     * @description: 转到
     */
    evGotoClick(item) {
      this.$emit("handleGoToPage", item);
    },

    /**
     * @description: 是否显示转到
     */
    checkIsShowGoto(item) {
      const content = item.content ? JSON.parse(item.content) : {};
      const redirectPageUrl = content.redirectPageUrl;
      const tenantId = content.tenantId;
      if (!redirectPageUrl) return false;
      return this.checkNavmenuExit(redirectPageUrl, tenantId);
    },
    /**
     * @description: 判断导航菜单路径对应界面是否存在
     */
    checkNavmenuExit(menuItem, tenantId) {
      let exit = false;
      let navmenu = conf.getNavmenu();
      if (tenantId) {
        //非平台跳项目内时，使用项目内导航菜单，否则使用平台用户所能访问的所有导航菜单
        navmenu = appState.info.config.completeNavmenu;
        //当传递了租户id，当前用户又不是平台用户时，则不显示转到
        if (user.getUserTenantId() !== PLATPROJECTID) return false;
      }
      loop(navmenu, menuItem);
      function loop(nav, menu) {
        nav.forEach(item => {
          if (item.type === "menuItem") {
            if (item.location === menu) {
              exit = item;
            }
          } else if (item.type === "subMenu") {
            loop(item.subMenuList, menu);
          }
        });
      }
      if (!exit) return false;
      if (!tenantId && exit) return true;
      const role = user.getRole();
      exit = role?.pageNodes?.find(item => item.id === exit.permission);
      return exit;
    },

    /**
     * @description: 时间转换
     */
    formatterTime(val) {
      return moment(val).format("YYYY-MM-DD HH:mm:ss.SSS");
    },

    /**
     * @description: 事件类型转换
     */
    formatterEventLevelName(val) {
      const content = val.content ? JSON.parse(val.content) : {};
      const eventClass = content.eventClass;
      const obj = EVENT_LEVEL.find(item => item.id === eventClass) || {};
      return obj.text || "--";
    },

    /**
     * @description: 事件类型样式转换
     */
    formatterEventLevelStyle(val) {
      const content = val.content ? JSON.parse(val.content) : {};
      const eventClass = content.eventClass;
      const obj = EVENT_LEVEL.find(item => item.id === eventClass) || {};
      const color = obj.color || "";
      return {
        backgroundColor: this.lightenColor(color, 0.88),
        color: color,
        border: "none"
      };
    },
    // 背景颜色转化
    lightenColor(color, amount = 0.8) {
      const rgb = this.parseColorToRGB(color);
      if (!rgb) return color;

      const mix = rgb.map((c, i) => Math.round(c + (255 - c) * amount));
      return `#${mix.map(c => c.toString(16).padStart(2, "0")).join("")}`;
    },

    parseColorToRGB(color) {
      color = color.trim();
      if (color.startsWith("#")) {
        // 支持 #RGB 和 #RRGGBB
        const hex = color.slice(1);
        if (hex.length === 3) {
          return hex.split("").map(c => parseInt(c + c, 16));
        } else if (hex.length === 6) {
          return [hex.slice(0, 2), hex.slice(2, 4), hex.slice(4, 6)].map(c =>
            parseInt(c, 16)
          );
        }
      } else if (color.startsWith("rgb")) {
        const nums = color.match(/[\d.]+/g);
        if (nums && nums.length >= 3) {
          return nums.slice(0, 3).map(Number);
        }
      }
      return null; // 不支持的格式
    }
  }
};
</script>
<style lang="scss" scoped>
.notice-item {
  height: 42px;
  margin-right: 4px;
  margin-top: 12px;
  position: relative;
  &-left {
    position: absolute;
    width: 58px;
    height: 100%;
    top: -3px;
    left: 0;
    display: flex;
    align-items: center;
  }
  &-main {
    margin-left: 58px + 8px;
    &-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 20px;
    }
    &-content {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      height: 20px;
      line-height: 20px;
      @include font_color(T2);
      @include font_size(Aa);
    }
  }
  &-time {
    height: 20px;
    line-height: 20px;
    @include font_color(T2);
    @include font_size(Ab);
  }
  &-operate {
    &-btn {
      width: 40px;
      height: 30px;
      @include font_size(Ab);
      &:hover {
        @include background_color(BG2);
      }
      &:active {
        @include background_color(BG3);
      }
    }
  }
  &-tag {
    display: inline-block;
    vertical-align: middle;
    margin: 0 auto;
    width: 48px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    @include font_size(Ab);
  }
}
</style>
