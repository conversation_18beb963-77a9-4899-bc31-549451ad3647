<template>
  <!-- 1弹窗组件 -->
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <div class="fullheight eem-cont-c1">
      <customElSelect
        v-model="ElSelect_1.value"
        v-bind="ElSelect_1"
        v-on="ElSelect_1.event"
        v-if="false"
        :prefix_in="$T('通讯情况')"
      >
        <ElOption
          v-for="item in ElOption_1.options_in"
          :key="item[ElOption_1.key]"
          :label="item[ElOption_1.label]"
          :value="item[ElOption_1.value]"
          :disabled="item[ElOption_1.disabled]"
        ></ElOption>
      </customElSelect>
      <!-- 父子关联 -->
      <CetGiantTree
        class="switch-tree"
        v-bind="CetGiantTree_1"
        v-on="CetGiantTree_1.event"
      ></CetGiantTree>
      <div class="selected mt-J1 pt-J1">
        <span>
          {{ $T("已选择") }}
          {{ (checkedNodes && checkedNodes.length) || 0 }}
          {{ $T("个") }}
          :&nbsp;
          <el-tag
            class="mr5 mb5"
            v-for="(item, index) in checkedNodes"
            :key="index"
            @close="handleClose(item)"
            closable
          >
            {{ item.text }}
          </el-tag>
        </span>
      </div>
    </div>
    <span slot="footer">
      <span class="fl ml-J1">
        {{ `${$T("最多选中{0}个节点", this.maxExportNodeNumber)}` }}
      </span>
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
export default {
  name: "shareRate",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    // 已关联管网设备列表
    deviceList: {
      type: Array,
      default() {
        return [];
      }
    },
    // 判断是已通讯还是未通讯
    connected: {
      type: Boolean
    }
  },

  computed: {
    projectTenantId() {
      return this.$store.state.userInfo.tenantId;
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: $T("选择节点"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        width: "600px",
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // 节点树正常点击效果的选中节点集合
      allCheckedNodes: [],
      // 底部tag选中的数量或者说是去除通道的选中节点的集合
      checkedNodes: [],
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "ps", N: "ps" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "id"
            },
            key: {
              name: "text"
            }
          },
          callback: {
            beforeCheck: this.zTreeBeforeCheck
          },
          view: {}
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out, //勾选节点输出
          created_out: this.CetGiantTree_left_created_out //ztree对象，当组件无法满足需求，可直接操作ztree对象实现
        }
      },
      maxExportNodeNumber: 100,
      ElSelect_1: {
        value: true,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [
          {
            value: true,
            label: $T("已通讯")
          },
          {
            value: false,
            label: $T("未通讯")
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.ElSelect_1.value = this.connected;
      this.initTree();
      this.getConfig();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    inputData_in(val) {
      this.CetDialog_1.inputData_in = val;
    },
    connected(val) {}
  },

  methods: {
    initTree() {
      this.CetGiantTree_1.checkedNodes = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.checkedNodes = [];
      this.getTreeData();
    },
    //选中节点树节点
    CetGiantTree_1_checkedNodes_out(val) {
      this.allCheckedNodes = this._.cloneDeep(val);
      let res = val.filter(item => {
        return item.nodeType !== 269619456;
      });
      this.checkedNodes = res;
    },
    zTreeBeforeCheck(treeId, treeNode) {
      if (this.checkedNodes.length >= this.maxExportNodeNumber) {
        // 选满最大数量后进行判断是添加还是删除操作
        let flag = false;
        this.allCheckedNodes.forEach(item => {
          if (item.id == treeNode.id) flag = true;
        });
        if (flag) return true;
        this.$message.warning(
          `${$T("最多选中{0}个节点", this.maxExportNodeNumber)}`
        );
        return false;
      }
      return true;
    },
    //获取导出节点数量配置
    getConfig() {
      let queryData = {};
      customApi.getFlinkMaxNodes(queryData).then(response => {
        if (response.code === 0) {
          this.maxExportNodeNumber =
            this._.get(response, "data.maxCount", 100) || 100;
        }
      });
    },
    //获取节点树数据
    getTreeData() {
      let queryData = {
        loadDevice: true,
        nodeId: 0,
        nodeType: 0,
        async: false,
        tenantId: this.projectTenantId,
        connected: this.ElSelect_1.value
      };
      customApi.pecCoreMeterTreeWithConnection(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.showChecked(data);
          this.CetGiantTree_1.inputData_in = data;
          this.CetGiantTree_1.selectNode = this._.get(response, "data[0]");
        }
      });
    },
    // 保存
    addSupplyRelation_out() {
      var me = this;
      if (me.checkedNodes.length === 0) {
        this.$message({
          message: $T("请选择导出表计"),
          type: "warning"
        });
        return;
      }
      const nodes = me.checkedNodes.map(item => {
        return {
          id: item.nodeId,
          name: item.text
        };
      });
      const params = {
        ...this.inputData_in,
        devices: nodes
      };
      const urlStr = "/eembasedatamaintain/flink/datalog/export";
      common.downExcel(urlStr, params, function () {
        me.$message({
          type: "success",
          message: $T("导出成功!")
        });
        me.CetDialog_1.closeTrigger_in = new Date().getTime();
      });
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      this.addSupplyRelation_out();
    },
    // 点击删除tag标签
    handleClose(tag) {
      let nodes = this.treeObj.getCheckedNodes(true);
      this.checkedNodes.splice(this.checkedNodes.indexOf(tag), 1);
      this.CetGiantTree_1.checkedNodes = this._.cloneDeep(this.checkedNodes);
      if (!this.checkedNodes.length) {
        this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      }
      if (nodes.length && nodes.indexOf(tag)) {
        this.treeObj.checkNode(nodes[nodes.indexOf(tag)], false);
      }
    },
    // 获取整个的树节点
    CetGiantTree_left_created_out(val) {
      this.treeObj = val;
    },
    // 处理树节点只展示表计数据的勾选框
    showChecked(tree) {
      const arr = [269619472, 269619456];
      if (this._.isArray(tree)) {
        tree.forEach(item => {
          if (!arr.includes(item.nodeType)) {
            item.nocheck = true;
          }
          if (item.children) {
            this.showChecked(item.children);
          }
        });
      }
    },
    ElSelect_1_change_out() {
      this.initTree();
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.switch-tree {
  height: 400px;
}
.selected {
  height: 30px;
  border-top: 1px solid;
  @include border_color(B1);
  overflow: auto;
}
</style>
