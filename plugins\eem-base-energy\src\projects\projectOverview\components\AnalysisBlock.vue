<template>
  <div class="relative bottom fullheight p-J4">
    <div class="head">
      <div class="title">{{ $T("多维度用能分析") }}</div>
      <el-radio-group v-model="selectRadio" @change="radioChange" class="radio">
        <el-radio-button
          v-for="item in radioList"
          :key="item.id"
          :label="item.id"
        >
          <el-tooltip
            :content="item.name"
            :visible-arrow="false"
            placement="top"
          >
            <span class="text-ellipsis radio-item">{{ item.name }}</span>
          </el-tooltip>
        </el-radio-button>
      </el-radio-group>
    </div>
    <div class="slider">
      <div class="button-arrow" :class="{ disable: left }" @click="onLeftClick">
        <i class="el-icon-arrow-left"></i>
      </div>
      <el-carousel
        height="100%"
        indicator-position="none"
        arrow="never"
        :initial-index="initIndex"
        :interval="15000"
        ref="sideCarousel"
      >
        <el-carousel-item
          v-for="(list, index) in blockList"
          :key="index"
          class="flex-row flex"
        >
          <div v-for="(item, i) in list" :key="i" class="card">
            <AnalysisCard
              :item="item"
              :index="getNum(index, i)"
              :eneryType="eneryType"
            />
          </div>
        </el-carousel-item>
      </el-carousel>
      <div
        class="button-arrow mlJ1"
        :class="{ disable: left }"
        @click="onRightClick"
      >
        <i class="el-icon-arrow-right"></i>
      </div>
    </div>
    <chartTypeSwitch
      v-model="chartType"
      class="chart-type"
      @change="handlerChartTypeChange"
    />
    <CetChart
      class="chart"
      v-bind="analysisChart"
      :key="analysisKey"
    ></CetChart>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import common from "eem-base/utils/common";
import AnalysisCard from "./AnalysisCard.vue";
import omegaTheme from "@omega/theme";
import chartTypeSwitch from "eem-base/components/chartTypeSwitch.vue";

export default {
  components: { AnalysisCard, chartTypeSwitch },
  props: {
    node: {
      type: Object
    },
    dateObj: {
      type: Object
    },
    eneryType: {
      type: Number
    },
    rootNode_in: Object
  },
  computed: {
    unitInfo() {
      return this.$store.state.unitInfo;
    },
    left() {
      return [0, 1].includes(this.blockList?.length);
    },
    chartColor() {
      let list1 = [
        "rgba(106, 222, 154, 0.9)",
        "rgba(76, 166, 255, 0.9)",
        "rgba(255, 206, 32, 0.9)",
        "rgba(125, 217, 255, 0.9)",
        "rgba(49, 102, 239, 0.9)",
        "rgba(255, 157, 9, 0.9)",
        "rgba(185, 225, 119, 0.9)",
        "rgba(111, 190, 11, 0.9)"
      ];
      let list2 = [
        "rgba(12, 130, 248, 0.9)",
        "rgba(74, 253, 167, 0.9)",
        "rgba(255, 197, 49, 0.9)",
        "rgba(65, 224, 228, 0.9)",
        "rgba(25, 61, 255, 0.9)",
        "rgba(255, 143, 39, 0.9)",
        "rgba(164, 215, 56, 0.9)",
        "rgba(109, 153, 12, 0.9)"
      ];
      return omegaTheme.theme === "light" ? list1 : list2;
    }
  },
  data() {
    return {
      chartType: 1,
      analysisKey: 0,
      initIndex: 0,
      selectRadio: null,
      radioList: [],
      propertys: [],
      blockList: [],
      unitName: "--",
      analysisChart: {
        options: {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            },
            formatter: val => {
              let list = val || [];
              let str = list[0]?.name + "<br/>";
              list.forEach(item => {
                let i = item.dimensionNames[item.encode.y[0]];
                str +=
                  `${item.marker}${item.seriesName}: ${
                    item.value[i] == null ? "--" : item.value[i]
                  }(${this.unitName})` + "<br />";
              });
              return str;
            }
          },
          legend: {},
          grid: {
            top: 40,
            bottom: 4,
            left: 1,
            right: "0%",
            containLabel: true
          },
          xAxis: {
            type: "category",
            axisTick: {
              show: false
            }
          },
          yAxis: {
            name: "",
            nameTextStyle: {
              align: "left"
            },
            type: "value",
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed"
              }
            }
          },
          color: [],
          dataset: {
            source: []
          },
          series: []
        }
      }
    };
  },
  methods: {
    handlerChartTypeChange(val) {
      this.analysisChart.options.series.forEach(item => {
        item.type = val === 1 ? "bar" : "line";
      });
    },
    radioChange() {
      this.getAnalysisData();
    },
    async getRadioList() {
      if (!this.rootNode_in) {
        return;
      }
      const queryData = {
        rootNode: {
          id: this.rootNode_in?.id,
          modelLabel: this.rootNode_in?.modelLabel
        }
      };
      const { data = [] } = await customApi.dimensionAll(queryData);
      const list = data.reduce((list, item) => {
        if (item.modelLabel === "dimension") {
          list.push({
            id: item.id,
            name: item.name
          });
        }
        return list;
      }, []);
      this.radioList = list;
      this.selectRadio = this.radioList?.[0]?.id;
      this.getAnalysisData();
    },
    async getAnalysisData() {
      if (!this.selectRadio) return;
      const { data = [] } = await customApi.getPropertysList({
        dimensionId: this.selectRadio
      });
      this.propertys =
        data?.map(item => ({
          name: item.name,
          tag: item.id
        })) ?? [];
      this.getChartData();
    },

    getChartData: _.debounce(async function () {
      if (!this.node) return;
      let nodes = [];
      let childNodes = this.node.children || [];
      childNodes.forEach(item => {
        let obj = {
          name: item.name,
          id: item.id,
          modelLabel: item.modelLabel
        };
        nodes.push(obj);
      });

      let params = {
        aggregationCycle: this.dateObj.type,
        startTime: this.dateObj.time[0],
        endTime: this.dateObj.time[1],
        modelLabel: this.node.modelLabel,
        nodes: nodes,
        node: {
          name: this.node.name,
          id: this.node.id,
          modelLabel: this.node.modelLabel
        },
        tags: this.propertys,
        normal: true,
        energyType: this.eneryType
      };
      const { data = {} } = await customApi.getEachItemized(params);
      let list = data.item ?? [];
      //处理只有两个幻灯片时滑动方向不一致的问题
      let cusList = this.splitArrChunks(list, 4);
      if (cusList?.length === 2) {
        this.initIndex = 1;
        this.blockList = [cusList[1], ...cusList, cusList[0]];
      } else {
        this.initIndex = 0;
        this.blockList = cusList;
      }
      this.getBarData(data.node);
    }, 100),
    getBarData(data) {
      this.chartType = 1;
      let series = [];
      let source = [];
      for (let item in data) {
        let list = data[item] || [];
        this.unitName = list[0]?.unitName || "--";
        list.forEach(node => {
          let index = _.findIndex(source, ["product", node.name]);
          if (index === -1 && Number.isFinite(node.value)) {
            let s = {};
            s["product"] = node.name;
            s[`yAxis_${item}`] = common.formatNumberWithPrecision(
              node.value,
              2
            );
            source.push(s);
          } else if (index > -1 && Number.isFinite(node.value)) {
            source[index][`yAxis_${item}`] = common.formatNumberWithPrecision(
              node.value,
              2
            );
          }
        });
        series.push({
          name: list[0]?.tagName ?? "--",
          type: "bar",
          barWidth: 32,
          stack: 1,
          encode: { x: "product", y: `yAxis_${item}` }
        });
      }
      this.analysisChart.options.yAxis.name = this.unitName;
      this.analysisChart.options.color = this.chartColor;
      this.analysisChart.options.series = source.length > 0 ? series : [];
      this.analysisChart.options.dataset = { source: source };
      this.analysisKey++;
    },
    splitArrChunks(arr, chunk) {
      let result = [];
      let temp = null;
      for (let i = 0; i < arr?.length; i += chunk) {
        temp = arr.slice(i, i + chunk);
        result.push(temp);
      }
      return result;
    },
    getNum(num1, num2) {
      if (this.initIndex === 0) {
        return num1 * 4 + num2;
      }
      //处理只有两个幻灯片时滑动方向不一致的问题
      if ([0, 2].includes(num1)) {
        return 4 + num2;
      } else if ([1, 3].includes(num1)) {
        return num2;
      }
    },
    onLeftClick() {
      if (this.left) return;
      this.$refs.sideCarousel.prev();
    },
    onRightClick() {
      if (this.left) return;
      this.$refs.sideCarousel.next();
    },
    method() {
      if (this.selectRadio) {
        this.getChartData();
      } else {
        this.getAnalysisData();
      }
    }
  },
  watch: {
    node() {
      this.method();
    },
    dateObj() {
      this.method();
    },
    eneryType() {
      this.method();
    },
    rootNode_in: {
      handler() {
        this.getRadioList();
      },
      immediate: true
    }
  }
};
</script>
<style lang="scss" scoped>
.bottom {
  box-sizing: border-box;
}
.head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    font-weight: bold;
    font-size: 16px;
  }
}
.slider {
  width: 100%;
  height: 96px;
  display: flex;
  margin-top: 16px;
  .button-arrow {
    width: 16px;
    height: 100%;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 12px;
    @include background_color(BG12);
  }
  .el-carousel {
    width: calc(100% - 32px);
  }
  .card {
    width: calc(25% - 8px);
    margin-left: 8px;
  }
  .disable {
    cursor: not-allowed;
    i {
      @include font_color(T6);
    }
  }
}
.chart {
  margin-top: 8px;
  height: calc(100% - 152px);
}
.radio-item {
  display: inline-block;
  max-width: 80px;
}
.chart-type {
  position: absolute;
  right: 24px;
  top: 179px;
  z-index: 99;
}
</style>
