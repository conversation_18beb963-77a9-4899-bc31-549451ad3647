<template>
  <div class="aggregation-page">
    <div class="aggregation-container">
      <!-- 搜索和筛选区域 -->
      <div class="search-filter-container">
        <!-- 搜索框和筛选器 -->
        <div class="search-filter-left">
          <!-- 搜索框 -->
          <el-input
            v-model="searchKeyword"
            :placeholder="$T('请输入关键字')"
            prefix-icon="el-icon-search"
            class="search-input"
            @change="handleSearch"
          />
          <!-- 机组类型筛选 -->
          <CustomElSelect
            v-model="selectedType"
            :prefix_in="$T('机组类型')"
            class="filter-select"
          >
            <el-option
              v-for="option in typeOptions"
              :key="option.id"
              :label="$T(option.text)"
              :value="option.id"
            />
          </CustomElSelect>
        </div>

        <!-- 新增按钮 -->
        <div class="action-buttons">
          <el-button type="primary" @click="handleAdd">
            {{ $T("新增") }}
          </el-button>
        </div>
      </div>
      <!-- 表格区域 -->
      <div class="table-section">
        <el-table :data="tableData" height="640">
          <el-table-column
            type="index"
            :label="$T('序号')"
            width="80"
          ></el-table-column>

          <el-table-column
            prop="unitName"
            :label="$T('机组名称')"
            min-width="200"
          />

          <el-table-column
            prop="unitType"
            :label="$T('机组类型')"
            min-width="150"
          />

          <el-table-column
            prop="resourceCount"
            :label="$T('聚合资源数量')"
            min-width="120"
          />

          <el-table-column :label="$T('操作')" width="200">
            <template slot-scope="scope">
              <div class="action-buttons-cell">
                <el-button type="text" @click="handleDetail(scope.row)">
                  {{ $T("详情") }}
                </el-button>
                <el-button type="text" @click="handleEdit(scope.row)">
                  {{ $T("编辑") }}
                </el-button>
                <el-button
                  type="text"
                  class="delete-btn"
                  @click="handleDelete(scope.row)"
                >
                  {{ $T("删除") }}
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页区域 -->
      <div class="text-right">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 40, 50]"
          :page-size="pageSize"
          :total="totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handlePageSizeChange"
        />
      </div>
      <!-- 新增/编辑弹窗 -->
      <AddUnitDialog
        :show-dialog="dialogShowTrigger"
        :mode="dialogMode"
        :editData="currentEditData"
        @confirm="handleAddConfirm"
        @update="handleEditConfirm"
      />

      <!-- 详情抽屉 -->
      <DetailDrawer
        :show-detail="detailShowTrigger"
        :detail-data="currentDetailData"
        @resource-unbound="handleResourceUnbound"
      />
    </div>
  </div>
</template>

<script>
import AddUnitDialog from "./components/AddUnitDialog.vue";
import DetailDrawer from "./components/DetailDrawer.vue";
import {
  listUnits,
  createUnit,
  updateUnit,
  deleteUnit
} from "@/api/resource-aggregation";

export default {
  name: "VppResourceAggregation",
  components: {
    AddUnitDialog,
    DetailDrawer
  },
  data() {
    return {
      // 搜索和筛选
      searchKeyword: "",
      selectedType: 0,

      // 表格数据
      tableData: [],
      totalCount: 0,

      // 分页
      currentPage: 1,
      pageSize: 20,

      // 弹窗控制
      dialogShowTrigger: Date.now(),
      dialogMode: "add", // 'add' 或 'edit'
      currentEditData: {},

      // 详情抽屉
      detailShowTrigger: Date.now(),
      currentDetailData: {}
    };
  },
  computed: {
    // 机组类型选项
    typeOptions() {
      const enumerations =
        this.$store.state.enumerations["vpp_unit_type"] || [];

      // 在第一项添加"全部"选项
      const options = [{ text: "全部", id: 0 }];

      // 添加枚举选项
      if (enumerations && Array.isArray(enumerations)) {
        options.push(...enumerations);
      }
      return options;
    }
  },
  mounted() {
    this.loadUnits();
  },
  methods: {
    // 加载机组列表
    async loadUnits() {
      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        unitType: this.selectedType
      };

      // 添加搜索关键词参数
      if (this.searchKeyword.trim()) {
        params.unitName = this.searchKeyword.trim();
      }

      const response = await listUnits(params);

      if (response.code === 0 && response.data) {
        this.tableData = response.data.map(item => ({
          unitId: item.unitId,
          unitName: item.unitName,
          unitType: this.getUnitTypeLabel(item.unitType),
          typeValue: item.unitType,
          resourceCount: item.resourceCount || 0,
          resourceIds: item.resourceIds || []
        }));

        this.totalCount = response?.total || 0;
      }
    },

    // 新增
    handleAdd() {
      this.dialogMode = "add";
      this.currentEditData = {}; // 清空编辑数据
      this.dialogShowTrigger = Date.now();
    },

    // 新增确认
    async handleAddConfirm(data) {
      const requestData = {
        unitName: data.unitName,
        unitType: data.unitType,
        resourceIds: data.selectedResourceIds
      };
      const res = await createUnit(requestData);
      if (res.code === 0) {
        this.$message.success($T("新增机组成功"));
        // 重新加载列表
        this.loadUnits();
      }
    },

    // 获取机组类型标签
    getUnitTypeLabel(value) {
      const option = this.typeOptions.find(opt => opt.id === value);
      return option ? option.text : "--";
    },

    // 详情
    handleDetail(row) {
      this.currentDetailData = row;
      this.detailShowTrigger = Date.now();
    },

    // 编辑
    handleEdit(row) {
      this.currentEditData = row;
      this.dialogMode = "edit";
      this.dialogShowTrigger = Date.now();
    },

    // 编辑确认
    async handleEditConfirm(data) {
      const requestData = {
        unitName: data.unitName,
        resourceIds: data.selectedResourceIds,
        unitType: data.unitType
      };

      const res = await updateUnit(this.currentEditData.unitId, requestData);
      if (res.code === 0) {
        this.$message.success($T("编辑机组成功"));

        // 重新加载列表
        this.loadUnits();
      }
    },

    // 删除
    handleDelete(row) {
      this.$confirm(
        $T("确定要删除该机组吗？删除后与资源的绑定关系也将解除。"),
        $T("提示"),
        {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        }
      )
        .then(async () => {
          const res = await deleteUnit(row.unitId);
          if (res.code === 0) {
            this.$message.success($T("删除机组成功"));
            this.loadUnits();
          }
        })
        .catch(() => {
          this.$message.info($T("已取消删除"));
        });
    },

    // 处理搜索
    handleSearch() {
      this.currentPage = 1;
      this.loadUnits();
    },

    // 分页变化
    handlePageChange(page) {
      this.currentPage = page;
      this.loadUnits();
    },
    // 页面大小变化
    handlePageSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.loadUnits();
    },

    // 处理资源解绑事件
    handleResourceUnbound() {
      // 重新加载机组列表以更新资源数量
      this.loadUnits();
    }
  },

  watch: {
    // 监听类型筛选变化
    selectedType() {
      this.currentPage = 1;
      this.loadUnits();
    }
  }
};
</script>

<style lang="scss" scoped>
.aggregation-page {
  height: 100%;
  background-color: var(--BG1);
}
.aggregation-container {
  padding: var(--J4);

  .search-filter-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--J3);
    margin-bottom: var(--J3);

    .search-filter-left {
      display: flex;
      align-items: center;
      gap: var(--J3);
      .search-input {
        width: 240px;
      }
      .filter-select {
        width: 240px;
      }
    }
  }

  .table-section {
    background-color: var(--BG1);
    border-radius: var(--Ra);
    overflow: hidden;
    margin-bottom: var(--J1);

    .action-buttons-cell {
      .el-button--text {
        font-size: var(--Aa);
      }

      .delete-btn {
        color: var(--Sta3);
      }
    }
  }
}
</style>
