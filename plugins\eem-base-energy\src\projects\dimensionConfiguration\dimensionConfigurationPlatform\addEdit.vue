<template>
  <CetDialog
    class="dialog"
    v-bind="CetDialog_pagedialog"
    v-on="CetDialog_pagedialog.event"
  >
    <CetForm
      :data.sync="CetForm_1.data"
      v-bind="CetForm_1"
      v-on="CetForm_1.event"
    >
      <el-form-item :label="$T('标签名称')" prop="name">
        <ElInput
          v-model="CetForm_1.data.name"
          :placeholder="$T('请输入')"
        ></ElInput>
      </el-form-item>
      <el-form-item :label="$T('标签类型')" prop="tagType">
        <ElSelect v-model="CetForm_1.data.tagType" style="width: 100%">
          <ElOption
            v-for="item in ElOption_type.options_in"
            :key="item[ElOption_type.key]"
            :label="item[ElOption_type.label]"
            :value="item[ElOption_type.value]"
            :disabled="item[ElOption_type.disabled]"
          ></ElOption>
        </ElSelect>
      </el-form-item>
      <el-form-item
        :label="$T('输入标签赋值(每行一个)')"
        prop="tagNameList"
        v-if="CetForm_1.data.tagType === 2"
      >
        <ElInput
          v-model="CetForm_1.data.tagNameList"
          type="textarea"
          :placeholder="$T('请输入')"
        ></ElInput>
      </el-form-item>
    </CetForm>
    <template v-slot:footer>
      <span>
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="ml-J1"
          v-bind="CetButton_preserve"
          v-on="CetButton_preserve.event"
        ></CetButton>
      </span>
    </template>
  </CetDialog>
</template>
<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";

export default {
  name: "addTree",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  props: {
    isPublic: Boolean,
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    rootNode_in: Object
  },
  data() {
    return {
      // 1表单组件
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "140px",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name,
            common.check_space,
            {
              trigger: ["blur", "change"],
              validator: (rule, value, callback) => {
                const nameList = [
                  $T("园区"),
                  $T("楼栋"),
                  $T("楼层"),
                  $T("房间")
                ].map(i => i.toLowerCase());
                const valueName = value?.toLowerCase() ?? "";
                if (nameList.includes(valueName)) {
                  return callback(
                    new Error($T("标签名称不能为") + nameList.join("、"))
                  );
                }

                callback();
              }
            }
          ],
          tagType: [
            {
              required: true,
              message: $T("请选择标签类型"),
              trigger: ["blur", "change"]
            }
          ],
          tagNameList: [
            {
              required: true,
              message: $T("请输入标签赋值"),
              trigger: ["blur", "change"]
            },
            {
              trigger: ["blur", "change"],
              validator: (rule, value, callback) => {
                const valueList = value?.split("\n") ?? [];
                valueList.forEach(item => {
                  if (item.length > 50) {
                    return callback(
                      new Error($T("每个标签赋值的长度在 1 到 {0} 个字符", 50))
                    );
                  }
                  const regex = /^((?![`~!@$%^&*+=[\]{}|;:'"<,>.?]).)*$/;
                  if (!regex.test(item)) {
                    return callback(new Error($T("请不要输入特殊字符")));
                  }
                  const repeatedList = valueList.filter(i => i === item);
                  if (repeatedList.length > 1) {
                    return callback(new Error($T("标签赋值存在重复")));
                  }
                });

                callback();
              }
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElOption_type: {
        options_in: [
          { value: 1, label: $T("文本") },
          { value: 2, label: $T("枚举") }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      CetDialog_pagedialog: {
        showClose: true,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: "",
        top: "5vh",
        width: "480px",
        appendToBody: true,
        event: {}
      },
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.CetDialog_pagedialog.openTrigger_in = val;
      this.CetDialog_pagedialog.title = this.inputData_in.id
        ? $T("编辑标签")
        : $T("新建标签");
      this.CetForm_1.data = {
        ...this.inputData_in,
        tagNameList:
          this.inputData_in.tagVoList?.map(i => i.name)?.join("\n") ?? ""
      };
      this.CetForm_1.resetTrigger_in = new Date().getTime();
    }
  },
  methods: {
    async CetForm_1_saveData_out(val) {
      const api = this.inputData_in.id
        ? "editAttributedimensionConfig"
        : "insertAttributedimensionConfig";
      const queryData = {
        id: this.inputData_in.id,
        isPublic: this.isPublic,
        name: val.name,
        tagType: val.tagType,
        modelLabel: this.inputData_in.modelLabel,
        isDeleted: this.inputData_in.isDeleted,
        rootNode: {
          id: this.rootNode_in.id,
          modelLabel: this.rootNode_in.modelLabel
        }
      };
      const tagNameList =
        val.tagType === 2
          ? val.tagNameList.split("\n").filter(item => item?.trim()?.length > 0)
          : [];
      queryData.tagNameList = tagNameList;

      const res = await customApi[api](queryData);
      if (res?.code !== 0) return;

      this.$message.success($T("保存成功"));
      this.CetDialog_pagedialog.closeTrigger_in = +new Date();
      this.$emit("finishTrigger_out");
    },
    CetButton_preserve_statusTrigger_out() {
      this.CetForm_1.localSaveTrigger_in = +new Date();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = val;
    }
  }
};
</script>
<style lang="scss" scoped>
.dialog {
  :deep(.el-form) {
    .el-textarea__inner {
      height: 440px;
    }
  }
}
</style>
