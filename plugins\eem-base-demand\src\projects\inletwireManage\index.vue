<template>
  <div class="page">
    <el-container class="h-full flex flex-col">
      <CetAside class="cet-aside">
        <template #aside>
          <leftTree
            ref="leftTree"
            @setCurrentNode="setCurrentNode"
            @changeProject="changeProject"
          >
            <groupsBtn
              slot="footerBtn"
              :currentNode="currentNode"
              @refresh="getTree"
            />
          </leftTree>
        </template>
        <template #container>
          <div class="cet-aside-main flex flex-col">
            <div class="cet-aside-main-header text-over">
              {{ currentNode && currentNode.name }}
            </div>
            <div class="flex-auto flex flex-col">
              <div class="mb-J3">
                <CetButton
                  v-if="$checkPermission('demandaccount_update')"
                  class="fr ml-J1"
                  v-bind="{ ...CetButton_6, title: $T('新建') }"
                  v-on="CetButton_6.event"
                ></CetButton>
                <CetButton
                  class="fr"
                  v-bind="{ ...CetButton_5, title: $T('需量数据维护') }"
                  v-on="CetButton_5.event"
                ></CetButton>
              </div>
              <div class="flex-auto">
                <CetTable
                  :data.sync="CetTable_1.data"
                  :dynamicInput.sync="CetTable_1.dynamicInput"
                  v-bind="CetTable_1"
                  v-on="CetTable_1.event"
                >
                  <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
                  <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
                  <ElTableColumn
                    v-bind="ElTableColumn_accountno"
                  ></ElTableColumn>
                  <ElTableColumn
                    v-bind="ElTableColumn_relatedDevice"
                  ></ElTableColumn>
                  <ElTableColumn v-bind="ElTableColumn_handele">
                    <template
                      v-if="$checkPermission('demandaccount_update')"
                      slot-scope="scope"
                    >
                      <span
                        class="handle fl mr-J3"
                        @click="handleEdit(scope.$index, scope.row)"
                      >
                        {{ $T("编辑") }}
                      </span>
                      <span
                        class="handle delete fl"
                        @click="handleDelete(scope.$index, scope.row)"
                      >
                        {{ $T("删除") }}
                      </span>
                    </template>
                  </ElTableColumn>
                </CetTable>
              </div>
              <div class="pt-J3 text-right">
                <el-pagination
                  class="inline-block"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentPageChange"
                  :current-page.sync="currentPage"
                  :page-size.sync="pageSize"
                  :total="totalCount"
                  layout="total,sizes, prev, pager, next, jumper"
                  :pageSizes="[10, 20, 50, 100]"
                ></el-pagination>
              </div>
            </div>
          </div>
        </template>
      </CetAside>
    </el-container>
    <addInletwire
      :visibleTrigger_in="addInletwire.visibleTrigger_in"
      :closeTrigger_in="addInletwire.closeTrigger_in"
      :queryId_in="addInletwire.queryId_in"
      :inputData_in="addInletwire.inputData_in"
      :projectSelect="projectSelect"
      @finishTrigger_out="getTree"
    />
  </div>
</template>
<script>
import addInletwire from "./addInletwire.vue";
import leftTree from "../components/leftTree.vue";
import groupsBtn from "./groupsBtn.vue";
import customApi from "@/api/custom";
import { api } from "@altair/knight";
export default {
  name: "inletwiremanage",
  components: {
    addInletwire,
    groupsBtn,
    leftTree
  },
  computed: {},
  data() {
    return {
      copyTableData: [],
      currentPage: 1,
      pageSize: 20,
      totalCount: 0,
      currentNode: null,
      currentTabItme: null, // 当前选中行
      copyTreeData: null,
      CetButton_5: {
        visible_in: true,
        disable_in: false,
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_5_statusTrigger_out
        }
      },
      CetButton_6: {
        visible_in: true,
        disable_in: true,
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_6_statusTrigger_out
        }
      },
      CetTable_1: {
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        // defaultSort: null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out
        }
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        label: "序号", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "65" //绝对宽度
      },
      ElTableColumn_name: {
        //type: "",      // selection 勾选 index 序号
        prop: "name", // 支持path a[0].b
        label: $T("进线名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "200" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_accountno: {
        //type: "",      // selection 勾选 index 序号
        prop: "accountno", // 支持path a[0].b
        label: $T("户号"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "200" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_relatedDevice: {
        //type: "",      // selection 勾选 index 序号
        prop: "relatedDevice", // 支持path a[0].b
        label: $T("关联设备"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "200" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_handele: {
        width: "195px", //绝对宽度
        label: $T("操作"),
        headerAlign: "left",
        align: "left",
        fixed: "right",
        showOverflowTooltip: true
      },

      addInletwire: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      SubGroup: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      projectSelect: null
    };
  },
  watch: {},
  methods: {
    changeProject(val) {
      this.projectSelect = val;
    },
    init() {},
    //返回查询页
    //分页大小变化
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.handleCurrentPageChange(1);
    },
    //分页当前页变化
    handleCurrentPageChange(val) {
      this.currentPage = val;
      this.CetTable_1.data = this.copyTableData.slice(
        (this.currentPage - 1) * this.pageSize,
        this.currentPage * this.pageSize
      );
    },
    setCurrentNode(currentNode) {
      this.currentNode = currentNode;
      this.CetButton_6.disable_in = false;
      this.CetButton_6.visible_in = false;
      if (this.currentNode.modelLabel == "demandgroup") {
        this.CetButton_6.visible_in = true;
      }
      this.getTabData();
    },
    // 获取列表数据
    async getTabData() {
      this.CetTable_1.data = [];
      var obj = {};
      if (!this.currentNode) {
        return;
      }
      this.getChildrenDevice(this.currentNode, obj);
      if (!obj?.demandaccount) {
        return;
      }
      const response = await customApi.monitorAccountData(obj.demandaccount);
      if (response.code === 0 && response.data) {
        // 保存数据，用于分页
        this.copyTableData = this._.cloneDeep(response.data);
        // 处理表格
        this.handleCurrentPageChange(1);
        this.totalCount = response.data.length;
      }
    },
    // 获取节点下的所有进线id
    getChildrenDevice(node, obj) {
      if (!node) {
        return;
      }
      if (node.modelLabel == "demandaccount") {
        // 找到设备
        if (obj[node.modelLabel]) {
          if (obj[node.modelLabel].indexOf(node.id) == -1) {
            obj[node.modelLabel].push(node.id);
          }
        } else {
          obj[node.modelLabel] = [node.id];
        }
      } else {
        if (node.children && node.children.length > 0) {
          node.children.forEach(item => {
            this.getChildrenDevice(item, obj);
          });
        }
      }
    },
    // 跳转到需量数据维护
    CetButton_5_statusTrigger_out(val) {
      api.routerPushWithQuery({
        path: "/fusion/eembasedemand/declareproposal",
        query: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel
        }
      });
    },
    // 新建进线
    CetButton_6_statusTrigger_out(val) {
      this.addInletwire.inputData_in = {
        groupid: this.currentNode.id,
        rootnodeid: this.currentNode.fatherId,
        rootnodelabel: this.currentNode.fatherModelLabel
      };
      this.addInletwire.visibleTrigger_in = this._.cloneDeep(val);
    },
    CetTable_1_record_out(val) {
      if (val.id != -1) {
        this.CetButton_5.disable_in = false;
        this.currentTabItme = val;
      } else {
        this.CetButton_5.disable_in = true;
        this.currentTabItme = null;
      }
    },
    handleEdit(index, row) {
      this.addInletwire.inputData_in = this._.cloneDeep(row);
      this.addInletwire.visibleTrigger_in = new Date().getTime();
    },
    handleDelete(index, row) {
      var vm = this;
      vm.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: async function (action, instance, done) {
          if (action == "confirm") {
            const response = await customApi.delDemandAccount([row.id]);
            if (response.code === 0) {
              vm.$message.success($T("删除成功"));
              vm.getTree(false);
            }
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action != "confirm") {
            vm.$message({
              type: "info",
              message: $T("取消删除")
            });
          }
        }
      });
    },
    getTree(selectFirstNodeFlag) {
      this.$refs.leftTree.getTree(selectFirstNodeFlag);
    }
  },
  created: function () {
    this.init();
  },
  activated: function () {
    this.init();
  },
  deactivated() {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.handle {
  cursor: pointer;
  @include font_color(ZS);
  &.delete {
    @include font_color(Sta3);
  }
}
.cet-aside {
  :deep(.cet-content-aside-container) {
    padding: 0;
    background-color: var(--BG1);
    border-radius: var(--Ra);
    padding: var(--J3) var(--J4);
  }
  &-main {
    overflow: auto;
    padding: 0;
    height: 100%;
    &-header {
      @include margin_bottom(J3);
      padding: 0;
      @include font_size(H1);
      font-weight: 700;
      max-width: 100%;
      text-overflow: ellipsis;
    }
  }
}
.text-over {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
</style>
