import fetch from "eem-base/utils/fetch";
/**
 * 查询节点树结构
 */
export function queryTreeConfig(params) {
  return fetch({
    url: `/eembaseconfig/v1/tree-config/query`,
    method: "GET",
    params
  });
}

/**
 * 查询指定节点类型以及子级
 */
export function findTreeConfigNode(data) {
  return fetch({
    url: `/eembaseconfig/v1/tree-config/findNode`,
    method: "POST",
    data
  });
}

/**
 * 获取节点树
 */
export function getNodeTree(data) {
  return fetch({
    url: `/eembaseconfig/v1/node/tree`,
    method: "POST",
    data
  });
}

/**
 * 查询节点详情
 */
export function getNodeDetail(data) {
  return fetch({
    url: `/eembaseconfig/v1/node/detail`,
    method: "POST",
    data
  });
}

/**
 * 根据父节点查询子层级节点
 */
export function getChildNodes(data) {
  return fetch({
    url: `/eembaseconfig/v1/node/child-node/query`,
    method: "POST",
    data
  });
}

/**
 * 新增节点
 */
export function nodeBatchAdd(data) {
  return fetch({
    url: `/eembaseconfig/v1/node/batch/add`,
    method: "POST",
    data
  });
}

/**
 * 更新节点
 */
export function nodeBatchUpdate(data) {
  return fetch({
    url: `/eembaseconfig/v1/node/batch/update`,
    method: "POST",
    data
  });
}
/**
 * 删除节点
 */
export function nodeBatchDelete(data) {
  return fetch({
    url: `/eembaseconfig/v1/node/batch/delete`,
    method: "POST",
    data
  });
}

/**
 * 查询子层级所有的节点
 */
export function queryChildNodeByLabel(data) {
  return fetch({
    url: `/eembaseconfig/v1/node/child-label/query`,
    method: "POST",
    data
  });
}
