import omegaApp from "@omega/app";
import { FusionManager } from "@altair/lord";
//开发环境的配置
const develop = {
  appList: [
    // {
    //   name: "cloud-auth"
    //   // url: "//localhost:9529"
    //   // alive: false
    //   // preload: true,
    //   // exec: true
    // },
    {
      name: "virtual-power-plant",
      // url: "/fusion/fusion-list/dist/",
      url: "//localhost:9529"
      // alive: false
      // preload: true,
      // exec: true
    }
  ],
  options: {
    systemConfig: "local",
    isMultiProject: false
  }
};
//部署环境的配置
const config = {
  appList: [],
  options: {
    isMultiProject: true
  }
};

omegaApp.plugin.register(
  FusionManager,
  process.env.NODE_ENV === "development" ? develop : config
);
