/** @type {import("vue-router").RouteConfig[]} */

import main from "./main.vue";
import { api } from "@altair/knight";

const checkPermission = (key, component) => {
  const auth = api.checkPermission(key);
  if (auth) {
    return component;
  }
  return import("@/projects/noPermission/NoQueryPermission.vue");
};

export const appRoutes = [
  {
    path: "/",
    component: main,
    children: [
      // 文档在线管理
      {
        path: "/knowledge",
        name: "knowledge",
        component: () => import("@/projects/knowledge/index.vue")
      },
      // 计量网络图
      {
        path: "/metrics",
        name: "metrics",
        component: () => import("@/projects/metrics/index.vue")
      },
      // 仪表管理
      {
        path: "/instrumentManagement",
        name: "instrumentManagement",
        component: () => import("@/projects/instrumentManagement/index.vue")
      },
      // 表计录入与重算
      {
        path: "/meterEntry",
        name: "meterEntry",
        component: () => {
          return checkPermission(
            "devicerepairdatalog_browser",
            import("@/projects/meterDataEntry/index.vue")
          );
        }
      },
      // 产量数据录入
      {
        path: "/energyEntry",
        name: "energyEntry",
        component: () => import("@/projects/cloudDataEntry/index.vue")
      }
    ]
  }
];

export default {
  // mode: "history",
  routes: appRoutes
};
