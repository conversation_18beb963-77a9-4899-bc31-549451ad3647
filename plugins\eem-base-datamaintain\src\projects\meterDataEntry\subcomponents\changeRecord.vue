<template>
  <div>
    <CetDialog class="CetDialog" v-bind="CetDialog_1" v-on="CetDialog_1.event">
      <div class="eem-cont-c1">
        <div class="clearfix mb-J3">
          <div class="year-range fr ml-J1">
            <el-date-picker
              ref="dayPicker"
              v-model="dayInfo"
              type="daterange"
              v-if="tabTimeValue === 12"
              @change="timeChanged"
              :clearable="false"
              :pickerOptions="CetDatePicker_2.pickerOptions"
            ></el-date-picker>
          </div>
          <el-date-picker
            ref="seasonPicker"
            class="fr ml-J1"
            v-if="tabTimeValue === 14"
            v-model="CetDatePicker_2.val"
            v-bind="CetDatePicker_2.config"
            @change="timeChanged"
            :pickerOptions="CetDatePicker_2.pickerOptions"
          ></el-date-picker>
          <el-radio-group
            v-model="tabTimeValue"
            @change="tabRadioChange"
            class="fr ml-J1"
          >
            <el-radio-button :label="14">
              {{ $T("月") }}
            </el-radio-button>
            <el-radio-button :label="12">
              {{ $T("日") }}
            </el-radio-button>
          </el-radio-group>
        </div>
        <div class="flex-auto clearfix">
          <CetTable
            ref="table"
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
            style="height: 500px"
            :span-method="arraySpanMethod"
          >
            <el-table-column
              prop="tableIndex"
              :label="$T('序号')"
              :fixed="true"
              align="left"
              :width="language ? 102 : 62"
            ></el-table-column>
            <el-table-column
              prop="logtime"
              :fixed="true"
              align="left"
              :label="$T('时间')"
              minWidth="120"
              :formatter="timeFormat"
            ></el-table-column>
            <el-table-column
              prop="circuit"
              :fixed="true"
              align="left"
              :label="$T('回路号')"
              minWidth="100"
            ></el-table-column>
            <el-table-column
              prop="actualData"
              :fixed="true"
              align="left"
              :label="$T('实际数据')"
              minWidth="100"
              :formatter="valueFormat"
            ></el-table-column>
            <el-table-column
              prop="feedData"
              :fixed="true"
              align="left"
              :label="$T('换表数据')"
              minWidth="100"
              :formatter="valueFormat"
            ></el-table-column>
            <!-- <template v-for="(item, index) in columnArr">
              <ElTableColumn :key="index" v-bind="item" align="left">
                <template slot-scope="scope">
                  {{ valueFormat2(scope.row[item.prop]) }}
                </template>
              </ElTableColumn>
            </template> -->
            <ElTableColumn
              :label="$T('操作')"
              width="80"
              headerAlign="left"
              align="left"
              fixed="right"
            >
              <template slot-scope="scope">
                <span @click="handleDelete(scope)" class="delete">
                  {{ $T("删除") }}
                </span>
              </template>
            </ElTableColumn>
          </CetTable>
          <div class="text-right mt-J0" style="height: 32px">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="totalCount"
            ></el-pagination>
          </div>
        </div>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom";
import { themeMap } from "cet-chart";
import omegaTheme from "@omega/theme";
import moment from "moment";

export default {
  name: "changeRecord",
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },
  data(vm) {
    return {
      // 设置组件唯一识别字段弹窗组件
      CetDialog_1: {
        title: $T("换表记录"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        width: "960px",
        showClose: true,
        // 遮盖层操作,将自身置于最外层展示
        "append-to-body": true
      },
      // confirm组件
      CetButton_confirm: {
        visible_in: false,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      // cancel组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // 按照天数选择信息
      dayInfo: [],
      tabTimeValue: 12,
      CetDatePicker_2: {
        disable_in: false,
        val: [],
        config: {
          valueFormat: "timestamp",
          type: "monthrange",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          clearable: false,
          style: {
            width: "300px"
          }
        },
        pickerOptions: {
          // 限制预约时间
          disabledDate(time) {
            let timeStr = moment(new Date()).startOf("day").valueOf();
            return time.getTime() > timeStr;
          }
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "", //查询按钮触发  trigger  ,或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          //   record_out: this.CetTable_1_record_out
        }
      },
      // 分页标签
      currentPage: 1,
      pageSize: 100,
      totalCount: 0,
      // 表头数据
      columnArr: [],
      // 数据查询
      params: {
        deviceid: null,
        dataid: null,
        startTime: null,
        endTime: null,
        status: 30,
        meterType: 2,
        aggregationcycle: 12,
        page: {
          index: 0,
          limit: 100
        }
      },
      // 表格测试数据
      tableData: [],
      spanArr1: [1],
      spanArr2: [1]
    };
  },
  watch: {
    openTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = this._.cloneDeep(val);
      this.timeInit();
      this.getTableData();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    inputData_in(val) {}
  },
  methods: {
    CetButton_confirm_statusTrigger_out() {},
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    },
    timeInit() {
      this.currentPage = 1;
      this.params.page.index = 0;
      this.tabTimeValue = 12;
      this.dayInfo = [
        this.$moment().startOf("day").valueOf(),
        this.$moment().endOf("day").valueOf()
      ];
      this.CetDatePicker_2.val = [
        this.$moment().startOf("month").valueOf(),
        this.$moment().endOf("month").valueOf()
      ];
    },
    getParams() {
      this.params.deviceid = this.inputData_in.deviceid;
      this.params.dataid = this.inputData_in.dataid;
      this.params.status = this.inputData_in.status;
      this.params.meterType = this.inputData_in.meterType;
      this.params.aggregationcycle = this.tabTimeValue;
      switch (this.tabTimeValue) {
        case 12:
          this.params.startTime = this.$moment(this.dayInfo[0])
            .startOf("day")
            .valueOf();
          this.params.endTime =
            this.$moment(this.dayInfo[1]).endOf("day").valueOf() + 1;
          break;
        case 14:
          this.params.startTime = this.$moment(this.CetDatePicker_2.val[0])
            .startOf("month")
            .valueOf();
          this.params.endTime =
            this.$moment(this.CetDatePicker_2.val[1]).endOf("month").valueOf() +
            1;
          break;
        default:
          break;
      }
    },
    // 获取表格数据
    getTableData() {
      this.getParams();
      let param = this.params;
      customApi.getFlinkDatalogData(param).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          let result = [];
          data.forEach((item, index) => {
            item.logicalValues.forEach(i => {
              result.push({
                ...item,
                circuit: $T("回路{0}", i.logicalid),
                feedData: i.value === null ? null : i.value.toFixed(2),
                tableIndex: index + 1,
                selectId: i.logicalid,
                actualData:
                  i.datalogValue === null ? null : i.datalogValue.toFixed(2)
              });
            });
          });
          this.totalCount = response.total;
          this.CetTable_1.data = this._.cloneDeep(result);
          this.$nextTick(() => {
            this.$refs.table.$refs.cetTable.doLayout();
          });
          this.mergeTableRows();
        }
      });
    },
    // 时间控件选择时间触发
    timeChanged() {
      if (!this.judgeTimeSection()) {
        return;
      }
      this.currentPage = 1;
      this.params.page.index = 0;
      this.getTableData();
    },
    // 对时间控制选择的时间进行判断是否合规
    judgeTimeSection() {
      let flag = true;
      switch (this.tabTimeValue) {
        case 12:
          if (
            this.$moment(this.dayInfo[0]).add(365, "day").valueOf() <
            this.$moment(this.dayInfo[1]).valueOf()
          ) {
            this.$message({
              type: "warning",
              message: $T("起止时间不能相差超过365天")
            });

            this.dayInfo[0] = this.$moment(this.dayInfo[1])
              .subtract(365, "day")
              .valueOf();
          }
          break;
        case 14:
          if (
            this.$moment(this.CetDatePicker_2.val[0])
              .add(11, "month")
              .startOf("month")
              .valueOf() <
            this.$moment(this.CetDatePicker_2.val[1]).startOf("month").valueOf()
          ) {
            this.$message({
              type: "warning",
              message: $T("起止时间不能相差超过12个月")
            });
            this.CetDatePicker_2.val[0] = this.$moment(
              this.CetDatePicker_2.val[1]
            )
              .startOf("month")
              .subtract(11, "month")
              .valueOf();
          }
          break;
        default:
          break;
      }
      return flag;
    },
    // 查询时段数据切换
    tabRadioChange(val) {
      // this.ElSelect_3.value = val;
      this.tabTimeValue = val;
      if (val === 12) {
        this.CetDatePicker_2.val = [
          this.$moment().startOf("month").valueOf(),
          this.$moment().endOf("month").valueOf()
        ];
      } else if (val === 14) {
        this.dayInfo = [
          this.$moment().startOf("day").valueOf(),
          this.$moment().endOf("day").valueOf()
        ];
      }
      this.currentPage = 1;
      this.params.page.index = 0;
      this.getTableData();
    },
    // 分段操作
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.params.page.index = 0;
      this.params.page.limit = val;
      this.getTableData();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.params.page.index = (val - 1) * this.pageSize;
      this.getTableData();
    },
    // 时间值校验
    timeFormat(row, column, cellValue, index) {
      let timeString = "YYYY-MM-DD HH:mm:ss";
      return this.$moment(cellValue).format(timeString);
    },
    valueFormat(row, column, cellValue, index) {
      return cellValue != null ? cellValue : "--";
    },
    // 合并表格数据操作
    mergeTableRows() {
      let count = 0,
        count2 = 0;
      let spanArr1 = [1];
      let spanArr2 = [1];
      this.tableData = this._.cloneDeep(this.CetTable_1.data);
      let length = this.tableData ? this.tableData.length : 0;
      for (let i = 1; i < length; i++) {
        while (
          this.tableData[i] &&
          this.tableData[i].tableIndex === this.tableData[i - 1].tableIndex
        ) {
          spanArr1[count] += 1;
          spanArr1.push(0);
          if (this.tableData[i].logtime === this.tableData[i - 1].logtime) {
            spanArr2[count2] += 1;
            spanArr2.push(0);
          } else {
            count2 = i;
            spanArr2.push(1);
          }
          i++;
        }
        count2 = i;
        spanArr2.push(1);
        count = i;
        spanArr1.push(1);
      }
      this.spanArr1 = spanArr1;
      this.spanArr2 = spanArr2;
      this.$nextTick(() => {
        this.$refs.table.$refs.cetTable.doLayout();
      });
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const rowI = this.spanArr1[rowIndex];
        const colJ = rowI > 0 ? 1 : 0;
        return {
          rowspan: rowI,
          colspan: colJ
        };
      }
      if (columnIndex === 1) {
        const rowI = this.spanArr2[rowIndex];
        const colJ = rowI > 0 ? 1 : 0;
        return {
          rowspan: rowI,
          colspan: colJ
        };
      }
    },
    handleDelete(data) {
      this.$confirm($T("确定要删除所选项吗？"), $T("删除确认"), {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消")
      }).then(() => {
        let row = data.row;
        let result = [];
        if (row.logicalValues.length) {
          row.logicalValues.forEach(item => {
            if (item.logicalid === row.selectId) {
              result.push(item);
            }
          });
        }
        let param = this._.cloneDeep(row);
        param.logicalValues = result;
        delete param.circuit;
        delete param.feedData;
        delete param.tableIndex;
        delete param.selectId;
        customApi.getFlinkDatalogDelete(param).then(response => {
          if (response.code === 0) {
            this.$message({
              message: $T("删除成功"),
              type: "success"
            });
            this.$emit("delete_sucessful");
            this.getTableData();
          } else {
            this.$message({
              message: $T("删除失败"),
              type: "error"
            });
          }
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.delete {
  @include font_color(Sta3);
  cursor: pointer;
}
</style>
