<template>
  <el-dialog
    :title="$T('选取地址')"
    width="50%"
    :visible.sync="visible"
    :before-close="handleClose"
    class="map-point-dialog"
    append-to-body
    destroy-on-close
  >
    <div class="map-container">
      <div class="coordinates-display">
        {{ $T("经度") }}：{{ currentLng }} {{ $T("纬度") }}：{{ currentLat }}
      </div>
      <!-- 搜索框 -->
      <div class="search-container">
        <el-input
          ref="searchInput"
          v-model="searchKeyword"
          type="text"
          :placeholder="$T('请输入地址或地点名称')"
          class="search-input"
          :loading="searchLoading"
          @keyup.enter="searchLocation"
          @input="onSearchInput"
          clearable
        ></el-input>
      </div>
      <div id="baiduMap" class="baidu-map" ref="mapContainer"></div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{ $T("取消") }}</el-button>
      <el-button
        type="primary"
        @click="confirmSelection"
        :disabled="!selectedPoint"
      >
        {{ $T("确定") }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "MapPointDialog",
  props: {
    value: {
      type: Boolean,
      default: false
    },
    defaultPoint: {
      type: Object,
      default: () => ({
        lng: 116.404,
        lat: 39.915
      })
    }
  },
  data() {
    return {
      visible: this.value,
      map: null,
      marker: null,
      selectedPoint: null,
      geolocation: null,
      searchKeyword: "",
      currentLng: "26.598194",
      currentLat: "106.70741",
      localSearch: null,
      autocomplete: null,
      mapContainer: null,
      searchLoading: false,
      searchTimer: null
    };
  },
  watch: {
    value(newVal) {
      this.visible = newVal;
      if (newVal) {
        this.$nextTick(() => {
          this.initBaiduMap();
        });
      }
    },
    visible(newVal) {
      this.$emit("input", newVal);
    }
  },
  methods: {
    //对子应用使用地图时对象错误进行修正的代理
    proxyMap() {
      return new Promise(resolve => {
        if (!window.__POWERED_BY_WUJIE__) resolve();
        window.__WUJIE_RAW_WINDOW__.parent["$BAIDU$"] = {};
        const proxyBmap = new Proxy(
          window.__WUJIE_RAW_WINDOW__.parent["$BAIDU$"],
          {
            get: function (target, key) {
              return window.__WUJIE_RAW_WINDOW__["$BAIDU$"][key];
            },
            set: function (target, key, val) {
              target[key] = val;
            }
          }
        );
        window.__WUJIE_RAW_WINDOW__.parent["$BAIDU$"] = proxyBmap;
        resolve();
      });
    },
    loadBaiduMapResource() {
      return new Promise(resolve => {
        const script = document.createElement("script");
        script.type = "text/javascript";
        script.async = true;
        // script.src =
        //   "https://api.map.baidu.com/api?v=3.0&ak=s8bJljiNjbUnFeafh3EDIf9Crb9cjZUb";
        script.src =
          "https://api.map.baidu.com/getscript?v=3.0&ak=s8bJljiNjbUnFeafh3EDIf9Crb9cjZUb&services=&t=20240515114120";
        document.body.appendChild(script);

        script.onload = () => {
          resolve(); // 加载完成后 resolve
        };
      });
    },
    /**
     * 初始化百度地图
     */
    async initBaiduMap() {
      this.proxyMap();
      // 创建地图实例
      if (!window.BMap) return;

      this.map = new window.BMap.Map("baiduMap");

      // 设置中心点和缩放级别
      const point = new window.BMap.Point(
        this.defaultPoint.lng,
        this.defaultPoint.lat
      );
      this.map.centerAndZoom(point, 15);

      // 启用鼠标滚轮缩放
      this.map.enableScrollWheelZoom(true);

      // 添加点击事件
      this.map.addEventListener("click", this.onMapClick);

      // 如果有默认点，添加标记
      if (this.defaultPoint.lng && this.defaultPoint.lat) {
        this.addMarker(this.defaultPoint);
      } else {
        // 更新经纬度显示为地图中心点
        this.updateCoordinatesDisplay({
          lng: this.defaultPoint.lng,
          lat: this.defaultPoint.lat
        });
      }

      // 初始化自动完成功能
      this.$nextTick(() => {
        this.initAutoComplete();
      });
    },

    /**
     * 地图点击事件处理
     */
    onMapClick(e) {
      const point = {
        lng: e.point.lng,
        lat: e.point.lat
      };

      this.addMarker(point);
      this.getAddressByPoint(point);
      this.updateCoordinatesDisplay(point);
    },

    /**
     * 添加标记点
     */
    addMarker(point) {
      // 清除之前的标记
      if (this.marker) {
        this.map.removeOverlay(this.marker);
      }

      // 创建新标记
      const baiduPoint = new window.BMap.Point(point.lng, point.lat);
      this.marker = new window.BMap.Marker(baiduPoint);
      this.map.addOverlay(this.marker);

      // 设置选中点
      this.selectedPoint = { ...point };

      // 更新经纬度显示
      this.updateCoordinatesDisplay(point);

      // 居中显示
      this.map.panTo(baiduPoint);
    },

    /**
     * 根据坐标获取地址
     */
    getAddressByPoint(point) {
      if (!window.BMap) return;

      const geoc = new window.BMap.Geocoder();
      const baiduPoint = new window.BMap.Point(point.lng, point.lat);

      geoc.getLocation(baiduPoint, result => {
        if (result && this.selectedPoint) {
          this.selectedPoint.address = result.address;
        }
      });
    },

    /**
     * 更新经纬度显示
     */
    updateCoordinatesDisplay(point) {
      this.currentLng = point.lng;
      this.currentLat = point.lat;
    },

    /**
     * 初始化自动完成功能
     */
    initAutoComplete() {
      if (!window.BMap || !this.map || !this.$refs.searchInput) {
        return;
      }

      // 获取搜索框的DOM元素
      const inputElement = this.$refs.searchInput.$el.querySelector("input");

      if (!inputElement) {
        console.warn("搜索框DOM元素获取失败");
        return;
      }

      // 创建自动完成实例
      this.autocomplete = new window.BMap.Autocomplete({
        input: inputElement,
        location: this.map,
        onSearchComplete: results => {
          // 修复在微前端环境下搜索结果位置不正确的问题
          if (window.__POWERED_BY_WUJIE__) {
            const rect = inputElement.getBoundingClientRect();
            const bodyRect = document
              .querySelector("body")
              .getBoundingClientRect();
            const main = document.querySelectorAll(".tangram-suggestion-main");
            const nowSearch = main[main.length - 1];
            if (nowSearch) {
              nowSearch.style.left = rect.left - bodyRect.left + "px";
              nowSearch.style.top = rect.top - bodyRect.top + 30 + "px";
            }
          }
        }
      });

      // 监听选择事件
      this.autocomplete.addEventListener("onconfirm", e => {
        const _value = e.item.value;
        const searchValue =
          _value.province +
          _value.city +
          _value.district +
          _value.street +
          _value.business;

        // 执行搜索并定位
        this.setPlace(searchValue);
      });
    },

    /**
     * 设置地点并标记
     */
    setPlace(searchValue) {
      if (!window.BMap || !this.map) {
        return;
      }

      // 清除之前的标记
      if (this.marker) {
        this.map.removeOverlay(this.marker);
      }

      // 创建本地搜索实例
      const localSearch = new window.BMap.LocalSearch(this.map, {
        onSearchComplete: () => {
          if (localSearch.getStatus() === window.BMAP_STATUS_SUCCESS) {
            const poi = localSearch.getResults().getPoi(0);
            if (poi && poi.point) {
              const point = {
                lng: poi.point.lng,
                lat: poi.point.lat
              };

              // 添加标记并更新显示
              this.addMarker(point);
              this.getAddressByPoint(point);
            } else {
              this.$message.warning(this.$T("未找到相关地点"));
            }
          } else {
            this.$message.error(this.$T("搜索失败，请重试"));
          }
        }
      });

      // 执行搜索
      localSearch.search(searchValue);
    },

    /**
     * 搜索输入处理（防抖）
     */
    onSearchInput() {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 设置防抖延迟
      this.searchTimer = setTimeout(() => {
        if (this.searchKeyword.trim()) {
          this.searchLocation();
        }
      }, 800);
    },

    /**
     * 搜索地点
     */
    searchLocation() {
      if (!this.searchKeyword.trim()) {
        this.$message.warning(this.$T("请输入搜索关键词"));
        return;
      }

      if (!window.BMap || !this.map) {
        this.$message.error(this.$T("地图未初始化"));
        return;
      }

      // 设置搜索状态
      this.searchLoading = true;

      // 创建搜索实例
      if (!this.localSearch) {
        this.localSearch = new window.BMap.LocalSearch(this.map, {
          onSearchComplete: results => {
            this.searchLoading = false;

            if (this.localSearch.getStatus() === window.BMAP_STATUS_SUCCESS) {
              const poi = results.getPoi(0);
              if (poi && poi.point) {
                const point = {
                  lng: poi.point.lng,
                  lat: poi.point.lat
                };

                this.addMarker(point);
                this.getAddressByPoint(point);
                this.updateCoordinatesDisplay(point);
              } else {
                this.$message.warning(this.$T("未找到相关地点"));
              }
            } else {
              this.$message.error(this.$T("搜索失败，请重试"));
            }
          }
        });
      }

      // 执行搜索
      this.localSearch.search(this.searchKeyword.trim());
    },

    /**
     * 确认选择
     */
    confirmSelection() {
      if (!this.selectedPoint) {
        this.$message.warning(this.$T("请先选择一个位置"));
        return;
      }

      this.$emit("confirm", this.selectedPoint);
      this.handleClose();
    },

    /**
     * 关闭弹窗
     */
    handleClose() {
      this.visible = false;
      this.selectedPoint = null;
      this.searchKeyword = "";
      this.searchLoading = false;

      // 清理定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
        this.searchTimer = null;
      }

      // 清理地图资源
      if (this.map) {
        this.map.removeEventListener("click", this.onMapClick);
        if (this.marker) {
          this.map.removeOverlay(this.marker);
        }
        this.map = null;
        this.marker = null;
      }

      // 清理搜索实例
      this.localSearch = null;
      this.autocomplete = null;
    }
  },

  mounted() {
    // 预加载百度地图资源
    this.loadBaiduMapResource();
  },

  beforeDestroy() {
    this.handleClose();
  }
};
</script>

<style lang="scss" scoped>
.map-point-dialog {
  .map-container {
    position: relative;
    height: 560px;

    .coordinates-display {
      text-align: center;
      margin-bottom: var(--J2);
    }

    .search-container {
      position: absolute;
      top: 80px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1000;

      .search-input {
        width: 300px;
      }
    }

    .baidu-map {
      width: 100%;
      height: 95%;
    }
  }

  .dialog-footer {
    text-align: right;
  }
}
// 全局样式，确保百度地图搜索建议列表正确显示
:global(.tangram-suggestion-main) {
  z-index: 9999 !important;
}
</style>
