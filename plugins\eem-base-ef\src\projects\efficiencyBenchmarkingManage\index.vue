<template>
  <div class="fullheight bg-BG1 p-J4 rounded-Ra box-border">
    <div class="fullheight flex-col flex">
      <div class="flex-row flex items-center mb-J3">
        <div class="flex-auto flex-row flex items-center">
          <ElInput
            size="small"
            class="mr-J3"
            v-model="ElInput_1.value"
            v-bind="ElInput_1"
            v-on="ElInput_1.event"
          ></ElInput>
          <customElSelect
            :prefix_in="$T('所属项目')"
            v-model="ElSelect_2.value"
            v-bind="ElSelect_2"
            v-on="ElSelect_2.event"
            v-show="ElOption_2.options_in?.length > 1"
            class="mr-J3"
          >
            <ElOption
              v-for="item in ElOption_2.options_in"
              :key="item[ElOption_2.key]"
              :label="item[ElOption_2.label]"
              :value="item[ElOption_2.value]"
              :disabled="item[ElOption_2.disabled]"
            ></ElOption>
          </customElSelect>
          <customElSelect
            class="mr-J3"
            :prefix_in="$T('能源类型')"
            v-model="ElSelect_1.value"
            v-bind="ElSelect_1"
            v-on="ElSelect_1.event"
          >
            <ElOption
              v-for="item in ElOption_1.options_in"
              :key="item[ElOption_1.key]"
              :label="item[ElOption_1.label]"
              :value="item[ElOption_1.value]"
              :disabled="item[ElOption_1.disabled]"
            ></ElOption>
          </customElSelect>
          <customElSelect
            :prefix_in="$T('指标属性')"
            v-model="ElSelect_unitType.value"
            v-bind="ElSelect_unitType"
            v-on="ElSelect_unitType.event"
          >
            <ElOption
              v-for="item in ElOption_unitType.options_in"
              :key="item[ElOption_unitType.key]"
              :label="item[ElOption_unitType.label]"
              :value="item[ElOption_unitType.value]"
              :disabled="item[ElOption_unitType.disabled]"
            ></ElOption>
          </customElSelect>
        </div>
        <CetButton
          class="ml-J3"
          v-permission="'efset_delete'"
          v-bind="CetButton_deleteAll"
          v-on="CetButton_deleteAll.event"
        ></CetButton>
        <CetButton
          class="ml-J3"
          v-bind="CetButton_batchRelevance"
          v-on="CetButton_batchRelevance.event"
        ></CetButton>
        <CetButton
          v-permission="'efset_add'"
          class="ml-J3"
          v-bind="CetButton_add"
          v-on="CetButton_add.event"
        ></CetButton>
      </div>
      <CetTable
        :data.sync="CetTable_1.data"
        :dynamicInput.sync="CetTable_1.dynamicInput"
        v-bind="CetTable_1"
        v-on="CetTable_1.event"
        class="flex-auto text-right"
      >
        <el-table-column type="expand" width="50px">
          <template slot-scope="props">
            <CetTable
              :data.sync="props.row.children"
              :dynamicInput.sync="CetTable_2.dynamicInput"
              v-bind="CetTable_2"
              v-on="CetTable_2.event"
              class="expandTable"
            >
              <ElTableColumn
                v-for="(item, index) in ElTableColumnArr2"
                v-bind="item"
                :key="index"
              ></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_handele"
                :width="enLanguage ? 250 : 150"
                fixed="right"
              >
                <template slot-scope="scope">
                  <span
                    v-permission="'efset_benchmarkset_upsert'"
                    class="handel fl mr-J3"
                    @click="editBenchmarkset(scope.row, props.row)"
                  >
                    {{ $T("编辑对标") }}
                  </span>
                  <span
                    v-permission="'efset_benchmarkset_delete'"
                    class="delete fl"
                    @click="deleteBenchmarkset(scope.row, props.row)"
                  >
                    {{ $T("删除") }}
                  </span>
                </template>
              </ElTableColumn>
            </CetTable>
          </template>
        </el-table-column>
        <ElTableColumn v-bind="ElTableColumn_selection"></ElTableColumn>
        <ElTableColumn
          v-for="(item, index) in newElTableColumnArr"
          v-bind="item"
          :key="index"
        ></ElTableColumn>
        <ElTableColumn v-bind="ElTableColumn_handele" min-width="200px">
          <template slot-scope="scope">
            <span
              class="handel fl mr-J3"
              v-permission="'efset_relate_node'"
              @click="addRelevance(scope.row)"
            >
              {{ $T("关联节点") }}
            </span>
            <span
              v-permission="'efset_edit'"
              class="handel fl mr-J3"
              @click="editEnergyefficiencyset(scope.row)"
            >
              {{ $T("编辑指标") }}
            </span>
            <span
              class="handel fl mr-J3"
              @click="addBenchmarkset(scope.row)"
              v-permission="'efset_benchmarkset_upsert'"
            >
              {{ $T("新建对标") }}
            </span>
          </template>
        </ElTableColumn>
      </CetTable>
    </div>
    <EditIndexes
      :visibleTrigger_in="EditIndexes.visibleTrigger_in"
      :closeTrigger_in="EditIndexes.closeTrigger_in"
      :queryId_in="EditIndexes.queryId_in"
      :inputData_in="EditIndexes.inputData_in"
      @saveData_out="EditIndexes_saveData_out"
      :defaultRootNodeId="ElSelect_2.value"
    />
    <BenchmarkingManage
      :visibleTrigger_in="BenchmarkingManage.visibleTrigger_in"
      :closeTrigger_in="BenchmarkingManage.closeTrigger_in"
      :queryId_in="BenchmarkingManage.queryId_in"
      :inputData_in="BenchmarkingManage.inputData_in"
      :energyefficiencyset_in="energyefficiencyset"
      @saveData_out="BenchmarkingManage_saveData_out"
    />
    <EditEnergyefficiencyIndexes
      :visibleTrigger_in="EditEnergyefficiencyIndexes.visibleTrigger_in"
      :closeTrigger_in="EditEnergyefficiencyIndexes.closeTrigger_in"
      :queryId_in="EditEnergyefficiencyIndexes.queryId_in"
      :inputData_in="EditEnergyefficiencyIndexes.inputData_in"
      :energyefficiencyset_in="energyefficiencyset"
      @saveData_out="EditEnergyefficiencyIndexes_saveData_out"
    />
    <Relevance
      :visibleTrigger_in="Relevance.visibleTrigger_in"
      :closeTrigger_in="Relevance.closeTrigger_in"
      :inputData_in="Relevance.inputData_in"
    />
  </div>
</template>
<script>
import commonApi from "@/api/custom.js";
import EditIndexes from "./dialog/EditIndexes.vue";
import BenchmarkingManage from "./dialog/BenchmarkingManage.vue";
import EditEnergyefficiencyIndexes from "./dialog/EditEnergyefficiencyIndexes.vue";
import Relevance from "./dialog/Relevance";
import omegaI18n from "@omega/i18n";
const isEN = omegaI18n.locale === "en";
export default {
  name: "EnergyEfficiencyIndexes",
  components: {
    EditIndexes,
    BenchmarkingManage,
    EditEnergyefficiencyIndexes,
    Relevance
  },

  computed: {
    projectEnergyArr() {
      return this.ElOption_1.options_in || [];
    },
    enLanguage() {
      return omegaI18n.locale === "en";
    },
    rootNode() {
      const id = this.ElSelect_2.value;
      const list = this.ElOption_2.options_in;
      return list.find(item => item.id === id);
    }
  },

  data(vm) {
    return {
      benchmarkset: [],
      energyefficiencyset: {},
      energyefficiencyRow: {}, // 暂存当前选中的指标，用于修改对标时更新当前行内table
      ElTableColumnArr: [
        {
          prop: "name",
          label: $T("指标名称"),
          minWidth: "120px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            if (cellValue || cellValue === 0) {
              return cellValue;
            } else {
              return "--";
            }
          }
        },
        {
          prop: "energyTypeName",
          label: $T("能源类型"),
          minWidth: "120px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            if (cellValue || cellValue === 0) {
              return cellValue;
            } else {
              return "--";
            }
          }
        },
        {
          prop: "symbol",
          label: $T("单位"),
          minWidth: "120px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            if (cellValue || cellValue === 0) {
              return cellValue;
            } else {
              return "--";
            }
          }
        },
        {
          prop: "unittype",
          label: $T("指标属性"),
          minWidth: isEN ? "160px" : "120px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: (row, column, cellValue) => {
            var obj =
              this.$store.state.enumerations.energyefficiencyunittype.find(
                item => item.id === cellValue
              );
            return obj ? obj.text : "--";
          }
        },
        {
          prop: "aggregationcycle",
          label: $T("统计周期"),
          minWidth: isEN ? "170px" : "140px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            var obj = [
              {
                id: 7,
                text: $T("小时")
              },
              {
                id: 12,
                text: $T("日")
              },
              {
                id: 13,
                text: $T("周")
              },
              {
                id: 14,
                text: $T("月")
              },
              {
                id: 17,
                text: $T("年")
              }
            ];
            let str = "";
            cellValue.forEach(item => {
              str = str + obj.find(key => key.id === item)?.text + "、";
            });
            str = str.slice(0, -1);
            return str ? str : "--";
          }
        }
      ],
      newElTableColumnArr: [{}],
      ElTableColumnArr2: [
        {
          prop: "name",
          label: $T("对标名称"),
          minWidth: "200px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            if (cellValue || cellValue === 0) {
              return cellValue;
            } else {
              return "--";
            }
          }
        },
        {
          prop: "indicatortype",
          label: $T("指标类型"),
          minWidth: "150px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            var obj = vm.$store.state.enumerations.indicatortype.find(
              item => item.id === cellValue
            );
            return obj ? obj.text : "--";
          }
        },
        {
          prop: "limittype",
          label: $T("越限类型"),
          minWidth: "150px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            var obj = vm.$store.state.enumerations.indicatorlimittype.find(
              item => item.id === cellValue
            );
            return obj ? obj.text : "--";
          }
        },
        {
          prop: "limitvalue",
          label: $T("越限值"),
          minWidth: "150px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            if (cellValue || cellValue === 0) {
              return cellValue;
            } else {
              return "--";
            }
          }
        },
        {
          prop: "aggregationcycle",
          label: $T("能效周期"),
          minWidth: "150px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            var obj = [
              {
                id: 7,
                text: $T("小时")
              },
              {
                id: 12,
                text: $T("日")
              },
              {
                id: 13,
                text: $T("周")
              },
              {
                id: 14,
                text: $T("月")
              },
              {
                id: 17,
                text: $T("年")
              }
            ];
            let str = "";
            cellValue.forEach(item => {
              str = str + obj.find(key => key.id === item)?.text + "、";
            });
            str = str.slice(0, -1);
            return str ? str : "--";
          }
        }
      ],
      BenchmarkingManage: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        treeData_in: null
      },
      EditEnergyefficiencyIndexes: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        treeData_in: null
      },
      Relevance: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      EditIndexes: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        treeData_in: null,
        clickProjectNode_in: null
      },
      ElInput_1: {
        value: "",
        "suffix-icon": "el-icon-search",
        placeholder: $T("请输入内容"),
        style: {
          width: "200px"
        },
        event: {
          change: this.ElInput_1_change_out
        }
      },
      ElSelect_unitType: {
        value: "",
        style: {
          width: isEN ? "230px" : "200px"
        },
        event: {
          change: this.ElSelect_unitType_change_out
        }
      },
      ElOption_unitType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_1: {
        value: null,
        style: {
          width: isEN ? "300px" : "200px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("新增指标"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      ElTableColumn_selection: {
        type: "selection",
        width: "50px",
        headerAlign: "left",
        align: "left"
      },
      ElTableColumn_handele: {
        label: $T("操作"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "getEfSet",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [
            { name: "rootNode_in", operator: "EQ", prop: "rootNode" },
            { name: "energyType_in", operator: "EQ", prop: "energyType" },
            { name: "keyword_in", operator: "LIKE", prop: "keyword" },
            { name: "unitType_in", operator: "EQ", prop: "unitType" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: $T("合计")
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {
          rootNode_in: null,
          energyType_in: null,
          keyword_in: "",
          unitType_in: null
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {},
        exportFileName: "",
        event: {
          "selection-change": this.CetTable_1_selectionChange,
          "expand-change": this.CetTable_1_expandChange,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      CetTable_2: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: $T("合计")
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      CetButton_deleteAll: {
        visible_in: true,
        disable_in: true,
        title: $T("批量删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_deleteAll_statusTrigger_out
        }
      },
      CetButton_batchRelevance: {
        visible_in: true,
        disable_in: true,
        title: $T("批量关联节点"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_batchRelevance_statusTrigger_out
        }
      },
      ElSelect_2: {
        value: 0,
        style: {},
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  watch: {
    "ElSelect_unitType.value": {
      handler(newVal) {
        this.changeHeader(newVal);
      },
      deep: true
    }
  },

  methods: {
    CetTable_1_selectionChange(val) {
      this.CetTable_1.selectionAll = val;
      this.CetButton_deleteAll.disable_in = !val?.length;
      this.CetButton_batchRelevance.disable_in = !val?.length;
    },
    async CetTable_1_expandChange(row, expanded, flag) {
      let efSetIds = [];
      row.mergeDisplayAdds.forEach(item => {
        efSetIds.push(item.efSetId);
      });
      // 打开才更新
      if (!flag && expanded.indexOf(row) === -1) {
        return;
      }
      const response = await commonApi.getBenchMarkSet(efSetIds);
      if (response.code !== 0) {
        return;
      }
      const data = response.data || [];
      this.handleCycle(data);
      this.$set(row, "children", data);
    },
    CetButton_deleteAll_statusTrigger_out() {
      var _this = this;
      let efSetIds = [];
      this.CetTable_1.selectionAll.forEach(item => {
        item.mergeDisplayAdds.forEach(key => {
          efSetIds.push(key.efSetId);
        });
      });
      this.$confirm($T("是否删除此指标?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          commonApi.deleteEnergyefficiencyset(efSetIds).then(response => {
            if (response.code === 0) {
              _this.$message({
                type: "success",
                message: $T("删除成功")
              });
              _this.getEfSet();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已取消删除")
          });
        });
    },
    CetButton_batchRelevance_statusTrigger_out() {
      this.Relevance.inputData_in = this._.cloneDeep(
        this.CetTable_1.selectionAll
      );
      this.Relevance.visibleTrigger_in = Date.now();
    },
    // 单条增加关联
    addRelevance(row) {
      this.Relevance.inputData_in = [row];
      this.Relevance.visibleTrigger_in = Date.now();
    },
    // 对标
    addBenchmarkset(row) {
      this.energyefficiencyRow = row;
      this.energyefficiencyset = this._.cloneDeep(row);
      this.BenchmarkingManage.inputData_in = {};
      this.BenchmarkingManage.visibleTrigger_in = Date.now();
    },
    editBenchmarkset(row, energyefficiencyset) {
      this.energyefficiencyRow = energyefficiencyset;
      this.energyefficiencyset = this._.cloneDeep(energyefficiencyset);
      this.BenchmarkingManage.inputData_in = this._.cloneDeep(row);
      this.BenchmarkingManage.visibleTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    deleteBenchmarkset(row, parent) {
      var _this = this;
      let benchMarkIds = [];
      row.mergeDisplayAdds.forEach(item => {
        benchMarkIds.push(item.benchMarkId);
      });
      this.$confirm($T("是否删除此对标?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          commonApi.deleteBenchmarkset(benchMarkIds).then(response => {
            if (response.code === 0) {
              _this.$message({
                type: "success",
                message: $T("删除成功")
              });
              _this.CetTable_1_expandChange(parent, [], true);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已取消删除")
          });
        });
    },
    // 编辑能效指标
    editEnergyefficiencyset(val) {
      this.energyefficiencyset = this._.cloneDeep(val);
      this.EditEnergyefficiencyIndexes.visibleTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    EditIndexes_saveData_out() {
      this.getEfSet();
    },
    BenchmarkingManage_saveData_out() {
      // 更新当前行
      this.CetTable_1_expandChange(this.energyefficiencyRow, [], true);
    },
    EditEnergyefficiencyIndexes_saveData_out() {
      this.getEfSet();
    },
    CetButton_add_statusTrigger_out(val) {
      this.EditIndexes.inputData_in = {};
      this.EditIndexes.visibleTrigger_in = this._.cloneDeep(val);
    },
    ElSelect_1_change_out() {
      this.getEfSet();
    },
    ElInput_1_change_out() {
      this.getEfSet();
    }, // 指标类型改变触发
    ElSelect_unitType_change_out() {
      this.getEfSet();
    },
    //获取能效指标列表
    getEfSet() {
      this.CetTable_1.dynamicInput.rootNode_in = this.rootNode;
      this.CetTable_1.dynamicInput.energyType_in = this.ElSelect_1.value;
      this.CetTable_1.dynamicInput.keyword_in = this.ElInput_1.value;
      this.CetTable_1.dynamicInput.unitType_in = this.ElSelect_unitType.value;
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    async getProjectEnergy() {
      this.ElOption_1.options_in = [];
      const res = await commonApi.getProjectEnergy();
      if (res.code !== 0) {
        return;
      }
      const selectData = res.data.map(item => {
        return {
          id: item.energytype,
          text: item.name
        };
      });
      this.ElOption_1.options_in = selectData;
      this.ElSelect_1.value = selectData[0].id;
      this.ElSelect_1_change_out(this.ElSelect_1.value);
    },
    // 获取指标类型
    grtEnergyefficiencyunittype() {
      const energyefficiencyunittypeArr =
        this.$store.state.enumerations.energyefficiencyunittype;
      const data = energyefficiencyunittypeArr.filter(item => {
        return item.id <= 4 || item.id === 7;
      });
      this.ElOption_unitType.options_in = data;
      this.ElSelect_unitType.value = data[0]?.id;
    },
    changeHeader(val) {
      var that = this;
      let array = this._.cloneDeep(this.ElTableColumnArr);
      if (val === 1) {
        array.push({
          prop: "productTypeName",
          label: $T("产品"),
          minWidth: "120px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true
        });
      } else if (val === 4) {
        array.push({
          prop: "coef",
          label: $T("系数"),
          minWidth: "120px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            if (cellValue || cellValue === 0) {
              return cellValue;
            } else {
              return "--";
            }
          }
        });
      } else if (val === 7) {
        array.push({
          prop: "prodenergytype",
          label: $T("产出载能工质"),
          minWidth: "120px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            var obj = that.projectEnergyArr.find(item => item.id === cellValue);
            return obj ? obj.text : "--";
          }
        });
      }
      array.push(
        ...[
          {
            prop: "dimensionName",
            label: $T("维度"),
            minWidth: "120px",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (row, column, cellValue) {
              if (cellValue || cellValue === 0) {
                return cellValue;
              } else {
                return "--";
              }
            }
          },
          {
            prop: "tagName",
            label: $T("标签"),
            minWidth: "120px",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (row, column, cellValue) {
              if (cellValue || cellValue === 0) {
                return cellValue;
              } else {
                return "--";
              }
            }
          }
        ]
      );
      this.newElTableColumnArr = this._.cloneDeep(array);
    },
    handleCycle(val) {
      if (!val?.length) {
        return;
      }
      val.forEach(item => {
        let res = [];
        if (item.mergeDisplayAdds && item.mergeDisplayAdds.length) {
          item.mergeDisplayAdds.forEach(key => {
            res.push(key.cycle);
          });
          if (item.mergeDisplayAdds.length === 1) {
            item.name = item.name + item.mergeDisplayAdds[0].cycleName;
          }
        }
        item.aggregationcycle = res;
      });
    },
    CetTable_1_outputData_out(val) {
      this.handleCycle(val);
      this.CetTable_1.data = this._.cloneDeep(val);
    },
    async getRootNode() {
      this.ElOption_2.options_in = [];
      const res = await commonApi.rootNode();
      const data = res.data || [];
      this.ElOption_2.options_in = data;
      this.ElSelect_2.value = data?.[0]?.id;
    },
    ElSelect_2_change_out() {
      this.getEfSet();
    }
  },
  async mounted() {
    await this.getProjectEnergy();
    await this.getRootNode();
    this.grtEnergyefficiencyunittype();
    this.changeHeader(this.ElSelect_unitType.value);
    this.getEfSet();
  }
};
</script>
<style lang="scss" scoped>
.handel {
  cursor: pointer;
  @include font_color(ZS);
}
.delete {
  cursor: pointer;
  @include font_color(Sta3);
}

.expandTable {
  :deep(.el-table__body-wrapper) {
    height: auto !important;
  }
}
:deep(.el-table .el-table__expanded-cell) {
  padding: var(--J3) var(--J4);
}
</style>
