const CompressionWebpackPlugin = require("compression-webpack-plugin");
const omegaCliDevserverHandler = require("@omega/cli-devserver");
const package = require("./package.json");
module.exports = omegaCliDevserverHandler({
  productionSourceMap: false,
  transpileDependencies: ["@omega"],
  outputDir: package.name + "-" + package.version,
  publicPath: "./",
  css: {
    loaderOptions: {
      scss: {
        additionalData: `@import "@/resources/var.scss";`
      },
      css: {
        url: {
          filter(url, resourcePath) {
            // 根路径静态文件特殊处理
            if (url.startsWith("/")) {
              return false;
            }
            return true;
          }
        }
      },
      postcss: {
        postcssOptions: {
          plugins: [
            require("postcss-import"),
            require("tailwindcss/nesting"),
            require("tailwindcss")
          ]
        }
      }
    }
  },
  configureWebpack: {
    plugins: [
      // 开发环境下开启缓存
      ...(process.env.NODE_ENV === "development"
        ? // ? [new HardSourceWebpackPlugin()]
          []
        : [new CompressionWebpackPlugin()])
    ]
  },
  devServer: {
    port: 9407,
    open: false,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Headers": "*",
      "Access-Control-Allow-Methods": "*"
    },
    // writeToDisk: true,
    proxy: {
      "^/eem-base": {
        target: "http://10.12.137.66:8093/",
        changeOrigin: true,
        pathRewrite: {}
      },
      "^/eembasedemand": {
        target: "http://10.12.137.66:9458/",
        // target: "http://10.12.137.93:38093/",
        changeOrigin: true,
        pathRewrite: {}
      },
      "^/model": {
        target: "http://10.12.137.66:8085",
        changeOrigin: true,
        pathRewrite: {
          "^/model": "/model"
        }
      }
    }
  },
  chainWebpack(config) {
    const path = require("path");

    function resolve(dir) {
      return path.join(__dirname, dir);
    }
    /* svgicon支持 */
    config.module
      .rule("svg")
      .exclude.add(resolve("../../base/icons"))
      .add(resolve("src/icons"))
      .end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("../../base/icons"))
      .add(resolve("src/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "[name]"
      })
      .end();
  }
});
