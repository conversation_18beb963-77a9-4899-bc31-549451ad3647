<template>
  <div class="flex flex-col treeBox">
    <customElSelect
      :prefix_in="$T('所属项目')"
      v-model="ElSelect_project.value"
      v-bind="ElSelect_project"
      v-on="ElSelect_project.event"
      class="mb-J3"
    >
      <ElOption
        v-for="item in ElOption_project.options_in"
        :key="item[ElOption_project.key]"
        :label="item[ElOption_project.label]"
        :value="item[ElOption_project.value]"
        :disabled="item[ElOption_project.disabled]"
      ></ElOption>
    </customElSelect>
    <customElSelect
      v-model="ElSelect_treeType.value"
      v-bind="ElSelect_treeType"
      v-on="ElSelect_treeType.event"
      :prefix_in="$T('节点树类型')"
      v-show="['1', '4'].includes(currentTab)"
      v-if="multidimensional"
      class="mb-J3"
    >
      <ElOption
        v-for="item in ElOption_treeType.options_in"
        :key="item[ElOption_treeType.key]"
        :label="item[ElOption_treeType.label]"
        :value="item[ElOption_treeType.value]"
        :disabled="item[ElOption_treeType.disabled]"
      ></ElOption>
    </customElSelect>
    <CetGiantTree
      class="flex-auto CetGiantTree"
      v-bind="CetGiantTree_1"
      v-on="CetGiantTree_1.event"
    ></CetGiantTree>
  </div>
</template>

<script>
//项目级逻辑组件
import customApi from "@/api/custom";
import TREE_PARAMS from "@/store/treeParams.js";
export default {
  props: {
    refreshTrigger: {
      type: Number
    },
    currentTab: [String, Number],
    queryTimeParams: Object,
    dimTreeRefreshTrigger: Number
  },

  computed: {
    dimTreeConfigId() {
      if (!["1", "4"].includes(this.currentTab)) return -1;
      return this.ElSelect_treeType.value;
    },
    multidimensional() {
      return this.$store.state.multidimensional;
    },
    rootNode() {
      const id = this.ElSelect_project.value;
      const list = this.ElOption_project.options_in;
      return list.find(item => item.id === id);
    }
  },

  watch: {
    async refreshTrigger() {
      this.CetGiantTree_1.inputData_in = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      await this.getRootNode();
      this.getTreeData1();
    },
    currentTab(val, oldVal) {
      // 在实际产量、计划产量中切换，不重新查节点树数据
      if (["1", "4"].includes(oldVal) && ["1", "4"].includes(val)) return;

      if (!["1", "4"].includes(val)) {
        if (this.ElSelect_treeType.value === -1) return;
        this.getTreeData1();
      }
      this.ElSelect_treeType_change_out(this.ElSelect_treeType.value);
    },
    dimTreeConfigId(val) {
      this.$emit("dimTreeConfigId_out", val);
    },
    dimTreeRefreshTrigger() {
      this.dimTreeRefresh();
    }
  },
  data() {
    const setNodeClasses = (treeId, treeNode) => {
      return treeNode.childSelectState == 2
        ? { add: ["halfSelectedNode"] }
        : { remove: ["halfSelectedNode"] };
    };
    return {
      // treeType组件
      ElSelect_treeType: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_treeType_change_out
        }
      },
      // treeType组件
      ElOption_treeType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          // 按照要求进行zTree的节点权限置灰操作
          view: {
            nodeClasses: setNodeClasses
          }
        },
        event: {
          currentNode_out: this.CetTree_1_currentNode_out //选中单行输出
        }
      },
      currentNode: null,
      ElSelect_project: {
        value: 0,
        event: {
          change: this.changeProject
        }
      },
      ElOption_project: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  methods: {
    async getRootNode() {
      this.ElOption_project.options_in = [];
      const res = await customApi.rootNode();
      const data = res.data || [];
      this.ElOption_project.options_in = data;
      this.ElSelect_project.value = data?.[0]?.id;

      this.queryTreeType();
    },
    changeProject() {
      this.queryTreeType();
    },
    // 获取节点树类型下拉框
    async queryTreeType() {
      const queryData = {
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        },
        status: true
      };
      const res = await customApi.getAttributeDimensionTreeNodeConfig(
        queryData
      );
      this.ElOption_treeType.options_in = (res?.data || []).map(i => ({
        id: i.id,
        name: i.name
      }));
      const flag = this.ElOption_treeType.options_in.find(i => i.id === -1);
      this.ElSelect_treeType.value = flag
        ? -1
        : this.ElOption_treeType.options_in?.[0]?.id ?? null;
    },
    ElSelect_treeType_change_out(val) {
      if (this._.isNil(val)) {
        this.CetGiantTree_1.inputData_in = [];
        return;
      }
      this.CetGiantTree_1.inputData_in = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      val === -1 ? this.getTreeData1() : this.getTreeData2();
    },
    // 获取管理层级节点树数据
    getTreeData1() {
      let queryData = {
        nodeTreeGroupId: 1,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        }
      };

      customApi.getNodeTreeSimple(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_1.inputData_in = data;
          // 选中第一个有数据childSelectState = 1的节点并展开节点
          const obj = this._.find(this.dataTransform(data), [
            "childSelectState",
            1
          ]);
          this.CetGiantTree_1.selectNode = obj;
          this.$emit("treeData_out", this._.cloneDeep(data));
        }
      });
    },
    // 获取维度节点树数据
    async getTreeData2() {
      const data = await this.getDimTreeData();
      this.CetGiantTree_1.inputData_in = data;
      // 选中第一个有数据childSelectState = 1的节点并展开节点
      const obj = this._.find(this.dataTransform(data), [
        "childSelectState",
        1
      ]);
      this.CetGiantTree_1.selectNode = obj;
      this.$emit("treeData_out", this._.cloneDeep(data));
    },
    async getDimTreeData() {
      const queryData = {
        dimTreeConfigId: this.ElSelect_treeType.value,
        rootNode: {
          id: this.rootNode?.id,
          modelLabel: this.rootNode?.modelLabel
        },
        aggregationCycle: this.queryTimeParams?.aggregationCycle,
        startTime: this.queryTimeParams?.startTime,
        endTime: this.queryTimeParams?.endTime
      };
      const res = await customApi.getAttributeDimensionTreeNodetree(queryData);
      return res?.data ?? [];
    },
    // 获取第一个有权限的节点
    dataTransform(array) {
      const cloneData = this._.cloneDeep(array);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },
    CetTree_1_currentNode_out(val) {
      this.currentNode = val;
      this.$emit("currentNode_out", val);
    },
    async dimTreeRefresh() {
      this.CetGiantTree_1.inputData_in = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      const data = await this.getDimTreeData();
      this.CetGiantTree_1.inputData_in = data;
      const nodeList = this.dataTransform(data);
      let obj;
      obj = this._.find(
        nodeList,
        ["id", this.currentNode?.id],
        ["modelLabel", this.currentNode?.modelLabel]
      );
      if (!obj) {
        // 选中第一个有数据childSelectState = 1的节点并展开节点
        obj = this._.find(nodeList, ["childSelectState", 1]);
      }
      this.CetGiantTree_1.selectNode = obj;

      if (!obj) {
        this.currentNode = null;
        this.$emit("currentNode_out", null);
      }
      this.$emit("treeData_out", this._.cloneDeep(data));
    }
  }
};
</script>

<style lang="scss" scoped>
.treeBox {
  height: 100%;
  // 进行zTree中的节点文本权限置灰
  :deep(.halfSelectedNode .node_name) {
    @include font_color(T6);
  }
}
</style>
