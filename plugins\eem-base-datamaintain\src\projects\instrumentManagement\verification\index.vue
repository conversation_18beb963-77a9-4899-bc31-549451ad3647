<template>
  <div class="ledger">
    <TopSerach
      :instrumentList="instrumentList"
      :queryTime="clearConditionSelect"
      :queryCondition="queryCondition"
      @resetSandingTable="resetSandingTable"
      @handelSearchParams="handelSearchParams"
      :metertypeOptions="metertypeOptions"
      class="ledger-top"
    >
      <CetButton
        class="ml-J3"
        v-bind="CetButton_advancedQuery"
        v-on="CetButton_advancedQuery.event"
      ></CetButton>
      <el-dropdown
        @command="handleCommand"
        @click="handleAddDropdown"
        split-button
        type="primary"
        class="ml-J3"
      >
        {{ $T("新建") }}
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="import">
            {{ $T("批量导入") }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown class="ml-J3" @command="handleCommandMore">
        <span class="cursor-pointer el-dropdown-link fcZS">
          {{ $T("展开") }}
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="1">{{ $T("导出") }}</el-dropdown-item>
          <el-dropdown-item
            command="2"
            :class="{
              delete: this.selectedIds && this.selectedIds.length
            }"
            :disabled="
              this.selectedIds && this.selectedIds.length ? false : true
            "
          >
            {{ $T("批量删除") }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </TopSerach>
    <div class="ledger-table mt-J3">
      <sandingBoxTable
        ref="sandTable"
        :actionTitle_in="actionTitle"
        v-bind="sandingBoxTable"
        v-on="sandingBoxTable.event"
        :queryCondition_in="queryCondition"
        :monitorOptions="monitorOptions"
        @selectionAll="selectionAll"
        @query="handlerQuery"
        @queryPlannedverificationcycle="queryPlannedverificationcycle"
        :simpleList="simpleList"
      />
    </div>
    <Add
      :actionTitle_in="actionTitle"
      v-bind="add"
      v-on="add.event"
      :simpleList="simpleList"
    />
    <AdvancedQuery v-bind="advancedQuery" v-on="advancedQuery.event" />
    <TableDetails v-bind="tableDetails" :template_in="this.deviceTemplate" />
    <UploadDialog v-bind="uploadDialog" v-on="uploadDialog.event" />
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import common from "eem-base/utils/common.js";
import { TopSerach, getInstrumentData, TableDetails } from "../components";
import SandingBoxTable from "../components/sandingBoxTable.vue";
import Add from "../components/add.vue";
import AdvancedQuery from "../components/advancedQuery.vue";
import UploadDialog from "eem-base/components/uploadDialog";
import { getTreeParams, TREE_TYPE } from "@/utils/analysisServiceConfig.js";
export default {
  components: {
    TopSerach,
    SandingBoxTable,
    AdvancedQuery,
    TableDetails,
    Add,
    UploadDialog
  },
  computed: {
    token() {
      return this.$store.state.token;
    },

    projectId() {
      return this.$store.state.projectId;
    }
  },
  watch: {},

  data() {
    return {
      instrumentList: [],
      CetButton_advancedQuery: {
        visible_in: true,
        disable_in: false,
        title: $T("高级查询"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_advancedQuery_statusTrigger_out
        }
      },
      // 选中删除的行
      selectedIds: [],
      queryCondition: {},
      actionTitle: 2,
      monitorOptions: null,
      sandingBoxTable: {
        resetTrigger_in: Date.now(),
        tableResetTrigger_in: Date.now(),
        event: {
          columnArr_out: this.sandingBoxTable_columnArr_out,
          tableDetail_out: this.sandingBoxTable_tableDetail_out,
          tableEdit_out: this.sandingBoxTable_tableEdit_out
        }
      },
      advancedQuery: {
        visibleTrigger_in: Date.now(),
        closeTrigger_in: Date.now(),
        template_in: [],
        queryCondition_in: null,
        monitorOptions: null,
        event: {
          queryCondition_out: this.CetButton_query_queryCondition_out
        }
      },
      tableDetails: {
        openTrigger_in: Date.now(),
        inputData_in: null,
        event: {}
      },
      add: {
        visibleTrigger_in: Date.now(),
        closeTrigger_in: Date.now(),
        template_in: [],
        editData_in: null,
        event: {
          upData_out: this.add_updata_out,
          upTableData_out: this.add_upTableData_out
        }
      },
      uploadDialog: {
        openTrigger_in: Date.now(),
        closeTrigger_in: Date.now(),
        extensionNameList_in: [".xlsx"],
        hideDownload: false,
        dialogTitle: $T("导入"),
        event: {
          download: this.uploadDialog_download,
          uploadFile: this.uploadDialog_uploadFile
        }
      },
      deviceTemplate: [],
      metertypeOptions: [],
      simpleList: [],
      clearConditionSelect: Date.now()
    };
  },
  methods: {
    // 表头输出
    sandingBoxTable_columnArr_out(val) {
      this.advancedQuery.template_in = val;
      this.add.template_in = val;
      if (!val || !val.length) {
        this.$message.warning(
          $T("获取不到设备台账模板，请在模板管理中导入预制字段！")
        );
      }
    },
    async getOverdueCheckData() {
      const res = await customApi.overdueCheck(this.projectId);
      if (res.code !== 0) return;
      this.instrumentList = getInstrumentData(res.data);
    },
    selectionAll(val) {
      this.selectedIds = val;
    },
    handlerQuery(name, value) {
      this.queryCondition[name] = value;
      this.sandingBoxTable.resetTrigger_in = Date.now();
    },
    sandingBoxTable_tableDetail_out(row, list) {
      this.deviceTemplate = list;
      this.tableDetails.inputData_in = this._.cloneDeep(row);
      this.tableDetails.openTrigger_in = Date.now();
    },
    sandingBoxTable_tableEdit_out(row) {
      this.add.editData_in = this._.cloneDeep(row);
      this.add.visibleTrigger_in = Date.now();
    },
    handleAddDropdown() {
      this.add.editData_in = null;
      this.add.visibleTrigger_in = Date.now();
    },
    CetButton_advancedQuery_statusTrigger_out(val) {
      this.advancedQuery.queryCondition_in = this.queryCondition;
      this.advancedQuery.visibleTrigger_in = this._.cloneDeep(val);
    },
    handleCommand(e) {
      if (e === "import") {
        this.uploadDialog.openTrigger_in = Date.now();
      }
    },
    add_updata_out(val) {
      this.sandingBoxTable.resetTrigger_in = Date.now();
    },
    add_upTableData_out(val) {
      this.sandingBoxTable.tableResetTrigger_in = Date.now();
      this.getOverdueCheckData();
    },

    uploadDialog_download(val) {
      const url = "/eem-service/v1/dashboard/record/template/" + this.projectId;
      common.downExcelGET(url, {}, this.token);
    },
    // 导入检查
    async uploadDialog_uploadFile(val) {
      const formData = new FormData();
      const checkFormData = new FormData();
      formData.append("file", val.file);
      checkFormData.append("xlsFile", val.file);
      const res = await customApi.importVerificationRecord(
        formData,
        this.projectId
      );
      if (res.code !== 0) return;
      this.$message({
        type: "success",
        message: $T("导入成功")
      });
      this.uploadDialog.closeTrigger_in = Date.now();
      this.sandingBoxTable.resetTrigger_in = Date.now();
    },
    handleCommandMore(command) {
      switch (command) {
        case "1":
          this.CetButton_export_statusTrigger_out();
          break;
        case "2":
          this.CetButton_batchDelete_statusTrigger_out();
          break;
        default:
          return;
      }
    },
    // 导出
    CetButton_export_statusTrigger_out(val) {
      var expressions = [];
      let queryCondition = this.queryCondition;
      Object.keys(queryCondition).forEach(key => {
        if (queryCondition[key] || queryCondition[key] === false) {
          var obj = this.add.template_in.find(i => i.name === key);
          if (!obj) {
            return;
          }
          var expressionsItem = {};
          expressionsItem.limit = queryCondition[key];
          expressionsItem.prop = key;
          if (obj.datatype === "string") {
            expressionsItem.operator = "LIKE";
            expressions.push(expressionsItem);
          } else if (
            ["int4", "int8", "float", "boolean"].includes(obj.datatype)
          ) {
            expressionsItem.operator = "EQ";
            expressions.push(expressionsItem);
          } else if (
            obj.datatype === "enum" ||
            (obj.enumerationvalue && obj.enumerationvalue.length)
          ) {
            expressionsItem.operator = "EQ";
            expressions.push(expressionsItem);
          } else if (obj.datatype === "date") {
            expressions.push({
              limit: queryCondition[key][0],
              prop: key,
              operator: "GE"
            });
            expressions.push({
              limit:
                this.$moment(queryCondition[key][1]).endOf("date").valueOf() +
                1,
              prop: key,
              operator: "LT"
            });
          }
        }
      });
      common.downExcel(`/eem-service/v1/dashboard/record/export`, {
        expressions: expressions,
        projectid: this.projectId
      });
    },
    // 批量删除
    CetButton_batchDelete_statusTrigger_out() {
      this.$confirm($T("是否删除此台账?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          customApi.deleteRecord(this.selectedIds).then(res => {
            if (res.code === 0) {
              this.$message({
                type: "success",
                message: $T("删除成功")
              });
              this.sandingBoxTable.tableResetTrigger_in = Date.now();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已取消删除")
          });
        });
    },
    // 模板管理
    templateAdmin_updata_out(val) {
      this.sandingBoxTable.resetTrigger_in = Date.now();
    },
    CetButton_query_queryCondition_out(val) {
      this.queryCondition = this._.cloneDeep(val);
      this.sandingBoxTable.resetTrigger_in = Date.now();
      this.clearConditionSelect = Date.now();
    },
    resetSandingTable(val) {
      const condition = _.cloneDeep(this.queryCondition);
      Object.keys(this.queryCondition).forEach(key => {
        condition[key] = undefined;
      });
      this.queryCondition = condition;
      this.sandingBoxTable.resetTrigger_in = Date.now();
    },
    handelSearchParams(params) {
      this.queryCondition = this._.cloneDeep(params);
      this.sandingBoxTable.resetTrigger_in = Date.now();
    },
    async getTypesOfMeasuringInstruments() {
      const res = await customApi.typesOfMeasuringInstruments(this.projectId);
      if (res.code !== 0) return;
      this.metertypeOptions = res.data;
    },
    async getNodeSimpleList() {
      let params = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: getTreeParams(TREE_TYPE.NETWORK),
        treeReturnEnable: true,
        filterNoAuthEndNode: true
      };
      const res = await customApi.getNodeList(params);
      if (res.code !== 0) return;
      this.simpleList = res.data;
    },
    queryPlannedverificationcycle(params) {
      this.queryCondition = params;
      this.sandingBoxTable.resetTrigger_in = Date.now();
    }
  },
  mounted() {
    this.sandingBoxTable.resetTrigger_in = Date.now();
    this.instrumentList = getInstrumentData();
    this.getOverdueCheckData();
    this.getTypesOfMeasuringInstruments();
    this.getNodeSimpleList();
  }
};
</script>

<style lang="scss" scoped>
.ledger {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  padding: 24px;
  .ledger-top {
    height: 96px;
  }
  .ledger-table {
    height: calc(100% - 112px);
    .page {
      height: 100% !important;
    }
  }
  .delete {
    cursor: pointer;
    @include font_color(Sta3);
  }
}
</style>
