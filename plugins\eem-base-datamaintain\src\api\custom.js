//能管基础
import commonApi from "eem-base/api";
// 文档在线管理
import * as knowledgeFunc from "./knowledgeFunc.js";
//计量网络图
import * as measureTopologyChart from "./measureTopologyChart";
// 仪表台账管理
import * as sandingBok from "./sandingBok.js";
//仪表管理
import * as instrumentManagement from "./instrumentManagement.js";
// 表计录入
import * as meterDataEntry from "./meterDataEntry.js";
// 产量数据录入
import * as energyEntry from "./energyEntry.js";
import * as dataImport from "./dataImport";
import * as config from "./config.js";

export default {
  ...commonApi,
  ...knowledgeFunc,
  ...measureTopologyChart,
  ...sandingBok,
  ...instrumentManagement,
  ...meterDataEntry,
  ...energyEntry,
  ...dataImport,
  ...config
};
