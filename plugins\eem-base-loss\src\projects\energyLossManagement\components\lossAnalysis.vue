<template>
  <div class="page">
    <div id="container" ref="container"></div>
  </div>
</template>

<script>
import G6 from "@antv/g6";
import common from "eem-base/utils/common";
import omegaTheme from "@omega/theme";

export default {
  name: "TopologyChart",
  props: {
    inputData_in: {
      type: [Object, Array]
    },
    unit_in: {
      type: String,
      default: () => {
        return "";
      }
    }
  },
  computed: {
    echartTheme() {
      return omegaTheme.theme;
    }
  },
  data() {
    return {
      graph: null
    };
  },
  watch: {
    inputData_in: {
      handler(val) {
        this.$nextTick(() => {
          this.paintChart();
        });
      },
      deep: true
    }
  },
  methods: {
    // 初始化图形配置
    initGraphConf() {
      if (this.graph) return;
      var width = this.$refs.container.scrollWidth;
      var height = this.$refs.container.scrollHeight || 500;
      this.graph = new G6.Graph({
        container: "container",
        width,
        height,
        fitView: true,
        /* minZoom: 0.5,
        maxZoom: 3, */
        modes: {
          default: [
            "drag-canvas",
            "drag-node",
            "zoom-canvas",
            {
              type: "tooltip",
              formatText: this.formatLabel
            }
          ]
        },
        layout: {
          type: "dagre",
          rankdir: "LR",
          ranksep: 125
        },
        defaultNode: {
          type: "energyLossManagement-lossAnalysis-node"
        },
        defaultEdge: {
          type: "cubic-horizontal",
          style: {
            lineWidth: 2,
            stroke: "#B6BAC7"
          }
        }
      });
    },
    formatLabel(params) {
      const isLast = this.inputData_in.linkNode.find(
        item => item.source === params.id
      );
      let str = `${params.label}`;

      const changeNodeData = params.changeNodeData;

      str += `<br />${
        changeNodeData?.length ? $T("总能耗") : $T("能耗值")
      }：${common.formatNum(common.roundNumber(params.value, 2, "--"))}${
        this.unit_in
      }`;

      // 展示合并前各个节点的能耗值
      if (changeNodeData?.length) {
        changeNodeData.forEach(item => {
          str += `<br />${item.name}：${common.formatNum(
            common.roundNumber(item.value, 2, "--")
          )}${this.unit_in}`;
        });
      }

      // 最后层级节点没有损耗
      if (!isLast) {
        return str;
      }

      str += `<br />${$T("下级能耗之和")}：${common.formatNum(
        common.roundNumber(params.nextLevelEnergy, 2, "--")
      )}${this.unit_in}`;

      str += `<br />${$T("损耗值")}：${common.formatNum(
        common.roundNumber(params.loss, 2, "--")
      )}${this.unit_in}`;

      str += `<br />${$T("损耗率")}：${
        params.lossPercent || params.lossPercent === 0
          ? Number(params.lossPercent * 100).toFixed(2) + "%"
          : "--"
      }`;
      return str;
    },
    // 获取图表数据
    getChartData() {
      if (!this.inputData_in) return;
      // 数据源
      const lossDataVoList = this.inputData_in.lossDataVoList || [];
      // 数据连线
      const edges = this.inputData_in.linkNode || [];
      // 点集
      const nodes = lossDataVoList.map(item => {
        let status =
          item.lossPercent > item.threshold ||
          Math.abs(item.lossPercent) > item.threshold
            ? "red"
            : "blue";
        const hasNextLevelEnergy = Object.prototype.hasOwnProperty.call(
          item,
          "nextLevelEnergy"
        );
        return {
          id: item.name,
          label: item.deviceName,
          value: item.value,
          loss: item.loss,
          nextLevelEnergy: hasNextLevelEnergy
            ? item.nextLevelEnergy
            : item.value - item.loss,
          lossPercent: item.lossPercent,
          status,
          modelLabel: item.modelLabel,
          changeNodeData: item.changeNodeData
        };
      });
      // 边集
      return { nodes, edges };
    },
    // 绘制图形
    paintChart() {
      const data = this.getChartData();
      this.$nextTick(() => {
        if (this._.isEmpty(this.graph)) this.initGraphConf();
        this.graph.data(data);
        this.graph.render();
      });
    },
    setData() {
      const data = this.getChartData();
      this.$nextTick(() => {
        if (this._.isEmpty(this.graph)) this.initGraphConf();
        this.graph.changeData(data);
        this.graph.layout();
      });
    },
    // 超长文本显示
    fittingString(str, maxWidth, fontSize) {
      const ellipsis = "...";
      const ellipsisLength = G6.Util.getTextSize(ellipsis, fontSize)[0];
      let currentWidth = 0;
      let res = str;
      const pattern = new RegExp("[\u4E00-\u9FA5]+"); // distinguish the Chinese charactors and letters
      if (!str) return;
      str.split("").forEach((letter, i) => {
        if (currentWidth > maxWidth - ellipsisLength) return;
        if (pattern.test(letter)) {
          // Chinese charactors
          currentWidth += fontSize;
        } else {
          // get the width of single letter according to the fontSize
          currentWidth += G6.Util.getLetterWidth(letter, fontSize);
        }
        if (currentWidth > maxWidth - ellipsisLength) {
          res = `${str.substr(0, i)}${ellipsis}`;
        }
      });
      return res;
    }
  },
  mounted() {
    const _this = this;
    G6.registerNode("energyLossManagement-lossAnalysis-node", {
      drawShape(cfg, group) {
        const color =
          cfg.status === "red"
            ? ["#F65D68", "#ffffff"]
            : ["#3E77FC", "#ffffff"];
        const r = 5;
        let shape;

        let width = 220;
        let height = 80;
        // 文字大小
        const H3 = 16;
        const H1 = 22;
        // 判断是否是最后一层级节点
        const isLast = _this.inputData_in.linkNode.find(
          item => item.source === cfg.id
        );
        shape = group.addShape("rect", {
          attrs: {
            x: -width / 2,
            y: -height / 2,
            stroke: color[0],
            width,
            height,
            radius: r
          }
        });
        group.addShape("rect", {
          attrs: {
            x: -width / 2,
            y: -height / 2,
            width,
            height: height / 2,
            fill: color[0],
            radius: [r, r, 0, 0]
          },
          name: "main-box",
          draggable: false
        });

        group.addShape("rect", {
          attrs: {
            x: -width / 2 + 1,
            y: 0,
            width: width - 2,
            height: height / 2 - 1,
            fill: color[1],
            radius: [0, 0, r, r]
          },
          name: "title-box",
          draggable: false
        });

        // 节点值
        group.addShape("text", {
          attrs: {
            x: 0,
            y: height / 4,
            textAlign: "center",
            textBaseline: "middle",
            text: _this.fittingString(
              common.formatNum(common.roundNumber(cfg.value, 2, "--")) +
                _this.unit_in,
              width - 32,
              H1
            ),
            fontSize: H1,
            fill: "#000",
            opacity: 0.7
          },
          name: "percentage"
        });

        // 节点文本
        group.addShape("text", {
          attrs: {
            x: 0,
            y: -height / 4,
            textAlign: "center",
            textBaseline: "middle",
            text: _this.fittingString(cfg.label, width - 32, H1),
            fontSize: H1,
            fill: "#fff"
          },
          name: "value"
        });
        if (isLast) {
          // 下级能耗之和
          group.addShape("text", {
            attrs: {
              x: width / 2 + 12,
              y: 0 - height / 2 + 15,
              textAlign: "left",
              textBaseline: "top",
              text: `${$T("下级能耗之和")}：${common.formatNum(
                common.roundNumber(cfg.nextLevelEnergy, 2, "--")
              )}${_this.unit_in}`,
              fontSize: 12,
              fill: ["dark", "blue"].includes(_this.echartTheme)
                ? "#fff"
                : "#000"
            },
            name: "percentage"
          });
          // 损耗量
          group.addShape("text", {
            attrs: {
              x: width / 2 + 12,
              y: 8,
              textAlign: "left",
              textBaseline: "top",
              text: `${$T("损耗量")}：${common.formatNum(
                common.roundNumber(cfg.loss, 2, "--")
              )}${_this.unit_in}`,
              fontSize: 12,
              fill: ["dark", "blue"].includes(_this.echartTheme)
                ? "#fff"
                : "#000"
            },
            name: "percentage"
          });
          // 损耗率
          group.addShape("text", {
            attrs: {
              x: width / 2 + 12,
              y: 22,
              textAlign: "left",
              textBaseline: "top",
              text: `${$T("损耗率")}：`,
              fontSize: 12,
              fill: ["dark", "blue"].includes(_this.echartTheme)
                ? "#fff"
                : "#000"
            },
            name: "percentage"
          });
          // 损耗率
          group.addShape("text", {
            attrs: {
              x: width / 2 + 60,
              y: 22,
              textAlign: "left",
              textBaseline: "top",
              text: `${
                cfg.lossPercent || cfg.lossPercent === 0
                  ? Number(cfg.lossPercent * 100).toFixed(2) + "%"
                  : "--"
              }`,
              fontSize: 12,
              fill:
                cfg.status === "red"
                  ? "#F65D68"
                  : ["dark", "blue"].includes(_this.echartTheme)
                  ? "#fff"
                  : "#000"
            },
            name: "percentage"
          });
        }
        return shape;
      },
      getAnchorPoints() {
        return [
          [1, 0.5],
          [0, 0.5]
        ];
      }
    });

    // G6.registerEdge(
    //   "line-arrow",
    //   {
    //     getPath(points) {
    //       const startPoint = points[0];
    //       const endPoint = points[1];
    //       return [
    //         ["M", startPoint.x, startPoint.y],
    //         ["L", endPoint.x / 3 + (2 / 3) * startPoint.x, startPoint.y],
    //         ["L", endPoint.x / 3 + (2 / 3) * startPoint.x, endPoint.y],
    //         ["L", endPoint.x, endPoint.y]
    //       ];
    //     }
    //   },
    //   "line"
    // );
    this.$nextTick(() => {
      this.paintChart();
    });
  },
  activated() {}
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
#container {
  width: 100%;
  height: 100%;
}
</style>
<style lang="scss">
.g6-tooltip {
  white-space: nowrap;
  transition: left 0.4s cubic-bezier(0.23, 1, 0.32, 1) 0s,
    top 0.4s cubic-bezier(0.23, 1, 0.32, 1) 0s;
  background-color: rgba(50, 50, 50, 0.7);
  font-size: 14px;
  border-radius: 4px;
  color: rgb(255, 255, 255);
  padding: 10px;
  pointer-events: none;
}
</style>
