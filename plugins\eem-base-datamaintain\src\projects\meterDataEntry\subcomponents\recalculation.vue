<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="small">
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <CetForm
        class="eem-cont-c1"
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
      >
        <el-row :gutter="$J3">
          <el-col :span="12">
            <el-form-item prop="startTime">
              <template slot="label">
                <span>{{ $T("重算开始时间") }}</span>
                <el-popover
                  class="popover"
                  placement="top-start"
                  trigger="hover"
                  width="220"
                >
                  <div>
                    {{ $T("1. 请选择有数据的时间开始重算；") }}
                    <br />
                    {{
                      $T(
                        "2. 仅支持3年内数据的重算，3年以前的请联系系统维护人员进行重算。"
                      )
                    }}
                  </div>
                  <i slot="reference" class="el-icon-question"></i>
                </el-popover>
              </template>
              <el-date-picker
                v-model="CetForm_1.data.startTime"
                type="date"
                :placeholder="$T('选择日期时间')"
                :picker-options="pickerOptions"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";
export default {
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      CetDialog_1: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("能耗重算"),
        showClose: true,
        event: {}
      },
      // ResetUserPassword表单组件

      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // 1表单组件
      CetForm_1: {
        dataMode: "static", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        labelPosition: "top",
        rules: {
          startTime: [
            {
              required: true,
              trigger: ["blur", "change"],
              message: $T("请选择开始时间")
            }
          ]
        },
        event: {
          //    currentData_out: this.CetForm_1_currentData_out,
          saveData_out: this.CetForm_1_saveData_out
          //    finishData_out: this.CetForm_1_finishData_out,
          //    finishTrigger_out: this.CetForm_1_finishTrigger_out
        }
        // new Date().setFullYear(new Date().getFullYear()-3)
      },
      pickerOptions: {
        disabledDate: time => {
          return (
            new Date() < time.getTime() ||
            time.getTime() <
              new Date().setFullYear(new Date().getFullYear() - 3)
          );
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = this._.cloneDeep(val);
      this.CetForm_1.data = {};
      this.CetForm_1.resetTrigger_in = Date.now();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      console.log(val);
    }
  },
  methods: {
    CetButton_preserve_statusTrigger_out(val) {
      //由于接口太耗性能，不需要前端请求用户列表进行判断用户是否重名，后端那边进行处理；
      this.CetForm_1.localSaveTrigger_in = new Date().getTime();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_saveData_out(val) {
      let param = {
        jobName: "BatchEnergy",
        nodes: [
          {
            objectId: this.inputData_in.objectId,
            objectLabel: this.inputData_in.objectLabel
          }
        ],
        reCalcStartTime: this.$moment(val.startTime).valueOf()
      };
      this.addData(param);
    },
    addData(val) {
      customApi.getEnergyReCalc(val).then(response => {
        if (response.code === 0) {
          this.$message({
            type: "success",
            message: response.msg
          });
        } else {
          this.$message.warning(response.msg);
        }
        this.$emit("updataReCalcState_out");
        this.CetDialog_1.closeTrigger_in = Date.now();
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped></style>
