<template>
  <div>
    <ElDrawer
      class="drawer"
      :title="drawerTitle"
      :visible.sync="openDrawer"
      destroy-on-close
      size="960px"
    >
      <CetForm
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
        class="flex flex-col flex-auto m-J1"
      >
        <el-row
          :gutter="16"
          class="flex flex-row flex-wrap content-start flex-auto"
          style="overflow-y: auto"
        >
          <el-col :span="24">
            <el-form-item :label="$T('方案名称')" prop="name">
              <ElInput
                v-model.trim="CetForm_1.data.name"
                v-bind="ElInput_1"
                v-on="ElInput_1.event"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$T('所属项目')" prop="rootNodeId">
              <ElSelect
                v-model="CetForm_1.data.rootNodeId"
                v-bind="ElSelect_7"
                v-on="ElSelect_7.event"
              >
                <ElOption
                  v-for="item in ElOption_7.options_in"
                  :key="item[ElOption_7.key]"
                  :label="item[ElOption_7.label]"
                  :value="item[ElOption_7.value]"
                  :disabled="item[ElOption_7.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$T('能源类型')" prop="energytype">
              <ElSelect
                v-model="CetForm_1.data.energytype"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in ElOption_1.options_in"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$T('费用类型')" prop="feeratetype">
              <ElSelect
                v-model="CetForm_1.data.feeratetype"
                v-bind="ElSelect_2"
                v-on="ElSelect_2.event"
              >
                <ElOption
                  v-for="item in ElOption_2.options_in"
                  :key="item[ElOption_2.key]"
                  :label="item[ElOption_2.label]"
                  :value="item[ElOption_2.value]"
                  :disabled="item[ElOption_2.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <!-- 基本电费 -->
          <template v-if="CetForm_1.data.feeratetype == 1">
            <el-col :span="8">
              <el-form-item :label="$T('费率类型')" prop="rateType">
                <ElSelect
                  v-model="CetForm_1.data.rateType"
                  v-bind="ElSelect_6"
                  v-on="ElSelect_6.event"
                >
                  <ElOption
                    v-for="item in ElOption_6.options_in"
                    :key="item[ElOption_6.key]"
                    :label="item[ElOption_6.label]"
                    :value="item[ElOption_6.value]"
                    :disabled="item[ElOption_6.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <template v-if="CetForm_1.data.rateType == 1">
              <el-col :span="8">
                <el-form-item :label="$T('费率值')" prop="volumefeerate">
                  <ElInputNumber
                    v-model="CetForm_1.data.volumefeerate"
                    v-bind="ElInputNumber_1"
                    v-on="ElInputNumber_1.event"
                  ></ElInputNumber>
                  <span class="form-item-unit">{{ $T("元/kVA") }}</span>
                </el-form-item>
              </el-col>
            </template>
            <template v-if="CetForm_1.data.rateType == 2">
              <el-col :span="8">
                <el-form-item :label="$T('费率值')" prop="demandfeerate">
                  <ElInputNumber
                    v-model="CetForm_1.data.demandfeerate"
                    v-bind="ElInputNumber_1"
                    v-on="ElInputNumber_1.event"
                  ></ElInputNumber>
                  <span class="form-item-unit">{{ $T("元/kW") }}</span>
                </el-form-item>
                <div class="tooltipInfo">
                  {{
                    $T(
                      "偏差费用=（实际最大需量-计划需量*上限比例）*需量电价*惩罚系数"
                    )
                  }}
                </div>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$T('上限比例')" prop="highLimit">
                  <ElInputNumber
                    v-model="CetForm_1.data.highLimit"
                    v-bind="ElInputNumber_2"
                    v-on="ElInputNumber_2.event"
                  ></ElInputNumber>
                  <span class="form-item-unit">%</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$T('惩罚系数')" prop="punishRate">
                  <ElInputNumber
                    v-model="CetForm_1.data.punishRate"
                    v-bind="ElInputNumber_3"
                    v-on="ElInputNumber_3.event"
                  ></ElInputNumber>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label prop="calculateDeviation">
                  <ElCheckbox
                    style="display: inline-block"
                    v-model="CetForm_1.data.calculateDeviation"
                    v-bind="ElCheckbox_1"
                    v-on="ElCheckbox_1.event"
                  >
                    {{ ElCheckbox_1.text }}
                  </ElCheckbox>
                  <el-popover placement="top-start" trigger="hover">
                    <div>
                      <div
                        v-text="
                          $T(
                            '实际最大需量<计划需量*上限比例时，是否计算负偏差，若不计算负偏差费用为0'
                          )
                        "
                      ></div>
                    </div>
                    <i
                      style="font-size: 16px"
                      slot="reference"
                      class="el-icon-question"
                    ></i>
                  </el-popover>
                </el-form-item>
              </el-col>
            </template>
          </template>
          <!-- 电度电费 -->
          <template
            v-if="
              CetForm_1.data.feeratetype == 2 || CetForm_1.data.feeratetype == 4
            "
          >
            <el-col :span="8">
              <el-form-item
                :label="$T('费率类型')"
                prop="electricitychargerate"
              >
                <ElSelect
                  v-model="CetForm_1.data.electricitychargerate"
                  v-bind="ElSelect_3"
                  v-on="ElSelect_3.event"
                >
                  <ElOption
                    v-for="item in ElOption_3.options_in"
                    :key="item[ElOption_3.key]"
                    :label="item[ElOption_3.label]"
                    :value="item[ElOption_3.value]"
                    :disabled="item[ElOption_3.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <template v-if="CetForm_1.data.electricitychargerate == 1">
              <el-col :span="8">
                <el-form-item :label="`${$T('费率值')}`" prop="feerate">
                  <ElInputNumber
                    v-model="CetForm_1.data.feerate"
                    v-bind="ElInputNumber_1"
                    v-on="ElInputNumber_1.event"
                  ></ElInputNumber>
                  <span class="form-item-unit">
                    {{ $T("元") }}/{{ symbol }}
                  </span>
                </el-form-item>
              </el-col>
            </template>
            <div
              v-if="CetForm_1.data.electricitychargerate == 2"
              class="flex-col flex w-full h-[500px]"
            >
              <div class="clearfix">
                <CetButton
                  class="fr"
                  v-bind="CetButton_1"
                  v-on="CetButton_1.event"
                ></CetButton>
                <CetButton
                  class="fr mr-J1"
                  v-bind="CetButton_2"
                  v-on="CetButton_2.event"
                ></CetButton>
                <CetButton
                  class="fr mr-J1"
                  v-bind="CetButton_3"
                  v-on="CetButton_3.event"
                ></CetButton>
                <div class="basic-box fr mr-J1">
                  <customElSelect
                    class=""
                    v-model="ElSelect_4.value"
                    v-bind="ElSelect_4"
                    v-on="ElSelect_4.event"
                    :prefix_in="$T('分时方案')"
                  >
                    <ElOption
                      v-for="item in ElOption_4.options_in"
                      :key="item[ElOption_4.key]"
                      :label="item[ElOption_4.label]"
                      :value="item[ElOption_4.value]"
                      :disabled="item[ElOption_4.disabled]"
                    ></ElOption>
                  </customElSelect>
                </div>
              </div>
              <div class="flex-auto mt-J3">
                <el-table
                  class="el-table--scrollable-y fullheight"
                  :data="timesharefeerateTableData"
                  border
                  tooltip-effect="light"
                >
                  <el-table-column
                    :label="$T('序号')"
                    type="index"
                    width="70"
                    header-align="center"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    :show-overflow-tooltip="true"
                    :label="$T('时段方案')"
                    prop="name"
                    header-align="left"
                    align="left"
                  ></el-table-column>
                  <el-table-column
                    :show-overflow-tooltip="true"
                    :label="$T('时段名称')"
                    prop="identification"
                    header-align="left"
                    align="left"
                  ></el-table-column>
                  <el-table-column
                    :show-overflow-tooltip="true"
                    :label="$T('时段区间')"
                    prop="dayshareset$text"
                    header-align="left"
                    align="left"
                  ></el-table-column>
                  <el-table-column
                    :show-overflow-tooltip="true"
                    :label="rateB"
                    prop="timesharefeesinglerate.rate"
                    header-align="left"
                    align="left"
                  >
                    <template slot-scope="scope">
                      <div
                        style="text-align: left"
                        v-if="!CetButton_2.visible_in"
                      >
                        {{ scope.row.timesharefeesinglerate.rate }}
                      </div>

                      <ElInputNumber
                        v-else-if="CetButton_2.visible_in"
                        v-model="scope.row.timesharefeesinglerate.rate"
                        v-bind="ElInputNumber_1"
                        v-on="ElInputNumber_1.event"
                        @change="
                          handleNum(scope.row.timesharefeesinglerate, 'rate', 5)
                        "
                      ></ElInputNumber>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
            <div v-if="CetForm_1.data.electricitychargerate == 3">
              <el-table
                class="h-[200px]"
                :data="stagefeerateTableData"
                highlight-current-row
                @current-change="stagefeerateTableDataCurrentChange"
                border
              >
                <el-table-column
                  :label="$T('序号')"
                  type="index"
                  width="100"
                  header-align="left"
                  align="left"
                ></el-table-column>
                <el-table-column
                  :show-overflow-tooltip="true"
                  :label="$T('名称')"
                  prop="name"
                  header-align="left"
                  align="left"
                ></el-table-column>
                <el-table-column
                  :show-overflow-tooltip="true"
                  :label="`${$T('用能量')}${$T('（{0}）', symbol)}`"
                  prop="energyNum"
                  header-align="left"
                  align="left"
                >
                  <template slot-scope="scope">
                    <div style="text-align: center">
                      <span
                        v-if="!CetButton_11.visible_in"
                        style="display: inline-block; width: 100px"
                      >
                        {{ scope.row.energyNum }}
                      </span>

                      <ElInputNumber
                        v-if="CetButton_11.visible_in"
                        v-model="scope.row.energyNum"
                        style="width: 100px"
                        v-bind="ElInputNumber_1"
                        v-on="ElInputNumber_1.event"
                        @change="handleNum(scope.row, 'energyNum', 2)"
                      ></ElInputNumber>
                      ~
                      <span style="display: inline-block; width: 100px">
                        {{
                          stagefeerateTableData[scope.$index + 1]
                            ? stagefeerateTableData[scope.$index + 1].energyNum
                            : ""
                        }}
                      </span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  :show-overflow-tooltip="true"
                  :label="rates"
                  prop="rate"
                  header-align="right"
                  align="right"
                >
                  <template slot-scope="scope">
                    <div
                      style="text-align: center"
                      v-if="!CetButton_11.visible_in"
                    >
                      {{ scope.row.rate }}
                    </div>
                    <ElInputNumber
                      v-else-if="CetButton_11.visible_in"
                      v-model="scope.row.rate"
                      v-bind="ElInputNumber_1"
                      v-on="ElInputNumber_1.event"
                      @change="handleNum(scope.row, 'rate', 5)"
                    ></ElInputNumber>
                  </template>
                </el-table-column>
              </el-table>
              <div class="clearfix mt10 mb10">
                <CetButton
                  class="fl mr5"
                  v-bind="CetButton_4"
                  v-on="CetButton_4.event"
                ></CetButton>
                <CetButton
                  class="fr ml5"
                  v-bind="CetButton_6"
                  v-on="CetButton_6.event"
                ></CetButton>
                <CetButton
                  class="fr ml5"
                  v-bind="CetButton_5"
                  v-on="CetButton_5.event"
                ></CetButton>
                <CetButton
                  class="fr ml5"
                  v-bind="CetButton_11"
                  v-on="CetButton_11.event"
                ></CetButton>
                <CetButton
                  class="fr ml5"
                  v-bind="CetButton_12"
                  v-on="CetButton_12.event"
                ></CetButton>
              </div>
            </div>
          </template>

          <el-col
            :span="24"
            v-if="CetForm_1.data.feeratetype == 3"
            style="height: calc(100% - 225px)"
          >
            <!-- 力调电费 -->
            <div class="flex flex-col fullheight">
              <el-form-item :label="$T('力调费率')" prop="powertarifffeerate">
                <ElSelect
                  v-model="CetForm_1.data.powertarifffeerate"
                  v-bind="ElSelect_5"
                  v-on="ElSelect_5.event"
                >
                  <ElOption
                    v-for="item in ElOption_5.options_in"
                    :key="item[ElOption_5.key]"
                    :label="item[ElOption_5.label]"
                    :value="item[ElOption_5.value]"
                    :disabled="item[ElOption_5.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
              <div class="clearfix mt-J1">
                <span style="font-size: 16px">
                  {{ ElSelect_5.dropItemText }}{{ $T("费率表详情") }}
                </span>
                <CetButton
                  class="fr ml-J1"
                  v-bind="CetButton_7"
                  v-on="CetButton_7.event"
                ></CetButton>
              </div>
              <div class="flex-auto mb-J1 mt-J1">
                <el-table
                  class="el-table--scrollable-y fullheight"
                  highlight-current-row
                  :data="powertarifffeerateTableData"
                  border
                  @current-change="changePowerItem"
                >
                  <el-table-column
                    :show-overflow-tooltip="true"
                    :label="$T('力率')"
                    prop="powerfactor"
                    header-align="left"
                    align="left"
                  >
                    <template slot-scope="scope">
                      <div class="text-center" v-if="!CetButton_10.visible_in">
                        {{ Number(scope.row.powerfactor).toFixed(2) }}
                      </div>
                      <ElInputNumber
                        v-else-if="CetButton_10.visible_in"
                        v-model="scope.row.powerfactor"
                        v-bind="ElInputNumber_2"
                        v-on="ElInputNumber_2.event"
                      ></ElInputNumber>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :show-overflow-tooltip="true"
                    :label="`${$T('调整电费')}${$T('（{0}）', '%')}`"
                    prop="punishrate"
                    header-align="left"
                    align="left"
                  >
                    <template slot="header">
                      <span>
                        {{ `${$T("调整电费")}${$T("（{0}）", "%")}` }}
                      </span>

                      <el-popover
                        placement="top-start"
                        trigger="hover"
                        effect="light"
                      >
                        <div>
                          {{ $T("正数：在电费总额上增收") }}
                          <br />
                          {{ $T("负数：在电费总额上减收") }}
                        </div>
                        <i slot="reference" class="el-icon-question"></i>
                      </el-popover>
                    </template>
                    <template slot-scope="scope">
                      <div class="text-center" v-if="!CetButton_10.visible_in">
                        {{ Number(scope.row.punishrate).toFixed(2) }}
                      </div>
                      <ElInputNumber
                        v-else-if="CetButton_10.visible_in"
                        v-model="scope.row.punishrate"
                        v-bind="ElInputNumber_6"
                        v-on="ElInputNumber_6.event"
                      ></ElInputNumber>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="clearfix mb10">
                <!-- 力调电费删除 -->
                <CetButton
                  v-bind="CetButton_delete"
                  v-on="CetButton_delete.event"
                ></CetButton>
                <!-- 力调电费新增 -->
                <CetButton
                  class="fr ml-J1"
                  v-bind="CetButton_add"
                  v-on="CetButton_add.event"
                ></CetButton>
                <CetButton
                  class="fr ml-J1"
                  v-bind="CetButton_9"
                  v-on="CetButton_9.event"
                ></CetButton>
                <!-- cancel按钮组件 -->
                <CetButton
                  class="fr ml-J1"
                  v-bind="CetButton_10"
                  v-on="CetButton_10.event"
                ></CetButton>
                <CetButton
                  class="fr ml-J1"
                  v-bind="CetButton_cancelEdit"
                  v-on="CetButton_cancelEdit.event"
                ></CetButton>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <el-form-item
              :label="$T('生效时间')"
              prop="effectivedate"
              v-if="
                !(
                  CetForm_1.data.feeratetype == 2 &&
                  CetForm_1.data.electricitychargerate == 2
                )
              "
            >
              <el-date-picker
                :disabled="disableDate"
                style="width: 100%"
                :clearable="false"
                v-model="CetForm_1.data.effectivedate"
                v-bind="CetDatePicker_1.config"
                :placeholder="$T('选择日期')"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </CetForm>
      <div class="flex flex-row justify-end footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="ml-J1"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </div>
    </ElDrawer>
    <UploadDialog
      v-bind="uploadDialog"
      v-on="uploadDialog.event"
    ></UploadDialog>
  </div>
</template>
<script>
import common from "eem-base/utils/common";
import commonApi from "@/api/custom";
import UploadDialog from "eem-base/components/uploadDialog";
export default {
  name: "AddRateScheme",
  components: { UploadDialog },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    disableDate: {
      type: Boolean,
      default: false
    },
    // 费率调整时已有记录的时间组
    dateArr: {
      type: Array,
      default: () => {
        return [];
      }
    },
    tableData: {
      type: Array
    },
    // 力调详情查看
    detailVisible: {
      type: Boolean
    },
    energyList_in: Array
  },

  computed: {
    rates() {
      return $T("费率标准（元/{0}）", this.symbol);
    },
    rateB() {
      return $T("费率（元/{0}）", this.symbol);
    },
    rootNode() {
      const id = this.CetForm_1.data.rootNodeId;
      const list = this.ElOption_7.options_in;
      return list.find(item => item.id === id);
    }
  },

  data() {
    return {
      openDrawer: false,
      drawerTitle: "",
      historyRecordId: 0,
      symbol: "kWh",
      timesharefeerateTableData: [], //分时列表
      stagefeerateTableData: [
        // {
        //   energyNum: 0
        // }
      ], //阶梯列表
      stagefeerateTableDataItemIndex: -1,
      powertarifffeerateTableDataItemIndex: -1, //选中的力调项索引
      powertarifffeerateTableData: [], //力调列表
      powertarifffeerateTableData_copy: [], //力调列表修改前数据，用于取消时恢复原来的数据
      ElSelect_1: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: 1,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [],
        key: "feeRateType",
        value: "feeRateType",
        label: "feeRateTypeName",
        disabled: "disabled"
      },
      ElSelect_6: {
        value: 1,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_6: {
        options_in: [],
        key: "feeRateSubType",
        value: "feeRateSubType",
        label: "feeRateSubTypeName",
        disabled: "disabled"
      },
      ElSelect_3: {
        value: 1,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_3_change_out
        }
      },
      ElOption_3: {
        options_in: [],
        key: "feeRateSubType",
        value: "feeRateSubType",
        label: "feeRateSubTypeName",
        disabled: "disabled"
      },
      ElSelect_4: {
        value: null,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_4_change_out
        }
      },
      ElOption_4: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_5: {
        value: 1,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_5_change_out
        }
      },
      ElOption_5: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: $T("录入费率"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: false,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetButton_3: {
        visible_in: false,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      CetButton_4: {
        visible_in: true,
        disable_in: true,
        title: $T("删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_4_statusTrigger_out
        }
      },
      CetButton_5: {
        visible_in: true,
        disable_in: true,
        title: $T("编辑"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_5_statusTrigger_out
        }
      },
      CetButton_6: {
        visible_in: true,
        disable_in: false,
        title: $T("新增"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_6_statusTrigger_out
        }
      },
      CetButton_7: {
        visible_in: true,
        disable_in: false,
        title: $T("导入"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_7_statusTrigger_out
        }
      },
      CetButton_9: {
        visible_in: true,
        disable_in: false,
        title: $T("编辑"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_9_statusTrigger_out
        }
      },
      // 力调电费删除
      CetButton_delete: {
        visible_in: true,
        disable_in: true,
        title: $T("删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_delete_statusTrigger_out
        }
      },
      // 力调电费新增
      CetButton_add: {
        visible_in: false,
        disable_in: false,
        title: $T("新增"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      // 取消力调电费编辑
      CetButton_cancelEdit: {
        visible_in: false,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancelEdit_statusTrigger_out
        }
      },
      CetButton_10: {
        visible_in: false,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_10_statusTrigger_out
        }
      },
      CetButton_11: {
        visible_in: false,
        disable_in: false,
        title: $T("保存"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_11_statusTrigger_out
        }
      },
      CetButton_12: {
        visible_in: false,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_12_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "140px",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入方案名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          energytype: [
            {
              required: true,
              message: $T("请选择能源类型"),
              trigger: ["blur", "change"]
            }
          ],
          feeratetype: [
            {
              required: true,
              message: $T("请选择费用类型"),
              trigger: ["blur", "change"]
            }
          ],
          electricitychargerate: [
            {
              required: true,
              message: $T("请选择费率类型"),
              trigger: ["blur", "change"]
            }
          ],
          rateType: [
            {
              required: true,
              message: $T("请选择费率类型"),
              trigger: ["blur", "change"]
            }
          ],
          powertarifffeerate: [
            {
              required: true,
              message: $T("请选择力调费率"),
              trigger: ["blur", "change"]
            }
          ],
          volumefeerate: [
            {
              required: true,
              message: $T("请输入容量费率值"),
              trigger: ["blur", "change"]
            }
          ],
          demandfeerate: [
            {
              required: true,
              message: $T("请输入需量费率值"),
              trigger: ["blur", "change"]
            }
          ],
          feerate: [
            {
              required: true,
              message: $T("请输入费率值"),
              trigger: ["blur", "change"]
            }
          ],
          effectivedate: [
            {
              required: true,
              message: $T("请选择生效时间"),
              trigger: ["blur", "change"]
            }
          ],
          rootNodeId: [
            {
              required: true,
              message: $T("请选择所属项目"),
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInputNumber_1: {
        value: "",
        style: {
          width: "100%"
        },
        min: 0,
        // eslint-disable-next-line no-loss-of-precision
        max: 999999999999.99999,
        step: 2,
        precision: 5,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        disabled: false,
        controls: false,
        event: {}
      },
      ElInputNumber_2: {
        value: "",
        style: {
          width: "100%"
        },
        min: 0,
        max: 999999999999.99,
        step: 2,
        precision: 2,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        disabled: false,
        controls: false,
        event: {}
      },
      ElInputNumber_3: {
        value: "",
        style: {
          width: "100%"
        },
        min: 0,
        max: 999999999999,
        step: 2,
        precision: 0,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        disabled: false,
        controls: false,
        event: {}
      },
      ElInputNumber_6: {
        value: "",
        style: {
          width: "100%"
        },
        max: 999999999999.99,
        step: 2,
        precision: 2,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        disabled: false,
        controls: false
      },
      ElCheckbox_1: {
        value: false,
        text: $T("计算负偏差"),
        disabled: false,
        event: {}
      },
      CetDatePicker_1: {
        disable_in: false,
        val: this.$moment().add(1, "month").startOf("month").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "month",
          rangeSeparator: "-",
          style: {
            display: "inline-block"
          },
          pickerOptions: common.pickerOptions_laterThanYesterd11
        }
      },
      powertariffData: null,
      allTypeList: [], // 所有能源类型、费用类型、费率类型的列表
      feeRateTypesList: [], // 选择的能源类型下的费用类型
      uploadDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        extensionNameList_in: [".xlsx", ".xls"],
        hideDownload: false,
        dialogTitle: $T("导入数据"),
        event: {
          download: this.uploadDialog_download,
          uploadFile: this.uploadDialog_uploadFile
        }
      },
      ElSelect_7: {
        value: "",
        style: {
          width: "100%"
        }
      },
      ElOption_7: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    async visibleTrigger_in() {
      this.openDrawer = true;
      // 恢复按钮状态
      this.CetButton_1.visible_in = true;
      this.CetButton_2.visible_in = false;
      this.CetButton_3.visible_in = false;
      this.CetButton_add.visible_in = true;
      this.CetButton_cancelEdit.visible_in = false;
      this.CetButton_10.visible_in = false;
      this.CetButton_11.visible_in = false;
      this.CetButton_12.visible_in = false;
      this.CetButton_4.visible_in = true;
      this.CetButton_5.visible_in = true;
      this.CetButton_6.visible_in = true;

      // 力调电费详情控制按钮隐藏
      this.CetButton_7.visible_in = !this.detailVisible;
      this.CetButton_delete.visible_in = !this.detailVisible;
      this.CetButton_9.visible_in = !this.detailVisible;
      this.CetButton_add.visible_in = !this.detailVisible;
      this.CetButton_confirm.visible_in = !this.detailVisible;
      await this.getEnergytype();
      await this.getRootNode();
      this.CetForm_1.resetTrigger_in = new Date().getTime();
      this.init();
    },
    closeTrigger_in() {
      this.openDrawer = false;
    },
    stagefeerateTableData: function (val) {
      if (val.length > 0) {
        this.CetButton_4.disable_in = false;
        this.CetButton_5.disable_in = false;
      } else {
        this.CetButton_4.disable_in = true;
        this.CetButton_5.disable_in = true;
      }
      if (val.length < 3) {
        this.CetButton_6.disable_in = false;
      } else {
        this.CetButton_6.disable_in = true;
      }
      if (this.stagefeerateTableDataItemIndex !== -1 && val.length > 0) {
        this.CetButton_4.disable_in = false;
      } else {
        this.CetButton_4.disable_in = true;
      }
    },
    stagefeerateTableDataItemIndex: function (val) {
      if (val !== -1 && this.stagefeerateTableData.length > 0) {
        this.CetButton_4.disable_in = false;
      } else {
        this.CetButton_4.disable_in = true;
      }
    },
    powertarifffeerateTableData(val) {
      this.CetButton_9.disable_in = !val.length;
      this.CetButton_delete.disable_in = !val.length;
    },
    powertarifffeerateTableDataItemIndex(val) {
      this.CetButton_delete.disable_in = val === -1;
    }
  },

  methods: {
    // 获取项目
    async getRootNode() {
      this.ElOption_7.options_in = [];
      const res = await commonApi.rootNode();
      const data = res.data || [];
      this.ElOption_7.options_in = data;
    },
    //获取能源类型
    async getEnergytype() {
      const energyList = this.energyList_in.filter(i => i.id);

      // 查询所有能源类型、费用类型、费率类型
      const energytypes = energyList.map(item => {
        return item.id;
      });
      const res = await commonApi.queryEnergySchemeConfig(energytypes);
      if (res.code !== 0) {
        return;
      }
      this.allTypeList = this._.cloneDeep(res.data);
      this.ElOption_1.options_in = this._.cloneDeep(energyList);
    },
    init() {
      const val = this._.cloneDeep(this.inputData_in);
      this.stagefeerateTableData = [];
      this.ElOption_5.options_in = [];
      this.powertarifffeerateTableData = [];
      if (val.id) {
        this.drawerTitle = this.detailVisible
          ? $T("费率方案详情")
          : $T("编辑费率方案");
        // 编辑不能修改能源类型
        this.ElSelect_1.disabled = true;
        this.ElSelect_2.disabled = true;
        this.ElSelect_3.disabled = true;
        this.ElSelect_6.disabled = true;
        this.ElSelect_7.disabled = true;
        this.ElInput_1.disabled = this.detailVisible;
        this.ElSelect_5.disabled = this.detailVisible;

        const energytype = this.inputData_in.energytype;
        this.ElSelect_1_change_out(energytype);
        // 编辑
        setTimeout(() => {
          this.CetForm_1.data = this._.cloneDeep(val);
          this.CetForm_1.data.feeratetype = val.feeratetype;
          this.ElSelect_2_change_out(this.CetForm_1.data.feeratetype);

          const energy = this.ElOption_1.options_in.find(
            item => item.id === this.CetForm_1.data.energytype
          );
          this.symbol = energy?.symbol ?? "--";

          if (val.stagefeerateTableData) {
            this.stagefeerateTableData = val.stagefeerateTableData;
          }
          if (val.powertariff_model) {
            this.loadPowerTariff([this._.cloneDeep(val.powertariff_model)]);
          }
          if (val.timeShareScheme_id) {
            this.getTimeShareScheme(val.timeShareScheme_id);
          }
          this.CetForm_1.resetTrigger_in = new Date().getTime();
          // 上限比例和惩罚系数不存在时，elinputnumber默认值为0，清空默认值
          if (this.CetForm_1.data.highLimit === null) {
            this.CetForm_1.data.highLimit = undefined;
          }
          if (this.CetForm_1.data.punishRate === null) {
            this.CetForm_1.data.punishRate = undefined;
          }
        }, 300);
      } else {
        this.drawerTitle = $T("新建费率方案");
        this.ElSelect_1.disabled = false;
        this.ElSelect_2.disabled = false;
        this.ElSelect_3.disabled = false;
        this.ElSelect_6.disabled = false;
        this.ElInput_1.disabled = false;
        this.ElSelect_5.disabled = false;
        this.CetForm_1.data = {};
        this.CetForm_1.data.energytype = val.energytype;
        this.ElSelect_1_change_out(val.energytype);
        this.CetForm_1.resetTrigger_in = new Date().getTime();
      }
    },
    // 阶梯费率tab选择
    stagefeerateTableDataCurrentChange(val, val2, cal3, cal4) {
      if (val) {
        this.CetButton_4.disable_in = false;
        this.stagefeerateTableData.forEach((item, index) => {
          if (item.name === val.name) {
            this.stagefeerateTableDataItemIndex = index;
          }
        });
      } else {
        this.CetButton_4.disable_in = true;
      }
    },
    CetButton_cancel_statusTrigger_out() {
      this.openDrawer = false;
    },
    CetButton_confirm_statusTrigger_out(val) {
      if (this.CetButton_10.visible_in) {
        return this.$message.warning($T("请先保存费率表"));
      }
      if (this.CetButton_2.visible_in) {
        return this.$message.warning($T("请先保存费率"));
      }
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_saveData_out(val) {
      // 调整历史记录的id，费率调整时，时间相同就是编辑，没有当前时间的就是新增
      const target = this._.find(this.tableData, [
        "effectivedate",
        val.effectivedate
      ]);
      this.historyRecordId = target ? target.id : 0;

      if (val.feeratetype === 1) {
        // 基本电费
        if (val.rateType === 1) {
          // 容量计费
          this.addScheme1(val);
        } else if (val.rateType === 2) {
          // 需量计费
          this.addScheme2(val);
        }
      } else if (val.feeratetype === 2) {
        // 电度电费
        if (val.electricitychargerate === 1) {
          // 单一费率
          this.addScheme3(val);
        } else if (val.electricitychargerate === 2) {
          // 分时费率
          this.addScheme4(val);
        } else if (val.electricitychargerate === 3) {
          // 阶梯费率
          this.addScheme5(val);
        }
      } else if (val.feeratetype === 3) {
        // 力调电费
        this.addScheme6(val);
      } else if (val.feeratetype === 4) {
        // 附加费
        if (val.electricitychargerate === 1) {
          // 单一费率
          this.addScheme3(val);
        } else if (val.electricitychargerate === 2) {
          // 分时费率
          this.addScheme4(val);
        } else if (val.electricitychargerate === 3) {
          // 阶梯费率
          this.addScheme5(val);
        }
      }
    },
    // 容量计费
    async addScheme1(val) {
      const data = {
        chargeWay: 1,
        effectiveDate:
          val.effectivedate &&
          this.$moment(val.effectivedate).startOf("month").valueOf(),
        fee: val.volumefeerate,
        name: val.name,
        recordId: this.historyRecordId,
        schemeId: val.id,
        rootNodeId: this.rootNode.id,
        rootNodeLabel: this.rootNode.modelLabel,
        energyType: val.energytype
      };
      const response = await commonApi.saveVolumeFeeScheme(data);
      if (response.code !== 0) {
        return;
      }
      this.$message({
        message: $T("保存成功"),
        type: "success"
      });
      this.openDrawer = false;
      this.$emit("finishTrigger_out", new Date().getTime());
    },
    // 需量计费
    async addScheme2(val) {
      const data = {
        calculateDeviation: val.calculateDeviation || false,
        chargeWay: 2,
        effectiveDate:
          val.effectivedate &&
          this.$moment(val.effectivedate).startOf("month").valueOf(),
        feeRate: val.demandfeerate,
        highLimit: val.highLimit,
        name: val.name,
        punishRate: val.punishRate,
        recordId: this.historyRecordId,
        schemeId: val.id,
        rootNodeId: this.rootNode.id,
        rootNodeLabel: this.rootNode.modelLabel,
        energyType: val.energytype
      };
      const response = await commonApi.saveDemandFeeScheme(data);
      if (response.code !== 0) {
        return;
      }
      this.$message({
        message: $T("保存成功"),
        type: "success"
      });
      this.openDrawer = false;
      this.$emit("finishTrigger_out", new Date().getTime());
    },
    // 单一费率
    async addScheme3(val) {
      const data = {
        rootNodeId: this.rootNode.id,
        rootNodeLabel: this.rootNode.modelLabel,
        feeScheme: {
          feeratetype: val.feeratetype,
          feeratesubtype: 1,
          name: val.name,
          energytype: val.energytype,
          id: val.id,
          singlefeerecord_model: [
            {
              effectivedate:
                val.effectivedate &&
                this.$moment(val.effectivedate).startOf("month").valueOf(),
              feerate: Number((val.feerate * 1000 * 100).toFixed(0)),
              id: this.historyRecordId
            }
          ]
        }
      };
      const response = await commonApi.singleFeeRecord(data);
      if (response.code !== 0) {
        return;
      }
      this.$message({
        message: $T("保存成功"),
        type: "success"
      });
      this.openDrawer = false;
      this.$emit("finishTrigger_out", new Date().getTime());
    },
    // 分时费率
    async addScheme4(val) {
      const data = {
        rootNodeId: this.rootNode.id,
        rootNodeLabel: this.rootNode.modelLabel,
        feeScheme: {
          energytype: val.energytype,
          feeratesubtype: 2,
          feeratetype: val.feeratetype,
          id: val.id,
          name: val.name,
          timeShareFeeRecordId: this.timeShareScheme.id,
          timeSharePeriodList: []
        }
      };
      if (this.timesharefeerateTableData.length > 0) {
        data.feeScheme.timeSharePeriodList = this.timesharefeerateTableData.map(
          item => {
            let obj = {
              id: item.id
            };
            if (
              item.timesharefeesinglerate.rate ||
              item.timesharefeesinglerate.rate === 0
            ) {
              obj.timesharefeesinglerate_model = [
                {
                  id: 0,
                  rate: Number(
                    (item.timesharefeesinglerate.rate * 1000 * 100).toFixed(0)
                  )
                }
              ];
            }
            return obj;
          }
        );
      }
      const response = await commonApi.timeShareFeeRecord(data);
      if (response.code !== 0) {
        return;
      }
      this.$message({
        message: $T("保存成功"),
        type: "success"
      });
      this.openDrawer = false;
      this.$emit("finishTrigger_out", new Date().getTime());
    },
    // 阶梯费率
    async addScheme5(val) {
      var data = {
        rootNodeId: this.rootNode.id,
        rootNodeLabel: this.rootNode.modelLabel,
        feeScheme: {
          energytype: val.energytype,
          feeratesubtype: 3,
          feeratetype: val.feeratetype,
          id: val.id,
          name: val.name,
          stagefeerecord_model: [
            {
              cycle: 14,
              effectivedate:
                val.effectivedate &&
                this.$moment(val.effectivedate).startOf("month").valueOf(),
              id: this.historyRecordId,
              stagefeeset_model: []
            }
          ]
        }
      };
      var flag = false;
      var wrap;
      if (this.stagefeerateTableData && this.stagefeerateTableData.length > 0) {
        data.feeScheme.stagefeerecord_model[0].stagefeeset_model =
          this.stagefeerateTableData.map(item => {
            if (wrap) {
              if (Number(item.energyNum) <= Number(wrap)) {
                flag = true;
              }
            }
            wrap = item.energyNum;
            var obj = {
              lowlimit: Number(item.energyNum),
              name: item.name,
              rate: Number((item.rate * 1000 * 100).toFixed(0))
            };
            return obj;
          });
      }
      if (flag) {
        this.$message({
          message: $T("请填写正确阶梯"),
          type: "warning"
        });
        return false;
      }
      const response = await commonApi.stageFeeRecord(data);
      if (response.code !== 0) {
        return;
      }

      this.$message({
        message: $T("保存成功"),
        type: "success"
      });
      this.openDrawer = false;
      this.$emit("finishTrigger_out", new Date().getTime());
    },
    // 力调电费
    async addScheme6(val) {
      var data = {
        rootNodeId: this.rootNode.id,
        rootNodeLabel: this.rootNode.modelLabel,
        feeScheme: {
          energytype: val.energytype,
          feeratesubtype: 1,
          feeratetype: val.feeratetype,
          id: val.id,
          name: val.name,
          powertariff_model: [
            {
              effectivedate:
                val.effectivedate &&
                this.$moment(val.effectivedate).startOf("month").valueOf(),
              id: this.historyRecordId,
              name: this.powertariffData.name,
              remark: this.powertariffData.remark,
              powertarifffactor_model: []
            }
          ]
        }
      };
      // 判断力率是否重复 powerfactor
      var flag = false;
      if (
        this.powertarifffeerateTableData &&
        this.powertarifffeerateTableData.length > 0
      ) {
        data.feeScheme.powertariff_model[0].powertarifffactor_model =
          this.powertarifffeerateTableData.map(item => {
            if (
              this.powertarifffeerateTableData.filter(
                i => i.powerfactor === item.powerfactor
              ).length >= 2
            ) {
              flag = true;
            }
            var obj = {
              modelLabel: "powertarifffactor",
              id: this.historyRecordId === 0 ? 0 : item.id,
              powerfactor: Number(Number(item.powerfactor).toFixed2(2)),
              punishrate: Number(Number(item.punishrate / 100).toFixed2(4))
            };
            return obj;
          });
      }
      if (flag) {
        this.$message({
          message: $T("力率有重复"),
          type: "warning"
        });
        return;
      }
      const response = await commonApi.powerTariffFeeRecord(data);
      if (response.code !== 0) {
        return;
      }
      this.$message({
        message: $T("保存成功"),
        type: "success"
      });
      this.openDrawer = false;
      this.$emit("finishTrigger_out", new Date().getTime());
    },
    ElSelect_1_change_out(val) {
      const energy = this.ElOption_1.options_in.filter(item => item.id === val);
      this.symbol = energy?.symbol ?? "--";

      if (val) {
        this.CetForm_1.data.energytype = val;
        // 选择的是分时费率需要重新请求分时方案
        if (
          this.CetForm_1.data.feeratetype === 2 &&
          this.CetForm_1.data.electricitychargerate === 2
        ) {
          this.getTimeShareScheme();
        }
      }

      // 费用类型列表
      const target = this._.find(this.allTypeList, ["energyType", val]);
      this.feeRateTypesList = (target && target.feeRateTypesList) || [];
      this.ElOption_2.options_in = this.feeRateTypesList;
      if (this.ElOption_2.options_in[0]) {
        this.$set(
          this.CetForm_1.data,
          "feeratetype",
          this.ElOption_2.options_in[0].feeRateType
        );
        this.ElSelect_2_change_out(this.CetForm_1.data.feeratetype);
      }
    },
    ElSelect_2_change_out(val) {
      // 费率类型列表
      const target = this._.find(this.feeRateTypesList, ["feeRateType", val]);
      this.ElOption_3.options_in = (target && target.feeRateSubTypesList) || [];
      this.ElOption_6.options_in = (target && target.feeRateSubTypesList) || [];
    },
    ElSelect_3_change_out(val) {
      if (val === 2) {
        // 选择分时费率
        this.getTimeShareScheme();
      } else if (val === 3) {
        // 阶梯费率
      }
    },
    // 根据能源类型和项目获取分时方案
    async getTimeShareScheme(id) {
      const params = {
        rootNode: {
          id: this.rootNode.id,
          modelLabel: this.rootNode.modelLabel
        },
        energyType: this.CetForm_1.data.energytype
      };
      const res = await commonApi.schemeConfigTimeShareScheme(params);
      if (!res.data?.length) {
        this.ElOption_4.options_in = [];
        this.ElSelect_4.value = null;
        this.ElSelect_4_change_out(null);
        return;
      }

      this.ElOption_4.options_in = res.data
        .map(item => {
          return {
            id: item.id,
            text: item.name
          };
        })
        .reverse();
      if (id) {
        this.ElSelect_4.value = id;
        this.ElSelect_4_change_out(id);
      } else {
        this.ElSelect_4.value = this.ElOption_4.options_in[0].id;
        this.ElSelect_4_change_out(this.ElOption_4.options_in[0].id);
      }
    },
    // 获取分时方案
    async getTimeShareSchemeDetail(id) {
      if (!id) {
        this.timesharefeerateTableData = [];
        this.timeShareScheme = {};
        return;
      }
      const res = await commonApi.timeShareSchemeDetail(id);
      if (res.code !== 0) {
        return;
      }
      // 处理日时段方案
      if (res.data[0].timeshareperiod_model?.length > 0) {
        res.data[0].timeshareperiod_model.forEach(item => {
          var dayshareset$text = [];
          if (item.dayshareset_model && item.dayshareset_model.length > 0) {
            item.dayshareset_model.forEach(ite => {
              if (Number(ite.beginhour) < 10) {
                ite.beginhour = "0" + Number(ite.beginhour);
              }
              if (Number(ite.beginminute) < 10) {
                ite.beginminute = "0" + Number(ite.beginminute);
              }
              if (Number(ite.endhour) < 10) {
                ite.endhour = "0" + Number(ite.endhour);
              }
              if (Number(ite.endminute) < 10) {
                ite.endminute = "0" + Number(ite.endminute);
              }
              dayshareset$text.push(
                `${ite.beginhour}:${ite.beginminute}~${ite.endhour}:${ite.endminute}`
              );
            });
          }
          item.dayshareset$text = dayshareset$text.join(";");
          if (
            item.timesharefeesinglerate_model &&
            item.timesharefeesinglerate_model.length > 0
          ) {
            item.timesharefeesinglerate = item.timesharefeesinglerate_model[0];
            item.timesharefeesinglerate.rate = Number(
              (item.timesharefeesinglerate.rate / 100000).toFixed(5)
            );
          } else {
            item.timesharefeesinglerate = {};
          }
        });
      }
      this.timesharefeerateTableData = res.data[0].timeshareperiod_model;
      this.timeShareScheme = res.data[0];
    },
    ElSelect_4_change_out(val) {
      this.getTimeShareSchemeDetail(val);
    },
    ElSelect_5_change_out(val) {
      var data = this.ElOption_5.options_in.filter(item => item.id === val)[0];
      this.ElSelect_5.dropItemText = data.name;

      this.CetForm_1.data.feeratetype = 3;
      data.name2 = data.name + $T("费率表");
      this.powertariffData = data;
      this.powertarifffeerateTableData = data.powertarifffactor_model || [];
      this.sortPowertarifffeerateTableData();
    },
    sortPowertarifffeerateTableData() {
      // 按力率大小排序 powerfactor
      var wrap;
      for (var i = 0; i < this.powertarifffeerateTableData.length; i++) {
        for (var k = i + 1; k < this.powertarifffeerateTableData.length; k++) {
          if (
            Math.abs(Number(this.powertarifffeerateTableData[i].powerfactor)) <
            Math.abs(Number(this.powertarifffeerateTableData[k].powerfactor))
          ) {
            wrap = this.powertarifffeerateTableData[k];
            this.powertarifffeerateTableData[k] =
              this.powertarifffeerateTableData[i];
            this.powertarifffeerateTableData[i] = wrap;
          }
        }
      }
      this.powertarifffeerateTableData_copy = this._.cloneDeep(
        this.powertarifffeerateTableData
      );
    },

    CetButton_1_statusTrigger_out(val) {
      this.CetButton_1.visible_in = false;
      this.CetButton_2.visible_in = true;
      this.CetButton_3.visible_in = true;
      this.copyTimesharefeerateTableData = this._.cloneDeep(
        this.timesharefeerateTableData
      );
    },
    CetButton_2_statusTrigger_out(val) {
      this.CetButton_1.visible_in = true;
      this.CetButton_2.visible_in = false;
      this.CetButton_3.visible_in = false;
    },
    CetButton_3_statusTrigger_out(val) {
      this.CetButton_1.visible_in = true;
      this.CetButton_2.visible_in = false;
      this.CetButton_3.visible_in = false;
      this.timesharefeerateTableData = this._.cloneDeep(
        this.copyTimesharefeerateTableData
      );
    },
    CetButton_4_statusTrigger_out(val) {
      this.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            this.stagefeerateTableData.splice(
              this.stagefeerateTableDataItemIndex,
              1
            );
            if (
              this.stagefeerateTableData &&
              this.stagefeerateTableData.length > 0
            ) {
              this.stagefeerateTableData.forEach((item, index) => {
                if (index === 0) {
                  item.name = $T("第一阶梯");
                } else if (index === 1) {
                  item.name = $T("第二阶梯");
                } else if (index === 2) {
                  item.name = $T("第三阶梯");
                } else if (index === 3) {
                  item.name = $T("第四阶梯");
                }
              });
            }
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action !== "confirm") {
            this.$message({
              type: "info",
              message: $T("取消删除")
            });
          }
        }
      });
    },
    CetButton_5_statusTrigger_out(val) {
      this.CetButton_11.visible_in = true;
      this.CetButton_12.visible_in = true;
      this.CetButton_4.visible_in = false;
      this.CetButton_5.visible_in = false;
      this.CetButton_6.visible_in = false;
      this.copyStagefeerateTableData = this._.cloneDeep(
        this.stagefeerateTableData
      );
    },
    CetButton_6_statusTrigger_out(val) {
      this.CetButton_11.visible_in = true;
      this.CetButton_12.visible_in = true;
      this.CetButton_4.visible_in = false;
      this.CetButton_5.visible_in = false;
      this.CetButton_6.visible_in = false;
      this.copyStagefeerateTableData = this._.cloneDeep(
        this.stagefeerateTableData
      );
      if (this.stagefeerateTableData.length === 0) {
        this.stagefeerateTableData.push({
          name: $T("第一阶梯"),
          energyNum: 0,
          rate: 0
        });
      } else if (this.stagefeerateTableData.length === 1) {
        this.stagefeerateTableData.push({
          name: $T("第二阶梯"),
          energyNum: 0,
          rate: 0
        });
      } else if (this.stagefeerateTableData.length === 2) {
        this.stagefeerateTableData.push({
          name: $T("第三阶梯"),
          energyNum: 0,
          rate: 0
        });
      } else if (this.stagefeerateTableData.length === 3) {
        this.stagefeerateTableData.push({
          name: $T("第四阶梯"),
          energyNum: 0,
          rate: 0
        });
      }
    },
    // 导入
    CetButton_7_statusTrigger_out(val) {
      this.uploadDialog.openTrigger_in = new Date().getTime();
    },
    // 加载力调费率
    loadPowerTariff(data) {
      if (data?.length > 0) {
        var newData = [];
        data.forEach((item, index) => {
          item.id = index + 1;
          newData.push(item);
        });
        this.ElOption_5.options_in = this._.cloneDeep(newData);
        this.$set(this.CetForm_1.data, "powertarifffeerate", 1);
        this.$nextTick(() => {
          this.ElSelect_5_change_out(data[0].id);
        });
      }
    },
    CetButton_9_statusTrigger_out(val) {
      this.CetButton_9.visible_in = false;
      this.CetButton_cancelEdit.visible_in = true;
      this.CetButton_10.visible_in = true;
    },
    // 力调电费行点击
    changePowerItem(val) {
      if (val) {
        this.CetButton_delete.disable_in = false;
        this.powertarifffeerateTableData.forEach((item, index) => {
          if (
            item.powerfactor === val.powerfactor &&
            item.punishrate === val.punishrate
          ) {
            this.powertarifffeerateTableDataItemIndex = index;
          }
        });
      } else {
        this.CetButton_delete.disable_in = true;
      }
    },
    // 力调电费删除
    CetButton_delete_statusTrigger_out(val) {
      this.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            this.powertarifffeerateTableData.splice(
              this.powertarifffeerateTableDataItemIndex,
              1
            );
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action !== "confirm") {
            this.$message({
              type: "info",
              message: $T("取消删除")
            });
          }
        }
      });
    },
    // 力调电费新增
    CetButton_add_statusTrigger_out(val) {
      this.powertarifffeerateTableData_copy = this._.cloneDeep(
        this.powertarifffeerateTableData
      );
      this.powertarifffeerateTableData.push({
        powerfactor: "",
        punishrate: ""
      });
      this.CetButton_9_statusTrigger_out();
    },
    CetButton_cancelEdit_statusTrigger_out() {
      this.CetButton_9.visible_in = true;
      this.CetButton_cancelEdit.visible_in = false;
      this.CetButton_10.visible_in = false;
      this.powertarifffeerateTableData = [];
      this.powertarifffeerateTableData_copy.forEach(item => {
        if (item.powerfactor || item.punishrate) {
          this.powertarifffeerateTableData.push(item);
        }
      });
      // this.powertarifffeerateTableData = this._.cloneDeep(this.powertarifffeerateTableData_copy);
    },
    CetButton_10_statusTrigger_out(val) {
      // 利率和调整电费都不能为空
      var isEmpty = false;
      this.powertarifffeerateTableData.forEach(item => {
        if (item.powerfactor.length === 0 || item.punishrate === undefined) {
          isEmpty = true;
        }
      });
      if (isEmpty) {
        return this.$message({
          message: $T("力率和调整电费不能为空"),
          type: "warning"
        });
      }
      // 判断力率是否重复 powerfactor
      var flag = false;
      this.powertarifffeerateTableData.forEach((item, index) => {
        if (
          this.powertarifffeerateTableData.filter(
            i => +i.powerfactor === +item.powerfactor
          ).length >= 2
        ) {
          flag = true;
        }
      });
      if (flag) {
        return this.$message({
          message: $T("力率有重复"),
          type: "warning"
        });
      }
      this.CetButton_9.visible_in = true;
      this.CetButton_10.visible_in = false;
      this.CetButton_cancelEdit.visible_in = false;
      this.sortPowertarifffeerateTableData();
    },
    CetButton_11_statusTrigger_out(val) {
      var wrap;
      if (this.stagefeerateTableData && this.stagefeerateTableData.length > 0) {
        for (let i = 0; i < this.stagefeerateTableData.length; i++) {
          if (wrap) {
            if (
              Number(this.stagefeerateTableData[i].energyNum) <= Number(wrap)
            ) {
              this.$message({
                message: $T("请填写正确阶梯"),
                type: "warning"
              });
              return false;
            }
          }
          wrap = this.stagefeerateTableData[i].energyNum;
        }
      }
      this.CetButton_11.visible_in = false;
      this.CetButton_12.visible_in = false;
      this.CetButton_4.visible_in = true;
      this.CetButton_5.visible_in = true;
      this.CetButton_6.visible_in = true;
    },
    CetButton_12_statusTrigger_out(val) {
      this.CetButton_11.visible_in = false;
      this.CetButton_12.visible_in = false;
      this.CetButton_4.visible_in = true;
      this.CetButton_5.visible_in = true;
      this.CetButton_6.visible_in = true;
      this.stagefeerateTableData = this._.cloneDeep(
        this.copyStagefeerateTableData
      );
    },
    // 输入数字控制
    handleNum(row, key) {
      let value;
      if (typeof row[key] === "object") {
        if (row[key]) {
          value = this._.cloneDeep(row[key].val);
        } else {
          return;
        }
      } else {
        value = this._.cloneDeep(row[key]);
      }

      this.$set(row, key, value);
    },
    async uploadDialog_uploadFile(val) {
      const formData = new FormData();
      formData.append("file", val.file);
      const response = await commonApi.importPowerTariff(formData);
      if (response.code !== 0) return;

      this.$message({
        message: $T("导入成功"),
        type: "success"
      });
      this.loadPowerTariff(response.data);
      this.uploadDialog.closeTrigger_in = Date.now();
    },
    uploadDialog_download() {
      common.downExcel(
        `/eembasecost/v1/fee/scheme/config/exportPowerTariff`,
        ""
      );
    }
  }
};
</script>
<style lang="scss" scoped>
.drawer {
  :deep() {
    .el-drawer__body {
      display: flex;
      flex-direction: column;
      padding: 0;
    }
  }
  .footer {
    border-radius: 0;
  }
}
.tooltipInfo {
  color: #999999;
  font-size: 13px;
  position: relative;
  top: -5px;
}
</style>
