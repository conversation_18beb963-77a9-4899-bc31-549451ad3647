<template>
  <CetDialog
    v-bind="CetDialog_pagedialog"
    v-on="CetDialog_pagedialog.event"
    class="min"
  >
    <template v-slot:footer>
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        class="ml-J1"
        v-bind="CetButton_preserve"
        v-on="CetButton_preserve.event"
      ></CetButton>
    </template>
    <CetForm
      :data.sync="CetForm_pagedialog.data"
      v-bind="CetForm_pagedialog"
      v-on="CetForm_pagedialog.event"
      class=""
    >
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item :label="$T('方案名称')" prop="name">
            <ElInput
              v-model.trim="CetForm_pagedialog.data.name"
              v-bind="ElInput_name"
              v-on="ElInput_name.event"
            ></ElInput>
          </el-form-item>
        </el-col>
      </el-row>
    </CetForm>
  </CetDialog>
</template>

<script>
import common from "eem-base/utils/common";
export default {
  name: "addOrEditScheme",
  components: {},
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    isEdit: {
      type: Boolean
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("新增方案"),
        "show-close": true,
        event: {}
      },
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "component", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入方案名称"),
              trigger: ["blur"]
            },
            common.check_name,
            common.pattern_name
          ]
        },
        event: {
          saveData_out: this.CetForm_pagedialog_saveData_out
        }
      },
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInput_name: {
        placeholder: $T("请输入内容"),
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      if (this.isEdit) {
        this.CetDialog_pagedialog.title = $T("编辑方案");
        this.CetForm_pagedialog.data = this._.cloneDeep(this.inputData_in);
      } else {
        this.CetDialog_pagedialog.title = $T("新增方案");
        this.CetForm_pagedialog.data = {};
      }
      this.CetForm_pagedialog.resetTrigger_in = new Date().getTime();
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      this.CetForm_pagedialog.inputData_in = this._.cloneDeep(val);
    }
  },
  methods: {
    CetForm_pagedialog_saveData_out(val) {
      this.$emit("saveData_out", this._.cloneDeep(val));
    },
    CetButton_preserve_statusTrigger_out(val) {
      this.CetForm_pagedialog.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    }
  }
};
</script>
