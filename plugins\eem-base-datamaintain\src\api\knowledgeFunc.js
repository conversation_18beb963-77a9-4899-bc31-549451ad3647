import fetch from "eem-base/utils/fetch";

import { store } from "@omega/app";
import { FullScreenLoading } from "@omega/http/loading.js";
const loading = new FullScreenLoading();
import { Message } from "element-ui";
import { api } from "@altair/knight";

function download(url, name) {
  const a = document.createElement("a");
  a.target = "_blank";

  if (name) {
    a.download = name;
  }

  a.href = url;
  a.click();
  a.remove();
}

function downloadByBlob(url) {
  fetch({
    url: url,
    method: "GET",
    responseType: "blob",
    headers: {
      "User-Id": store.state.userInfo.id,
      Authorization: `Bearer ${store.state.token}`
    }
  })
    .then(res => {
      let fileName = res.config.url.split("fileName=")[1];
      fileName = fileName.replace(/-\d{13}/, "");
      fileName = decodeURIComponent(fileName);
      loading.hideLoading();

      const blob = res.data;
      // FileReader主要用于将文件内容读入内存
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      // onload当读取操作成功完成时调用
      reader.onload = function (e) {
        download(e.target.result, fileName);
      };
    })
    .catch(err => {
      loading.hideLoading();
      Message({
        message: $T("请检查网络是否连接正常"),
        showClose: true,
        type: "error"
      });
    });
}

function downloadByBlobZip(url, data) {
  fetch({
    url: url,
    method: "POST",
    responseType: "blob",
    headers: {
      "User-Id": store.state.userInfo.id,
      Authorization: `Bearer ${store.state.token}`
    }
  })
    .then(res => {
      loading.hideLoading();

      const blob = res.data;
      // FileReader主要用于将文件内容读入内存
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      // onload当读取操作成功完成时调用
      reader.onload = function (e) {
        let fileName = res.headers["content-disposition"].split("=");
        fileName = "知识库" + fileName[fileName.length - 1];
        download(e.target.result, fileName);
      };
    })
    .catch(err => {
      loading.hideLoading();
      Message({
        message: $T("请检查网络是否连接正常"),
        showClose: true,
        type: "error"
      });
    });
}
// 获取目录列表(目录下的文件列表)
export function getFolder(data) {
  return fetch({
    url: `/eembasedatamaintain/knowledge/folder`,
    method: "POST",
    data: data
  });
}
// 新建目录
export function createFolder(data) {
  return fetch({
    url: `/eembasedatamaintain/knowledge/create/folder`,
    method: "POST",
    data: data
  });
}
// 删除目录（文件）
export function deleteFilemodel(data) {
  return fetch({
    url: `/eembasedatamaintain/knowledge/delete/filemodel`,
    method: "DELETE",
    data: data
  });
}
// 目录(文件)重命名
export function updateFolder(data) {
  return fetch({
    url: `/eembasedatamaintain/knowledge/update/folder`,
    method: "PUT",
    data: data
  });
}
// 获取知识库可以上传的类型
export function getFileTypes(data) {
  return fetch({
    url: `/eembasedatamaintain/knowledge/fileTypes`,
    method: "POST",
    data: data
  });
}

// 获取知识库上传大小限制
export function getMaxSize(data) {
  return fetch({
    url: `/eembasedatamaintain/knowledge/uploadFile/maxSize`,
    method: "GET",
    data: data
  });
}

// 复制多个文件到另一个文件夹
export function fileCopy(data) {
  return fetch({
    url: `/eembasedatamaintain/knowledge/file/copy/many`,
    method: "POST",
    data: data
  });
}

// 移动文件到另一个文件夹
export function fileMove(data) {
  return fetch({
    url: `/eembasedatamaintain/knowledge/file/move/many`,
    method: "POST",
    data: data
  });
}

// 下载知识库文件(多个)
export function knowladgeDownloadMany(data) {
  // let data1 = JSON.stringify(data);
  const url = `/eembasedatamaintain/knowledge/zipByFileIds`;
  downloadByBlobZip(url, data);
}

// 下载知识库文件（单个）
export function knowladgeDownload(data) {
  // let fileName = encodeURIComponent(data.fileName);
  const url = `/eembasedatamaintain/knowledge/download?fileName=${data.fileName}`;
  downloadByBlob(url);
}

// 保存用户的模型节点权限
export function addModelnodes(data) {
  return fetch({
    url: `/eembasedatamaintain/knowledge/modelNode`,
    method: "PUT",
    data: data.modelNodes[0]
  });
}

// 获取用户的视频和知识库列表
export function getVideoPermission() {
  return fetch({
    url: `/eembasedatamaintain/knowledge/auth/fold/list`,
    method: "GET"
  });
}
