<template>
  <div>
    <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
      <el-main class="eem-cont-c1 fullheight">
        <div class="clearfix">
          <CetButton
            class="fr mb-J3"
            v-bind="CetButton_add"
            v-on="CetButton_add.event"
          ></CetButton>
          <CetButton
            v-if="!CetTable_1.data.length"
            class="fr mr-J1"
            v-bind="CetButton_import"
            v-on="CetButton_import.event"
          ></CetButton>
        </div>
        <CetTable
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
          :empty-text="$T('请先导入预置字段！')"
        >
          <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_active">
            <template slot-scope="scope">
              <div v-if="!scope.row.allowdeactivation">
                {{ $T("不允许停用") }}
              </div>
              <div v-if="scope.row.allowdeactivation">
                <el-switch
                  @change="switchChange(scope.row)"
                  v-model="scope.row[ElTableColumn_active.prop]"
                  active-color="#1F96F0"
                  inactive-color="#94A2AD"
                ></el-switch>
              </div>
            </template>
          </ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_display">
            <template slot-scope="scope">
              <el-switch
                :disabled="
                  scope.row.allowdeactivation &&
                  !scope.row[ElTableColumn_active.prop]
                "
                @change="switchChange(scope.row)"
                v-model="scope.row[ElTableColumn_display.prop]"
                active-color="#1F96F0"
                inactive-color="#94A2AD"
              ></el-switch>
            </template>
          </ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_handele">
            <template slot-scope="scope">
              <span
                class="eem-row-handle fl mr-J3"
                :class="{ 'eem-row-no-handle': !scope.row.allowdeactivation }"
                @click="
                  scope.row.allowdeactivation ? handelEdit(scope.row) : null
                "
              >
                {{ $T("编辑") }}
              </span>
              <span
                class="delete fl"
                :class="{
                  'eem-row-no-handle': !scope.row.allowdeactivation,
                  'eem-row-handle': scope.row.allowdeactivation
                }"
                @click="
                  scope.row.allowdeactivation ? handelDelete(scope.row) : null
                "
              >
                {{ $T("删除") }}
              </span>
            </template>
          </ElTableColumn>
        </CetTable>
      </el-main>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
    <add
      :visibleTrigger_in="add.visibleTrigger_in"
      :editData_in="add.editData_in"
      :closeTrigger_in="add.closeTrigger_in"
      @updata_out="getData"
    />
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import add from "./add.vue";
export default {
  name: "templateAdmin",
  components: {
    add
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    }
  },
  data(vm) {
    return {
      add: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        editData_in: null
      },
      // 1弹窗组件
      CetDialog_1: {
        title: $T("模板管理"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          close: this.CetDialog_1_close_out
        }
      },
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("新增"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      CetButton_import: {
        visible_in: true,
        disable_in: false,
        title: $T("导入预制字段"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_import_statusTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: $T("合计")
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {},
        style: {
          height: "500px"
        }
      },
      ElTableColumn_name: {
        prop: "alias", // 支持path a[0].b
        minWidth: "2", //该宽度会自适应
        label: $T("名称"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      ElTableColumn_active: {
        prop: "active", // 支持path a[0].b
        label: $T("启用"),
        minWidth: "1", //该宽度会自适应
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      ElTableColumn_display: {
        prop: "display", // 支持path a[0].b
        label: $T("显示"),
        minWidth: "1", //该宽度会自适应
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },

      ElTableColumn_handele: {
        width: "120", //绝对宽度
        label: $T("操作"),
        fixed: "right",
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      }
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      vm.getData();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },
  methods: {
    // 导入系统预制模板字段
    CetButton_import_statusTrigger_out() {
      customApi.dashboardImportFixedTemplate(this.projectId).then(response => {
        if (response.code === 0) {
          this.$message({
            type: "success",
            message: $T("导入成功！")
          });
          this.getData();
        }
      });
    },
    getData() {
      customApi.getDeviceTemplate(this.projectId).then(response => {
        if (response.code === 0) {
          const data = this._.get(response, "data", []);
          this.CetTable_1.data = data;
          this.CetButton_add.disable_in = data.length <= 0;
        }
      });
    },
    switchChange(row) {
      // 修改单条状态后刷新列表
      customApi.editDeviceTemplate([row], this.projectId).then(response => {
        if (response.code === 0) {
          this.getData();
        }
      });
    },
    CetDialog_1_close_out(val) {
      this.$emit("updata_out");
    },
    handelEdit(row) {
      this.add.editData_in = this._.cloneDeep(row);
      this.add.visibleTrigger_in = new Date().getTime();
    },
    handelDelete(row) {
      let bool = false;
      this.$confirm($T("是否删除此模板?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          customApi.deleteDeviceTemplate([row.id], bool).then(response => {
            if (response.code === 0) {
              this.$message({
                type: "success",
                message: $T("删除成功")
              });
              this.getData();
            } else if (response.code === -5) {
              this.$confirm(response.msg, $T("提示"), {
                confirmButtonText: $T("确定"),
                cancelButtonText: $T("取消"),
                type: "warning"
              })
                .then(() => {
                  bool = true;
                  customApi
                    .deleteDeviceTemplate([row.id], bool)
                    .then(response => {
                      if (response.code === -5) {
                        this.$message({
                          type: "success",
                          message: $T("删除成功")
                        });
                        this.getData();
                      }
                    });
                })
                .catch(() => {
                  this.$message({
                    type: "info",
                    message: $T("已取消删除")
                  });
                });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已取消删除")
          });
        });
    },
    CetButton_add_statusTrigger_out(val) {
      this.add.editData_in = null;
      this.add.visibleTrigger_in = new Date().getTime();
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = val;
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },
  activated: function () {}
};
</script>
<style lang="scss" scoped>
.eem-row-handle.delete {
  @include font_color(Sta3);
}
</style>
