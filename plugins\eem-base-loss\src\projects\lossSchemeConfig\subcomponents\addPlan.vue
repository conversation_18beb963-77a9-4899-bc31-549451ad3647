<template>
  <div>
    <CetDialog
      class="CetDialog"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
      :before-close="beforeClose"
    >
      <div class="mt-J1">
        <div class="title">{{ $T("基础信息") }}</div>
        <CetForm
          :data.sync="CetForm_1.data"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
        >
          <el-row :gutter="16">
            <el-col :span="10">
              <div class="mt-J3 mb-J3">
                <div class="label mb-J1 mr-J2">
                  <span style="color: red">*</span>
                  {{ $T("方案名称") }}
                </div>
                <div class="flex-auto value">
                  <el-form-item prop="name">
                    <ElInput
                      v-model.trim="CetForm_1.data.name"
                      v-bind="ElInput_1"
                      v-on="ElInput_1.event"
                      show-word-limit
                    ></ElInput>
                  </el-form-item>
                </div>
              </div>
            </el-col>
          </el-row>
        </CetForm>
      </div>
      <div class="flex-row flex mt-J1">
        <div style="width: 250px">
          <div class="title">{{ $T("被分摊信息") }}</div>
          <el-row :gutter="16">
            <el-col :span="24">
              <div class="label">
                <span style="color: red">*</span>
                {{ $T("被分摊能源类型") }}
              </div>
              <div class="value mt-J1">
                <ElSelect
                  class="mr-J1"
                  v-model="queryBody.shareEnergyType"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                >
                  <ElOption
                    v-for="item in ElOption_1.options_in"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </ElSelect>
              </div>
            </el-col>
          </el-row>
          <div class="mt-J1">
            <div class="label">
              <span style="color: red">*</span>
              {{ $T("关联被分摊对象") }}
              <el-popover
                class="popover ml-J1"
                placement="bottom-start"
                trigger="hover"
                :content="$T('被分摊对象的节点树只会展示有子节点的内容')"
              >
                <i slot="reference" class="el-icon-question"></i>
              </el-popover>
            </div>
            <div class="value mt-J1 value_border">
              <div class="label tree_title">
                {{ $T("被分摊对象关联") }}
              </div>
              <CetTree
                style="height: calc(100% - 36px)"
                :selectNode.sync="CetTree_1.selectNode"
                :checkedNodes.sync="CetTree_1.checkedNodes"
                :searchText_in.sync="CetTree_1.searchText_in"
                v-bind="CetTree_1"
                v-on="CetTree_1.event"
                ref="cetTree"
              ></CetTree>
            </div>
          </div>
        </div>
        <div class="flex-auto ml-J3">
          <div class="title">{{ $T("分摊信息") }}</div>
          <el-row :gutter="16">
            <el-col :span="enLanguage ? 7 : 5">
              <div class="label">
                <span style="color: red">*</span>
                {{ $T("分摊方式") }}
              </div>
              <div class="value mt-J1">
                <el-radio-group
                  v-model="queryBody.energyShareMethod"
                  class="fullwidth method_radio"
                >
                  <template v-for="item in options_method">
                    <el-radio-button
                      :key="item.value"
                      :label="item.value"
                      style="width: 50%"
                    >
                      {{ item.label }}
                    </el-radio-button>
                  </template>
                </el-radio-group>
              </div>
            </el-col>
            <el-col :span="7">
              <div class="label">
                <span style="color: red">*</span>
                {{ $T("生效时间") }}
              </div>
              <div class="value mt-J1 flex flex-row items-center">
                <CustomElDatePicker
                  v-show="!queryBody.isForever"
                  v-bind="CetDatePicker_time.config"
                  v-model="queryBody.effectiveTime"
                  style="width: 200px"
                />
                <CustomElDatePicker
                  v-show="queryBody.isForever"
                  v-bind="CetDatePicker_time2.config"
                  v-model="queryBody.startTime"
                  style="width: 200px"
                />
                <el-checkbox
                  class="ml-J3"
                  v-model="queryBody.isForever"
                  @change="foreverChange"
                >
                  {{ $T("永远") }}
                </el-checkbox>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="label">
                <span style="color: red">*</span>
                {{ $T("分摊时间间隔") }}
              </div>
              <div class="value mt-J1">
                <el-radio-group
                  v-model="queryBody.shareCalcCycle"
                  class="fullwidth method_radio"
                >
                  <template v-for="item in options_interval">
                    <el-radio-button
                      :key="item.value"
                      :label="item.value"
                      style="width: 33%"
                    >
                      {{ item.label }}
                    </el-radio-button>
                  </template>
                </el-radio-group>
              </div>
            </el-col>
            <el-col :span="6" v-if="false">
              <div class="label">
                <span style="color: red">*</span>
                {{ $T("分摊能源类型") }}
              </div>
              <div class="value mt-J1">
                <ElSelect
                  class="mr-J1"
                  v-model="queryBody.energytype"
                  v-bind="ElSelect_2"
                  v-on="ElSelect_2.event"
                >
                  <ElOption
                    v-for="item in ElOption_1.options_in"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </ElSelect>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <div class="label">
                <span style="color: red">*</span>
                {{ $T("关联分摊对象") }}
                <el-popover
                  class="popover ml-J1"
                  placement="bottom-start"
                  trigger="hover"
                  :content="$T('左侧选中的被分摊节点的下一级节点列表')"
                >
                  <i slot="reference" class="el-icon-question"></i>
                </el-popover>
              </div>
              <div class="value mt-J1 transfer_flex" v-if="testTransfer">
                <TreeTransfer
                  ref="treeTransfer"
                  width="550px"
                  height="300px"
                  :openAll="false"
                  :data-source.sync="objectData"
                  node-key="tree_id"
                  :titles="[$T('对象列表'), $T('已选对象列表')]"
                  :defaultProps="{
                    label: 'name',
                    children: 'children',
                    disabled: 'disabled'
                  }"
                  :father-choose="true"
                  :listSortFifo="false"
                  :default-checked-keys="defaultChecked"
                  :default-expanded-keys="defaultChecked"
                  :filterable="true"
                  @change="handleChange"
                ></TreeTransfer>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="label">
                <span style="color: red">*</span>
                {{ $T("分摊节点列表") }}
              </div>
              <div class="value mt-J1">
                <el-table
                  :data="rightSelectedNode"
                  border
                  height="300"
                  class="table_style"
                >
                  <el-table-column
                    :label="$T('序号')"
                    type="index"
                    width="70"
                  ></el-table-column>
                  <template v-if="queryBody.energyShareMethod == 1">
                    <el-table-column
                      :show-overflow-tooltip="true"
                      :label="$T('分摊对象')"
                      prop="name"
                      header-align="left"
                      align="left"
                    ></el-table-column>
                    <el-table-column
                      :show-overflow-tooltip="true"
                      :label="$T('分摊对象')"
                      prop="rate"
                      header-align="left"
                      align="left"
                    >
                      <template slot="header">
                        <div>
                          {{ $T("分摊比例") }}
                          <el-tooltip
                            width="200"
                            :content="$T(titleMsg)"
                            effect="light"
                          >
                            <i class="el-icon-question"></i>
                          </el-tooltip>
                        </div>
                      </template>
                      <template slot-scope="scope">
                        <el-input
                          v-model="scope.row.rate"
                          @keyup.native="handleNum(scope.row, 'rate', 4)"
                          @blur="handleNum(scope.row, 'rate', 4)"
                          :placeholder="$T('输入系数')"
                        ></el-input>
                      </template>
                    </el-table-column>
                  </template>
                  <template v-if="queryBody.energyShareMethod == 2">
                    <el-table-column
                      :show-overflow-tooltip="true"
                      :label="$T('分摊对象')"
                      prop="name"
                      header-align="left"
                      align="left"
                    ></el-table-column>
                  </template>
                </el-table>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="ml-J1"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom";
import TreeTransfer from "eem-base/components/TreeTransfer";
import common from "eem-base/utils/common";
import omegaI18n from "@omega/i18n";

export default {
  name: "addPlan",
  components: {
    TreeTransfer
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    rootNode_in: Object
  },
  computed: {
    enLanguage() {
      return omegaI18n.locale === "en";
    },
    standardEnergyType() {
      return this.$store.state.standardEnergyType;
    }
  },
  data(vm) {
    return {
      CetDialog_1: {
        title: $T("新增方案"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        top: "10vh",
        width: "1440px",
        showClose: true,
        "append-to-body": true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        maxlength: 50,
        placeholder: $T("请输入方案名称"),
        style: {
          width: "100%"
        },
        event: {}
      },
      // 被分摊能源类型
      ElSelect_1: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        label: "text",
        value: "id",
        disabled: "disabled"
      },
      // 1树组件
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      options_method: [
        {
          value: 1,
          label: $T("固定比例")
        },
        {
          value: 2,
          label: $T("动态分摊")
        }
      ],
      // 选择时段
      CetDatePicker_time: {
        val: null,
        config: {
          valueFormat: "timestamp",
          type: "daterange",
          clearable: false,
          unlinkPanels: true,
          rangeSeparator: $T("至"),
          // 时间范围限制
          pickerOptions: {
            // 禁止选择器选中同一天
            onPick: ({ maxDate, minDate }) => {
              if (minDate && !maxDate) {
                vm.disabledTime = minDate;
              }
              if (maxDate) {
                vm.disabledTime = null;
              }
            },
            disabledDate(time) {
              let disabledTime = vm.disabledTime;
              if (disabledTime) {
                return time.getTime() === disabledTime.getTime();
              }
            }
          }
        }
      },
      // 开始时间
      CetDatePicker_time2: {
        val: null,
        config: {
          valueFormat: "timestamp",
          type: "date",
          clearable: false
        }
      },
      // 永远时间
      foreverTime: 9999000000000,
      // 动态分摊间隔
      options_interval: [
        {
          value: 7,
          label: $T("小时")
        },
        {
          value: 12,
          label: $T("日")
        },
        {
          value: 14,
          label: $T("月")
        }
      ],
      // 分摊能源类型
      ElSelect_2: {
        value: "",
        style: {
          width: "250px"
        },
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      objectData: [],
      // 分摊节点列表
      tableData: [],
      tableDataColumns: [
        {
          title: $T("分摊对象"),
          key: "name",
          width: 100
        },
        {
          title: $T("分摊比例"),
          key: "rate",
          width: 100
        }
      ],
      // 关联被分摊对象当前选中节点
      currentNode: {},
      // 关联对象中已被选择的节点
      rightSelectedNode: [],
      testTransfer: false,
      // 穿梭框默认选择节点
      defaultChecked: [],
      titleMsg: $T("比例相加必须等于1，单个比例必须大于0"),
      //  定义是否进行重新重算弹窗
      whetherToRecal: false,
      // 判断编辑时是否需要清空
      isClearRightData: false,
      // 判断关联被分摊对象节点是否是最后一层级需要调用接口查询
      isLastNode: false,
      queryBody: {
        name: "",
        energyShareMethod: 1,
        effectiveTime: [
          this.$moment().startOf("day").valueOf(),
          this.$moment().add(1, "day").startOf("day").valueOf()
        ],
        startTime: this.$moment().startOf("day").valueOf(),
        shareCalcCycle: 7,
        shareToObjectVoList: [],
        energytype: null,
        isForever: true
      },
      // 按张壮要求对方案名称进行输入校验
      // 1表单组件
      CetForm_1: {
        dataMode: "static", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入方案名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ]
        },
        event: {
          // saveData_out: this.CetForm_1_saveData_out
        }
      },
      // 定义两个缓存数据来缓存关联分摊对象和节点列表数据，3.5迭代中测试需要切换节点回来数据仍旧回显的操作
      cachesObjectId: null,
      cachesDefaultChecked: [],
      cachesRightSelectedNode: []
    };
  },
  watch: {
    openTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = this._.cloneDeep(val);
      this.CetForm_1.data = {};
      this.CetForm_1.resetTrigger_in = Date.now();
      this.currentNode = {};
      this.isClearRightData = false;
      this.cachesObjectId = null;
      this.cachesDefaultChecked = [];
      this.cachesRightSelectedNode = [];
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
      this.testTransfer = false;
    },
    async inputData_in(val) {
      // 重新构建穿梭框
      await this.init();
      this.testTransfer = true;
      this.defaultChecked = [];
      this.whetherToRecal = false;
      // this.currentNode = {};
      if (val.id === 0 || val.id) {
        this.CetDialog_1.title =
          val.id === 0 ? $T("新增分摊方案") : $T("修改分摊方案");
        if (val.id && val.startTime <= this.$moment().valueOf()) {
          this.whetherToRecal = true;
        }
        this.queryBody = this._.cloneDeep(val);
        // 要使用set进行双向绑定处理，否则无法响应
        this.$set(this.queryBody, "effectiveTime", [
          val.startTime,
          val.endTime
        ]);
        this.$set(this.CetForm_1.data, "name", val.name);
        this.CetTree_1.selectNode = {
          id: val.shareObjectId,
          modelLabel: val.shareObjectLabel,
          tree_id: val.shareObjectLabel + "_" + val.shareObjectId
        };
        this.isClearRightData = true;
        let data = [];
        val.shareToObjectVoList.forEach(item => {
          data.push({
            id: item.shareToObjectId,
            modelLabel: item.shareToObjectLabel,
            name: item.shareToObjectName,
            rate: item.rate
          });
          let tree_id = item.shareToObjectLabel + "_" + item.shareToObjectId;
          this.defaultChecked.push(tree_id);
        });
        this.rightSelectedNode = data;
        this.cachesObjectId = val.shareObjectLabel + "_" + val.shareObjectId;
        this.cachesDefaultChecked = this._.cloneDeep(this.defaultChecked);
        this.cachesRightSelectedNode = this._.cloneDeep(data);
      } else {
        this.CetDialog_1.title = $T("新增分摊方案");
        // 新增方案进行默认值处理
        this.queryBody.name = "";
        this.queryBody.energyShareMethod = 1;
        this.queryBody.effectiveTime = [
          this.$moment().startOf("day").valueOf(),
          this.$moment().add(1, "day").startOf("day").valueOf()
        ];
        this.queryBody.startTime = this.$moment().startOf("day").valueOf();
        this.queryBody.shareCalcCycle = 7;
        this.queryBody.shareToObjectVoList = [];
        // this.currentNode = {};
        this.rightSelectedNode = [];
        this.queryBody.isForever = true;
      }
    }
  },
  methods: {
    async init() {
      await this.getApportionedEnergy();
      await this.getTreeData1();
    },
    CetButton_confirm_statusTrigger_out() {
      let param = this.updateQueryBody();
      if (!param) {
        return;
      }
      customApi.checkLossShareConfigSchemeConfig(param).then(res => {
        if (res.code === 0) {
          let result = this._.get(res, "data", {});
          if (result.checkType === 1) {
            this.ischeckRecal(param);
          } else if (result.checkType === 2) {
            let str = "";
            str += `<div>${$T("当前分摊方案与")}${result.configName}${$T(
              "在生效时间上存在交叉"
            )}</div>
            <div>${$T("当前方案生效时间")}：${this.getTimeText(
              param.startTime
            )} ~ ${
              this.queryBody.isForever
                ? $T("永远")
                : this.getTimeText(param.endTime)
            }</div>
            <div>${$T("冲突方案生效时间")}：${this.getTimeText(
              result.startTime
            )} ~ ${this.getTimeText(result.endTime)}</div></br>
            <div>${$T("是否可以修改冲突方案的生效时间")}</div>
            <div>${$T("修改之后两个方案生效时间")}：</div>
            <div>${$T("当前方案生效时间")}：${this.getTimeText(
              param.startTime
            )} ~ ${
              this.queryBody.isForever
                ? $T("永远")
                : this.getTimeText(param.endTime)
            }</div>
            <div>${$T("冲突方案生效时间")}：${this.getTimeText(
              result.startTimeEdit
            )} ~ ${this.getTimeText(result.endTimeEdit)}</div></br>`;
            this.$confirm(str, $T("提示"), {
              confirmButtonText: $T("确定"),
              cancelButtonText: $T("取消"),
              type: "warning",
              dangerouslyUseHTMLString: true
            })
              .then(() => {
                this.ischeckRecal(param);
              })
              .catch(() => {});
          } else if (result.checkType === 3) {
            let str = "";
            if (result.desc) {
              str = result.desc;
            } else {
              str += `<div>${$T("当前分摊方案与")}${result.configName}${$T(
                "在生效时间上存在交叉"
              )}</div>
            <div>${$T("当前方案生效时间")}：${this.getTimeText(
                param.startTime
              )} ~ ${
                this.queryBody.isForever
                  ? $T("永远")
                  : this.getTimeText(param.endTime)
              }</div>
            <div>${$T("冲突方案生效时间")}：${this.getTimeText(
                result.startTime
              )} ~ ${this.getTimeText(result.endTime)}</div></br>
            <div>${$T("请修改当前方案的生效时间")}</div>`;
            }

            this.$confirm(str, $T("提示"), {
              confirmButtonText: $T("确定"),
              showCancelButton: false,
              type: "warning",
              dangerouslyUseHTMLString: true
            })
              .then(() => {})
              .catch(() => {});
          }
        }
      });
    },
    ischeckRecal(param) {
      this.confirmSheme(param);

      // 重算功能未实现，先屏蔽提示
      // if (param.id) {
      //   if (this.whetherToRecal) {
      //     this.$confirm(
      //       $T("该方案正在生效中,生效时间已修改,请点击重算"),
      //       $T("重算提示"),
      //       {
      //         type: "warning",
      //         distinguishCancelAndClose: true,
      //         confirmButtonText: $T("确定"),
      //         showClose: false,
      //         showCancelButton: false,
      //         closeOnClickModal: false,
      //         closeOnPressEscape: false
      //       }
      //     )
      //       .then(() => {
      //         this.confirmSheme(param);
      //       })
      //       .catch(() => {});
      //   } else {
      //     this.confirmSheme(param);
      //   }
      // } else {
      //   this.confirmSheme(param);
      // }
    },
    getTimeText(time) {
      if (time === 9999000000000) {
        return $T("永远");
      } else {
        return this.$moment(time).format("YYYY-MM-DD");
      }
    },
    // 更新方案条件
    updateQueryBody() {
      let param = {
        id: 0,
        shareCalcCycle: this.queryBody.shareCalcCycle,
        // createtime: this.$moment().valueOf(),
        endTime: this.queryBody.isForever
          ? this.foreverTime
          : this.$moment(this.queryBody.effectiveTime[1])
              .startOf("day")
              .valueOf(),
        startTime: this.queryBody.isForever
          ? this.queryBody.startTime
          : this.$moment(this.queryBody.effectiveTime[0])
              .startOf("day")
              .valueOf(),
        energyShareMethod: this.queryBody.energyShareMethod,
        shareToObjectVoList: [],
        // shareEnergyType: this.queryBody.energytype,
        name: this.CetForm_1.data.name,
        shareEnergyType: this.queryBody.shareEnergyType,
        shareObjectId: this.currentNode.id,
        shareObjectLabel: this.currentNode.modelLabel

        // objectname: this.currentNode.name
      };
      if (!param.shareObjectId) {
        this.$message.error($T("被分摊对象不能为空"));
        return false;
      }
      if (!(this.rightSelectedNode && this.rightSelectedNode.length > 0)) {
        this.$message.error($T("分摊对象不能为空"));
        return false;
      }
      if (!param.name) {
        this.$message.error($T("方案名称不能为空"));
        return false;
      }
      if (!param.shareEnergyType) {
        this.$message.error($T("能源类型不能为空"));
        return false;
      }
      if (this.inputData_in && this.inputData_in.id) {
        param.id = this.inputData_in.id;
      }
      if (param.energyShareMethod == 1) {
        let num = 0;
        for (let i = 0; i < this.rightSelectedNode.length; i++) {
          if (
            this.rightSelectedNode[i].id == param.shareObjectId &&
            this.rightSelectedNode[i].modelLabel == param.shareObjectLabel
          ) {
            this.$message.error($T("分摊对象与被分摊对象不能是同一个节点"));
            return false;
          }
          if (Number(this.rightSelectedNode[i].rate) == 0) {
            this.$message.error($T("单个比例必须大于0"));
            return false;
          }
          param.shareToObjectVoList.push({
            shareToObjectLabel: this.rightSelectedNode[i].modelLabel,
            shareToObjectId: this.rightSelectedNode[i].id,
            // objectname: this.rightSelectedNode[i].name,
            rate: Number(this.rightSelectedNode[i].rate)
          });
          num += Number(this.rightSelectedNode[i].rate) * 1000;
        }
        if (num / 1000 !== 1) {
          this.$message.error($T("分摊比例总和必须为1"));
          return false;
        }
        // param.energytype = this.queryBody.shareEnergyType;
      } else if (param.energyShareMethod == 2) {
        for (let i = 0; i < this.rightSelectedNode.length; i++) {
          if (
            this.rightSelectedNode[i].id == param.shareObjectId &&
            this.rightSelectedNode[i].modelLabel == param.shareObjectLabel
          ) {
            this.$message.error($T("分摊对象与被分摊对象不能是同一个节点"));
            return false;
          }
          param.shareToObjectVoList.push({
            shareToObjectLabel: this.rightSelectedNode[i].modelLabel,
            shareToObjectId: this.rightSelectedNode[i].id
            // objectname: this.rightSelectedNode[i].name
          });
        }
        // param.energytype = this.queryBody.energytype;
      }
      return param;
    },
    // 方案保存
    confirmSheme(param) {
      customApi
        .saveLossShareConfigScheme({
          config: param,
          rootNode: {
            id: this.rootNode_in.id,
            modelLabel: this.rootNode_in.modelLabel
          }
        })
        .then(res => {
          if (res.code === 0) {
            this.$message({
              message: $T("保存成功"),
              type: "success"
            });
            this.$emit("finishTrigger_out");
            this.CetDialog_1.closeTrigger_in = new Date().getTime();
            this.testTransfer = false;
          }
        });
    },
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
      this.testTransfer = false;
    },
    filterMethod() {},
    // 输入数字控制
    handleNum(row, key, num) {
      var value;
      if (typeof row[key] === "object") {
        if (row[key]) {
          value = row[key].val;
        } else {
          return;
        }
      } else {
        value = row[key];
      }
      var reg = new RegExp("^(\\-)*(\\d+)\\.(\\d{0," + num + "}).*$");
      var reg2 = new RegExp("^(\\d{0,1})(\\d+)\\.(\\d{0," + num + "}).*$");
      var reg3 = new RegExp("^(\\d{0,1})(\\d+)$");
      var val = String(value);
      if (val) {
        val = val.replace(/[^\d.]/g, ""); //清除数字和'.'以外的字符
        val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", "."); //只保留顺位第一的'.'
        val = val.replace(reg, "$1$2.$3"); //只能输入两位位小数
      }
      if (val.length >= 2 && val.indexOf(".") != 1 && val[0] == 0) {
        //在非小数的时候清除前导0
        val = val.replace(/0/, "");
      }
      if (val.length >= 2 && val.indexOf(".") == 0) {
        // 在先输入小数点时补0
        val = Number(val);
      }
      if (typeof row[key] === "object") {
        if (String(val).indexOf(".") != -1 && Number(val) > 1) {
          val = val.replace(reg2, "$1.$3");
        } else if (String(val).indexOf(".") == -1 && String(val).length > 1) {
          val = val.replace(reg3, "$1");
        }
        row[key].val = val;
      } else {
        if (String(val).indexOf(".") != -1 && Number(val) > 1) {
          val = val.replace(reg2, "$1.$3");
        } else if (String(val).indexOf(".") == -1 && String(val).length > 1) {
          val = val.replace(reg3, "$1");
        }
        row[key] = val;
      }
    },
    ElSelect_1_change_out() {
      this.$refs.treeTransfer.clearReset();
      this.rightSelectedNode = [];
      this.getTreeData1();
    },
    // 被关联对象节点树获取
    async getTreeData1() {
      if (!this.queryBody.shareEnergyType) {
        return;
      }
      let data = {
        energyType: this.queryBody.shareEnergyType,
        rootNode: {
          id: this.rootNode_in?.id,
          modelLabel: this.rootNode_in?.modelLabel
        }
      };
      await customApi
        .lossConfigProjectTreeWithoutgroup(data, { keepTransformer: false })
        .then(async response => {
          if (response.code === 0) {
            let data = response.data?.[0];
            let treeData = [];
            if (data && data.children && data.children.length > 0) {
              treeData.push(data);
              this.CetTree_1.inputData_in = treeData[0].children;
              let selectNode = {};
              if (this.inputData_in?.id != null) {
                selectNode = {
                  id: this.inputData_in.shareObjectId,
                  modelLabel: this.inputData_in.shareObjectLabel,
                  tree_id:
                    this.inputData_in.shareObjectLabel +
                    "_" +
                    this.inputData_in.shareObjectId
                };
              } else {
                selectNode = treeData[0].children[0];
              }
              this.CetTree_1.selectNode = selectNode;
            } else {
              this.CetTree_1.inputData_in = [];
              this.CetTree_1.selectNode = {};
              this.currentNode = {};
            }
            this.objectData = await this.getFirstChild(
              this.CetTree_1.selectNode
            );
          }
        });
    },
    // 只获取当前节点下的第一层子节点
    async getFirstChild(data) {
      if (!data.id) {
        return [];
      }
      this.isLastNode = false;
      let result = [];
      let param = {
        node: {
          id: data.id,
          modelLabel: data.modelLabel
        },
        energyType: this.queryBody.shareEnergyType
      };
      await customApi.getLossShareConfigTopologyChild(param).then(response => {
        if (response.code === 0) {
          let node = this._.get(response, "data");
          node.forEach(item => {
            let key = this._.cloneDeep(item);
            key.children = [];
            result.push(key);
          });
          this.isLastNode = true;
        }
      });
      return result;
    },
    async CetTree_1_currentNode_out(val) {
      if (_.isEmpty(this.currentNode)) {
        this.currentNode = val;
        return;
      }
      if (this.currentNode.tree_id !== val.tree_id) {
        this.currentNode = val;
        const data = await this.getFirstChild(this.currentNode);
        this.objectData = data;
        this.$refs.treeTransfer?.clearReset();
      }
      if (this.isClearRightData) {
        this.isClearRightData = false;
      } else {
        this.rightSelectedNode = [];
        if (this.cachesObjectId && val.tree_id === this.cachesObjectId) {
          this.defaultChecked = this._.cloneDeep(this.cachesDefaultChecked);
          this.rightSelectedNode = this._.cloneDeep(
            this.cachesRightSelectedNode
          );
        }
      }
    },
    // 获取能源类型
    async getApportionedEnergy() {
      await customApi.getProjectEnergy().then(res => {
        if (res.code === 0) {
          let data = this._.get(res, "data", []);
          let selectData = [];
          if (data.length > 0) {
            data.forEach(item => {
              if (!this.standardEnergyType.includes(item.energytype)) {
                selectData.push({
                  id: item.energytype,
                  text: item.name
                });
              }
            });
          }
          this.ElOption_1.options_in = this._.cloneDeep(selectData);
          let shareEnergyType = selectData.length ? selectData[0].id : null;
          this.queryBody.shareEnergyType =
            this.inputData_in.id === 0 || this.inputData_in.id
              ? this.inputData_in.shareEnergyType
              : shareEnergyType;
        }
      });
    },
    handleChange(value, direction, currentKeys) {
      let data = this._.cloneDeep(value);
      let result = [];
      data.forEach(item => {
        let testData = this.isLastNode
          ? this.objectData.find(key => key.tree_id === item)
          : this.$refs.cetTree.$refs.tree.getNode(item).data;
        result.push({
          id: testData.id,
          modelLabel: testData.modelLabel,
          name: testData.name,
          rate: 0
        });
      });
      // 除去第一次以外，
      // 把选中的节点和之前选中的节点进行比较，
      // 如果有相同的，就更新rate，
      if (this.rightSelectedNode.length) {
        result.forEach(item => {
          this.rightSelectedNode.forEach(node => {
            if (item.id === node.id && item.modelLabel === node.modelLabel) {
              item.rate = node.rate;
            }
          });
        });
      }
      this.rightSelectedNode = result;
    },
    // 弹窗关闭之前的调用方法
    beforeClose(done) {
      this.testTransfer = false;
      done();
    },
    // 勾选永远的交互逻辑
    foreverChange(val) {
      if (val) {
        let date = this.queryBody.effectiveTime;
        this.queryBody.startTime = date[0];
      } else {
        let date = this.queryBody.startTime;
        let end = this.$moment(date).add(1, "day").startOf("day").valueOf();
        this.queryBody.effectiveTime = [date, end];
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.title {
  font-weight: bold;
  font-size: 16px;
}
.label {
  font-weight: 400;
  font-size: 14px;
  margin-top: 16px;
}
.value_border {
  height: 300px;
  border: 1px solid;
  @include border_color(B1);
  border-radius: var(--Ra);
}
.tree_title {
  border-bottom: 1px solid;
  @include border_color(B1);
  @include background_color(BG);
  padding: 8px;
  margin-top: 0px;
}
.method_radio {
  :deep(.el-radio-button__inner) {
    width: 100%;
  }
}
.table_style {
  :deep(.el-table__body-wrapper) {
    height: 258px !important;
  }
}
</style>
