<template>
  <ElDrawer
    class="drawer"
    :title="$T('详情')"
    :visible.sync="openDrawer"
    destroy-on-close
    size="800px"
  >
    <div>
      <div class="title">{{ $T("基础信息") }}</div>
      <el-row :gutter="16">
        <el-col :span="24">
          <div class="label">
            {{ $T("方案名称") }}
          </div>
          <div class="value">
            {{ formatText(detailData.name) }}
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="mt-J0">
      <div class="title">{{ $T("被分摊信息") }}</div>
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="label">
            {{ $T("被分摊能源类型") }}
          </div>
          <div class="value">
            {{ formatText(detailData.objectenergytypeText) }}
          </div>
        </el-col>
        <el-col :span="6">
          <div class="label">
            {{ $T("被分摊对象") }}
          </div>
          <div class="value">
            <el-tooltip
              :content="formatText(detailData.objectname)"
              placement="bottom-start"
            >
              <div class="text-ellipsis">
                {{ formatText(detailData.objectname) }}
              </div>
            </el-tooltip>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="mt-J0 flex flex-col flex-auto">
      <div class="title">{{ $T("分摊信息") }}</div>
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="label">
            {{ $T("分摊类型") }}
          </div>
          <div class="value">
            {{ formatText(detailData.energysharemethodText) }}
          </div>
        </el-col>
        <el-col :span="6">
          <div class="label">
            {{ $T("生效时间") }}
          </div>
          <div class="value">
            {{ formatText(detailData.timeText) }}
          </div>
        </el-col>
        <el-col :span="6">
          <div class="label">
            {{ $T("分摊时间间隔") }}
          </div>
          <div class="value">
            {{ formatText(detailData.cycleText) }}
          </div>
        </el-col>
        <el-col :span="6" v-if="false">
          <div class="label">
            {{ $T("分摊能源类型") }}
          </div>
          <div class="value">
            {{ formatText(detailData.energytypeText) }}
          </div>
        </el-col>
      </el-row>
      <CetTable
        class="mt-J3 flex-auto overflow-auto"
        :data.sync="CetTable_2.data"
        :dynamicInput.sync="CetTable_2.dynamicInput"
        v-bind="CetTable_2"
        v-on="CetTable_2.event"
      >
        <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
        <ElTableColumn v-bind="ElTableColumn_objectname"></ElTableColumn>
        <ElTableColumn
          v-if="detailData && detailData.energyShareMethod == 1"
          v-bind="ElTableColumn_rate"
        ></ElTableColumn>
      </CetTable>
    </div>
  </ElDrawer>
</template>

<script>
export default {
  name: "detailView",
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      openDrawer: false,
      detailData: {},
      CetTable_2: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        label: $T("序号"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "70" //绝对宽度
      },
      ElTableColumn_objectname: {
        prop: "shareToObjectName", // 支持path a[0].b
        label: $T("分摊对象"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.shareToObjectName) {
            return val.shareToObjectName;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_rate: {
        prop: "rate", // 支持path a[0].b
        label: $T("分摊比例"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.rate || val.rate === 0) {
            return val.rate;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      }
    };
  },
  watch: {
    openTrigger_in() {
      this.openDrawer = true;
    },
    closeTrigger_in() {
      this.openDrawer = false;
    },
    inputData_in(val) {
      this.detailData = val;
      if (val && val.shareToObjectVoList && val.shareToObjectVoList.length) {
        this.CetTable_2.data = val.shareToObjectVoList;
      }
    }
  },
  methods: {
    formatText(val) {
      return val ? val : "--";
    }
  }
};
</script>

<style lang="scss" scoped>
.drawer {
  :deep(.el-drawer__body) {
    display: flex;
    flex-direction: column;
  }
}
.title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}
.label {
  font-weight: 400;
  font-size: 14px;
  @include font_color(T4);
}
.value {
  margin-top: 4px;
}
</style>
