{"时段": "Period", "1. 请选择有数据的时间开始重算；": "1. Please select the time to start recalculation;", "2. 仅支持3年内数据的重算，3年以前的请联系系统维护人员进行重算。": "2. Only supports recalculation of data within 3 years; 3 years before please contact the system maintenance person to recalculate.", "Al智能报警设置": "Al Intelligent Alarm Setting", "websokcet未连接,禁止操作": "Websokcet not connected, operation prohibited", "yyyy 年": "yyyy", "yyyy 年 MM 月": "yyyy-MM", "yyyy年第WW周(M.D-M.D)": "yyyy-WW(M.D-M.D)", "{0}-副本": "{0}-Copy", "{0}年第{1}周": "Week {1} of year {0}", "一级": "LV 1", "一级告警": "Alarm LV I", "一键清空": "Clear", "七级": "LV 7", "三级": "LV 3", "三级告警": "Alarm LV III", "上传成功！": "Upload Successfully", "与上期对比": "MoM Comparison", "九级": "LV 9", "事件-高级查询": "Events - Advanced Query", "事件状态": "Event Status", "事件确认": "Event Confirmation", "事件等级": "Event Level", "事件类型": "Event Type", "事件详情": "Event Details", "二十级": "LV 20", "二级": "LV 2", "二级告警": "Alarm LV II", "五级": "LV 5", "今年": "This Year", "使用快捷键全选或者点击列头全选，支持快捷键的方式复制粘贴和表格下拉的操作，支持delete键删除，支持筛选；": "Use shortcut keys to select all or click on column heads to select all. Support shortcut key copying, pasting, and table drop-down operations. Support delete key deletion and filtering;", "使用状态": "Use Status", "保存为图片": "Save as Picture", "修改分摊方案": "Edit Apportionment Program", "修改成功": "Modification Successfully", "停用节点树后才能删除": "Cannot be deleted until the node tree is deactivated", "八级": "LV 8", "公共分摊能耗": "Public Sharing", "公有维度标签": "Public Dimension Label", "六级": "LV 6", "关联分摊对象": "Associated Apportionment Object", "关联年历": "Associated Calendar", "关联节点": "Associated Node", "关联被分摊对象": "Associate With Apportionment Object", "分": "Min", "分摊信息": "Apportionment Information", "分摊对象": "Allocation Object", "分摊对象不能为空": "The apportionment object cannot be empty", "分摊对象与被分摊对象不能是同一个节点": "The apportioned object and the apportioned object cannot be the same node", "分摊方式": "Apportionment Method", "分摊时间间隔": "Apportionment Time Interval", "分摊比例": "Apportionment Proportion", "分摊比例总和必须为1": "The sum of apportioned proportions must be 1", "分摊类型": "Apportionment Type", "分摊能源类型": "Apportionment Energy Types", "分摊节点列表": "Apportionment Node List", "分时分析": "Time-of-Use Analysis", "分时占比": "Time-of-Use Ratio", "分时方案": "Time-of-Use Scheme", "分时方案名称": "Time-of-Use Scheme Came", "分时统计": "Time-of-Use Statistics", "分析": "Analysis", "分析周期": "Analysis Period", "分析方式": "Analysis Method", "分析类型": "Analysis Type", "分项用能占比": "Sub-Dimension Ratio", "分项维度": "Sub-Dimension", "分项维度配置": "Sub-Dimension Configuration", "动态分摊": "Dynamic Apportionment", "十一级": "LV 11", "十七级": "LV 17", "十三级": "LV 13", "十九级": "LV 19", "十二级": "LV 12", "十五级": "LV 15", "十八级": "LV 18", "十六级": "LV 16", "十四级": "LV 14", "十级": "LV 10", "单个比例必须大于0": "Each single proportion must be greater than 0", "发生时间": "Occurred Time", "可展开": "Expandable", "各分项用能趋势": "Energy Consumption Trends Each Sub-Item", "同期对比": "<PERSON><PERSON>on", "同比": "YoY", "同比环比": "YoY and MoM", "同环比": "YoY and MoM", "启用告警": "Enable", "启用报警": "Enable Alarm", "启用的报警名称不能为空": "Enabled alarm name cannot be empty", "启用预警": "Enable", "告警分析": "Warning Analysis", "告警收敛事件导出": "Alarm Convergence Event Export", "周告警": "Weekly Alerts", "周数": "Week", "周次": "Week", "周能效阈值": "Weekly Energy Efficiency Threshold", "周能耗阈值": "Wk Energy Thresh", "四级": "LV 4", "园区": "Base", "固定比例": "Fixed Proportion", "固定管理层级": "Fixed Management Hierarchy", "基础信息": "Basic Information", "处理详情": "Handling Details", "复制分时方案": "Copy Time-of-Use Scheme", "复制方案": "Copy Scheme", "多维度用能分析": "Multidimensional Energy Analysis", "存在未保存项，数据会丢失，是否保存?": "There are unsaved items, the data will be lost, do you want to save?", "存在未配置费率时段，是否保存？": "Exist unconfigured periods, confirm to save", "实际值": "Actual Value", "实际能耗": "Actual Energy Consumption", "导入实际产量数据": "Import actual production data", "导入计划产量数据": "Import plan production data", "导出": "Export", "导出报表": "Export Report", "尊敬的用户：": "Dear User:", "小时": "Hour", "小时告警": "Hour Arert", "层级对象": "Hierarchical Objects", "展示类型": "Display Type", "已关联": "Associated", "已关联年历时段方案出现点标记": "Marked with dots in the associated calendar period scheme", "已处理": "Processed", "已处理事件": "Processed Event", "已存在相同日时段方案": "The same day period scheme already exists", "已存在相同时段名称": "The same period name already exists", "已存在相同颜色的选择": "Existing same color selection", "平均值": "Avg", "年历关联已修改，是否保存?": "The annual calendar association has been modified, do you want to save it?", "年告警": "Annual Alerts", "年度": "Year", "序号": "No.", "开始时段": "Start Period", "开始时间": "Start Time", "归属开始时段": "Attribution Start Period", "归属结束时段": "Belonging End Period", "当前存在未配置的节点树层级，请先配置": "There are currently unconfigured node tree levels, please configure them first", "当前正在{0},禁止操作": "Currently at {0}, operations are prohibited", "当前节点未直接关联采集表计，能耗数据由下层节点汇总而来！": "The current node is not directly associated with the collection meter, and the energy consumption data is summarized by lower level nodes!", "待处理": "Pending", "待处理事件": "Pending Event", "总": "Total", "总能耗": "Total Energy Consumption", "总量": "Total", "您切换到AI智能模式，自定义方案将不再生效，只能选择一种方案配置能效报警计划！": "If you switch to AI intelligent mode, the customized scheme will no longer take effect. You can only select one scheme to config the energy efficiency alarm plan", "您切换到自定义模式，AI智能报警方案将不再生效，只能选择一种方案配置能效报警计划！": "If you switch to the custom mode, the AI intelligent alarm scheme will no longer take effect. You can only select one scheme to config the energy efficiency alarm plan", "房间": "Room", "所属项目": "Project", "批量导入": "Batch Import", "批量导出": "Batch Export", "批量确认": "<PERSON><PERSON> Confirm", "批量筛选时间": "", "批量配置-刷新节点树": "Bulk Configure - Refresh Node Tree", "报警名称": "Alarm Name", "报警名称长度不能大于255个字符": "The length of alarm name cannot exceed 255 characters", "报警等级": "Alarm Level", "报警等级一": "Alarm LV I", "报警等级三": "Alarm LV III", "报警等级二": "Alarm LV II", "报警等级标签": "Alarm Level Label", "报警等级设置": "Alarm Level Setting", "报警等级配置": "Alarm Level Config", "报警类型": "Alarm Type", "拓扑图": "Topological Graph", "拓扑配置": "Topology Configuration", "持续时长": "Duration", "按年度": "Annually", "按日": "Daily", "损耗分摊能耗": "Loss Sharing", "排名": "Ranking", "排序": "Sort", "描述": "Description", "操作人": "Operator", "收敛": "Convergence", "收敛事件": "Convergence Event", "放弃保存": "Cancel Save", "数据已修改，请先保存在进行筛选": "The data has been modified, please save it for filtering first", "文件名称": "File Name", "文本": "Text", "新增分摊方案": "Add Apportionment Program", "新增分时方案": "Add Time-of-Use Scheme", "新增时段": "New Period", "新增时段方案": "New Time Period Scheme", "新建分项节点树": "New Subitem Node Tree", "新建多维度节点树": "New Dimension Node Tree", "新建标签": "Add Tag", "方案名称": "Scheme Name", "方案名称不能为空": "Scheme name cannot be empty", "方案名称不能输入特殊字符": "Special characters cannot be entered for scheme name", "方案名称长度在 1 到 20 个字符": "The scheme name is 1 to 20 characters long", "无TOP排名": "No TOP Ranking", "无能耗数据仅显示拓扑图": "No energy data only shows topology map", "日告警": "Daily Alerts", "日时段方案": "Daily Period Scheme", "时段名称": "Period Name", "时段方案": "Time Period Scheme", "时间": "Time", "时间排序": "Time Sorting", "是否已勾选报警等级?": "Alarm level already selected?", "显示分时数据": "Show Time-of-Use Data", "暂无拓扑图": "No Topology Diagram", "暂无环比数据": "No MoM Data", "暂无能流图": "No Energy Flow Diagram", "最值": "Max/Min", "最多对比{0}个节点": "Compare up to {0} nodes", "最大值": "Max", "最小值": "Min", "月告警": "Monthly Alerts", "未关联": "Not Associated", "枚举": "Enumeration", "枚举是一组相关的标签赋值的定义；文本是可以自由定义的内容": "Enumeration is a definition of assigning values to a set of related tags; Text is content that can be freely defined", "查询时段": "Query Period", "标签名称": "Label Name", "标签名称不能为": "The tag name cannot be", "标签类型": "Tag Type", "标签赋值": "Label Assignment", "标签赋值存在重复": "There are duplicate tag", "楼层": "Floor", "楼栋": "Building", "横排列": "Horizontal Arrangement", "橙色报警": "Orange Alarm", "每个标签赋值的长度在 1 到 {0} 个字符": "The length of each label assignment ranges from 1 to {0} characters", "比": "<PERSON><PERSON>", "永远": "Forever", "没有该节点权限": "No permission for this node", "流入": "In", "流出": "Out", "状态": "Status", "环比": "MoM", "生成层级配置": "Generate Hierarchical Configuration", "生成管网配置": "Generate Equipment Configuration", "生效时间": "Effective Date", "生效时间、标签赋值不同时为空，请检查数据": "When the effective time and label assignment are different, it is empty. Please check the data", "生效状态": "Effective Status", "用": "Use ", "用{0}量（{1}）": "Use {0}({1})", "用能同环比分析": "Energy Consumption YoY and MoM Analysis", "用能排名": "Energy Ranking", "用能趋势": "Energy Consumption Trend", "用能量": "Energy Usage", "用量": "Usage", "确定要删除吗？": "Are you sure to delete?", "确定要删除所选项吗？": "Are you sure to delete the selected item?", "确定要删除该便签吗？": "Are you sure you want to delete this note?", "确定要离开此页面吗？你未保存的更改将丢失。": "Are you sure you want to leave this page? Your unsaved changes will be lost.", "确认人": "Approver", "确认信息": "Approval Content", "确认意见": "Approval Comments", "确认时间": "Approval Time", "私有维度标签": "Private Dimension Label", "第WW周": "WW", "第【{0}】行{1}名称过长": "Line {0}: {1} name too long", "第【{0}】行{1}存在特殊字符": "There are special characters in line {0} {1}", "第【{0}】行未配置标签，不允许选择生效时间": "The label is not configured on line {0}, and it is not allowed to select the effective time", "第【{0}】行生效时间未选择": "The effective time for line [{0}] has not been selected", "第【{0}】行节点名称不能为空": "The node name in line [{0}] cannot be empty", "第【{0}】行节点名称过长": "The node name on line [{0}] is too long", "筛选时段": "Filter Period", "红色报警": "Red Alarm", "纵排列": "Vertical Arrangement", "结束时段": "End Period", "结束时间": "End Time", "统计周期": "Statistical cycle", "统计归属": "Statistical Attribution", "维度列表": "Dimension List", "维度属性": "Dimension Attribute", "维度标签管理": "Dimension Label Manage", "维度标签赋值": "Dimension Label Assignment", "维度标签赋值修改，动力分摊方案可能需要重新关联，是否确定保存？": "Dimension tag assignment modification, the power distribution scheme may need to be re-linked. Are you sure you want to save?", "维度节点树配置": "Dimension node tree config", "编辑分时方案": "Edit Time-of-Use Scheme", "编辑多维度节点树": "Edit Dimension Node Tree", "编辑时段方案": "Edit Time Period Scheme", "编辑标签": "Edit Tag", "编辑节点维度属性": "Edit Node Dimension Attributes", "联系方式": "Contact Information", "能效阈值": "Energy Efficiency Threshold", "能流图": "Energy Flow Diagram", "能源类型": "Energy Type", "能源类型不能为空": "Energy type cannot be empty", "能耗": "Energy Consumption", "能耗分时数据导出": "Export Energy Consumption Time-of-Use Data", "能耗分析": "Energy Analysis", "能耗同环比数据导出": "Export Energy Consumption YoY Data", "能耗定额": "Energy Consumption Quotas", "能耗查询与分析": "Energy Consumption Query and Analysis", "能耗概览": "Energy Consumption Overview", "能耗详情": "Energy Consumption Details", "能耗详情数据导出": "Export Energy Consumption Details Data", "能耗重算": "Energy Recalculation", "能耗阈值": "Energy Thresh", "自动计算比例": "Calculate Scale Automatically", "自定义名称": "Customized Name", "自定义报警设置": "Custom Alarm Settings", "节点ID": "Node ID", "节点信息": "Node Information", "节点名称": "Name", "节点名称重复！": "Duplicate node name", "节点对比": "Node Comparison", "节点层级": "Node Hierachy", "节点树列表": "Tree List", "节点树名称": "Tree Name", "节点树名称已存在": "Node tree name already exists", "节点树禁用之后无法再次启用，确定要禁用吗？": "After disabling the node tree, it cannot be enabled again. Are you sure you want to disable it?", "节点树类型": "Node Tree Type", "节点树结构配置": "Node Tree Structure Config", "节点类型": "Node type", "表计数据": "Meter Data", "表计数据：原始表计数据；能耗分析：原始表计数据+分摊数据": "Meter data: Original meter data; Energy consumption analysis: raw meter data+shared data", "被分摊信息": "Approtioned Infomation", "被分摊对象": "Allocated Object", "被分摊对象不能为空": "The apportioned object cannot be empty", "被分摊对象关联": "Apportioned Object Assoc", "被分摊能源类型": "Apportionment Energy Types", "设备名称": "Device Name", "该表格支持类似excel的操作": "This table supports operations similar to Excel", "请先配置项目能源类型": "Please set energy type first", "请填写日时段方案名称": "Please enter daily time interval scheme name", "请填写时段名称": "Please enter period name", "请点击重置全部过滤": "Please click to reset all filters", "请点击重置全部过滤，再进行保存！": "Please click to reset all filters before saving", "请输入内容（必需）": "Please enter content(required)", "请输入名称": "Please enter name", "请输入文件名称": "Please enter file name", "请输入方案名称": "Please enter scheme name", "请输入标签赋值": "Please enter label assignment", "请输入节点名称": "Please enter node name", "请输入节点名称进行搜索": "Please enter the node name to search", "请输入阈值": "Please enter threshold", "请选择开始时间": "Please select start time", "请选择所属项目": "Please select project", "请选择标签类型": "Please select tag type", "请选择节点": "Please select node", "请配置节点树结构": "Please config the node tree structure", "调整记录": "Adjustment Records", "调整记录查询": "Adjustment Record Query", "趋势分析": "Trend Analysis", "输入事件描述关键字": "Enter event description keywords", "输入关键字搜索": "Please enter", "输入方案名称": "Enter scheme name", "输入标签赋值(每行一个)": "Input label assignment(one per line)", "输入系数": "Input Factor", "选择对象": "Select Object", "选择日期": "Select date", "选择日期时间": "Select Time", "选择时间个数超过限制": "The number of selected time exceeds the limit", "选择时间个数超过限制{0}个": "The number of selected time exceeds the limit of {0}", "选择自定义模式，系统判定停用AI智能模式阈值计划！": "Select the user-defined mode, and the system will decide to disable the AI intelligent mode threshold plan", "选择起始节点": "Select Start Node", "配置": "Config", "配置报警阈值": "Config Alarm Thresholds", "配置维度标签赋值": "Config dimension label assignment", "重命名": "<PERSON><PERSON>", "重算开始时间": "Recalculation Start Time", "重置全部过滤": "Reset all Filters", "阈值": "<PERSON><PERSON><PERSON><PERSON>", "预测值": "Forecasted Value", "预测动作": "Predictive Action", "预测返回": "Predicted Return", "预警": "Early Warning", "高级查询": "Advanced Query", "黄色报警": "Yellow Alarm", "默认由软件自动设置动态阈值AI智能模式；月阈值提前一个月给出，日阈值提前一天给出": "By default, the software automatically sets the dynamic threshold AI intelligent mode; Monthly threshold is given one month in advance, and daily threshold is given one day in advance", "默认选中子节点": "Default Select Child Node", "（{0}）": "({0})"}