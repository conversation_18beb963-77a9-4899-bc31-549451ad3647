import fetch from "eem-base/utils/fetch";
//暂时无需解密
// import { decryptAttributes } from "eem-base/utils/crypto.js";

// function parseAttribute(data) {
//   if (typeof data === "string") {
//     data = JSON.parse(data);
//   }
//   let response = _.get(data, "data");
//   response = decryptAttributes(response, [
//     "name",
//     "nicName",
//     "email",
//     "mobilePhone"
//   ]);
//   return {
//     ...data,
//     data: response
//   };
// }

// 根据用户id查用户信息
export function queryUserInfoById(id) {
  return fetch({
    url: `/eembaseef/energy-efficiency/v1/ef/getUserById`,
    method: "GET",
    // transformResponse: [parseAttribute],
    params: {
      userId: id
    }
  });
}
