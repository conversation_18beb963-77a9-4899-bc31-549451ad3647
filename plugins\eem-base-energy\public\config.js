__registeAppConfig.setAppconfig({
  version: "1.0.0",
  i18n: {
    en: {
      能耗: "Energy",
      能耗报警配置: "Energy Alarm Configuration",
      非自然周期配置: "Non-natural cycle configuration",
      分时方案: "Time-based solution",
      公共分摊方案配置: "Common share scheme configuration",
      损耗分摊配置: "Loss share configuration",
      项目概览: "Project Overview",
      能耗查询与分析: "Energy Query and Analysis",
      分项能耗分析: "Subitem Energy Consumption Analysis",
      多维用能分析: "Multi-dimensional energy analysis"
    }
  },
  navmenu: [
    {
      label: "能耗",
      icon: "permission-management-lin",
      type: "subMenu",
      subMenuList: [
        {
          label: "能耗报警配置",
          category: "project",
          type: "menuItem",
          location: "/energyAlarmConfig",
          permission: "energyAlarmConfig"
        },
        {
          label: "非自然周期配置",
          category: "project",
          type: "menuItem",
          location: "/cycleconfig",
          permission: "cycleconfig"
        },
        {
          label: "分时方案",
          category: "project",
          type: "menuItem",
          location: "/timesharingConfig",
          permission: "timesharingConfig"
        },
        {
          label: "公共分摊方案配置",
          category: "project",
          type: "menuItem",
          location: "/publicSchemeConfig",
          permission: "publicSchemeConfig"
        },
        {
          label: "维度配置",
          category: "project",
          type: "menuItem",
          location: "/dimensionConfiguration",
          permission: "dimensionConfiguration"
        },
        {
          label: "项目概览",
          category: "project",
          type: "menuItem",
          location: "/projectOverview",
          permission: "projectOverview"
        },
        {
          label: "能耗查询与分析",
          category: "project",
          type: "menuItem",
          location: "/energyQueryAndAnalysis",
          permission: "energyQueryAndAnalysis"
        },
        {
          label: "分项能耗分析",
          category: "project",
          type: "menuItem",
          location: "/subEnergyConsumption",
          permission: "subEnergyConsumption"
        },
        {
          label: "多维用能分析",
          category: "project",
          type: "menuItem",
          location: "/energyattributionanalysis",
          permission: "energyattributionanalysis"
        },
        {
          label: "能源流向分析",
          category: "project",
          type: "menuItem",
          location: "/energyFlowAnalysis",
          permission: "energyFlowAnalysis"
        },
        {
          label: "能耗超标报警",
          category: "project",
          type: "menuItem",
          location: "/energyConsumptionEvent",
          permission: "energyConsumptionEvent"
        }
      ]
    }
  ],
  newGuideSteps: []
});
