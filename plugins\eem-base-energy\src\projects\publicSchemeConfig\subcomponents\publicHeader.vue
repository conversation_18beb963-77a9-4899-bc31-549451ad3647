<template>
  <div>
    <div class="w-fullflex-row flex items-center justify-between">
      <div class="flex flex-row items-center">
        <ElInput
          class="mr-J3"
          v-model="ElInput_1.value"
          v-bind="ElInput_1"
          v-on="ElInput_1.event"
        ></ElInput>
        <customElSelect
          :prefix_in="$T('所属项目')"
          v-model="ElSelect_3.value"
          v-bind="ElSelect_3"
          v-on="ElSelect_3.event"
          class="w-[200px] mr-J3"
          v-show="ElOption_3.options_in?.length > 1"
        >
          <ElOption
            v-for="item in ElOption_3.options_in"
            :key="item[ElOption_3.key]"
            :label="item[ElOption_3.label]"
            :value="item[ElOption_3.value]"
            :disabled="item[ElOption_3.disabled]"
          ></ElOption>
        </customElSelect>
        <customElSelect
          class="mr-J3"
          :prefix_in="$T('被分摊能源类型')"
          v-model="ElSelect_1.value"
          v-bind="ElSelect_1"
          v-on="ElSelect_1.event"
        >
          <ElOption
            v-for="item in ElOption_1.options_in"
            :key="item[ElOption_1.key]"
            :label="item[ElOption_1.label]"
            :value="item[ElOption_1.value]"
            :disabled="item[ElOption_1.disabled]"
          ></ElOption>
        </customElSelect>
        <customElSelect
          class="mr-J3"
          :prefix_in="$T('分摊方式')"
          v-model="ElSelect_2.value"
          v-bind="ElSelect_2"
          v-on="ElSelect_2.event"
        >
          <ElOption
            v-for="item in ElOption_2.options_in"
            :key="item[ElOption_2.key]"
            :label="item[ElOption_2.label]"
            :value="item[ElOption_2.value]"
            :disabled="item[ElOption_2.disabled]"
          ></ElOption>
        </customElSelect>
        <CustomElDatePicker
          class="mr-J3"
          :prefix_in="$T('生效时间')"
          v-bind="CetDatePicker_time.config"
          v-model="CetDatePicker_time.val"
          @change="updateDate"
        />
      </div>
      <div class="flex flex-row items-center">
        <!-- 重算功能未实现 -->
        <CetButton
          v-if="false"
          class="mr-J3"
          v-bind="CetButton_recalculation"
          v-on="CetButton_recalculation.event"
          v-permission="'energyshareconfig_update'"
        ></CetButton>
        <CetButton
          class="mr-J3"
          v-bind="CetButton_2"
          v-on="CetButton_2.event"
          v-permission="'energyshareconfig_update'"
        ></CetButton>
        <el-dropdown @command="handleCommand">
          <span class="el-dropdown-link">
            {{ $T("更多") }}
            <i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="query">
              {{ $T("高级查询") }}
            </el-dropdown-item>
            <el-dropdown-item
              command="delete"
              :class="isDelete ? 'delete' : 'disabled'"
              v-permission="'energyshareconfig_update'"
            >
              {{ $T("批量删除") }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <advancedQuery
      v-bind="advancedQuery"
      @query_update="query_update"
      :rootNodes_in="ElOption_3.options_in"
    ></advancedQuery>
    <recalculation
      v-bind="recalculation"
      @updataReCalcState_out="updataReCalcState_out"
    ></recalculation>
  </div>
</template>

<script>
import advancedQuery from "./advancedQuery.vue";
import customApi from "@/api/custom";
import recalculation from "./recalculation.vue";

export default {
  name: "publicHeader",
  components: { advancedQuery, recalculation },
  computed: {
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    }
  },
  props: {
    isDelete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 搜索框
      ElInput_1: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "180px"
        },
        event: {
          change: this.ElInput_1_change_out
        }
      },
      // 被分摊能源类型
      ElSelect_1: {
        value: "",
        style: {
          width: "250px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        label: "text",
        value: "id",
        disabled: "disabled"
      },
      // 分摊方式
      ElSelect_2: {
        value: 0,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [
          {
            id: 0,
            text: $T("全部")
          },
          {
            id: 1,
            text: $T("固定比例")
          },
          {
            id: 2,
            text: $T("动态分摊")
          }
        ],
        key: "id",
        label: "text",
        value: "id",
        disabled: "disabled"
      },
      ElSelect_3: {
        value: 0,
        style: {},
        event: {
          change: this.ElSelect_3_change_out
        }
      },
      ElOption_3: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // 选择时段
      CetDatePicker_time: {
        val: null,
        config: {
          valueFormat: "timestamp",
          type: "daterange",
          clearable: false,
          unlinkPanels: true,
          rangeSeparator: $T("至")
        }
      },
      // 重算按钮
      CetButton_recalculation: {
        visible_in: true,
        disable_in: false,
        title: "重算",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_recalculation_statusTrigger_out
        }
      },
      // 新增方案
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: "新增方案",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      // 高级查询
      advancedQuery: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        energyOptions: null
      },
      // 筛选条件集合
      param: {
        key: "",
        objectEnergyType: 0,
        energyShareMethod: 0,
        startTime: this.$moment().startOf("year").valueOf(),
        endTime: this.$moment().endOf("year").valueOf(),
        page: {
          index: 0,
          limit: 20
        }
      },
      // 重算
      recalculation: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      }
    };
  },
  methods: {
    // 初始化
    async init() {
      await this.getApportionedEnergy();
      this.ElSelect_2.value = 0;
      this.CetDatePicker_time.val = [
        this.$moment().startOf("year").valueOf(),
        this.$moment().endOf("year").valueOf()
      ];
      this.ElInput_1.value = "";
      delete this.param.baseVo;
      this.updateParam();
    },
    // 获取被分摊能源类型
    async getApportionedEnergy() {
      const res = await customApi.getProjectEnergyNoStandardized();
      if (res.code !== 0) {
        return;
      }

      let data = res.data || [];
      let selectData = data.map(item => ({
        id: item.energytype,
        text: item.name
      }));

      let param = {
        id: 0,
        text: "全部"
      };
      selectData.unshift(param);
      this.ElOption_1.options_in = selectData;
      this.ElSelect_1.value = selectData?.[0]?.id ?? null;
    },
    updateParam() {
      this.param.key = this.ElInput_1.value;
      this.param.objectEnergyType = this.ElSelect_1.value;
      this.param.energyShareMethod = this.ElSelect_2.value;
      this.param.startTime = this.$moment(
        this.CetDatePicker_time.val[0]
      ).valueOf();
      this.param.endTime = this.$moment(
        this.CetDatePicker_time.val[1]
      ).valueOf();

      const rootNode = this.ElOption_3.options_in.find(
        item => item.id === this.ElSelect_3.value
      );
      this.param.rootNode = {
        id: rootNode?.id,
        modelLabel: rootNode?.modelLabel
      };
      this.$emit("update_tableData", this.param, this.ElOption_1.options_in);
    },
    // 更多触发
    handleCommand(command) {
      if (command === "query") {
        this.advancedQuery.openTrigger_in = new Date().getTime();
        this.advancedQuery.inputData_in = this._.cloneDeep(this.param);
        this.advancedQuery.energyOptions = this.ElOption_1.options_in;
        this.advancedQuery.rootNodeId_in = this.ElSelect_3.value;
      } else if (command === "delete") {
        this.$emit("delete_scheme");
      }
    },
    ElInput_1_change_out(val) {
      this.param.key = val;
      this.param.page.index = 0;
      delete this.param.baseVo;
      this.updateParam();
    },
    ElSelect_1_change_out(val) {
      this.param.objectEnergyType = val;
      this.param.page.index = 0;
      delete this.param.baseVo;
      this.updateParam();
    },
    ElSelect_2_change_out(val) {
      this.param.energyShareMethod = val;
      this.param.page.index = 0;
      delete this.param.baseVo;
      this.updateParam();
    },
    ElSelect_3_change_out() {
      this.param.page.index = 0;
      delete this.param.baseVo;
      this.updateParam();
    },
    updateDate(val) {
      this.param.startTime = this.$moment(val[0]).valueOf();
      this.param.endTime = this.$moment(val[1]).valueOf();
      this.param.page.index = 0;
      delete this.param.baseVo;
      this.updateParam();
    },
    CetButton_recalculation_statusTrigger_out(val) {
      this.recalculation.visibleTrigger_in = this._.cloneDeep(val);
      let projectNode = {
        objectId: this.projectId,
        objectLabel: "project"
      };
      this.recalculation.inputData_in = this._.cloneDeep(projectNode);
    },
    // 更新项目的重算状态
    updataReCalcState_out() {},
    CetButton_2_statusTrigger_out() {
      this.$emit("new_plan");
    },
    query_update(val) {
      this.ElSelect_1.value = val.objectEnergyType;
      this.ElSelect_2.value = val.energyShareMethod;
      this.CetDatePicker_time.val = [val.startTime, val.endTime];
      this.param.objectEnergyType = val.objectEnergyType;
      this.param.energyShareMethod = val.energyShareMethod;
      this.param.startTime = val.startTime;
      this.param.endTime = val.endTime;
      this.param.baseVo = val.baseVo;
      this.param.page.index = 0;
      this.ElSelect_3.value = val.rootNodeId;
      this.updateParam();
    },
    async getRootNode() {
      this.ElOption_3.options_in = [];
      const res = await customApi.rootNode();
      const data = res.data || [];
      this.ElOption_3.options_in = data;
      this.ElSelect_3.value = data?.[0]?.id;
    }
  },
  async mounted() {
    await this.getRootNode();
    this.init();
    this.CetButton_recalculation.disable_in = true;
  }
};
</script>

<style lang="scss" scoped>
.delete {
  cursor: pointer;
  @include font_color(Sta3);
}
.disabled {
  pointer-events: none;
  cursor: default;
  opacity: 0.6;
}
</style>
