<template>
  <el-drawer class="detailDrawer" size="960px" :visible.sync="drawer">
    <div slot="title" class="clearfix">
      <span class="fcT1">
        {{ inputData_in?.name }}{{ $T("详情") }}
        <el-tag class="ml-J1" type="success" v-if="energyTypeName">
          {{ energyTypeName }}
        </el-tag>
      </span>
    </div>
    <div class="flex flex-col flex-auto p-J4 brC bg1">
      <div class="head brC">
        <el-row :gutter="$J3">
          <el-col
            :span="6"
            class="mb-J4"
            v-for="(field, index) in detailFields"
            :key="index"
          >
            <div class="label">{{ field.label }}</div>
            <div class="value mt-J0 text-ellipsis">
              <template v-if="field.isDate">
                {{
                  newInputData_in[field.key]
                    ? $moment(newInputData_in[field.key]).format("YYYY-MM-DD")
                    : "--"
                }}
              </template>
              <template v-else-if="field.isStatus">
                {{ newInputData_in[field.key] ? $T("是") : $T("否") }}
              </template>
              <!-- 普通字段 -->
              <template v-else>
                {{ newInputData_in[field.key] ?? "--" }}
              </template>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="fwBold">
        <div
          v-for="(tag, index) in tagList"
          :key="index"
          class="tagStyle ml-J0 brC"
          :style="{ color: tag.color, background: tag.bgColor }"
        >
          {{ tag.text }}：{{ tag.value }}{{ tag.unit }}
        </div>
      </div>
      <div class="flex flex-col flex-auto mt-J3">
        <CetTable
          class="flex-auto"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <template v-for="item in Columns_1">
            <ElTableColumn
              :key="item.label"
              v-bind="item"
              showOverflowTooltip
            ></ElTableColumn>
          </template>
          <el-table-column :label="$T('检定结果')">
            <template slot-scope="{ row }">
              {{ verificationName(row.result) || "--" }}
            </template>
          </el-table-column>
          <el-table-column :label="$T('检定证书')">
            <template slot-scope="{ row }">
              <el-tag :type="row.certiication ? 'success' : 'info'">
                {{ row.certiication ? $T("有") : $T("无") }}
              </el-tag>
            </template>
          </el-table-column>
          <ElTableColumn :label="$T('操作')" width="88">
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="!row.certiication"
                @click="handleDownload(row)"
              >
                {{ $T("下载证书") }}
              </el-button>
            </template>
          </ElTableColumn>
        </CetTable>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import omegaTheme from "@omega/theme";
import customApi from "@/api/custom.js";
import common from "eem-base/utils/common";
import { getTagDetail } from "./index.js";
export default {
  props: {
    openTrigger_in: Number,
    inputData_in: Object,
    template_in: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  components: {},
  computed: {
    lightTheme() {
      return omegaTheme.theme === "light";
    },
    token() {
      return this.$store.state.token;
    },
    energyTypeName() {
      const field = this.template_in.find(item => item.name === "energytype");
      if (!field || !this.inputData_in) return "";
      const enumList = field.enumerationvalue || [];
      const selected = enumList.find(
        i => i.id === this.inputData_in?.energytype
      );
      return selected?.text || "";
    },
    verificationresulttypeList() {
      return this.$store.state.enumerations?.verificationresulttype;
    }
  },
  data() {
    return {
      drawer: false,
      CetTable_1: {
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        data: [{ id: 1, name: "xxxxx机构", lastOverHaulDate: 1749030141258 }],
        showPagination: false,
        event: {}
      },
      Columns_1: [
        {
          type: "index",
          label: "#",
          headerAlign: "left",
          align: "left",
          width: "50"
        },
        {
          prop: "lastOverHaulDate",
          label: $T("检定日期"),
          formatter: common.formatDateCol("YYYY-MM-DD")
        },
        {
          prop: "verificationAgency",
          label: $T("检定机构")
        }
      ],
      tagList: [],
      detailFields: [
        { key: "code", label: $T("仪表编号") },
        { key: "metertype", label: $T("计量器具类型") },
        { key: "model", label: $T("型号规格") },
        { key: "accuracylevel", label: $T("准确度等级") },
        { key: "manufacturer", label: $T("生产厂家") },
        { key: "productiondate", label: $T("生产日期"), isDate: true },
        { key: "managementnumber", label: $T("管理编号") },
        { key: "location", label: $T("安装使用地点") },
        { key: "monitorproperty", label: $T("测量对象属性") },
        { key: "monitorname", label: $T("测量对象") },
        { key: "installer", label: $T("安装方（厂家）") },
        { key: "installdate", label: $T("安装时间"), isDate: true },
        { key: "status", label: $T("目前状态") },
        { key: "verificationstatus", label: $T("检定/校准状态") },
        {
          key: "measuringequipment",
          label: $T("是否为用能量计量器具"),
          isStatus: true
        },
        { key: "redundant", label: $T("是否重复计量"), isStatus: true },
        { key: "backup", label: $T("是否为备用表计"), isStatus: true }
      ],
      newInputData_in: {}
    };
  },
  watch: {
    openTrigger_in() {
      this.drawer = true;
    },
    inputData_in(val) {
      this.init();
    }
  },
  methods: {
    tabHeaderStyle() {
      return {
        background: this.lightTheme ? "#F8FAFB" : "#16191a",
        fontSize: "12px",
        fontWeight: "400"
      };
    },
    formatDate(date) {
      return date ? this.$moment(date).format("YYYY-MM-DD") : undefined;
    },
    mapFields(fieldMapping, template) {
      const result = {};
      fieldMapping.forEach(({ key, name }) => {
        const enumValues =
          template.find(i => i.name === name)?.enumerationvalue || [];
        result[key] =
          enumValues.find(i => i.id === this.inputData_in[key])?.text || "--";
      });
      return result;
    },
    init() {
      this.queryRecordHistory();
      const fieldMapping = [
        { key: "metertype", name: "metertype" },
        { key: "monitorproperty", name: "monitorproperty" },
        { key: "status", name: "status" },
        { key: "verificationstatus", name: "verificationstatus" }
      ];
      const params = {
        ..._.cloneDeep(this.inputData_in),
        ...this.mapFields(fieldMapping, this.template_in)
      };
      this.newInputData_in = params;
    },
    async queryRecordHistory() {
      if (!this.inputData_in) return;
      const {
        lastoverhauldate,
        nextoverhauldate,
        plannedverificationcycle,
        verificationcycle
      } = this.inputData_in;
      const res = await customApi.queryRecordHistory(this.inputData_in.id);
      if (res.code !== 0) return;
      const { number, recordHistoryList } = res.data;
      this.CetTable_1.data = recordHistoryList;
      const verificationcycleMap = {
        12: "天",
        14: "月",
        17: "年"
      };
      this.tagList = getTagDetail({
        number,
        lastoverhauldate: this.formatDate(lastoverhauldate),
        nextoverhauldate: this.formatDate(nextoverhauldate),
        plannedverificationcycle,
        verificationcycle: verificationcycleMap[verificationcycle] || ""
      });
    },
    handleDownload(row) {
      const uploadPath = row.certiication;
      if (!uploadPath) {
        return;
      }
      var url = "/eem-service/v1/common/downloadFile?path=" + uploadPath;
      common.downExcelGET(url, {}, this.token);
    },
    verificationName(e) {
      if (!e) return "";
      const field = this.verificationresulttypeList.find(item => item.id === e);
      return field?.text || "";
    }
  }
};
</script>

<style lang="scss" scoped>
.detailDrawer {
  :deep(.el-drawer__header) {
    @include margin-left(J3);
    line-height: 56px;
    height: 56px;
    margin-bottom: 0;
    padding: 0;
  }
  :deep(.el-drawer__body) {
    @include background_color(BG);
    display: flex;
    flex-direction: column;
    @include padding(J1);
    height: calc(100% - 72px);
  }
  :deep(.el-button.el-button--text.is-disabled) {
    @include font_color(T3);
  }
  .head {
    .label,
    .value {
      @include font_size(Aa);
    }
    .label {
      @include font_color(T3);
    }
    .headTitle {
      @include font_color(T2);
      font-weight: bold;
    }
  }
  .fwBold {
    display: flex;
    align-items: center;
    .tagStyle {
      width: 196px;
      height: 32px;
      line-height: 32px;
      text-align: center;
    }
  }
}
</style>
