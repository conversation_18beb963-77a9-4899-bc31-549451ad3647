<template>
  <div class="yearPicker" ref="yearPicker" :width="width">
    <input
      ref="inputLeft"
      v-model="startShowYear"
      @focus="onFocus"
      @blur="onBlur"
      type="text"
      name="yearInput"
      @keyup="checkStartInput($event)"
      :placeholder="$T('选择年份')"
    />
    <span>{{ sp }}</span>
    <input
      ref="inputRight"
      v-model="endShowYear"
      @focus="onFocus"
      @blur="onBlur"
      type="text"
      name="yearInput"
      @keyup="checkEndInput($event)"
      :placeholder="$T('选择年份')"
    />
    <i class="dateIcon el-icon-date"></i>
    <div class="floatPanel" v-if="showPanel">
      <div class="leftPanel">
        <div class="panelHead">
          <i class="el-icon-d-arrow-left" @click="onClickLeft"></i>
          {{ leftYearList[0] + "-" + leftYearList[9] }}
        </div>
        <div class="panelContent">
          <div
            :class="{
              oneSelected: item === startYear && oneSelected,
              startSelected: item === startYear,
              endSelected: item === endYear,
              betweenSelected: item > startYear && item < endYear
            }"
            v-for="item in leftYearList"
            :key="item"
          >
            <a
              :class="{
                cell: true,
                selected: item === startYear || item === endYear,
                forbid: !optionPicker(item + '/01/01')
              }"
              @click="onClickItem(item)"
              @mouseover="onHoverItem(item)"
            >
              {{ item }}
            </a>
          </div>
        </div>
      </div>
      <div class="rightPanel">
        <div class="panelHead">
          <i class="el-icon-d-arrow-right" @click="onClickRight"></i>
          {{ rightYearList[0] + "-" + rightYearList[9] }}
        </div>
        <div class="panelContent">
          <div
            :class="{
              startSelected: item === startYear,
              endSelected: item === endYear,
              betweenSelected: item > startYear && item < endYear
            }"
            v-for="item in rightYearList"
            :key="item"
          >
            <a
              :class="{
                cell: true,
                selected: item === endYear || item === startYear,
                forbid: !optionPicker(item + '/01/01')
              }"
              @click="onClickItem(item)"
              @mouseover="onHoverItem(item)"
            >
              {{ item }}
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
const SELECT_STATE = {
  unselect: 0,
  selecting: 1,
  selected: 2
};
export default {
  name: "yearPicker",
  computed: {
    oneSelected() {
      return (
        this.curState === SELECT_STATE.selecting &&
        (this.startYear === this.endYear || this.endYear == null)
      );
    },
    startDate() {
      return this.startYear;
    },
    leftYearList() {
      return this.yearList.slice(0, 10);
    },
    rightYearList() {
      return this.yearList.slice(10, 20);
    },
    startYearFormat() {
      if (this._.isNil(this.startYear)) {
        return null;
      }
      return this.$moment(this.startYear).startOf("year").format("yyyy");
    },
    endYearFormat() {
      if (this._.isNil(this.endYear)) {
        return null;
      }
      return this.$moment(this.endYear).endOf("year").format("yyyy");
    }
  },
  props: {
    width: {
      default: 200
    },
    sp: {
      default: "-"
    },
    dateList: {
      default: function () {
        return [Date.now(), Date.now()];
      }
    },
    optionPicker: {
      default: function () {
        return function (val) {
          if (moment(val).valueOf() > moment().endOf("year").valueOf()) {
            return false;
          }
          return true;
        };
      }
    }
  },
  watch: {
    dateList: {
      deep: true,
      handler: function (val) {
        if (
          val &&
          val.length === 2 &&
          moment(val[0]).isValid() &&
          moment(val[1]).isValid()
        ) {
          this.startYear = moment(val[0]).year();
          this.endYear = moment(val[1]).year();

          this.changeYear();
        } else {
          console.error("时间不合法", val.length, val[0], val[1]);
        }
      }
    }
  },
  data() {
    return {
      itemBg: {},
      startShowYear: null,
      endShowYear: null,
      yearList: [],
      showPanel: false,
      startYear: null,
      endYear: null,
      curYear: 0,
      curSelectedYear: 0,
      curState: SELECT_STATE.unselect
    };
  },
  methods: {
    checkStartInput(event) {
      if (isNaN(this.startShowYear)) {
        this.startShowYear = this.startYear;
      } else {
        this.startYear = this.startShowYear * 1;
        this.changeYear();
        this.checkYear();
      }
    },

    checkEndInput() {
      if (isNaN(this.endShowYear)) {
        this.endShowYear = this.endYear;
      } else {
        this.endYear = this.endShowYear * 1;
        this.changeYear();
        this.checkYear();
      }
    },
    changeYear() {
      if (this.startYear && this.endYear && this.startYear >= this.endYear) {
        let tmp = this.endYear;
        this.endYear = this.startYear;
        this.startYear = tmp;
        this.startShowYear = this.startYear;
        this.endShowYear = this.endYear;
      } else {
        this.startShowYear = this.startYear;
        this.endShowYear = this.endYear;
      }
    },
    checkYear() {
      if (this.startYear && this.endYear) {
        this.$emit("updateTimeRange", {
          startYear: this.$moment(this.startYear + "")
            .startOf("year")
            .valueOf(),
          endYear: this.$moment(this.endYear + "")
            .endOf("year")
            .valueOf()
        });
      } else {
        console.warn("WARN:年份不合法", this.startYear, this.endYear);
      }
    },
    onHoverItem(iYear) {
      if (!this.optionPicker(iYear + "/01/01")) {
        return;
      }
      if (this.curState === SELECT_STATE.selecting) {
        let tmpStart = this.curSelectedYear;
        this.endYear = Math.max(tmpStart, iYear);
        this.startYear = Math.min(tmpStart, iYear);
      }
    },
    onClickItem(iYear) {
      if (!this.optionPicker(iYear + "/01/01")) {
        return;
      }

      if (
        this.curState === SELECT_STATE.unselect ||
        this.curState === SELECT_STATE.selected
      ) {
        this.startYear = iYear;
        this.curSelectedYear = iYear;
        this.endYear = null;
        this.curState = SELECT_STATE.selecting;
      } else if (this.curState === SELECT_STATE.selecting) {
        if (!this.endYear) {
          //处理鼠标点击一个直接点击下一个没有悬浮的bug
          this.onHoverItem(iYear);
        }
        this.endShowYear = this.endYear;
        this.startShowYear = this.startYear;
        this.curState = SELECT_STATE.selected;
        this.$emit("updateTimeRange", {
          startYear: this.$moment(this.startYear + "")
            .startOf("year")
            .valueOf(),
          endYear: this.$moment(this.endYear + "")
            .endOf("year")
            .valueOf()
        });

        setTimeout(() => {
          //为动画留的时间，可优化
          this.showPanel = false;
        }, 300);
      }
    },
    onFocus() {
      this.$nextTick(() => {
        this.showPanel = true;
      });
    },
    onBlur() {
      //   this.showPanel = false;
    },
    updateYearList() {
      let iStart = Math.floor(this.curYear / 10) * 10 - 10;
      iStart = iStart < 0 ? 0 : iStart;
      this.yearList = [];
      for (let index = 0; index < 20; index++) {
        this.yearList.push(iStart + index);
      }
    },
    closePanel(e) {
      if (!this.showPanel) {
        return;
      }
      let classList = [
        "floatPanel",
        "leftPanel",
        "el-icon-d-arrow-right",
        "el-icon-d-arrow-left",
        "panelHead",
        "panelContent",
        "rightPanel",
        "cell"
      ];
      if (typeof e.target.className !== "string") {
        this.$nextTick(() => {
          this.showPanel = false;
        });
        return;
      }
      let classSelected = e.target.className.split(" ")[0];
      if (
        (classSelected && !classList.includes(classSelected)) ||
        (e.target.name === "yearInput" &&
          e.target !== this.$refs.inputLeft &&
          e.target !== this.$refs.inputRight)
      ) {
        this.$nextTick(() => {
          this.showPanel = false;
        });
      }

      e.stopPropagation();
      return false;
    },
    onClickLeft() {
      this.curYear = this.curYear * 1 - 10;
      this.updateYearList();
    },
    onClickRight() {
      this.curYear = this.curYear * 1 + 10;
      this.updateYearList();
    },

    //------------------对外接口------------------------
    //直接传时间戳
    setYear(startYearStamp, endYearStamp) {
      if (!isNaN(startYearStamp) && !isNaN(endYearStamp)) {
        let startYear = this.$moment(startYearStamp).format("yyyy");
        let endYear = this.$moment(endYearStamp).format("yyyy");
        this.startYear = startYear * 1;
        this.endYear = endYear * 1;
        this.endShowYear = endYear * 1;
        this.startShowYear = startYear * 1;
      }
    }
  },

  created() {
    this.curYear = this.$moment().format("yyyy");
    this.updateYearList();
  },
  beforeDestroy() {
    document.removeEventListener("click", this.closePanel.bind(this));
  },

  mounted() {
    this.$refs.yearPicker.style = "padding-left:60px";
    this.startYear = moment(this.dateList[0]).year();
    this.endYear = moment(this.dateList[1]).year();
    this.changeYear();
    document.addEventListener("click", this.closePanel.bind(this));
  }
};
</script>
<style lang="scss" scoped>
.yearPicker {
  display: flex;
  position: relative;
  transition: all 0.3s;
  @include background_color(BG1);
  input:first-child {
    text-align: right;
  }
  // .labelText {
  //   position: absolute;
  //   @include font_size(Ab);
  //   @include font_color("ZS");
  //   // left: mh-get(J);
  // }
  span {
    padding: 0 32px;
    height: 32px;
    line-height: 32px;
  }
  border: 1px solid;
  @include border_color(B1);
  height: 32px;
  line-height: 32px;
  border-radius: 4px;
  padding: 0 28px 0 8px;
  box-sizing: border-box;
  .floatPanel {
    border: 1px solid;
    @include border_color(B1);
    > div {
      width: 50%;
    }
    padding: 0 16px;
    position: absolute;
    display: flex;
    @include background_color(BG1);
    z-index: 2000;
    border-radius: 4px;
    width: 650px;
    height: 250px;
    top: 40px;
    right: 0px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    .panelContent {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      height: calc(100% - 70px);
      .oneSelected {
        border-top-right-radius: mh-get(J3);
        border-bottom-right-radius: mh-get(J3);
      }
      .startSelected {
        @include background_color(BG3);
        border-top-left-radius: mh-get(J3);
        border-bottom-left-radius: mh-get(J3);
      }
      .endSelected {
        @include background_color(BG3);
        border-top-right-radius: mh-get(J3);
        border-bottom-right-radius: mh-get(J3);
      }
      .betweenSelected {
        @include background_color(BG3);
      }
      > div {
        width: 75px;
        height: mh-get(J7);
        line-height: 48px;
        margin: 3px 0;
        // border-radius: 24px;
        text-align: center;
        a {
          @include font_size(Aa);
          display: inline-block;
          width: 60px;
          height: 36px;
          cursor: pointer;
          line-height: 36px;
          border-radius: 18px;
        }
        .selected {
          @include background_color(ZS);
          color: #fff;
        }
        .forbid {
          cursor: no-drop;
          @include font_color(T3);
        }
      }
    }
    .panelHead {
      position: relative;
      height: 46px;
      line-height: 46px;
      text-align: center;
      button::before {
        content: "\e6dd";
      }
      i {
        position: absolute;
        cursor: pointer;
        top: 16px;

        &:hover {
          color: #3e77fc;
        }
      }
    }
    .rightPanel {
      padding-left: 50px;
    }
    .leftPanel .panelHead i {
      left: 20px;
    }
    .rightPanel .panelHead i {
      right: 20px;
    }
  }
  .floatPanel::before {
    content: "";
    height: 100%;
    position: absolute;
    left: 50%;
    width: 1px;
    border-left: 1px solid;
    @include border_color(B1);
  }
}
input {
  width: 60px;
  border: none;
  height: 30px;
  line-height: 32px;
  box-sizing: border-box;
  background-color: transparent;
  @include font_color(T1);
}
input:focus {
  outline: none;
  background-color: transparent;
}
.yearPicker:hover {
  @include border_color(ZS);
}
.dateIcon {
  position: absolute;
  left: mh-get(J1);
  top: 9px;
  color: #adb2bc;
}
</style>
