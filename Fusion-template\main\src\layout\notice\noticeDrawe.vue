<template>
  <div class="noticedrawepage" v-if="showNotice && alarmNotice">
    <div class="noticeHead">
      {{ $T("事件告警") }}
      <i class="el-icon-close closebar" @click="changeShow"></i>
    </div>
    <div class="noticebox">
      <div
        v-for="(item, index) in notice"
        :key="index"
        class="noticeitem pointer"
        :class="themeShadowClass"
        :style="{ background: themeBgStyle, backgroundSize: '100%' }"
        @click="handleJump(item)"
      >
        <div class="topTitle pb6">
          <div class="eventTypeName">
            {{ item.logTypeName }}
          </div>
          <div class="eventType" :style="{ background: getEventBgColor(item) }">
            {{ getEventAlias(item) }}
          </div>
        </div>
        <div class="contentBox">
          <div class="fs14 pb5">{{ formatterTime(item.time) }}</div>
          <div class="content fs14" :title="item.description">
            {{ item.description }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import OmegaTheme from "@omega/theme";
import { mapState } from "vuex";
import { EVENT_LEVEL } from "@/config/const.js";
export default {
  name: "noticeDrawe",
  data() {
    return {
      notice: [],
      EVENT_LEVEL,
      showNotice: false
    };
  },
  computed: {
    ...mapState("settings", ["alarmNotice"]),
    ...mapState("notice", ["items"]),
    themeBgStyle() {
      const theme = OmegaTheme.theme;
      const titleBg = require("./assets/noticeBgLight.png");
      const titleBgDark = require("./assets/noticeBgDark.png");
      return theme === "light"
        ? `url(${titleBg}) no-repeat`
        : `url(${titleBgDark}) no-repeat`;
    },
    themeShadowClass() {
      return OmegaTheme.theme === "light" ? "shadowLight" : "shadowDark";
    }
  },
  watch: {
    items(val) {
      if (val.length > this.notice.length) {
        this.showNotice = true;
      } else {
        if (val.length === 0) {
          this.showNotice = false;
        }
      }
      this.notice = _.cloneDeep(val);
    }
  },
  methods: {
    changeShow() {
      this.showNotice = false;
    },
    handleJump(val) {
      this.$emit("handleGoToPageByItem", val);
    },
    formatterTime(time) {
      return this.$moment(time).format("YYYY-MM-DD HH:mm:ss.SSS");
    },
    getEventBgColor(item) {
      const content = item.content ? JSON.parse(item.content) : {};
      const eventClass = content.eventClass;
      const eventLevel = this.EVENT_LEVEL.find(
        level => level.id === eventClass
      );
      return eventLevel ? eventLevel.color : "";
    },
    getEventAlias(item) {
      const content = item.content ? JSON.parse(item.content) : {};
      const eventClass = content.eventClass;
      const eventLevel = this.EVENT_LEVEL.find(
        level => level.id === eventClass
      );
      return eventLevel ? eventLevel.text : "";
    }
  }
};
</script>
<style scoped lang="scss">
.noticedrawepage {
  position: fixed;
  right: 10px;
  bottom: 10px;
  width: 480px;
  height: 528px;
  @include background_color(BG1);
  border: 1px solid;
  @include border_color(BG3);
  z-index: 999;
  border-radius: 8px;

  .noticeHead {
    font-size: 16px;
    padding: 16px 16px 0px;
    font-weight: bold;
    .closebar {
      cursor: pointer;
      float: right;
    }
  }
  .noticebox {
    overflow-y: auto;
    height: calc(100% - 38px);

    .topTitle {
      display: flex;
      justify-content: space-between;
      height: 23px;
      .eventTypeName {
        color: #fff;
        font-size: 14px;
        padding: 2px 0 2px 18px;
        box-sizing: border-box;
        width: 189px;
      }
      .eventType {
        width: 54px;
        height: 23px;
        border-radius: 0 8px 0 8px;
        line-height: 23px;
        text-align: center;
        font-size: 12px;
        color: #fff;
        font-weight: 600;
      }
    }

    .noticeitem {
      margin: 16px;
      padding-bottom: 10px;
      border-radius: 8px;
      .contentBox {
        padding: 8px 16px;
        box-sizing: border-box;
        @include font_color(T2);
        .content {
          text-overflow: -o-ellipsis-lastline;
          overflow: hidden; //溢出内容隐藏
          text-overflow: ellipsis; //文本溢出部分用省略号表示
          display: -webkit-box; //特别显示模式
          -webkit-line-clamp: 3; //行数
          line-clamp: 3;
          -webkit-box-orient: vertical; //盒子中内容竖直排列
        }
      }
    }
    .shadowDark {
      box-shadow: inset 0px 5px 20px 1px rgba(43, 46, 52, 0.56);
    }
    .shadowLight {
      box-shadow: inset 0px 5px 20px 1px rgba(0, 98, 255, 0.05),
        inset 0px -1px 1px 0px rgba(4, 105, 255, 0.15);
    }
  }
}
</style>
