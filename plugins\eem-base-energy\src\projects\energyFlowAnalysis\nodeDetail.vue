<template>
  <el-drawer
    :title="title"
    :visible.sync="drawer"
    direction="rtl"
    :size="480"
    class="drawer"
  >
    <div class="content">
      <CetTree
        v-if="showDevice"
        :selectNode.sync="CetTree_1.selectNode"
        :checkedNodes.sync="CetTree_1.checkedNodes"
        :searchText_in.sync="CetTree_1.searchText_in"
        v-bind="CetTree_1"
        v-on="CetTree_1.event"
        default-expand-all
      ></CetTree>
      <div v-else class="flex flex-col items-center justify-center h-full">
        <img
          v-if="isLight"
          src="static/assets/empty_min_light.png"
          alt=""
          class="w-[216px] h-[93px]"
        />
        <img
          v-else
          src="static/assets/empty_min.png"
          alt=""
          class="w-[216px] h-[93px]"
        />
        <div class="fcT3 mtJ1">
          {{ $T("当前节点未直接关联采集表计，能耗数据由下层节点汇总而来！") }}
        </div>
      </div>
    </div>
  </el-drawer>
</template>
<script>
import customApi from "@/api/custom";
import omegaTheme from "@omega/theme";

export default {
  props: {
    openTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    params: {
      type: Object
    }
  },
  computed: {
    isLight() {
      return omegaTheme.theme === "light";
    }
  },
  data() {
    return {
      drawer: false,
      title: null,
      showDevice: false,
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: false,
        event: {}
      }
    };
  },
  watch: {
    openTrigger_in(val) {
      this.title = this.inputData_in.label;
      this.getTreeData();
      this.drawer = true;
    }
  },
  methods: {
    async getTreeData() {
      const list = this.inputData_in.id ? this.inputData_in.id.split("_") : [];
      const data = {
        time: this.params.time,
        node: list
          ? {
              id: Number(list[0]),
              modelLabel: list[1]
            }
          : null
      };
      const res = await customApi.queryEnergyConnection(data);
      let tree = null;
      if (res.code === 0) {
        tree = res.data || [];
      }
      this.showDevice = !_.isEmpty(tree);
      this.CetTree_1.inputData_in = tree;
    }
  }
};
</script>
<style lang="scss" scoped>
.drawer {
  z-index: 10000 !important;
  :deep(.el-drawer__header) {
    height: 56px;
    margin: 0;
    padding-top: 0;
    @include font_color(T1);
    span {
      font-size: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    i {
      font-size: 16px;
    }
  }
  :deep(.el-drawer__body) {
    @include background_color(BG);
  }
  .content {
    height: calc(100% - 16px);
    margin: 8px;
    padding: 24px;
    border-radius: 8px;
    @include background_color(BG1);
    :deep(.el-tree) {
      overflow: auto;
    }
  }
}
</style>
