<template>
  <div>
    <el-row :gutter="$J3">
      <el-col :span="6" v-for="(item, index) in instrumentList" :key="item.id">
        <div
          @click.stop="handleInstrumentClick(item)"
          class="cursor-pointer instrument"
          :class="getClassName(item)"
        >
          <!-- :style="{
            backgroundImage: `url(${require(`../assets/${
              selectedIndex === item.id ? item.img : item.selectedImg
            }.png`)})`
          }" -->
          <div class="right">
            <span
              class="number"
              :style="{
                '--text-shadow': item.textShadow,
                '--background': item.background
              }"
            >
              {{ item.value }}
            </span>
            <span class="ml-J0">{{ $T("个") }}</span>
          </div>
        </div>
      </el-col>
    </el-row>
    <div class="mt-J3 searchParams">
      <ElInput
        v-model="name"
        v-bind="ElInput"
        v-on="ElInput.event"
        maxlength="100"
      ></ElInput>
      <ElSelect
        v-model="serachParams.type"
        v-bind="ElSelect_1"
        v-on="ElSelect_1.event"
        @change="handlerQueryDevice"
        class="ml-J3"
        style="width: 104px"
        clearable
      >
        <ElOption
          v-for="item in deviceList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        ></ElOption>
      </ElSelect>
      <div class="cascader-box ml-J1">
        <span class="eem-cascader-label">{{ $T("选择仪表") }}</span>
        <el-cascader
          ref="instrumentCascader"
          class="cascader"
          v-model="serachParams.nodeList"
          :props="treeProps"
          :options="areaOptions"
          collapse-tags
          popper-class="meter-type-cascader"
          @visible-change="handelSelectInstrument"
          @remove-tag="handlerInstrumentRemoveTag"
        ></el-cascader>
      </div>
      <div class="eem-cascader ml-J3">
        <span class="eem-cascader-label">{{ $T("仪表类型") }}</span>
        <el-cascader
          ref="cascader"
          v-model="serachParams.metertype"
          :props="metertypeProps"
          :options="metertypeOptions"
          collapse-tags
          :show-all-levels="false"
          popper-class="meter-type-cascader"
          @visible-change="visiblePeceventType"
          @remove-tag="handlerRemoveTag"
        />
      </div>
      <CustomElDatePicker
        class="ml-J3 date-picker"
        :prefix_in="$T('下次检定日期')"
        :range-separator="$T('至')"
        v-model="serachParams.nextoverhauldate"
        value-format="timestamp"
        type="daterange"
        style="width: 320px"
        :disabled="selectedIndex >= 1"
        @change="init"
      />
      <CetButton
        class="ml-J3"
        v-bind="CetButton_reset"
        v-on="CetButton_reset.event"
      ></CetButton>
      <slot></slot>
    </div>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import { getTreeParams, TREE_TYPE } from "@/utils/analysisServiceConfig.js";
import DomCalc from "eem-base/utils/domCalc.js";
export default {
  props: {
    instrumentList: {
      type: Array,
      default: () => []
    },
    metertypeOptions: {
      type: Array,
      default: () => []
    },
    queryTime: Number,
    queryCondition: Object
  },
  computed: {
    projectTenantId() {
      return this.$store.state.projectTenantId;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  watch: {
    serachParams: {
      deep: true,
      handler(val, oldVal) {
        // const params = this.buildSearchParams(val);
        // this.$emit("handelSearchParams", params);
      }
    },
    queryTime(val) {
      this.selectedIndex = 0;
    },
    queryCondition(val) {
      this.name = val.name || null;
      this.serachParams.nextoverhauldate = val.nextoverhauldate || null;
    }
  },
  data() {
    return {
      name: "",
      selectedIndex: 0,
      serachParams: {},
      ElInput: {
        value: "",
        placeholder: $T("请输入名称"),
        controls: false,
        style: {
          width: "180px"
        },
        event: {
          change: this.ElInput_name_change_out
        }
      },
      ElSelect_1: {
        value: "",
        style: {},
        event: {}
      },
      treeProps: {
        expandTrigger: "hover",
        children: "children",
        multiple: true,
        label: "text",
        value: "id"
      },
      metertypeProps: {
        children: "child",
        multiple: true,
        label: "text",
        value: "id"
      },
      areaOptions: [],
      deviceList: [
        {
          name: $T("设备通讯"),
          id: 1
        },
        {
          name: $T("管网层级"),
          id: 2
        }
      ],
      CetButton_reset: {
        visible_in: true,
        disable_in: false,
        title: $T("重置全部筛选"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_reset_statusTrigger_out
        }
      }
    };
  },
  methods: {
    getClassName(item) {
      return this.selectedIndex === item.id ? item.img : item.selectedImg;
    },
    buildSearchParams(val) {
      const { nodeList, ...rest } = val;
      let formattedNodeList =
        this.findMatchedObjects(nodeList, this.areaOptions) || [];
      formattedNodeList = formattedNodeList.map(i => ({
        id: i.id,
        modelLabel: i.modelLabel || "device"
      }));
      if (val.type === 1) {
        formattedNodeList = formattedNodeList.map(i => ({
          ...i,
          id: i.id ? Number(i.id.split("_")[1]) : null,
          modelLabel: "device"
        }));
      }
      const params = {
        ...rest,
        outerNodeList: formattedNodeList
      };
      return params;
    },
    handleInstrumentClick(item) {
      if (item.id === this.selectedIndex) {
        // 二次点击时取消筛选
        this.selectedIndex = 0;
        this.serachParams = {
          ...this.serachParams,
          nextoverhauldate: null,
          chooseOverdue: false
        };
        this.init();
        return;
      }
      const newParams = this.buildSearchParamsFromInstrument(
        item,
        this.serachParams
      );
      this.serachParams = newParams;
      this.selectedIndex = item.id;
      this.init();
    },
    buildSearchParamsFromInstrument(item, currentParams = {}) {
      const { time } = item || {};
      const { chooseOverdue, nextoverhauldate, ...rest } = currentParams;
      let result = { ...rest };
      if (!time) {
        result.chooseOverdue = true;
        result.nextoverhauldate = null;
      } else {
        result.nextoverhauldate = time;
      }
      return result;
    },
    handlerQueryDevice(e) {
      this.$set(this.serachParams, "nodeList", []);
      this.areaOptions = [];
      if (e) {
        this.getInstrumentList(e);
      } else {
        this.init();
      }
    },
    async getInstrumentList(type) {
      let params;
      this.treeProps = {
        ...this.treeProps,
        label: type === 1 ? "text" : "name"
      };
      if (type === 1) {
        // 类型1：设备通讯
        params = {
          loadDevice: true,
          nodeId: 0,
          nodeType: 0,
          async: false,
          tenantId: this.projectTenantId
        };
        const res = await customApi.queryPecCoreMeterTree(params);
        if (res.code !== 0) return;
        this.areaOptions = res.data;
      } else if (type === 2) {
        // 类型2：管网层级
        params = {
          rootID: this.projectId,
          rootLabel: "project",
          subLayerConditions: getTreeParams(TREE_TYPE.NETWORK),
          treeReturnEnable: true,
          filterNoAuthEndNode: true
        };
        const res = await customApi.getNodeList(params);
        if (res.code !== 0) return;
        this.areaOptions = res.data;
      }
    },
    ElInput_name_change_out(e) {
      this.$set(this.serachParams, "name", e);
      this.init();
    },
    CetButton_reset_statusTrigger_out() {
      this.serachParams = {};
      this.name = "";
      this.selectedIndex = 0;
      this.$emit("resetSandingTable");
    },
    findMatchedObjects(array, responseData) {
      if (!array?.length || !responseData?.length) return null;
      const matchedObjects = [];
      const idsToMatch = array.map(item => item[item.length - 1]);
      for (const id of idsToMatch) {
        let found = false;
        function traverse(nodes) {
          for (const node of nodes) {
            if (node.id === id) {
              matchedObjects.push(node);
              found = true;
              return;
            }
            if (node.children && Array.isArray(node.children)) {
              traverse(node.children);
              if (found) return;
            }
          }
        }
        traverse(responseData);
        found = false;
      }
      return matchedObjects;
    },
    visiblePeceventType(val) {
      if (val) {
        // 弹出级联面板，在面板底部添加按钮
        const ref = this.$refs.cascader;
        let popper = ref.$refs.popper;
        // 避免重复插入按钮
        if (
          !Array.from(popper.children).some(
            v => v.className === "custom-buttom-row"
          )
        ) {
          const div = document.createElement("div");
          div.className = "custom-buttom-row";

          const all = document.createElement("span");
          all.className = "custom-buttom";
          all.innerHTML = $T("全选");
          div.appendChild(all);

          const clear = document.createElement("span");
          clear.className = "custom-buttom";
          clear.innerHTML = $T("清空");
          div.appendChild(clear);
          popper.appendChild(div);
          all.onclick = () => {
            if (!this.metertypeOptions.length) return;
            let selected = [];
            this.metertypeOptions.forEach((item, index) => {
              if (item.child && item.child.length > 0) {
                item.child.forEach(data => {
                  selected.push([item.id, data.id]);
                });
              } else {
                selected.push([item.id]);
              }
            });
            this.$set(this.serachParams, "metertype", selected);
            // this.init();
          };
          clear.onclick = () => {
            this.$set(this.serachParams, "metertype", null);
          };
        }
      } else {
        // 关闭级联面板，筛选表格数据
        this.init();
      }
    },
    handelSelectInstrument(val) {
      if (val) {
        // 若没有选择前面的节点树类型，进行提示
        if (!this.serachParams.type) {
          this.$message.warning($T("请选择前面的节点树类型"));
        }
        // 弹出级联面板，在面板底部添加按钮
        const ref = this.$refs.instrumentCascader;
        let popper = ref.$refs.popper;
        // 避免重复插入按钮
        if (
          !Array.from(popper.children).some(
            v => v.className === "custom-buttom-row"
          )
        ) {
          const div = document.createElement("div");
          div.className = "custom-buttom-row";

          const all = document.createElement("span");
          all.className = "custom-buttom";
          all.innerHTML = $T("全选");
          div.appendChild(all);

          const clear = document.createElement("span");
          clear.className = "custom-buttom";
          clear.innerHTML = $T("清空");
          div.appendChild(clear);
          popper.appendChild(div);
          all.onclick = () => {
            if (!this.areaOptions.length) return;
            const selected = this.getAllLeafPaths(this.areaOptions);
            this.$set(this.serachParams, "nodeList", selected);
          };
          clear.onclick = () => {
            this.$set(this.serachParams, "nodeList", null);
          };
        }
      } else {
        // 关闭级联面板，筛选表格数据
        this.init();
      }
    },
    getAllLeafPaths(nodes, path = []) {
      let paths = [];
      nodes.forEach(node => {
        const currentPath = [...path, node.id];
        if (node.children && node.children.length > 0) {
          paths = paths.concat(
            this.getAllLeafPaths(node.children, currentPath)
          );
        } else {
          paths.push(currentPath);
        }
      });
      return paths;
    },
    async handlerInstrumentRemoveTag() {
      await this.$nextTick();
      this.$refs.instrumentCascader.toggleDropDownVisible(true);
    },
    async handlerRemoveTag(val) {
      await this.$nextTick();
      this.$refs.cascader.toggleDropDownVisible(true);
    },
    init() {
      const params = this.buildSearchParams(this.serachParams);
      this.$emit("handelSearchParams", params);
    }
  },
  mounted() {
    const textWidth = DomCalc.calcTextWidthPad($T("仪表类型"), 16);
    const dom = this.$refs.cascader.$el.querySelector(
      ".el-cascader .el-input .el-input__inner"
    );
    const tags = this.$refs.cascader.$el.querySelector(".el-cascader__tags");
    dom.style.paddingLeft = `${textWidth}px`;
    tags.style.paddingLeft = `${textWidth}px`;
  }
};
</script>

<style lang="scss" scoped>
.instrument {
  box-sizing: border-box;
  height: 48px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: end;
  @include padding(J2 J4);
  background-size: 100% 100%;
  vertical-align: middle;
  .right {
    display: flex;
    align-items: center;
    .number {
      background: var(--background);
      text-shadow: var(--text-shadow);
      font-family: "Barlow";
      font-size: 20px;
      font-style: normal;
      font-weight: 700;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
.bgDark1 {
  background-image: url(../assets/bgDark1.png);
}
.bgDark2 {
  background-image: url(../assets/bgDark2.png);
}
.bgDark3 {
  background-image: url(../assets/bgDark3.png);
}
.bgDark4 {
  background-image: url(../assets/bgDark4.png);
}
.bgLigth1 {
  background-image: url(../assets/bgLigth1.png);
}
.bgLigth2 {
  background-image: url(../assets/bgLigth2.png);
}
.bgLigth3 {
  background-image: url(../assets/bgLigth3.png);
}
.bgLigth4 {
  background-image: url(../assets/bgLigth4.png);
}
.noBgDark1 {
  background-image: url(../assets/noBgDark1.png);
}
.noBgDark2 {
  background-image: url(../assets/noBgDark2.png);
}
.noBgDark3 {
  background-image: url(../assets/noBgDark3.png);
}
.noBgDark4 {
  background-image: url(../assets/noBgDark4.png);
}
.noBgLigth1 {
  background-image: url(../assets/noBgLigth1.png);
}
.noBgLigth2 {
  background-image: url(../assets/noBgLigth2.png);
}
.noBgLigth3 {
  background-image: url(../assets/noBgLigth3.png);
}
.noBgLigth4 {
  background-image: url(../assets/noBgLigth4.png);
}
.searchParams {
  display: flex;
  align-items: center;
  .cascader-box {
    position: relative;
    span {
      position: absolute;
      left: 11px;
      z-index: 1;
      line-height: 32px;
    }
    .cascader {
      width: 320px;
      :deep(.el-cascader__tags) {
        left: 70px;
      }
      :deep(.el-input__inner) {
        padding-left: 75px;
      }
      :deep(.el-cascader__tags .el-tag) {
        max-width: 65% !important;
      }
    }
  }
}
.eem-cascader {
  position: relative;
  width: 210px;
  :deep(.el-tag) {
    max-width: 50% !important;
  }
  :deep(.el-input__inner) {
    padding-left: 75px;
  }
}
.eem-cascader-label {
  position: absolute;
  z-index: 1;
  height: 32px;
  line-height: 32px;
  left: 12px;
  @include font_color(ZS);
  font-size: 14px;
}
.date-picker {
  :deep(.el-range__close-icon) {
    position: absolute;
    right: 16px;
  }
  :deep(.el-range__icon.el-icon-date) {
    right: 0 !important;
  }
}
</style>
<style lang="scss">
.meter-type-cascader {
  .custom-buttom-row {
    display: flex;
    justify-content: end;
    align-items: center;
    height: 48px;
    border-top: 1px solid;
    @include border_color(B1);
  }
  .custom-buttom {
    width: 60px;
    text-align: center;
    margin-right: 8px;
    cursor: pointer;
  }
  .el-cascader-menu__wrap {
    height: 228px;
  }
}
</style>
