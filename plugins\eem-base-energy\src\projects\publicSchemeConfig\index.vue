<template>
  <div class="page bg-BG1 rounded-Ra p-J4 box-border">
    <div class="fullheight flex-col flex">
      <publicHeader
        :isDelete="deletePlanIds && deletePlanIds.length > 0"
        @update_tableData="update_tableData"
        @new_plan="new_plan"
        @delete_scheme="delete_scheme"
      ></publicHeader>
      <div class="mt-J3 flex-auto flex-col flex">
        <CetTable
          ref="table"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
          @selection-change="handleSelectionChange"
          row-key="id"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <template v-for="item in Columns_1">
            <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
          </template>
          <ElTableColumn
            :label="$T('操作')"
            width="180"
            header-align="left"
            align="left"
          >
            <template slot-scope="scope">
              <span class="handle mr-J0" @click.stop="toDetail(scope)">
                {{ $T("详情") }}
              </span>
              <span
                class="handle mr-J0"
                @click.stop="toEdit(scope)"
                v-permission="'energyshareconfig_update'"
              >
                {{ $T("编辑") }}
              </span>
              <span
                class="handle"
                @click.stop="copyScheme(scope)"
                v-permission="'energyshareconfig_update'"
              >
                {{ $T("复制方案") }}
              </span>
            </template>
          </ElTableColumn>
        </CetTable>
        <div class="text-right mt-J1">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount"
          ></el-pagination>
        </div>
      </div>
    </div>
    <addPlan v-bind="addPlan" @finishTrigger_out="finishTrigger_out"></addPlan>
    <detailView v-bind="detailView"></detailView>
  </div>
</template>

<script>
import publicHeader from "./subcomponents/publicHeader.vue";
import addPlan from "./subcomponents/addPlan.vue";
import detailView from "./subcomponents/detailView.vue";
import customApi from "@/api/custom";
import common from "eem-base/utils/common";

export default {
  name: "publicSchemeConfig",
  components: { publicHeader, addPlan, detailView },
  data() {
    return {
      CetTable_1: {
        //组件模式设置项
        queryMode: "diff", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        // 是否是树形结构数据
        isTreeData: true,
        event: {}
      },
      Columns_1: [
        {
          type: "",
          prop: "index", // selection 勾选 index 序号
          label: $T("序号"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "70" //绝对宽度
        },
        {
          prop: "objectnameCopy", // 支持path a[0].b
          label: $T("被分摊对象"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "140" //该宽度会自适应
        },
        {
          prop: "objectEnergyTypeNameCopy", // 支持path a[0].b
          label: $T("被分摊能源类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "120" //该宽度会自适应
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("方案名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "200", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "energyShareMethodName", // 支持path a[0].b
          label: $T("分摊方式"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "120", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "energyTypeName", // 支持path a[0].b
          label: $T("分摊能源类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "100", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "timeText", // 支持path a[0].b
          label: $T("生效时间"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "200", //该宽度会自适应
          formatter: common.formatTextCol()
        }
      ],
      // 分页标签
      currentPage: 1,
      pageSize: 20,
      totalCount: 0,
      // 新增方案
      addPlan: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      // 详情页面
      detailView: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      // 能源类型枚举值
      energyOptions: [],
      // 详细id查询数据值
      detailInfoData: {},
      // 周期展示
      timeOptions: [
        {
          id: 7,
          text: "小时"
        },
        {
          id: 12,
          text: "日"
        },
        {
          id: 14,
          text: "月"
        }
      ],
      // 删除方案id集合
      deletePlanIds: [],
      // 筛选数据
      param: {}
    };
  },
  methods: {
    // 筛选条件数据更新
    update_tableData(val, options) {
      this.energyOptions = options;
      this.param = val;
      this.getTableData(val);
    },
    // 打开详情页面
    async toDetail(scope) {
      await this.getDetailInfo(scope.row);
      this.detailView.openTrigger_in = new Date().getTime();
      this.detailView.inputData_in = this._.cloneDeep(this.detailInfoData);
    },
    async toEdit(scope) {
      await this.getDetailInfo(scope.row);
      this.addPlan.openTrigger_in = new Date().getTime();
      this.addPlan.inputData_in = this._.cloneDeep(this.detailInfoData);
    },
    async copyScheme(scope) {
      await this.getDetailInfo(scope.row);
      this.addPlan.openTrigger_in = new Date().getTime();
      let data = this._.cloneDeep(this.detailInfoData);
      data.id = 0;
      data.name = $T("{0}-副本", data.name);
      this.addPlan.inputData_in = data;
    },
    // 批量选中进行删除操作
    handleSelectionChange(val) {
      this.deletePlanIds = [];
      if (val.length) {
        val.forEach(item => {
          this.deletePlanIds.push(item.id);
        });
      }
    },
    // 调用删除接口
    delete_scheme() {
      if (!this.deletePlanIds.length) {
        return this.$message.warning("请选择要删除的方案！");
      }
      this.$confirm("确定要删除所选方案吗？", "删除确认", {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消"
      })
        .then(() => {
          customApi
            .delEnergyShareConfigSchemeBatch(this.deletePlanIds)
            .then(res => {
              if (res.code === 0) {
                this.$message.success("删除成功");
                this.getTableData();
              }
            });
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },
    // 分段操作
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.param.page.index = 0;
      this.param.page.limit = val;
      this.getTableData();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.param.page.index = (val - 1) * this.pageSize;
      this.getTableData();
    },
    // 新增方案
    new_plan() {
      this.addPlan.openTrigger_in = new Date().getTime();
      this.addPlan.inputData_in = {};
    },
    getTableData() {
      customApi.getEnergyShareConfigSchemeGroup(this.param).then(res => {
        if (res.code === 0) {
          let data = this._.get(res, "data", []);
          let tableTestData = [];
          if (data && data.length > 0) {
            data.forEach((item, index) => {
              item.energyShareConfigFirst.index = index + 1;
              item.energyShareConfigFirst.objectnameCopy =
                item.energyShareConfigFirst.objectname;
              item.energyShareConfigFirst.objectEnergyTypeNameCopy =
                item.energyShareConfigFirst.objectEnergyTypeName;

              item.energyShareConfigFirst.timeText = this.getTimeText(
                item.energyShareConfigFirst
              );
              if (
                item.energyShareConfigVos &&
                item.energyShareConfigVos.length
              ) {
                item.energyShareConfigVos.forEach(key => {
                  key.timeText = this.getTimeText(key);
                });
                item.energyShareConfigFirst.children =
                  item.energyShareConfigVos;
              }
              tableTestData.push(item.energyShareConfigFirst);
            });
          }
          this.CetTable_1.data = tableTestData;
          this.totalCount = res.total;
          this.$nextTick(() => {
            this.$refs.table.$refs.cetTable.doLayout();
          });
        }
      });
    },
    getTimeText(item) {
      let timeText = "";
      if (item.endtime) {
        if (item.endtime == 9999000000000) {
          timeText =
            this.$moment(item.starttime).format("YYYY-MM-DD") +
            " 至 " +
            " 永远";
        } else {
          timeText =
            this.$moment(item.starttime).format("YYYY-MM-DD") +
            " 至 " +
            this.$moment(item.endtime).format("YYYY-MM-DD");
        }
      } else {
        timeText = this.$moment(item.starttime).format("YYYY/MM/DD");
      }
      return timeText;
    },
    // 获取分摊方案的详细数据信息
    async getDetailInfo(val) {
      if (!(val && val.id)) {
        return;
      }
      this.detailInfoData = {};
      await customApi.getApportDetailData(val.id).then(res => {
        if (res.code == 0) {
          let data = this._.get(res, "data", []);
          if (data.length > 0) {
            data.forEach(item => {
              item.energysharemethodText =
                item.energysharemethod == 1
                  ? $T("固定比例")
                  : item.energysharemethod == 2
                  ? $T("动态分摊")
                  : "";
              item.energytypeText = this.energyOptions.find(
                x => x.id === item.energytype
              )?.text;
              item.objectenergytypeText = this.energyOptions.find(
                x => x.id === item.objectenergytype
              )?.text;
              item.cycleText = this.timeOptions.find(
                x => x.id === item.aggregationcycle
              )?.text;
              item.objectname = val.objectname;
              if (item.endtime) {
                item.isForever = false;
                if (item.endtime == 9999000000000) {
                  item.isForever = true;
                  item.timeText =
                    this.$moment(item.starttime).format("YYYY-MM-DD") +
                    " 至 " +
                    " 永远";
                } else {
                  item.timeText =
                    this.$moment(item.starttime).format("YYYY-MM-DD") +
                    " 至 " +
                    this.$moment(item.endtime).format("YYYY-MM-DD");
                }
              } else {
                item.timeText = this.$moment(item.starttime).format(
                  "YYYY-MM-DD"
                );
              }
            });
          }
          this.detailInfoData = data[0];
        }
      });
    },
    finishTrigger_out() {
      this.param.page.index = 0;
      delete this.param.baseVo;
      this.getTableData(this.param);
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .handle {
    cursor: pointer;
    @include font_color(ZS);
  }
}
</style>
